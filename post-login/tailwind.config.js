module.exports = {
  content: [
    "./client/**/*.{js,jsx,ts,tsx}",
    "./node_modules/@sr/**/*.{js,jsx,ts,tsx}"
  ],
  // darkMode: false, // or 'media' or 'class'
  // important: true,
  theme: {
    extend: {
      fontFamily: {
        'muli': ['muli', 'sans-serif'],
        'poppins': ['poppins', 'sans-serif'],
        'ptsans': ['PT Sans', 'sans-serif'],
        'playfairdisplay': ['Playfair Display', 'sans-serif'],
        'sourcesanspro': ['Source Sans Pro', 'sans-serif'],
        'roboto': ['Roboto', 'sans-serif'],
      },
      colors: {
        'blue-1': '#0f69fa',
        'blue-2': "#0002F2",
        'blue-default-anchor': '#4183c4',
        'grey-1': '#f5f5f5',
        'grey-2': '#223e2626',
        'green-1': '#00e19b',
        'accent-1': '#ffb900',
        'tawny': '#c83200',
        'blue-pricing': 'rgba(15,105,250,.04)',
        'black-1': '#282828',

        // sr design system
        'sr-default-blue': '#0F69FA',
        'sr-dark-blue': '#0459E0',
        'sr-soft-blue': '#73A7FD',
        'sr-light-blue': '#E4EEFF',
        'sr-border-blue': '#009EFF',
        'sr-default-red': '#E21D12',
        'sr-dark-red': '#CA150C',
        'sr-soft-red': '#EF8983',
        'sr-light-red': '#FBD8D6',
        'sr-default-yellow': '#E8C515',
        'sr-dark-yellow': '#BF9D40',
        'sr-soft-yellow': '#EFD65B',
        'sr-light-yellow': '#FCF6DC',
        'sr-lightest-yellow': '#FEF5D5',
        'sr-default-orange': '#FFA500',
        'sr-dark-orange': '#FF7518',
        'sr-soft-orange': '#F5CA7B',
        'sr-light-orange': '#FCE9C5',
        'sr-default-green': '#19994F',
        'sr-dark-green': '#0A7637',
        'sr-soft-green': '#8BC3A2',
        'sr-light-green': 'rgba(22, 136, 70, 0.1)',
        'sr-default-grey': '#626C7C',
        'sr-dark-grey': '#263246',
        'sr-soft-grey': '#8992A1',
        'sr-light-grey': '#DBDFE5',
        'sr-text-grey': '#122E59',
        'sr-subtext-grey': '#626C7C',
        'sr-placeholder-grey': '#8992A1',
        'sr-icon-grey': '#A9B0BC',
        'sr-border-grey': '#C4CAD3',
        'sr-divider-grey': '#DBDFE5',
        'sr-lighter-grey': '#E8EBEF',
        'sr-page-grey': '#F4F5F7',
        'sr-header-grey': '#F5F7FA',
        'sr-title-grey': '#2C3644',
        'sr-light-grey-2': '#7F8A9C',
        'sr-dark-purple': '#6E17ED',
        'sr-default-purple': '#A00FFA',
        'sr-soft-purple': '#BF68FE',
        'sr-light-purple': '#E4C0FD',
        'sr-lightest-purple': '#F5E6FE',
        "sr-dark-indigo": "#6D43FF",
        "sr-default-indigo": "#2A0FFA",
        "sr-soft-indigo": "#EEE5FF",
        "sr-warning-brown": "#92530F",

        /*
        Date: 23-may-2024
         - New SR Color Palettes.
         - These are the standard colors we will use across our app.
         - We have 10 shades of each color, 1 being the lightest and 10 being the darkest.
         - Ref: https://www.figma.com/design/FM1t6ejAFFZlbM7Ajl1iXg?node-id=4333-13250#813695752
        */
        'sr-primary-10': '#E1F1FF',
        'sr-primary-100': '#2C30C7',
        'sr-primary-20': '#B8DCFF',
        'sr-primary-30': '#87C6FF',
        'sr-primary-40': '#4CB0FF',
        'sr-primary-50': '#009EFF',
        'sr-primary-60': '#008CFF',
        'sr-primary-70': '#007DFF',
        'sr-primary-80': '#0F69FA',
        'sr-primary-90': '#1F56E7',

        'sr-secondary-10': '#FEF8E1',
        'sr-secondary-100': '#F97109',
        'sr-secondary-20': '#FDECB4',
        'sr-secondary-30': '#FCDF84',
        'sr-secondary-40': '#FCD453',
        'sr-secondary-50': '#FBC92F',
        'sr-secondary-60': '#FAC018',
        'sr-secondary-70': '#FAB212',
        'sr-secondary-80': '#FAA00F',
        'sr-secondary-90': '#FA8F0D',

        'sr-cyan-10': '#DFFAFE',
        'sr-cyan-100': '#00707B',
        'sr-cyan-20': '#AFF1FD',
        'sr-cyan-30': '#74E9FC',
        'sr-cyan-40': '#0FDFFA',
        'sr-cyan-50': '#00D7F7',
        'sr-cyan-60': '#00CFF4',
        'sr-cyan-70': '#00BEDF',
        'sr-cyan-80': '#00A8C3',
        'sr-cyan-90': '#0094AA',

        'sr-danger-05': '#FDF2F2',
        'sr-danger-10': '#FDE8E8',
        'sr-danger-20': '#FBD5D5',
        'sr-danger-30': '#F8B4B4',
        'sr-danger-40': '#F98080',
        'sr-danger-50': '#F05252',
        'sr-danger-60': '#E02424',
        'sr-danger-70': '#C81E1E',
        'sr-danger-80': '#9B1C1C',
        'sr-danger-90': '#771D1D',

        'sr-gray-00': '#FFFFFF',
        'sr-gray-10': '#F4F7FB',
        'sr-gray-20': '#ECEEF0',
        'sr-gray-30': '#E0E5EB',
        'sr-gray-40': '#CFD3DA',
        'sr-gray-50': '#BFC4CD',
        'sr-gray-60': '#ACB3BF',
        'sr-gray-70': '#94A1B8',
        'sr-gray-80': '#7F8A9C',
        'sr-grey-80': '#6A85AF',
        'sr-gray-90': '#556A8B',
        'sr-grey-90': '#516990',
        'sr-grey-100': '#223858',
        'sr-gray-100': '#223858',
        'sr-focus-grey': '#F2F4F8',

        'sr-indigo-10': '#EEE5FF',
        'sr-indigo-100': '#0000EB',
        'sr-indigo-20': '#D2C0FE',
        'sr-indigo-30': '#B295FF',
        'sr-indigo-40': '#8E68FF',
        'sr-indigo-50': '#6D43FF',
        'sr-indigo-60': '#4213FF',
        'sr-indigo-70': '#2A0FFA',
        'sr-indigo-80': '#0002F2',
        'sr-indigo-90': '#0000EE',

        'sr-info-10': '#CDFCFF',
        'sr-info-20': '#9BF2FF',
        'sr-info-30': '#6AE3FF',
        'sr-info-40': '#45CFFF',
        'sr-info-50': '#07B0FF',
        'sr-info-60': '#0589DB',
        'sr-info-70': '#0366B7',
        'sr-info-80': '#024893',
        'sr-info-90': '#01347A',

        'sr-lime-10': '#F8FECA',
        'sr-lime-100': '#3C4304',
        'sr-lime-20': '#E2FA26',
        'sr-lime-30': '#D4EE0E',
        'sr-lime-40': '#C7DF0D',
        'sr-lime-50': '#B9CF0C',
        'sr-lime-60': '#A9BD0B',
        'sr-lime-70': '#97A90A',
        'sr-lime-80': '#819109',
        'sr-lime-90': '#667307',

        'sr-pink-10': '#FEE5ED',
        'sr-pink-100': '#980057',
        'sr-pink-20': '#FDBCD3',
        'sr-pink-30': '#FD91B6',
        'sr-pink-40': '#FC6298',
        'sr-pink-50': '#FB3D80',
        'sr-pink-60': '#FA0F69',
        'sr-pink-70': '#E80B66',
        'sr-pink-80': '#D20761',
        'sr-pink-90': '#BD015E',

        'sr-success-10': '#EEFDD2',
        'sr-success-20': '#D9FBA7',
        'sr-success-30': '#BBF379',
        'sr-success-40': '#9DE756',
        'sr-success-50': '#72D824',
        'sr-success-60': '#56B91A',
        'sr-success-70': '#3E9B12',
        'sr-success-80': '#297D0B',
        'sr-success-90': '#1B6706',

        'sr-teal-10': '#E8FFF6',
        'sr-teal-100': '#04492F',
        'sr-teal-20': '#B0FDE0',
        'sr-teal-30': '#57FCBD',
        'sr-teal-40': '#0EF09A',
        'sr-teal-50': '#0DDF8F',
        'sr-teal-60': '#0CCC83',
        'sr-teal-70': '#0BB675',
        'sr-teal-80': '#099D64',
        'sr-teal-90': '#077C4F',

        'sr-violet-10': '#F5E6FE',
        'sr-violet-100': '#0019E3',
        'sr-violet-20': '#E4C0FD',
        'sr-violet-30': '#D395FE',
        'sr-violet-40': '#BF68FE',
        'sr-violet-50': '#B042FD',
        'sr-violet-60': '#A00FFA',
        'sr-violet-70': '#8C15F4',
        'sr-violet-80': '#6E17ED',
        'sr-violet-90': '#4F17E8',

        'sr-warning-10': '#FEF5D5',
        'sr-warning-20': '#FEE9AD',
        'sr-warning-30': '#FEDA83',
        'sr-warning-40': '#FDCB64',
        'sr-warning-50': '#FCB232',
        'sr-warning-60': '#D88F24',
        'sr-warning-70': '#B57019',
        'sr-warning-80': '#92530F',
        'sr-warning-90': '#783F09',

        'sr-illustration-grey': '#DAD5D4',
        'sr-illustration-pink': '#FF5872',
        'sr-illustration-yellow': '#FFCA15',
        'sr-llustration-lime': '#F9F871'
      },
      lineHeight: {
        'h1': '50px',
      },
      
      height: {
        'webkit-fill': '-webkit-fill-available',
      },
    
      boxShadow: {
        'home-hero-input': '0 2px 6px rgb(12 113 195 / 20%)',
        'home-hero-button': '0 4px 14px rgb(0 225 155 / 30%)',
        'home-testimonial': '20px 20px 100px rgb(0 0 0 / 10%)',
        'integration-tile': '0 6px 40px rgb(0 0 0 / 6%)',
        'top-bottom-left': '-15px 15px 25px -5px rgba(0, 0, 0, 0.1), -8px 8px 10px -6px rgba(0, 0, 0, 0.1)', // Custom shadow for top, bottom, and left

      },
      gridTemplateColumns: {
        // Simple 16 column grid
        '16': 'repeat(16, minmax(0, 1fr))',
      },
      // gradientColorStops: {
      backgroundImage: {
        'agency-hero': 'linear-gradient(180deg,rgba(15,105,250,0),rgba(15,105,250,.06))',
      },
      flexBasis: {
        'heading-strip': '60px'
      },
      keyframes: {
        "shimmer": {
          "100%": {
            "transform": "translateX(100%)",
          },
        }
      }
    },
  },
  variants: {
    extend: {},
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
  // safelist: [{
  //   pattern: /(bg|text|border)-sr-(dark|default|soft|light)-(blue|red|grey)/
  // }
  // ]
}