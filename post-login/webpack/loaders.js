'use strict';

exports.tslint = {
  test: /\.tsx?$/,
  enforce: 'pre',
  use: ['tslint-loader'],
  include: [/client/],
  exclude: [/node_modules/, /typings/],
};

exports.tsx = {
  test: /\.tsx?$/,
  use: ['ts-loader'],
  include: [/client/],
  exclude: [/node_modules/, /typings/],
};

exports.ts = {
  test: /\.ts?$/,
  use: ['ts-loader'],
  include: [/client/],
  exclude: [/node_modules/, /typings/],
};

exports.html = {
  test: /\.html$/,
  use: ['raw-loader'],
  include: [/client/],
  exclude: [/node_modules/, /typings/],
};

exports.scss = {
  test: /\.scss$/,
  // loader: 'style-loader!css-loader?-minimize!sass-loader',
  use: ['style-loader', 'css-loader', 'sass-loader'],
  // loader: 'style-loader!css-loader?-minimize&importLoaders=1!sass-loader',
  // loaders: ['style', 'css', 'sass'],
  exclude: [/node_modules/, /typings/],
};

exports.css = {
  test: /\.css$/,
  use: ['style-loader', 'css-loader', 'postcss-loader'],
  exclude: /client\/styles\/bootstrap-glyphicons.css/,
};

// exports.json = {
//   test: /\.json$/,
//   loader: 'json-loader',
// };

// exports.png = makeUrlLoader(/\.png$/);

exports.fontLoader = {
  test: /\.(png|webp|ttf|eot|svg|woff(2)?)(\?[a-z0-9]+)?$/,
  use: ['file-loader'],
};


// exports.less={
//         test: /\.less$/,
//         loader: "style!css!less"
// };


// exports.svg = makeUrlLoader(/\.svg$/);
// exports.eot = makeUrlLoader(/\.eot$/);
// exports.woff = {
//   // Match woff2 in addition to patterns like .woff?v=1.1.1.
//   test: /\.woff(2)?(\?v=[0-9]\.[0-9]\.[0-9])?$/,
//   loader: 'url',
//   query: {
//     limit: 50000,
//     mimetype: 'application/font-woff',
//   },
// };
// // exports.woff = makeUrlLoader(/\.woff$/);
// // exports.woff2 = makeUrlLoader(/\.woff2$/);
// exports.ttf = makeUrlLoader(/\.ttf$/);


function makeUrlLoader(pattern) {
  return {
    test: pattern,
    loader: 'url-loader?limit=100000',
    // include: '/node_modules/bootstrap-sass',
    exclude: [
      /node_modules/,
      /typings/,
    ],
  };
}
