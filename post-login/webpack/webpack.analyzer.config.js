//run this command: 
//NODE_ENV=production node_modules/.bin/webpack   --config webpack/webpack.analyzer.config.js
const production = require('../webpack.config.js')
const { merge } = require('webpack-merge');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

module.exports = merge(production, {
	plugins: [
		new BundleAnalyzerPlugin({
			analyzerMode: 'server',
			openAnalyzer: true
		} )
	]
})
