"use strict";

const path = require("path");
const webpack = require("webpack");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const AddAssetHtmlPlugin = require("add-asset-html-webpack-plugin");
const WebpackRequireFrom = require('webpack-require-from');


// const StyleLintPlugin = require('stylelint-webpack-plugin');
// const SplitByPathPlugin = require("webpack-split-by-path");
const CopyWebpackPlugin = require("copy-webpack-plugin");
const CompressionPlugin = require("compression-webpack-plugin");
// const UglifyJsPlugin = require("uglifyjs-webpack-plugin");
var HardSourceWebpackPlugin = require('hard-source-webpack-plugin');
// const { SubresourceIntegrityPlugin } = require("webpack-subresource-integrity");

// const sourceMap = process.env.TEST || process.env.NODE_ENV !== 'production'
//   ? [new webpack.SourceMapDevToolPlugin({ filename: null, test: /\.tsx?$/ })]
//   : [];


const basePlugins = [
  new webpack.DefinePlugin({
    // __DEV__: process.env.NODE_ENV !== "production",
    // __TEST__: JSON.stringify(process.env.TEST || false),
    // "process.env.NODE_ENV": JSON.stringify(process.env.NODE_ENV),

    "process.env.NODE_DEBUG": false,
    "process.env.REACT_APP_ASSET_PATH": JSON.stringify(process.env.REACT_APP_ASSET_PATH)



    // This has effect on the react lib size
    // NODE_ENV: JSON.stringify("production")
  }),
  new HtmlWebpackPlugin({
    template: "./client/index.html",
    inject: "body",
      /** COMMENT publicPath  For local testing  */
    publicPath: process.env.REACT_APP_ASSET_PATH || '/assets'
  }),

  // REF: https://webpack.js.org/plugins/ignore-plugin/#example-of-ignoring-moment-locales
  new webpack.IgnorePlugin({
    resourceRegExp: /^\.\/locale$/,
    contextRegExp: /moment$/,
  }),

  /** COMMENT publicPath For local testing */
  new WebpackRequireFrom({
    variableName: 'window.__webpack_public_path__'
  })


];

const devPlugins = [
  // new StyleLintPlugin({
  //   configFile: './.stylelintrc',
  //   files: ['client/**/*.scss'],
  //   failOnError: false,
  // }),

  /*
  // start: DLL config

  new webpack.DllReferencePlugin({
    context: ".",
    manifest: require("../public/dll/core-manifest.json")
  }),

  new webpack.DllReferencePlugin({
    context: ".",
    manifest: require("../public/dll/react-manifest.json")
  }),

  new webpack.DllReferencePlugin({
    context: '.',
    manifest: require('../public/dll/semantic-manifest.json'),
  }),
  */

  new AddAssetHtmlPlugin([
    { filepath: path.resolve(__dirname, '../public/dll/*.dll.js') },

    // { filepath: require.resolve("../public/dll/core.dll.js") },
    // { filepath: require.resolve("../public/dll/react.dll.js") }
    // { filepath: require.resolve('./public/dll/semantic.dll.js')  },
  ]),

  // new HardSourceWebpackPlugin(),


  // end: DLL config

  new CopyWebpackPlugin({ patterns: [{ from: "client/assets", to: "assets" }] })
];

const prodPlugins = [
  new CopyWebpackPlugin({ patterns: [{ from: "client/assets", to: "." }] }),

  // new webpack.optimize.DedupePlugin(), // dedupe similar code

  // new webpack.optimize.OccurrenceOrderPlugin(),

  new webpack.optimize.AggressiveMergingPlugin(), // Merge chunks

  // new SplitByPathPlugin([
  //   { name: "vendor", path: [path.join(__dirname, "..", "node_modules/")] }
  // ]),

  // new webpack.optimize.UglifyJsPlugin({
  //   deadCode: true,
  //   compress: {
  //     warnings: false,
  //     dead_code: true,
  //     screw_ie8: true,
  //     unused: true,

  //     // Drop console statements
  //     drop_console: true
  //   },
  //   comments: false,
  //   sourceMap: false
  // }),

  // new UglifyJsPlugin({
  //   sourceMap: true,
  //   uglifyOptions: {
  //     ecma: 8,
  //     ie8: false,
  //     compress: {
  //       warnings: false,
  //       dead_code: true,
  //       unused: true,

  //       // Drop console statements
  //       drop_console: false
  //     }
  //   }
  // }),

  new CompressionPlugin({
    filename: "[name].gz[query]",
    algorithm: "gzip",
    test: /\.js$|\.html$/,
    threshold: 10240,
    minRatio: 0.8,
    // cache: true
  }),

  // new SubresourceIntegrityPlugin({
  //   hashFuncNames: ['sha384'],
  //   enabled: true
  // })
];

module.exports = basePlugins
  .concat(process.env.NODE_ENV === "production" ? prodPlugins : [])
  .concat(process.env.NODE_ENV === "development" ? devPlugins : []);
