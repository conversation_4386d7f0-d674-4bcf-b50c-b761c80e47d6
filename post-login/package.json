{"name": "heaplabs-coldemail-app", "version": "0.1.0", "private": true, "engines": {"node": ">10"}, "scripts": {"cypress:run": "cypress run --browser chrome", "dev": "vite", "build": "npm run cypress:run && cross-env NODE_ENV=production REACT_APP_ASSET_PATH=https://cdn.smartreach.io/sr/public/public/assets/ vite build", "build-without-test": "cross-env NODE_ENV=production REACT_APP_ASSET_PATH=https://cdn.smartreach.io/sr/public/public/assets/ vite build", "build-without-test-staging": "cross-env NODE_ENV=production vite build", "build-local": "npm run cypress:run && vite build", "preview": "vite preview", "upload": "gsutil -m rsync -r  sr gs://cdn-sr/sr", "clean": "rimraf sr/public/public/", "test": "jest", "artifactregistry-login": "npx google-artifactregistry-auth", "bundle-analyzer": "vite-bundle-analyzer", "typings": "rimraf typings/ && typings install", "stats": "vite build --mode analyze"}, "nyc": {"extends": "@istanbuljs/nyc-config-typescript", "all": true}, "devDependencies": {"@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@cypress/code-coverage": "^3.12.20", "@headlessui/react": "1.6.5", "@heroicons/react": "^1.0.5", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@sr/design-component": "1.1.65", "@sr/shared-product-components": "0.1.206", "@srio/sr-calling-services": "^0.1.5", "@stripe/react-stripe-js": "^3.4.0", "@stripe/stripe-js": "^6.0.0", "@tailwindcss/forms": "^0.5.3", "@testing-library/cypress": "^10.0.1", "@testing-library/jest-dom": "^4.2.4", "@tinymce/tinymce-react": "3.13.1", "@types/axios": "0.9.36", "@types/chrome": "^0.0.193", "@types/d3-hierarchy": "3.1.7", "@types/dompurify": "^2.0.0", "@types/file-saver": "^2.0.5", "@types/jest": "^29.2.4", "@types/lodash": "4.14.76", "@types/moment": "2.13.0", "@types/moment-timezone": "^0.5.9", "@types/node": "^18.11.18", "@types/react": "^17.0.2", "@types/react-color": "^3.0.1", "@types/react-datepicker": "2.3.0", "@types/react-dom": "^17.0.2", "@types/react-helmet": "6.1.11", "@types/react-router": "5.1.5", "@types/react-router-dom": "5.1.3", "@types/react-window": "^1.8.5", "@types/react-youtube": "7.4.0", "@types/recharts": "^1.8.24", "@types/testing-library__jest-dom": "^5.14.6", "@vitejs/plugin-react": "^4.7.0", "add-asset-html-webpack-plugin": "3.2.0", "ag-grid-community": "28.0.0", "ag-grid-react": "28.0.0", "autoprefixer": "^10.4.0", "axios": "0.19.1", "babel-loader": "^9.1.3", "babel-plugin-istanbul": "^6.1.1", "babel-plugin-styled-components": "^2.1.4", "compression-webpack-plugin": "9.0.1", "concurrently": "^8.2.2", "copy-webpack-plugin": "10.0.0", "cross-env": "2.0.1", "css-loader": "5.2.7", "cypress": "^13.6.3", "cypress-iframe": "^1.0.1", "date-fns": "^2.10.0", "dompurify": "^2.0.8", "es6-promise": "4.1.1", "eslint": "3.19.0", "fast-deep-equal": "^3.1.1", "file-loader": "6.2.0", "formik": "^2.2.9", "hard-source-webpack-plugin": "^0.13.1", "html-webpack-plugin": "^5.5.0", "i18n-iso-countries": "^7.12.0", "istanbul-lib-coverage": "^3.2.2", "jest": "^29.3.1", "jest-canvas-mock": "^2.5.1", "jest-environment-jsdom": "^29.5.0", "js-file-download": "0.4.9", "libphonenumber-js": "^1.10.39", "lodash": "^4.17.15", "lottie-web": "^5.10.0", "mobx": "6.3.7", "mobx-react": "7.2.1", "moment": "^2.24.0", "moment-timezone": "^0.5.27", "nodemon": "^1.19.1", "nyc": "^15.1.0", "papaparse": "4.3.6", "postcss": "^8.4.5", "postcss-loader": "^6.2.1", "postcss-preset-env": "^7.1.0", "process": "^0.11.10", "prop-types": "^15.6.0", "qrcode.react": "^1.0.1", "query-string": "6.9.0", "raw-loader": "4.0.2", "react": "17.0.2", "react-calendly": "^4.1.1", "react-color": "^2.17.3", "react-csv": "1.1.2", "react-datepicker": "^4.8.0", "react-dom": "17.0.2", "react-dropzone": "3.13.4", "react-helmet": "6.1.0", "react-hot-loader": "2.0.0-alpha", "react-phone-input-2": "^2.15.1", "react-recaptcha": "^2.3.10", "react-router-dom": "5.1.2", "react-youtube": "7.9.0", "recharts": "^2.10.4", "resolve-url-loader": "4.0.0", "rimraf": "2.6.2", "sass": "^1.57.1", "sass-loader": "12.3.0", "source-map-loader": "3.0.0", "source-map-support": "0.5.21", "store": "2.0.12", "style-loader": "3.3.1", "styled-components": "^6.1.8", "tailwindcss": "^3.0.7", "terser-webpack-plugin": "^5.2.5", "ts-jest": "^29.0.3", "ts-loader": "^9.2.6", "ts-node": "^10.9.1", "tslib": "2.3.1", "tslint": "6.1.1", "tslint-loader": "3.5.4", "typescript": "5.6.2", "universal-cookie": "2.1.2", "url-loader": "4.1.1", "util": "^0.12.4", "vite": "^5.4.19", "webpack": "5.64.2", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^4.9.1", "webpack-dev-server": "4.5.0", "webpack-subresource-integrity": "^5.2.0-rc.1"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^7.0.2", "@sentry/react": "8.0.0", "@stripe/react-stripe-js": "^3.5.1", "@stripe/stripe-js": "^6.1.0", "@testing-library/react": "12.1.2", "@twilio/voice-sdk": "^2.7.1", "@xyflow/react": "12.0.4", "apexcharts": "^3.49.0", "cypress-xpath": "^2.0.1", "d3-hierarchy": "^3.1.2", "file-saver": "^2.0.5", "intl-tel-input": "23.8.0", "leva": "^0.9.35", "lucide-react": "^0.482.0", "pusher-js": "^7.1.1-beta", "react-apexcharts": "^1.4.1", "react-select": "^5.7.4", "react-virtualized-auto-sizer": "^1.0.20", "react-virtuoso": "^4.7.10", "react-window": "^1.8.9", "reactjs-popup": "^2.0.6", "webpack-require-from": "^1.8.6"}, "resolutions": {"lodash": "^4.17.13"}}