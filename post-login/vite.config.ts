import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  
  // Entry point - Vite will automatically look for index.html in root
  root: '.',
  
  // Build configuration
  build: {
    // Output directory equivalent to webpack's output.path
    outDir: 'sr/public/public/assets',
    
    // Generate source maps for production (equivalent to webpack's devtool)
    sourcemap: process.env.NODE_ENV === 'development' ? true : 'hidden',
    
    // Asset file naming (equivalent to webpack's filename patterns)
    rollupOptions: {
      output: {
        // Equivalent to webpack's filename and chunkFilename patterns
        entryFileNames: process.env.NODE_ENV === 'production' ? '[name].[hash].js' : '[name].js',
        chunkFileNames: process.env.NODE_ENV === 'production' ? '[name].chunk.[hash].js' : '[name].chunk.js',
        assetFileNames: process.env.NODE_ENV === 'production' ? '[name].[hash].[ext]' : '[name].[ext]',
      }
    },
    
    // Equivalent to webpack's target
    target: 'es2015',
    
    // Clear output directory before build
    emptyOutDir: true,
  },
  
  // Development server configuration
  server: {
    port: 3001,
    host: true, // equivalent to allowedHosts: 'all'
    
    // Proxy configuration (equivalent to webpack-dev-server proxy)
    proxy: {
      '/api': {
        target: 'https://devapi.sreml.com',
        changeOrigin: true,
        secure: false,
      }
    }
  },
  
  // Module resolution (equivalent to webpack's resolve)
  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.json'],
    alias: {
      // Equivalent to webpack's resolve.alias
      'react/jsx-runtime': 'react/jsx-runtime.js'
    }
  },
  
  // CSS configuration
  css: {
    // PostCSS will be automatically detected from postcss.config.js
    postcss: './postcss.config.js',
  },
  
  // Define global constants (equivalent to webpack's DefinePlugin)
  define: {
    'process.env.NODE_DEBUG': false,
    'process.env.REACT_APP_ASSET_PATH': JSON.stringify(process.env.REACT_APP_ASSET_PATH || '/assets/'),
    // Set __webpack_public_path__ for compatibility
    '__webpack_public_path__': JSON.stringify(process.env.REACT_APP_ASSET_PATH || '/assets/'),
  },
  
  // Equivalent to webpack's externals (if needed)
  // external: [],
  
  // Asset handling
  assetsInclude: ['**/*.png', '**/*.webp', '**/*.ttf', '**/*.eot', '**/*.svg', '**/*.woff', '**/*.woff2'],
  
  // Public directory for static assets (equivalent to CopyWebpackPlugin)
  publicDir: 'client/assets',
  
  // Base public path
  base: process.env.REACT_APP_ASSET_PATH || '/assets/',
})
