{"compileOnSave": false, "buildOnSave": false, "compilerOptions": {"module": "esnext", "moduleResolution": "bundler", "target": "es2020", "jsx": "react-jsx", "emitDecoratorMetadata": false, "experimentalDecorators": true, "noImplicitAny": true, "alwaysStrict": true, "forceConsistentCasingInFileNames": true, "allowJs": false, "removeComments": true, "sourceMap": true, "inlineSourceMap": false, "noEmitHelpers": true, "importHelpers": true, "strictNullChecks": true, "allowUnreachableCode": false, "allowUnusedLabels": false, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "pretty": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "useDefineForClassFields": true, "skipLibCheck": true, "types": ["node", "chrome", "jest", "@testing-library/jest-dom", "vite/client"], "lib": ["dom", "dom.iterable", "es6", "es2020"]}, "include": ["client/**/*", "vite.config.ts"], "exclude": ["node_modules", "dist", "typings", "serverBuild", "sr"]}