{
  "compileOnSave": false,
  "buildOnSave": false,
  "compilerOptions": {
    "module": "esnext",
    "moduleResolution": "node",
    "target": "es6",
    "jsx": "react",
    "emitDecoratorMetadata": false,
    "experimentalDecorators": true,
    "noImplicitAny": true,
    "alwaysStrict": true,
    "forceConsistentCasingInFileNames": true,
    "allowJs": false,
    "removeComments": true,
    "sourceMap": true,
    "inlineSourceMap": false,
    "noEmitHelpers": true,
    "importHelpers": true,
    "strictNullChecks": true,
    "allowUnreachableCode": false,
    "allowUnusedLabels": false,
    "noFallthroughCasesInSwitch": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "pretty": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "types": [
      "node",
      "chrome" //https://github.com/angular/angular-cli/issues/17849
      ,"jest"
      ,"@testing-library/jest-dom"
    ],
    "lib": [
      "dom",
      "scripthost",
      "es5",
      "es2015.promise"
    ]
  },
  "exclude": [
    "node_modules",
    "dist",
    "typings",
    "serverBuild"
  ]
}