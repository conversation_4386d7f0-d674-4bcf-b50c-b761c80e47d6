/*

To customize, datagrid, following references would be helpful:

https://www.ag-grid.com/javascript-grid-themes-provided/#customizing-sass-variables
https://stackoverflow.com/questions/49183522/issues-customizing-the-ag-grid-themes/49325516

*/

/*
// Set the colors to blue and amber
$ag-primary-color: #2196f3; // blue-500
$ag-accent-color: #2185d0; // amber-A200

$ag-grid-size: 7px; // 8 by default
$ag-icon-size: 16px; // 18 by default

$ag-font-family: inherit;
$ag-secondary-font-family: inherit;

$ag-font-size: inherit;
$ag-secondary-font-size: inherit;

$ag-border-color: #e9e9e9; // as per figma
$ag-foreground-color: inherit; // inherit font color from the body
*/

@import "ag-grid-community/src/styles/ag-grid.scss";

// Import the ag-Grid material theme
// @import "ag-grid-community/src/styles/ag-theme-material/sass/ag-theme-material.scss";
@import "ag-grid-community/src/styles/ag-theme-material/sass/ag-theme-material-mixin.scss";

// REF: https://www.ag-grid.com/javascript-grid-themes-v23-migration/
.ag-theme-material {
  border-radius: 6px;

  @include ag-theme-material(
    (
      material-primary-color: #2196f3,
      // blue-500
      material-accent-color: #0f69fa,
      // amber-A200
      grid-size: 7px,
      // 8 by default
      icon-size: 16px,
      // 18 by default
      font-family: inherit,
      line-height: 56px,
      // secondary-font-family: inherit,
      font-size: inherit,
      // secondary-font-size: inherit,
      // foreground-color: inherit, // inherit font color from the body,
    )
  );

  // .ag-header {
  //     // or write CSS selectors to make customisations beyond what the parameters support
  //     text-shadow: deeppink;
  // }
}

.ag-theme-material {
  .ag-row-selected {
    background-color: #e4eeff;
    &::before {
      background-color: transparent;
    }
  }
}
.ag-theme-material .ag-header {
  border-bottom: 0px;
  background-color: #f4f7fb;
  border-radius: 8px;
}

.ag-theme-material .ag-header-cell {
  font-family: Source Sans Pro, sans-serif;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  letter-spacing: 0em;
  color: #556a8b;
}

.ag-theme-material .ag-header-cell:hover {
  background-color: #f4f7fb !important;
}

.ag-theme-material {
  .ag-row {
    border-left-color: transparent;
    border-right: 0px !important;
    height: 56px;
  }
  .ag-center-cols-clipper {
    margin-bottom: 16px;
  }
  .ag-center-cols-container {
    margin-right: 16px;
  }
}

.ag-theme-material .ag-checkbox-input {
  position: absolute;
}

.ag-theme-material .ag-ltr .ag-cell {
  border-right: 0px;
}

.ag-theme-material
  .ag-cell.ag-cell-last-left-pinned:not(.ag-cell-range-right):not(
    .ag-cell-range-single-cell
  ) {
  border-right: 0px;
}

.ag-theme-material .ag-pinned-left-header {
  border-right: 0px;
}

.ag-theme-material .ag-ltr .ag-cell-focus {
  border: 0px !important;
}

.ag-theme-material .ag-row-hover:not(.ag-full-width-row)::before,
.ag-theme-material .ag-row-hover.ag-full-width-row.ag-row-group::before {
  background-color: #e1f1ff;
}

/*
// Following is an attempt to create a border shadow for the pinned "email" first column
.ag-theme-material .ag-ltr .ag-root:not(.ag-has-focus) .ag-cell.ag-cell-last-left-pinned:not(.ag-cell-range-right) {
  border-right: 1px solid #e2e2e2;
  box-shadow: 3px 0 5px -2px rgba(136, 136, 136, .3) !important;
}


.ag-theme-material .ag-ltr .ag-root:not(.ag-has-focus) .ag-cell.ag-cell-last-left-pinned:not(.ag-cell-range-right) .ag-row-hover {
  border-right: 1px solid #e2e2e2;
  box-shadow: 3px 0 5px -2px rgba(136, 136, 136, .3) !important;
}
*/
