(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["react-popper"],{93272:function(e,t,r){"use strict";r.r(t),r.d(t,{Manager:function(){return i},Popper:function(){return S},Reference:function(){return P},usePopper:function(){return h}});var n=r(89526),u=n.createContext(),o=n.createContext();function i(e){var t=e.children,r=n.useState(null),i=r[0],a=r[1],f=n.useRef(!1);n.useEffect((function(){return function(){f.current=!0}}),[]);var c=n.useCallback((function(e){f.current||a(e)}),[]);return n.createElement(u.Provider,{value:i},n.createElement(o.Provider,{value:c},t))}var a=function(e){return Array.isArray(e)?e[0]:e},f=function(e){if("function"===typeof e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e.apply(void 0,r)}},c=function(e,t){if("function"===typeof e)return f(e,t);null!=e&&(e.current=t)},s=function(e){return e.reduce((function(e,t){var r=t[0],n=t[1];return e[r]=n,e}),{})},l="undefined"!==typeof window&&window.document&&window.document.createElement?n.useLayoutEffect:n.useEffect,p=r(73961),d=r(46818),y=r(63609),m=r.n(y),v=[],h=function(e,t,r){void 0===r&&(r={});var u=n.useRef(null),o={onFirstUpdate:r.onFirstUpdate,placement:r.placement||"bottom",strategy:r.strategy||"absolute",modifiers:r.modifiers||v},i=n.useState({styles:{popper:{position:o.strategy,left:"0",top:"0"},arrow:{position:"absolute"}},attributes:{}}),a=i[0],f=i[1],c=n.useMemo((function(){return{name:"updateState",enabled:!0,phase:"write",fn:function(e){var t=e.state,r=Object.keys(t.elements);p.flushSync((function(){f({styles:s(r.map((function(e){return[e,t.styles[e]||{}]}))),attributes:s(r.map((function(e){return[e,t.attributes[e]]})))})}))},requires:["computeStyles"]}}),[]),y=n.useMemo((function(){var e={onFirstUpdate:o.onFirstUpdate,placement:o.placement,strategy:o.strategy,modifiers:[].concat(o.modifiers,[c,{name:"applyStyles",enabled:!1}])};return m()(u.current,e)?u.current||e:(u.current=e,e)}),[o.onFirstUpdate,o.placement,o.strategy,o.modifiers,c]),h=n.useRef();return l((function(){h.current&&h.current.setOptions(y)}),[y]),l((function(){if(null!=e&&null!=t){var n=(r.createPopper||d.fi)(e,t,y);return h.current=n,function(){n.destroy(),h.current=null}}}),[e,t,r.createPopper]),{state:h.current?h.current.state:null,styles:a.styles,attributes:a.attributes,update:h.current?h.current.update:null,forceUpdate:h.current?h.current.forceUpdate:null}},b=function(){},g=function(){return Promise.resolve(null)},w=[];function S(e){var t=e.placement,r=void 0===t?"bottom":t,o=e.strategy,i=void 0===o?"absolute":o,f=e.modifiers,s=void 0===f?w:f,l=e.referenceElement,p=e.onFirstUpdate,d=e.innerRef,y=e.children,m=n.useContext(u),v=n.useState(null),S=v[0],E=v[1],O=n.useState(null),P=O[0],U=O[1];n.useEffect((function(){c(d,S)}),[d,S]);var k=n.useMemo((function(){return{placement:r,strategy:i,onFirstUpdate:p,modifiers:[].concat(s,[{name:"arrow",enabled:null!=P,options:{element:P}}])}}),[r,i,p,s,P]),R=h(l||m,S,k),x=R.state,A=R.styles,M=R.forceUpdate,_=R.update,j=n.useMemo((function(){return{ref:E,style:A.popper,placement:x?x.placement:r,hasPopperEscaped:x&&x.modifiersData.hide?x.modifiersData.hide.hasPopperEscaped:null,isReferenceHidden:x&&x.modifiersData.hide?x.modifiersData.hide.isReferenceHidden:null,arrowProps:{style:A.arrow,ref:U},forceUpdate:M||b,update:_||g}}),[E,U,r,x,A,_,M]);return a(y)(j)}var E=r(626),O=r.n(E);function P(e){var t=e.children,r=e.innerRef,u=n.useContext(o),i=n.useCallback((function(e){c(r,e),f(u,e)}),[r,u]);return n.useEffect((function(){return function(){return c(r,null)}}),[]),n.useEffect((function(){O()(Boolean(u),"`Reference` should not be used outside of a `Manager` component.")}),[u]),a(t)({ref:i})}},63609:function(e){var t="undefined"!==typeof Element,r="function"===typeof Map,n="function"===typeof Set,u="function"===typeof ArrayBuffer&&!!ArrayBuffer.isView;function o(e,i){if(e===i)return!0;if(e&&i&&"object"==typeof e&&"object"==typeof i){if(e.constructor!==i.constructor)return!1;var a,f,c,s;if(Array.isArray(e)){if((a=e.length)!=i.length)return!1;for(f=a;0!==f--;)if(!o(e[f],i[f]))return!1;return!0}if(r&&e instanceof Map&&i instanceof Map){if(e.size!==i.size)return!1;for(s=e.entries();!(f=s.next()).done;)if(!i.has(f.value[0]))return!1;for(s=e.entries();!(f=s.next()).done;)if(!o(f.value[1],i.get(f.value[0])))return!1;return!0}if(n&&e instanceof Set&&i instanceof Set){if(e.size!==i.size)return!1;for(s=e.entries();!(f=s.next()).done;)if(!i.has(f.value[0]))return!1;return!0}if(u&&ArrayBuffer.isView(e)&&ArrayBuffer.isView(i)){if((a=e.length)!=i.length)return!1;for(f=a;0!==f--;)if(e[f]!==i[f])return!1;return!0}if(e.constructor===RegExp)return e.source===i.source&&e.flags===i.flags;if(e.valueOf!==Object.prototype.valueOf&&"function"===typeof e.valueOf&&"function"===typeof i.valueOf)return e.valueOf()===i.valueOf();if(e.toString!==Object.prototype.toString&&"function"===typeof e.toString&&"function"===typeof i.toString)return e.toString()===i.toString();if((a=(c=Object.keys(e)).length)!==Object.keys(i).length)return!1;for(f=a;0!==f--;)if(!Object.prototype.hasOwnProperty.call(i,c[f]))return!1;if(t&&e instanceof Element)return!1;for(f=a;0!==f--;)if(("_owner"!==c[f]&&"__v"!==c[f]&&"__o"!==c[f]||!e.$$typeof)&&!o(e[c[f]],i[c[f]]))return!1;return!0}return e!==e&&i!==i}e.exports=function(e,t){try{return o(e,t)}catch(r){if((r.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw r}}}}]);
//# sourceMappingURL=react-popper.fbb563a4dcf00ec2b817b2a41a04eff9.js.map