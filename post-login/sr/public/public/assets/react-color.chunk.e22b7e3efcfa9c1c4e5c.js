"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["react-color"],{12302:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.AlphaPicker=void 0;var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},n=u(a(89526)),l=u(a(29790)),o=a(82111),i=u(a(50646));function u(e){return e&&e.__esModule?e:{default:e}}var d=t.AlphaPicker=function(e){var t=e.rgb,a=e.hsl,i=e.width,u=e.height,d=e.onChange,s=e.direction,p=e.style,c=e.renderers,f=e.pointer,h=e.className,b=void 0===h?"":h,g=(0,l.default)({default:{picker:{position:"relative",width:i,height:u},alpha:{radius:"2px",style:p}}});return n.default.createElement("div",{style:g.picker,className:"alpha-picker "+b},n.default.createElement(o.Alpha,r({},g.alpha,{rgb:t,hsl:a,pointer:f,renderers:c,onChange:d,direction:s})))};d.defaultProps={width:"316px",height:"16px",direction:"horizontal",pointer:i.default},t.default=(0,o.ColorWrap)(d)},50646:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.AlphaPointer=void 0;var r=l(a(89526)),n=l(a(29790));function l(e){return e&&e.__esModule?e:{default:e}}var o=t.AlphaPointer=function(e){var t=e.direction,a=(0,n.default)({default:{picker:{width:"18px",height:"18px",borderRadius:"50%",transform:"translate(-9px, -1px)",backgroundColor:"rgb(248, 248, 248)",boxShadow:"0 1px 4px 0 rgba(0, 0, 0, 0.37)"}},vertical:{picker:{transform:"translate(-3px, -9px)"}}},{vertical:"vertical"===t});return r.default.createElement("div",{style:a.picker})};t.default=o},61517:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Block=void 0;var r=s(a(89526)),n=s(a(2652)),l=s(a(29790)),o=s(a(72739)),i=s(a(53650)),u=a(82111),d=s(a(29109));function s(e){return e&&e.__esModule?e:{default:e}}var p=t.Block=function(e){var t=e.onChange,a=e.onSwatchHover,n=e.hex,s=e.colors,p=e.width,c=e.triangle,f=e.styles,h=void 0===f?{}:f,b=e.className,g=void 0===b?"":b,v="transparent"===n,x=function(e,a){i.default.isValidHex(e)&&t({hex:e,source:"hex"},a)},y=(0,l.default)((0,o.default)({default:{card:{width:p,background:"#fff",boxShadow:"0 1px rgba(0,0,0,.1)",borderRadius:"6px",position:"relative"},head:{height:"110px",background:n,borderRadius:"6px 6px 0 0",display:"flex",alignItems:"center",justifyContent:"center",position:"relative"},body:{padding:"10px"},label:{fontSize:"18px",color:i.default.getContrastingColor(n),position:"relative"},triangle:{width:"0px",height:"0px",borderStyle:"solid",borderWidth:"0 10px 10px 10px",borderColor:"transparent transparent "+n+" transparent",position:"absolute",top:"-10px",left:"50%",marginLeft:"-10px"},input:{width:"100%",fontSize:"12px",color:"#666",border:"0px",outline:"none",height:"22px",boxShadow:"inset 0 0 0 1px #ddd",borderRadius:"4px",padding:"0 7px",boxSizing:"border-box"}},"hide-triangle":{triangle:{display:"none"}}},h),{"hide-triangle":"hide"===c});return r.default.createElement("div",{style:y.card,className:"block-picker "+g},r.default.createElement("div",{style:y.triangle}),r.default.createElement("div",{style:y.head},v&&r.default.createElement(u.Checkboard,{borderRadius:"6px 6px 0 0"}),r.default.createElement("div",{style:y.label},n)),r.default.createElement("div",{style:y.body},r.default.createElement(d.default,{colors:s,onClick:x,onSwatchHover:a}),r.default.createElement(u.EditableInput,{style:{input:y.input},value:n,onChange:x})))};p.propTypes={width:n.default.oneOfType([n.default.string,n.default.number]),colors:n.default.arrayOf(n.default.string),triangle:n.default.oneOf(["top","hide"]),styles:n.default.object},p.defaultProps={width:170,colors:["#D9E3F0","#F47373","#697689","#37D67A","#2CCCE4","#555555","#dce775","#ff8a65","#ba68c8"],triangle:"top",styles:{}},t.default=(0,u.ColorWrap)(p)},29109:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.BlockSwatches=void 0;var r=i(a(89526)),n=i(a(29790)),l=i(a(34118)),o=a(82111);function i(e){return e&&e.__esModule?e:{default:e}}var u=t.BlockSwatches=function(e){var t=e.colors,a=e.onClick,i=e.onSwatchHover,u=(0,n.default)({default:{swatches:{marginRight:"-10px"},swatch:{width:"22px",height:"22px",float:"left",marginRight:"10px",marginBottom:"10px",borderRadius:"4px"},clear:{clear:"both"}}});return r.default.createElement("div",{style:u.swatches},(0,l.default)(t,(function(e){return r.default.createElement(o.Swatch,{key:e,color:e,style:u.swatch,onClick:a,onHover:i,focusStyle:{boxShadow:"0 0 4px "+e}})})),r.default.createElement("div",{style:u.clear}))};t.default=u},86971:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Chrome=void 0;var r=p(a(89526)),n=p(a(2652)),l=p(a(29790)),o=p(a(72739)),i=a(82111),u=p(a(7305)),d=p(a(77663)),s=p(a(68834));function p(e){return e&&e.__esModule?e:{default:e}}var c=t.Chrome=function(e){var t=e.width,a=e.onChange,n=e.disableAlpha,p=e.rgb,c=e.hsl,f=e.hsv,h=e.hex,b=e.renderers,g=e.styles,v=void 0===g?{}:g,x=e.className,y=void 0===x?"":x,m=(0,l.default)((0,o.default)({default:{picker:{width:t,background:"#fff",borderRadius:"2px",boxShadow:"0 0 2px rgba(0,0,0,.3), 0 4px 8px rgba(0,0,0,.3)",boxSizing:"initial",fontFamily:"Menlo"},saturation:{width:"100%",paddingBottom:"55%",position:"relative",borderRadius:"2px 2px 0 0",overflow:"hidden"},Saturation:{radius:"2px 2px 0 0"},body:{padding:"16px 16px 12px"},controls:{display:"flex"},color:{width:"32px"},swatch:{marginTop:"6px",width:"16px",height:"16px",borderRadius:"8px",position:"relative",overflow:"hidden"},active:{absolute:"0px 0px 0px 0px",borderRadius:"8px",boxShadow:"inset 0 0 0 1px rgba(0,0,0,.1)",background:"rgba("+p.r+", "+p.g+", "+p.b+", "+p.a+")",zIndex:"2"},toggles:{flex:"1"},hue:{height:"10px",position:"relative",marginBottom:"8px"},Hue:{radius:"2px"},alpha:{height:"10px",position:"relative"},Alpha:{radius:"2px"}},disableAlpha:{color:{width:"22px"},alpha:{display:"none"},hue:{marginBottom:"0px"},swatch:{width:"10px",height:"10px",marginTop:"0px"}}},v),{disableAlpha:n});return r.default.createElement("div",{style:m.picker,className:"chrome-picker "+y},r.default.createElement("div",{style:m.saturation},r.default.createElement(i.Saturation,{style:m.Saturation,hsl:c,hsv:f,pointer:s.default,onChange:a})),r.default.createElement("div",{style:m.body},r.default.createElement("div",{style:m.controls,className:"flexbox-fix"},r.default.createElement("div",{style:m.color},r.default.createElement("div",{style:m.swatch},r.default.createElement("div",{style:m.active}),r.default.createElement(i.Checkboard,{renderers:b}))),r.default.createElement("div",{style:m.toggles},r.default.createElement("div",{style:m.hue},r.default.createElement(i.Hue,{style:m.Hue,hsl:c,pointer:d.default,onChange:a})),r.default.createElement("div",{style:m.alpha},r.default.createElement(i.Alpha,{style:m.Alpha,rgb:p,hsl:c,pointer:d.default,renderers:b,onChange:a})))),r.default.createElement(u.default,{rgb:p,hsl:c,hex:h,onChange:a,disableAlpha:n})))};c.propTypes={width:n.default.oneOfType([n.default.string,n.default.number]),disableAlpha:n.default.bool,styles:n.default.object},c.defaultProps={width:225,disableAlpha:!1,styles:{}},t.default=(0,i.ColorWrap)(c)},7305:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.ChromeFields=void 0;var r=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),n=d(a(89526)),l=d(a(29790)),o=d(a(53650)),i=a(82111),u=d(a(47327));function d(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}var c=t.ChromeFields=function(e){function t(){var e,a,r;s(this,t);for(var n=arguments.length,l=Array(n),i=0;i<n;i++)l[i]=arguments[i];return a=r=p(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(l))),r.state={view:""},r.toggleViews=function(){"hex"===r.state.view?r.setState({view:"rgb"}):"rgb"===r.state.view?r.setState({view:"hsl"}):"hsl"===r.state.view&&(1===r.props.hsl.a?r.setState({view:"hex"}):r.setState({view:"rgb"}))},r.handleChange=function(e,t){e.hex?o.default.isValidHex(e.hex)&&r.props.onChange({hex:e.hex,source:"hex"},t):e.r||e.g||e.b?r.props.onChange({r:e.r||r.props.rgb.r,g:e.g||r.props.rgb.g,b:e.b||r.props.rgb.b,source:"rgb"},t):e.a?(e.a<0?e.a=0:e.a>1&&(e.a=1),r.props.onChange({h:r.props.hsl.h,s:r.props.hsl.s,l:r.props.hsl.l,a:Math.round(100*e.a)/100,source:"rgb"},t)):(e.h||e.s||e.l)&&("string"===typeof e.s&&e.s.includes("%")&&(e.s=e.s.replace("%","")),"string"===typeof e.l&&e.l.includes("%")&&(e.l=e.l.replace("%","")),r.props.onChange({h:e.h||r.props.hsl.h,s:Number(e.s&&e.s||r.props.hsl.s),l:Number(e.l&&e.l||r.props.hsl.l),source:"hsl"},t))},r.showHighlight=function(e){e.currentTarget.style.background="#eee"},r.hideHighlight=function(e){e.currentTarget.style.background="transparent"},p(r,a)}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"componentDidMount",value:function(){1===this.props.hsl.a&&"hex"!==this.state.view?this.setState({view:"hex"}):"rgb"!==this.state.view&&"hsl"!==this.state.view&&this.setState({view:"rgb"})}},{key:"componentWillReceiveProps",value:function(e){1!==e.hsl.a&&"hex"===this.state.view&&this.setState({view:"rgb"})}},{key:"render",value:function(){var e=this,t=(0,l.default)({default:{wrap:{paddingTop:"16px",display:"flex"},fields:{flex:"1",display:"flex",marginLeft:"-6px"},field:{paddingLeft:"6px",width:"100%"},alpha:{paddingLeft:"6px",width:"100%"},toggle:{width:"32px",textAlign:"right",position:"relative"},icon:{marginRight:"-4px",marginTop:"12px",cursor:"pointer",position:"relative"},iconHighlight:{position:"absolute",width:"24px",height:"28px",background:"#eee",borderRadius:"4px",top:"10px",left:"12px",display:"none"},input:{fontSize:"11px",color:"#333",width:"100%",borderRadius:"2px",border:"none",boxShadow:"inset 0 0 0 1px #dadada",height:"21px",textAlign:"center"},label:{textTransform:"uppercase",fontSize:"11px",lineHeight:"11px",color:"#969696",textAlign:"center",display:"block",marginTop:"12px"},svg:{fill:"#333",width:"24px",height:"24px",border:"1px transparent solid",borderRadius:"5px"}},disableAlpha:{alpha:{display:"none"}}},this.props,this.state),a=void 0;return"hex"===this.state.view?a=n.default.createElement("div",{style:t.fields,className:"flexbox-fix"},n.default.createElement("div",{style:t.field},n.default.createElement(i.EditableInput,{style:{input:t.input,label:t.label},label:"hex",value:this.props.hex,onChange:this.handleChange}))):"rgb"===this.state.view?a=n.default.createElement("div",{style:t.fields,className:"flexbox-fix"},n.default.createElement("div",{style:t.field},n.default.createElement(i.EditableInput,{style:{input:t.input,label:t.label},label:"r",value:this.props.rgb.r,onChange:this.handleChange})),n.default.createElement("div",{style:t.field},n.default.createElement(i.EditableInput,{style:{input:t.input,label:t.label},label:"g",value:this.props.rgb.g,onChange:this.handleChange})),n.default.createElement("div",{style:t.field},n.default.createElement(i.EditableInput,{style:{input:t.input,label:t.label},label:"b",value:this.props.rgb.b,onChange:this.handleChange})),n.default.createElement("div",{style:t.alpha},n.default.createElement(i.EditableInput,{style:{input:t.input,label:t.label},label:"a",value:this.props.rgb.a,arrowOffset:.01,onChange:this.handleChange}))):"hsl"===this.state.view&&(a=n.default.createElement("div",{style:t.fields,className:"flexbox-fix"},n.default.createElement("div",{style:t.field},n.default.createElement(i.EditableInput,{style:{input:t.input,label:t.label},label:"h",value:Math.round(this.props.hsl.h),onChange:this.handleChange})),n.default.createElement("div",{style:t.field},n.default.createElement(i.EditableInput,{style:{input:t.input,label:t.label},label:"s",value:Math.round(100*this.props.hsl.s)+"%",onChange:this.handleChange})),n.default.createElement("div",{style:t.field},n.default.createElement(i.EditableInput,{style:{input:t.input,label:t.label},label:"l",value:Math.round(100*this.props.hsl.l)+"%",onChange:this.handleChange})),n.default.createElement("div",{style:t.alpha},n.default.createElement(i.EditableInput,{style:{input:t.input,label:t.label},label:"a",value:this.props.hsl.a,arrowOffset:.01,onChange:this.handleChange})))),n.default.createElement("div",{style:t.wrap,className:"flexbox-fix"},a,n.default.createElement("div",{style:t.toggle},n.default.createElement("div",{style:t.icon,onClick:this.toggleViews,ref:function(t){return e.icon=t}},n.default.createElement(u.default,{style:t.svg,onMouseOver:this.showHighlight,onMouseEnter:this.showHighlight,onMouseOut:this.hideHighlight}))))}}]),t}(n.default.Component);t.default=c},77663:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.ChromePointer=void 0;var r=l(a(89526)),n=l(a(29790));function l(e){return e&&e.__esModule?e:{default:e}}var o=t.ChromePointer=function(){var e=(0,n.default)({default:{picker:{width:"12px",height:"12px",borderRadius:"6px",transform:"translate(-6px, -1px)",backgroundColor:"rgb(248, 248, 248)",boxShadow:"0 1px 4px 0 rgba(0, 0, 0, 0.37)"}}});return r.default.createElement("div",{style:e.picker})};t.default=o},68834:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.ChromePointerCircle=void 0;var r=l(a(89526)),n=l(a(29790));function l(e){return e&&e.__esModule?e:{default:e}}var o=t.ChromePointerCircle=function(){var e=(0,n.default)({default:{picker:{width:"12px",height:"12px",borderRadius:"6px",boxShadow:"inset 0 0 0 1px #fff",transform:"translate(-6px, -6px)"}}});return r.default.createElement("div",{style:e.picker})};t.default=o},18476:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Circle=void 0;var r=p(a(89526)),n=p(a(2652)),l=p(a(29790)),o=p(a(34118)),i=p(a(72739)),u=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t.default=e,t}(a(26321)),d=a(82111),s=p(a(28004));function p(e){return e&&e.__esModule?e:{default:e}}var c=t.Circle=function(e){var t=e.width,a=e.onChange,n=e.onSwatchHover,u=e.colors,d=e.hex,p=e.circleSize,c=e.styles,f=void 0===c?{}:c,h=e.circleSpacing,b=e.className,g=void 0===b?"":b,v=(0,l.default)((0,i.default)({default:{card:{width:t,display:"flex",flexWrap:"wrap",marginRight:-h,marginBottom:-h}}},f)),x=function(e,t){return a({hex:e,source:"hex"},t)};return r.default.createElement("div",{style:v.card,className:"circle-picker "+g},(0,o.default)(u,(function(e){return r.default.createElement(s.default,{key:e,color:e,onClick:x,onSwatchHover:n,active:d===e.toLowerCase(),circleSize:p,circleSpacing:h})})))};c.propTypes={width:n.default.oneOfType([n.default.string,n.default.number]),circleSize:n.default.number,circleSpacing:n.default.number,styles:n.default.object},c.defaultProps={width:252,circleSize:28,circleSpacing:14,colors:[u.red[500],u.pink[500],u.purple[500],u.deepPurple[500],u.indigo[500],u.blue[500],u.lightBlue[500],u.cyan[500],u.teal[500],u.green[500],u.lightGreen[500],u.lime[500],u.yellow[500],u.amber[500],u.orange[500],u.deepOrange[500],u.brown[500],u.blueGrey[500]],styles:{}},t.default=(0,d.ColorWrap)(c)},28004:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.CircleSwatch=void 0;var r=i(a(89526)),n=a(29790),l=i(n),o=a(82111);function i(e){return e&&e.__esModule?e:{default:e}}var u=t.CircleSwatch=function(e){var t=e.color,a=e.onClick,n=e.onSwatchHover,i=e.hover,u=e.active,d=e.circleSize,s=e.circleSpacing,p=(0,l.default)({default:{swatch:{width:d,height:d,marginRight:s,marginBottom:s,transform:"scale(1)",transition:"100ms transform ease"},Swatch:{borderRadius:"50%",background:"transparent",boxShadow:"inset 0 0 0 "+d/2+"px "+t,transition:"100ms box-shadow ease"}},hover:{swatch:{transform:"scale(1.2)"}},active:{Swatch:{boxShadow:"inset 0 0 0 3px "+t}}},{hover:i,active:u});return r.default.createElement("div",{style:p.swatch},r.default.createElement(o.Swatch,{style:p.Swatch,color:t,onClick:a,onHover:n,focusStyle:{boxShadow:p.Swatch.boxShadow+", 0 0 5px "+t}}))};u.defaultProps={circleSize:28,circleSpacing:14},t.default=(0,n.handleHover)(u)},29230:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Alpha=void 0;var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},n=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),l=a(89526),o=s(l),i=s(a(29790)),u=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t.default=e,t}(a(24526)),d=s(a(42563));function s(e){return e&&e.__esModule?e:{default:e}}function p(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}var f=t.Alpha=function(e){function t(){var e,a,r;p(this,t);for(var n=arguments.length,l=Array(n),o=0;o<n;o++)l[o]=arguments[o];return a=r=c(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(l))),r.handleChange=function(e){var t=u.calculateChange(e,r.props.hsl,r.props.direction,r.props.a,r.container);t&&"function"===typeof r.props.onChange&&r.props.onChange(t,e)},r.handleMouseDown=function(e){r.handleChange(e),window.addEventListener("mousemove",r.handleChange),window.addEventListener("mouseup",r.handleMouseUp)},r.handleMouseUp=function(){r.unbindEventListeners()},r.unbindEventListeners=function(){window.removeEventListener("mousemove",r.handleChange),window.removeEventListener("mouseup",r.handleMouseUp)},c(r,a)}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),n(t,[{key:"componentWillUnmount",value:function(){this.unbindEventListeners()}},{key:"render",value:function(){var e=this,t=this.props.rgb,a=(0,i.default)({default:{alpha:{absolute:"0px 0px 0px 0px",borderRadius:this.props.radius},checkboard:{absolute:"0px 0px 0px 0px",overflow:"hidden",borderRadius:this.props.radius},gradient:{absolute:"0px 0px 0px 0px",background:"linear-gradient(to right, rgba("+t.r+","+t.g+","+t.b+", 0) 0%,\n           rgba("+t.r+","+t.g+","+t.b+", 1) 100%)",boxShadow:this.props.shadow,borderRadius:this.props.radius},container:{position:"relative",height:"100%",margin:"0 3px"},pointer:{position:"absolute",left:100*t.a+"%"},slider:{width:"4px",borderRadius:"1px",height:"8px",boxShadow:"0 0 2px rgba(0, 0, 0, .6)",background:"#fff",marginTop:"1px",transform:"translateX(-2px)"}},vertical:{gradient:{background:"linear-gradient(to bottom, rgba("+t.r+","+t.g+","+t.b+", 0) 0%,\n           rgba("+t.r+","+t.g+","+t.b+", 1) 100%)"},pointer:{left:0,top:100*t.a+"%"}},overwrite:r({},this.props.style)},{vertical:"vertical"===this.props.direction,overwrite:!0});return o.default.createElement("div",{style:a.alpha},o.default.createElement("div",{style:a.checkboard},o.default.createElement(d.default,{renderers:this.props.renderers})),o.default.createElement("div",{style:a.gradient}),o.default.createElement("div",{style:a.container,ref:function(t){return e.container=t},onMouseDown:this.handleMouseDown,onTouchMove:this.handleChange,onTouchStart:this.handleChange},o.default.createElement("div",{style:a.pointer},this.props.pointer?o.default.createElement(this.props.pointer,this.props):o.default.createElement("div",{style:a.slider}))))}}]),t}(l.PureComponent||l.Component);t.default=f},42563:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Checkboard=void 0;var r=o(a(89526)),n=o(a(29790)),l=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t.default=e,t}(a(62880));function o(e){return e&&e.__esModule?e:{default:e}}var i=t.Checkboard=function(e){var t=e.white,a=e.grey,o=e.size,i=e.renderers,u=e.borderRadius,d=e.boxShadow,s=(0,n.default)({default:{grid:{borderRadius:u,boxShadow:d,absolute:"0px 0px 0px 0px",background:"url("+l.get(t,a,o,i.canvas)+") center left"}}});return r.default.createElement("div",{style:s.grid})};i.defaultProps={size:8,white:"transparent",grey:"rgba(0,0,0,.08)",renderers:{}},t.default=i},92275:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.ColorWrap=void 0;var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},n=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),l=a(89526),o=d(l),i=d(a(76897)),u=d(a(53650));function d(e){return e&&e.__esModule?e:{default:e}}var s=t.ColorWrap=function(e){var t=function(t){function a(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a);var t=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}(this,(a.__proto__||Object.getPrototypeOf(a)).call(this));return t.handleChange=function(e,a){if(u.default.simpleCheckForValidColor(e)){var r=u.default.toState(e,e.h||t.state.oldHue);t.setState(r),t.props.onChangeComplete&&t.debounce(t.props.onChangeComplete,r,a),t.props.onChange&&t.props.onChange(r,a)}},t.handleSwatchHover=function(e,a){if(u.default.simpleCheckForValidColor(e)){var r=u.default.toState(e,e.h||t.state.oldHue);t.props.onSwatchHover&&t.props.onSwatchHover(r,a)}},t.state=r({},u.default.toState(e.color,0)),t.debounce=(0,i.default)((function(e,t,a){e(t,a)}),100),t}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(a,t),n(a,[{key:"componentWillReceiveProps",value:function(e){this.setState(r({},u.default.toState(e.color,this.state.oldHue)))}},{key:"render",value:function(){var t={};return this.props.onSwatchHover&&(t.onSwatchHover=this.handleSwatchHover),o.default.createElement(e,r({},this.props,this.state,{onChange:this.handleChange},t))}}]),a}(l.PureComponent||l.Component);return t.propTypes=r({},e.propTypes),t.defaultProps=r({},e.defaultProps,{color:{h:250,s:.5,l:.2,a:1}}),t};t.default=s},59835:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.EditableInput=void 0;var r=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),n=a(89526),l=i(n),o=i(a(29790));function i(e){return e&&e.__esModule?e:{default:e}}var u=[38,40],d=t.EditableInput=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var a=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return a.handleBlur=function(){a.state.blurValue&&a.setState({value:a.state.blurValue,blurValue:null})},a.handleChange=function(e){a.setUpdatedValue(e.target.value,e)},a.handleKeyDown=function(e){var t,r=function(e){return Number(String(e).replace(/%/g,""))}(e.target.value);if(!isNaN(r)&&(t=e.keyCode,u.indexOf(t)>-1)){var n=a.getArrowOffset(),l=38===e.keyCode?r+n:r-n;a.setUpdatedValue(l,e)}},a.handleDrag=function(e){if(a.props.dragLabel){var t=Math.round(a.props.value+e.movementX);t>=0&&t<=a.props.dragMax&&a.props.onChange&&a.props.onChange(a.getValueObjectWithLabel(t),e)}},a.handleMouseDown=function(e){a.props.dragLabel&&(e.preventDefault(),a.handleDrag(e),window.addEventListener("mousemove",a.handleDrag),window.addEventListener("mouseup",a.handleMouseUp))},a.handleMouseUp=function(){a.unbindEventListeners()},a.unbindEventListeners=function(){window.removeEventListener("mousemove",a.handleDrag),window.removeEventListener("mouseup",a.handleMouseUp)},a.state={value:String(e.value).toUpperCase(),blurValue:String(e.value).toUpperCase()},a}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"componentWillReceiveProps",value:function(e){var t=this.input;e.value!==this.state.value&&(t===document.activeElement?this.setState({blurValue:String(e.value).toUpperCase()}):this.setState({value:String(e.value).toUpperCase(),blurValue:!this.state.blurValue&&String(e.value).toUpperCase()}))}},{key:"componentWillUnmount",value:function(){this.unbindEventListeners()}},{key:"getValueObjectWithLabel",value:function(e){return function(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}({},this.props.label,e)}},{key:"getArrowOffset",value:function(){return this.props.arrowOffset||1}},{key:"setUpdatedValue",value:function(e,t){var a=null!==this.props.label?this.getValueObjectWithLabel(e):e;this.props.onChange&&this.props.onChange(a,t);var r,n=function(e){return String(e).indexOf("%")>-1}(t.target.value);this.setState({value:n?(r=e,r+"%"):e})}},{key:"render",value:function(){var e=this,t=(0,o.default)({default:{wrap:{position:"relative"}},"user-override":{wrap:this.props.style&&this.props.style.wrap?this.props.style.wrap:{},input:this.props.style&&this.props.style.input?this.props.style.input:{},label:this.props.style&&this.props.style.label?this.props.style.label:{}},"dragLabel-true":{label:{cursor:"ew-resize"}}},{"user-override":!0},this.props);return l.default.createElement("div",{style:t.wrap},l.default.createElement("input",{style:t.input,ref:function(t){return e.input=t},value:this.state.value,onKeyDown:this.handleKeyDown,onChange:this.handleChange,onBlur:this.handleBlur,placeholder:this.props.placeholder,spellCheck:"false"}),this.props.label&&!this.props.hideLabel?l.default.createElement("span",{style:t.label,onMouseDown:this.handleMouseDown},this.props.label):null)}}]),t}(n.PureComponent||n.Component);t.default=d},8262:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Hue=void 0;var r=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),n=a(89526),l=u(n),o=u(a(29790)),i=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t.default=e,t}(a(48228));function u(e){return e&&e.__esModule?e:{default:e}}function d(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}var p=t.Hue=function(e){function t(){var e,a,r;d(this,t);for(var n=arguments.length,l=Array(n),o=0;o<n;o++)l[o]=arguments[o];return a=r=s(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(l))),r.handleChange=function(e){var t=i.calculateChange(e,r.props.direction,r.props.hsl,r.container);t&&"function"===typeof r.props.onChange&&r.props.onChange(t,e)},r.handleMouseDown=function(e){r.handleChange(e),window.addEventListener("mousemove",r.handleChange),window.addEventListener("mouseup",r.handleMouseUp)},r.handleMouseUp=function(){r.unbindEventListeners()},s(r,a)}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"componentWillUnmount",value:function(){this.unbindEventListeners()}},{key:"unbindEventListeners",value:function(){window.removeEventListener("mousemove",this.handleChange),window.removeEventListener("mouseup",this.handleMouseUp)}},{key:"render",value:function(){var e=this,t=this.props.direction,a=void 0===t?"horizontal":t,r=(0,o.default)({default:{hue:{absolute:"0px 0px 0px 0px",borderRadius:this.props.radius,boxShadow:this.props.shadow},container:{padding:"0 2px",position:"relative",height:"100%",borderRadius:this.props.radius},pointer:{position:"absolute",left:100*this.props.hsl.h/360+"%"},slider:{marginTop:"1px",width:"4px",borderRadius:"1px",height:"8px",boxShadow:"0 0 2px rgba(0, 0, 0, .6)",background:"#fff",transform:"translateX(-2px)"}},vertical:{pointer:{left:"0px",top:-100*this.props.hsl.h/360+100+"%"}}},{vertical:"vertical"===a});return l.default.createElement("div",{style:r.hue},l.default.createElement("div",{className:"hue-"+a,style:r.container,ref:function(t){return e.container=t},onMouseDown:this.handleMouseDown,onTouchMove:this.handleChange,onTouchStart:this.handleChange},l.default.createElement("style",null,"\n            .hue-horizontal {\n              background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0\n                33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);\n              background: -webkit-linear-gradient(to right, #f00 0%, #ff0\n                17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);\n            }\n\n            .hue-vertical {\n              background: linear-gradient(to top, #f00 0%, #ff0 17%, #0f0 33%,\n                #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);\n              background: -webkit-linear-gradient(to top, #f00 0%, #ff0 17%,\n                #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);\n            }\n          "),l.default.createElement("div",{style:r.pointer},this.props.pointer?l.default.createElement(this.props.pointer,this.props):l.default.createElement("div",{style:r.slider}))))}}]),t}(n.PureComponent||n.Component);t.default=p},2593:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Raised=void 0;var r=i(a(89526)),n=i(a(2652)),l=i(a(29790)),o=i(a(72739));function i(e){return e&&e.__esModule?e:{default:e}}var u=t.Raised=function(e){var t=e.zDepth,a=e.radius,n=e.background,i=e.children,u=e.styles,d=void 0===u?{}:u,s=(0,l.default)((0,o.default)({default:{wrap:{position:"relative",display:"inline-block"},content:{position:"relative"},bg:{absolute:"0px 0px 0px 0px",boxShadow:"0 "+t+"px "+4*t+"px rgba(0,0,0,.24)",borderRadius:a,background:n}},"zDepth-0":{bg:{boxShadow:"none"}},"zDepth-1":{bg:{boxShadow:"0 2px 10px rgba(0,0,0,.12), 0 2px 5px rgba(0,0,0,.16)"}},"zDepth-2":{bg:{boxShadow:"0 6px 20px rgba(0,0,0,.19), 0 8px 17px rgba(0,0,0,.2)"}},"zDepth-3":{bg:{boxShadow:"0 17px 50px rgba(0,0,0,.19), 0 12px 15px rgba(0,0,0,.24)"}},"zDepth-4":{bg:{boxShadow:"0 25px 55px rgba(0,0,0,.21), 0 16px 28px rgba(0,0,0,.22)"}},"zDepth-5":{bg:{boxShadow:"0 40px 77px rgba(0,0,0,.22), 0 27px 24px rgba(0,0,0,.2)"}},square:{bg:{borderRadius:"0"}},circle:{bg:{borderRadius:"50%"}}},d),{"zDepth-1":1===t});return r.default.createElement("div",{style:s.wrap},r.default.createElement("div",{style:s.bg}),r.default.createElement("div",{style:s.content},i))};u.propTypes={background:n.default.string,zDepth:n.default.oneOf([0,1,2,3,4,5]),radius:n.default.number,styles:n.default.object},u.defaultProps={background:"#fff",zDepth:1,radius:2,styles:{}},t.default=u},41145:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Saturation=void 0;var r=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),n=a(89526),l=d(n),o=d(a(29790)),i=d(a(38172)),u=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t.default=e,t}(a(34655));function d(e){return e&&e.__esModule?e:{default:e}}var s=t.Saturation=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var a=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return a.handleChange=function(e){"function"===typeof a.props.onChange&&a.throttle(a.props.onChange,u.calculateChange(e,a.props.hsl,a.container),e)},a.handleMouseDown=function(e){a.handleChange(e),window.addEventListener("mousemove",a.handleChange),window.addEventListener("mouseup",a.handleMouseUp)},a.handleMouseUp=function(){a.unbindEventListeners()},a.throttle=(0,i.default)((function(e,t,a){e(t,a)}),50),a}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"componentWillUnmount",value:function(){this.throttle.cancel(),this.unbindEventListeners()}},{key:"unbindEventListeners",value:function(){window.removeEventListener("mousemove",this.handleChange),window.removeEventListener("mouseup",this.handleMouseUp)}},{key:"render",value:function(){var e=this,t=this.props.style||{},a=t.color,r=t.white,n=t.black,i=t.pointer,u=t.circle,d=(0,o.default)({default:{color:{absolute:"0px 0px 0px 0px",background:"hsl("+this.props.hsl.h+",100%, 50%)",borderRadius:this.props.radius},white:{absolute:"0px 0px 0px 0px",borderRadius:this.props.radius},black:{absolute:"0px 0px 0px 0px",boxShadow:this.props.shadow,borderRadius:this.props.radius},pointer:{position:"absolute",top:-100*this.props.hsv.v+100+"%",left:100*this.props.hsv.s+"%",cursor:"default"},circle:{width:"4px",height:"4px",boxShadow:"0 0 0 1.5px #fff, inset 0 0 1px 1px rgba(0,0,0,.3),\n            0 0 1px 2px rgba(0,0,0,.4)",borderRadius:"50%",cursor:"hand",transform:"translate(-2px, -2px)"}},custom:{color:a,white:r,black:n,pointer:i,circle:u}},{custom:!!this.props.style});return l.default.createElement("div",{style:d.color,ref:function(t){return e.container=t},onMouseDown:this.handleMouseDown,onTouchMove:this.handleChange,onTouchStart:this.handleChange},l.default.createElement("style",null,"\n          .saturation-white {\n            background: -webkit-linear-gradient(to right, #fff, rgba(255,255,255,0));\n            background: linear-gradient(to right, #fff, rgba(255,255,255,0));\n          }\n          .saturation-black {\n            background: -webkit-linear-gradient(to top, #000, rgba(0,0,0,0));\n            background: linear-gradient(to top, #000, rgba(0,0,0,0));\n          }\n        "),l.default.createElement("div",{style:d.white,className:"saturation-white"},l.default.createElement("div",{style:d.black,className:"saturation-black"}),l.default.createElement("div",{style:d.pointer},this.props.pointer?l.default.createElement(this.props.pointer,this.props):l.default.createElement("div",{style:d.circle}))))}}]),t}(n.PureComponent||n.Component);t.default=s},99633:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Swatch=void 0;var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},n=u(a(89526)),l=u(a(29790)),o=a(49686),i=u(a(42563));function u(e){return e&&e.__esModule?e:{default:e}}var d=t.Swatch=function(e){var t=e.color,a=e.style,o=e.onClick,u=void 0===o?function(){}:o,d=e.onHover,s=e.title,p=void 0===s?t:s,c=e.children,f=e.focus,h=e.focusStyle,b=void 0===h?{}:h,g="transparent"===t,v=(0,l.default)({default:{swatch:r({background:t,height:"100%",width:"100%",cursor:"pointer",position:"relative",outline:"none"},a,f?b:{})}}),x={};return d&&(x.onMouseOver=function(e){return d(t,e)}),n.default.createElement("div",r({style:v.swatch,onClick:function(e){return u(t,e)},title:p,tabIndex:0,onKeyDown:function(e){return 13===e.keyCode&&u(t,e)}},x),c,g&&n.default.createElement(i.default,{borderRadius:v.swatch.borderRadius,boxShadow:"inset 0 0 0 1px rgba(0,0,0,0.1)"}))};t.default=(0,o.handleFocus)(d)},82111:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=a(29230);Object.defineProperty(t,"Alpha",{enumerable:!0,get:function(){return p(r).default}});var n=a(42563);Object.defineProperty(t,"Checkboard",{enumerable:!0,get:function(){return p(n).default}});var l=a(59835);Object.defineProperty(t,"EditableInput",{enumerable:!0,get:function(){return p(l).default}});var o=a(8262);Object.defineProperty(t,"Hue",{enumerable:!0,get:function(){return p(o).default}});var i=a(2593);Object.defineProperty(t,"Raised",{enumerable:!0,get:function(){return p(i).default}});var u=a(41145);Object.defineProperty(t,"Saturation",{enumerable:!0,get:function(){return p(u).default}});var d=a(92275);Object.defineProperty(t,"ColorWrap",{enumerable:!0,get:function(){return p(d).default}});var s=a(99633);function p(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"Swatch",{enumerable:!0,get:function(){return p(s).default}})},29838:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Compact=void 0;var r=c(a(89526)),n=c(a(2652)),l=c(a(29790)),o=c(a(34118)),i=c(a(72739)),u=c(a(53650)),d=a(82111),s=c(a(66501)),p=c(a(96830));function c(e){return e&&e.__esModule?e:{default:e}}var f=t.Compact=function(e){var t=e.onChange,a=e.onSwatchHover,n=e.colors,c=e.hex,f=e.rgb,h=e.styles,b=void 0===h?{}:h,g=e.className,v=void 0===g?"":g,x=(0,l.default)((0,i.default)({default:{Compact:{background:"#f6f6f6",radius:"4px"},compact:{paddingTop:"5px",paddingLeft:"5px",boxSizing:"initial",width:"240px"},clear:{clear:"both"}}},b)),y=function(e,a){e.hex?u.default.isValidHex(e.hex)&&t({hex:e.hex,source:"hex"},a):t(e,a)};return r.default.createElement(d.Raised,{style:x.Compact,styles:b},r.default.createElement("div",{style:x.compact,className:"compact-picker "+v},r.default.createElement("div",null,(0,o.default)(n,(function(e){return r.default.createElement(s.default,{key:e,color:e,active:e.toLowerCase()===c,onClick:y,onSwatchHover:a})})),r.default.createElement("div",{style:x.clear})),r.default.createElement(p.default,{hex:c,rgb:f,onChange:y})))};f.propTypes={colors:n.default.arrayOf(n.default.string),styles:n.default.object},f.defaultProps={colors:["#4D4D4D","#999999","#FFFFFF","#F44E3B","#FE9200","#FCDC00","#DBDF00","#A4DD00","#68CCCA","#73D8FF","#AEA1FF","#FDA1FF","#333333","#808080","#cccccc","#D33115","#E27300","#FCC400","#B0BC00","#68BC00","#16A5A5","#009CE0","#7B64FF","#FA28FF","#000000","#666666","#B3B3B3","#9F0500","#C45100","#FB9E00","#808900","#194D33","#0C797D","#0062B1","#653294","#AB149E"],styles:{}},t.default=(0,d.ColorWrap)(f)},66501:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.CompactColor=void 0;var r=i(a(89526)),n=i(a(29790)),l=i(a(53650)),o=a(82111);function i(e){return e&&e.__esModule?e:{default:e}}var u=t.CompactColor=function(e){var t=e.color,a=e.onClick,i=void 0===a?function(){}:a,u=e.onSwatchHover,d=e.active,s=(0,n.default)({default:{color:{background:t,width:"15px",height:"15px",float:"left",marginRight:"5px",marginBottom:"5px",position:"relative",cursor:"pointer"},dot:{absolute:"5px 5px 5px 5px",background:l.default.getContrastingColor(t),borderRadius:"50%",opacity:"0"}},active:{dot:{opacity:"1"}},"color-#FFFFFF":{color:{boxShadow:"inset 0 0 0 1px #ddd"},dot:{background:"#000"}},transparent:{dot:{background:"#000"}}},{active:d,"color-#FFFFFF":"#FFFFFF"===t,transparent:"transparent"===t});return r.default.createElement(o.Swatch,{style:s.color,color:t,onClick:i,onHover:u,focusStyle:{boxShadow:"0 0 4px "+t}},r.default.createElement("div",{style:s.dot}))};t.default=u},96830:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.CompactFields=void 0;var r=o(a(89526)),n=o(a(29790)),l=a(82111);function o(e){return e&&e.__esModule?e:{default:e}}var i=t.CompactFields=function(e){var t=e.hex,a=e.rgb,o=e.onChange,i=(0,n.default)({default:{fields:{display:"flex",paddingBottom:"6px",paddingRight:"5px",position:"relative"},active:{position:"absolute",top:"6px",left:"5px",height:"9px",width:"9px",background:t},HEXwrap:{flex:"6",position:"relative"},HEXinput:{width:"80%",padding:"0px",paddingLeft:"20%",border:"none",outline:"none",background:"none",fontSize:"12px",color:"#333",height:"16px"},HEXlabel:{display:"none"},RGBwrap:{flex:"3",position:"relative"},RGBinput:{width:"70%",padding:"0px",paddingLeft:"30%",border:"none",outline:"none",background:"none",fontSize:"12px",color:"#333",height:"16px"},RGBlabel:{position:"absolute",top:"3px",left:"0px",lineHeight:"16px",textTransform:"uppercase",fontSize:"12px",color:"#999"}}}),u=function(e,t){e.r||e.g||e.b?o({r:e.r||a.r,g:e.g||a.g,b:e.b||a.b,source:"rgb"},t):o({hex:e.hex,source:"hex"},t)};return r.default.createElement("div",{style:i.fields,className:"flexbox-fix"},r.default.createElement("div",{style:i.active}),r.default.createElement(l.EditableInput,{style:{wrap:i.HEXwrap,input:i.HEXinput,label:i.HEXlabel},label:"hex",value:t,onChange:u}),r.default.createElement(l.EditableInput,{style:{wrap:i.RGBwrap,input:i.RGBinput,label:i.RGBlabel},label:"r",value:a.r,onChange:u}),r.default.createElement(l.EditableInput,{style:{wrap:i.RGBwrap,input:i.RGBinput,label:i.RGBlabel},label:"g",value:a.g,onChange:u}),r.default.createElement(l.EditableInput,{style:{wrap:i.RGBwrap,input:i.RGBinput,label:i.RGBlabel},label:"b",value:a.b,onChange:u}))};t.default=i},97876:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Github=void 0;var r=s(a(89526)),n=s(a(2652)),l=s(a(29790)),o=s(a(34118)),i=s(a(72739)),u=a(82111),d=s(a(47567));function s(e){return e&&e.__esModule?e:{default:e}}var p=t.Github=function(e){var t=e.width,a=e.colors,n=e.onChange,u=e.onSwatchHover,s=e.triangle,p=e.styles,c=void 0===p?{}:p,f=e.className,h=void 0===f?"":f,b=(0,l.default)((0,i.default)({default:{card:{width:t,background:"#fff",border:"1px solid rgba(0,0,0,0.2)",boxShadow:"0 3px 12px rgba(0,0,0,0.15)",borderRadius:"4px",position:"relative",padding:"5px",display:"flex",flexWrap:"wrap"},triangle:{position:"absolute",border:"7px solid transparent",borderBottomColor:"#fff"},triangleShadow:{position:"absolute",border:"8px solid transparent",borderBottomColor:"rgba(0,0,0,0.15)"}},"hide-triangle":{triangle:{display:"none"},triangleShadow:{display:"none"}},"top-left-triangle":{triangle:{top:"-14px",left:"10px"},triangleShadow:{top:"-16px",left:"9px"}},"top-right-triangle":{triangle:{top:"-14px",right:"10px"},triangleShadow:{top:"-16px",right:"9px"}},"bottom-left-triangle":{triangle:{top:"35px",left:"10px",transform:"rotate(180deg)"},triangleShadow:{top:"37px",left:"9px",transform:"rotate(180deg)"}},"bottom-right-triangle":{triangle:{top:"35px",right:"10px",transform:"rotate(180deg)"},triangleShadow:{top:"37px",right:"9px",transform:"rotate(180deg)"}}},c),{"hide-triangle":"hide"===s,"top-left-triangle":"top-left"===s,"top-right-triangle":"top-right"===s,"bottom-left-triangle":"bottom-left"===s,"bottom-right-triangle":"bottom-right"===s}),g=function(e,t){return n({hex:e,source:"hex"},t)};return r.default.createElement("div",{style:b.card,className:"github-picker "+h},r.default.createElement("div",{style:b.triangleShadow}),r.default.createElement("div",{style:b.triangle}),(0,o.default)(a,(function(e){return r.default.createElement(d.default,{color:e,key:e,onClick:g,onSwatchHover:u})})))};p.propTypes={width:n.default.oneOfType([n.default.string,n.default.number]),colors:n.default.arrayOf(n.default.string),triangle:n.default.oneOf(["hide","top-left","top-right","bottom-left","bottom-right"]),styles:n.default.object},p.defaultProps={width:200,colors:["#B80000","#DB3E00","#FCCB00","#008B02","#006B76","#1273DE","#004DCF","#5300EB","#EB9694","#FAD0C3","#FEF3BD","#C1E1C5","#BEDADC","#C4DEF6","#BED3F3","#D4C4FB"],triangle:"top-left",styles:{}},t.default=(0,u.ColorWrap)(p)},47567:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.GithubSwatch=void 0;var r=i(a(89526)),n=a(29790),l=i(n),o=a(82111);function i(e){return e&&e.__esModule?e:{default:e}}var u=t.GithubSwatch=function(e){var t=e.hover,a=e.color,n=e.onClick,i=e.onSwatchHover,u={position:"relative",zIndex:"2",outline:"2px solid #fff",boxShadow:"0 0 5px 2px rgba(0,0,0,0.25)"},d=(0,l.default)({default:{swatch:{width:"25px",height:"25px",fontSize:"0"}},hover:{swatch:u}},{hover:t});return r.default.createElement("div",{style:d.swatch},r.default.createElement(o.Swatch,{color:a,onClick:n,onHover:i,focusStyle:u}))};t.default=(0,n.handleHover)(u)},73879:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.HuePicker=void 0;var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},n=s(a(89526)),l=s(a(2652)),o=s(a(29790)),i=s(a(72739)),u=a(82111),d=s(a(26870));function s(e){return e&&e.__esModule?e:{default:e}}var p=t.HuePicker=function(e){var t=e.width,a=e.height,l=e.onChange,d=e.hsl,s=e.direction,p=e.pointer,c=e.styles,f=void 0===c?{}:c,h=e.className,b=void 0===h?"":h,g=(0,o.default)((0,i.default)({default:{picker:{position:"relative",width:t,height:a},hue:{radius:"2px"}}},f));return n.default.createElement("div",{style:g.picker,className:"hue-picker "+b},n.default.createElement(u.Hue,r({},g.hue,{hsl:d,pointer:p,onChange:function(e){return l({a:1,h:e.h,l:.5,s:1})},direction:s})))};p.propTypes={styles:l.default.object},p.defaultProps={width:"316px",height:"16px",direction:"horizontal",pointer:d.default,styles:{}},t.default=(0,u.ColorWrap)(p)},26870:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.SliderPointer=void 0;var r=l(a(89526)),n=l(a(29790));function l(e){return e&&e.__esModule?e:{default:e}}var o=t.SliderPointer=function(e){var t=e.direction,a=(0,n.default)({default:{picker:{width:"18px",height:"18px",borderRadius:"50%",transform:"translate(-9px, -1px)",backgroundColor:"rgb(248, 248, 248)",boxShadow:"0 1px 4px 0 rgba(0, 0, 0, 0.37)"}},vertical:{picker:{transform:"translate(-3px, -9px)"}}},{vertical:"vertical"===t});return r.default.createElement("div",{style:a.picker})};t.default=o},72508:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Material=void 0;var r=u(a(89526)),n=u(a(29790)),l=u(a(72739)),o=u(a(53650)),i=a(82111);function u(e){return e&&e.__esModule?e:{default:e}}var d=t.Material=function(e){var t=e.onChange,a=e.hex,u=e.rgb,d=e.styles,s=void 0===d?{}:d,p=e.className,c=void 0===p?"":p,f=(0,n.default)((0,l.default)({default:{material:{width:"98px",height:"98px",padding:"16px",fontFamily:"Roboto"},HEXwrap:{position:"relative"},HEXinput:{width:"100%",marginTop:"12px",fontSize:"15px",color:"#333",padding:"0px",border:"0px",borderBottom:"2px solid "+a,outline:"none",height:"30px"},HEXlabel:{position:"absolute",top:"0px",left:"0px",fontSize:"11px",color:"#999999",textTransform:"capitalize"},Hex:{style:{}},RGBwrap:{position:"relative"},RGBinput:{width:"100%",marginTop:"12px",fontSize:"15px",color:"#333",padding:"0px",border:"0px",borderBottom:"1px solid #eee",outline:"none",height:"30px"},RGBlabel:{position:"absolute",top:"0px",left:"0px",fontSize:"11px",color:"#999999",textTransform:"capitalize"},split:{display:"flex",marginRight:"-10px",paddingTop:"11px"},third:{flex:"1",paddingRight:"10px"}}},s)),h=function(e,a){e.hex?o.default.isValidHex(e.hex)&&t({hex:e.hex,source:"hex"},a):(e.r||e.g||e.b)&&t({r:e.r||u.r,g:e.g||u.g,b:e.b||u.b,source:"rgb"},a)};return r.default.createElement(i.Raised,{styles:s},r.default.createElement("div",{style:f.material,className:"material-picker "+c},r.default.createElement(i.EditableInput,{style:{wrap:f.HEXwrap,input:f.HEXinput,label:f.HEXlabel},label:"hex",value:a,onChange:h}),r.default.createElement("div",{style:f.split,className:"flexbox-fix"},r.default.createElement("div",{style:f.third},r.default.createElement(i.EditableInput,{style:{wrap:f.RGBwrap,input:f.RGBinput,label:f.RGBlabel},label:"r",value:u.r,onChange:h})),r.default.createElement("div",{style:f.third},r.default.createElement(i.EditableInput,{style:{wrap:f.RGBwrap,input:f.RGBinput,label:f.RGBlabel},label:"g",value:u.g,onChange:h})),r.default.createElement("div",{style:f.third},r.default.createElement(i.EditableInput,{style:{wrap:f.RGBwrap,input:f.RGBinput,label:f.RGBlabel},label:"b",value:u.b,onChange:h})))))};t.default=(0,i.ColorWrap)(d)},48374:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Photoshop=void 0;var r=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),n=h(a(89526)),l=h(a(2652)),o=h(a(29790)),i=h(a(72739)),u=a(82111),d=h(a(15791)),s=h(a(21003)),p=h(a(61384)),c=h(a(2e3)),f=h(a(98784));function h(e){return e&&e.__esModule?e:{default:e}}var b=t.Photoshop=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var a=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return a.state={currentColor:e.hex},a}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"render",value:function(){var e=this.props,t=e.styles,a=void 0===t?{}:t,r=e.className,l=void 0===r?"":r,h=(0,o.default)((0,i.default)({default:{picker:{background:"#DCDCDC",borderRadius:"4px",boxShadow:"0 0 0 1px rgba(0,0,0,.25), 0 8px 16px rgba(0,0,0,.15)",boxSizing:"initial",width:"513px"},head:{backgroundImage:"linear-gradient(-180deg, #F0F0F0 0%, #D4D4D4 100%)",borderBottom:"1px solid #B1B1B1",boxShadow:"inset 0 1px 0 0 rgba(255,255,255,.2), inset 0 -1px 0 0 rgba(0,0,0,.02)",height:"23px",lineHeight:"24px",borderRadius:"4px 4px 0 0",fontSize:"13px",color:"#4D4D4D",textAlign:"center"},body:{padding:"15px 15px 0",display:"flex"},saturation:{width:"256px",height:"256px",position:"relative",border:"2px solid #B3B3B3",borderBottom:"2px solid #F0F0F0",overflow:"hidden"},hue:{position:"relative",height:"256px",width:"19px",marginLeft:"10px",border:"2px solid #B3B3B3",borderBottom:"2px solid #F0F0F0"},controls:{width:"180px",marginLeft:"10px"},top:{display:"flex"},previews:{width:"60px"},actions:{flex:"1",marginLeft:"20px"}}},a));return n.default.createElement("div",{style:h.picker,className:"photoshop-picker "+l},n.default.createElement("div",{style:h.head},this.props.header),n.default.createElement("div",{style:h.body,className:"flexbox-fix"},n.default.createElement("div",{style:h.saturation},n.default.createElement(u.Saturation,{hsl:this.props.hsl,hsv:this.props.hsv,pointer:s.default,onChange:this.props.onChange})),n.default.createElement("div",{style:h.hue},n.default.createElement(u.Hue,{direction:"vertical",hsl:this.props.hsl,pointer:p.default,onChange:this.props.onChange})),n.default.createElement("div",{style:h.controls},n.default.createElement("div",{style:h.top,className:"flexbox-fix"},n.default.createElement("div",{style:h.previews},n.default.createElement(f.default,{rgb:this.props.rgb,currentColor:this.state.currentColor})),n.default.createElement("div",{style:h.actions},n.default.createElement(c.default,{label:"OK",onClick:this.props.onAccept,active:!0}),n.default.createElement(c.default,{label:"Cancel",onClick:this.props.onCancel}),n.default.createElement(d.default,{onChange:this.props.onChange,rgb:this.props.rgb,hsv:this.props.hsv,hex:this.props.hex}))))))}}]),t}(n.default.Component);b.propTypes={header:l.default.string,styles:l.default.object},b.defaultProps={header:"Color Picker",styles:{}},t.default=(0,u.ColorWrap)(b)},2e3:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.PhotoshopButton=void 0;var r=l(a(89526)),n=l(a(29790));function l(e){return e&&e.__esModule?e:{default:e}}var o=t.PhotoshopButton=function(e){var t=e.onClick,a=e.label,l=e.children,o=e.active,i=(0,n.default)({default:{button:{backgroundImage:"linear-gradient(-180deg, #FFFFFF 0%, #E6E6E6 100%)",border:"1px solid #878787",borderRadius:"2px",height:"20px",boxShadow:"0 1px 0 0 #EAEAEA",fontSize:"14px",color:"#000",lineHeight:"20px",textAlign:"center",marginBottom:"10px",cursor:"pointer"}},active:{button:{boxShadow:"0 0 0 1px #878787"}}},{active:o});return r.default.createElement("div",{style:i.button,onClick:t},a||l)};t.default=o},15791:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.PhotoshopPicker=void 0;var r=i(a(89526)),n=i(a(29790)),l=i(a(53650)),o=a(82111);function i(e){return e&&e.__esModule?e:{default:e}}var u=t.PhotoshopPicker=function(e){var t=e.onChange,a=e.rgb,i=e.hsv,u=e.hex,d=(0,n.default)({default:{fields:{paddingTop:"5px",paddingBottom:"9px",width:"80px",position:"relative"},divider:{height:"5px"},RGBwrap:{position:"relative"},RGBinput:{marginLeft:"40%",width:"40%",height:"18px",border:"1px solid #888888",boxShadow:"inset 0 1px 1px rgba(0,0,0,.1), 0 1px 0 0 #ECECEC",marginBottom:"5px",fontSize:"13px",paddingLeft:"3px",marginRight:"10px"},RGBlabel:{left:"0px",width:"34px",textTransform:"uppercase",fontSize:"13px",height:"18px",lineHeight:"22px",position:"absolute"},HEXwrap:{position:"relative"},HEXinput:{marginLeft:"20%",width:"80%",height:"18px",border:"1px solid #888888",boxShadow:"inset 0 1px 1px rgba(0,0,0,.1), 0 1px 0 0 #ECECEC",marginBottom:"6px",fontSize:"13px",paddingLeft:"3px"},HEXlabel:{position:"absolute",top:"0px",left:"0px",width:"14px",textTransform:"uppercase",fontSize:"13px",height:"18px",lineHeight:"22px"},fieldSymbols:{position:"absolute",top:"5px",right:"-7px",fontSize:"13px"},symbol:{height:"20px",lineHeight:"22px",paddingBottom:"7px"}}}),s=function(e,r){e["#"]?l.default.isValidHex(e["#"])&&t({hex:e["#"],source:"hex"},r):e.r||e.g||e.b?t({r:e.r||a.r,g:e.g||a.g,b:e.b||a.b,source:"rgb"},r):(e.h||e.s||e.v)&&t({h:e.h||i.h,s:e.s||i.s,v:e.v||i.v,source:"hsv"},r)};return r.default.createElement("div",{style:d.fields},r.default.createElement(o.EditableInput,{style:{wrap:d.RGBwrap,input:d.RGBinput,label:d.RGBlabel},label:"h",value:Math.round(i.h),onChange:s}),r.default.createElement(o.EditableInput,{style:{wrap:d.RGBwrap,input:d.RGBinput,label:d.RGBlabel},label:"s",value:Math.round(100*i.s),onChange:s}),r.default.createElement(o.EditableInput,{style:{wrap:d.RGBwrap,input:d.RGBinput,label:d.RGBlabel},label:"v",value:Math.round(100*i.v),onChange:s}),r.default.createElement("div",{style:d.divider}),r.default.createElement(o.EditableInput,{style:{wrap:d.RGBwrap,input:d.RGBinput,label:d.RGBlabel},label:"r",value:a.r,onChange:s}),r.default.createElement(o.EditableInput,{style:{wrap:d.RGBwrap,input:d.RGBinput,label:d.RGBlabel},label:"g",value:a.g,onChange:s}),r.default.createElement(o.EditableInput,{style:{wrap:d.RGBwrap,input:d.RGBinput,label:d.RGBlabel},label:"b",value:a.b,onChange:s}),r.default.createElement("div",{style:d.divider}),r.default.createElement(o.EditableInput,{style:{wrap:d.HEXwrap,input:d.HEXinput,label:d.HEXlabel},label:"#",value:u.replace("#",""),onChange:s}),r.default.createElement("div",{style:d.fieldSymbols},r.default.createElement("div",{style:d.symbol},"\xb0"),r.default.createElement("div",{style:d.symbol},"%"),r.default.createElement("div",{style:d.symbol},"%")))};t.default=u},61384:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.PhotoshopPointerCircle=void 0;var r=l(a(89526)),n=l(a(29790));function l(e){return e&&e.__esModule?e:{default:e}}var o=t.PhotoshopPointerCircle=function(){var e=(0,n.default)({default:{triangle:{width:0,height:0,borderStyle:"solid",borderWidth:"4px 0 4px 6px",borderColor:"transparent transparent transparent #fff",position:"absolute",top:"1px",left:"1px"},triangleBorder:{width:0,height:0,borderStyle:"solid",borderWidth:"5px 0 5px 8px",borderColor:"transparent transparent transparent #555"},left:{Extend:"triangleBorder",transform:"translate(-13px, -4px)"},leftInside:{Extend:"triangle",transform:"translate(-8px, -5px)"},right:{Extend:"triangleBorder",transform:"translate(20px, -14px) rotate(180deg)"},rightInside:{Extend:"triangle",transform:"translate(-8px, -5px)"}}});return r.default.createElement("div",{style:e.pointer},r.default.createElement("div",{style:e.left},r.default.createElement("div",{style:e.leftInside})),r.default.createElement("div",{style:e.right},r.default.createElement("div",{style:e.rightInside})))};t.default=o},21003:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.PhotoshopPointerCircle=void 0;var r=l(a(89526)),n=l(a(29790));function l(e){return e&&e.__esModule?e:{default:e}}var o=t.PhotoshopPointerCircle=function(e){var t=e.hsl,a=(0,n.default)({default:{picker:{width:"12px",height:"12px",borderRadius:"6px",boxShadow:"inset 0 0 0 1px #fff",transform:"translate(-6px, -6px)"}},"black-outline":{picker:{boxShadow:"inset 0 0 0 1px #000"}}},{"black-outline":t.l>.5});return r.default.createElement("div",{style:a.picker})};t.default=o},98784:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.PhotoshopPreviews=void 0;var r=l(a(89526)),n=l(a(29790));function l(e){return e&&e.__esModule?e:{default:e}}var o=t.PhotoshopPreviews=function(e){var t=e.rgb,a=e.currentColor,l=(0,n.default)({default:{swatches:{border:"1px solid #B3B3B3",borderBottom:"1px solid #F0F0F0",marginBottom:"2px",marginTop:"1px"},new:{height:"34px",background:"rgb("+t.r+","+t.g+", "+t.b+")",boxShadow:"inset 1px 0 0 #000, inset -1px 0 0 #000, inset 0 1px 0 #000"},current:{height:"34px",background:a,boxShadow:"inset 1px 0 0 #000, inset -1px 0 0 #000, inset 0 -1px 0 #000"},label:{fontSize:"14px",color:"#000",textAlign:"center"}}});return r.default.createElement("div",null,r.default.createElement("div",{style:l.label},"new"),r.default.createElement("div",{style:l.swatches},r.default.createElement("div",{style:l.new}),r.default.createElement("div",{style:l.current})),r.default.createElement("div",{style:l.label},"current"))};t.default=o},97554:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Sketch=void 0;var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},n=p(a(89526)),l=p(a(2652)),o=p(a(29790)),i=p(a(72739)),u=a(82111),d=p(a(89448)),s=p(a(70520));function p(e){return e&&e.__esModule?e:{default:e}}var c=t.Sketch=function(e){var t=e.width,a=e.rgb,l=e.hex,p=e.hsv,c=e.hsl,f=e.onChange,h=e.onSwatchHover,b=e.disableAlpha,g=e.presetColors,v=e.renderers,x=e.styles,y=void 0===x?{}:x,m=e.className,w=void 0===m?"":m,E=(0,o.default)((0,i.default)({default:r({picker:{width:t,padding:"10px 10px 0",boxSizing:"initial",background:"#fff",borderRadius:"4px",boxShadow:"0 0 0 1px rgba(0,0,0,.15), 0 8px 16px rgba(0,0,0,.15)"},saturation:{width:"100%",paddingBottom:"75%",position:"relative",overflow:"hidden"},Saturation:{radius:"3px",shadow:"inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)"},controls:{display:"flex"},sliders:{padding:"4px 0",flex:"1"},color:{width:"24px",height:"24px",position:"relative",marginTop:"4px",marginLeft:"4px",borderRadius:"3px"},activeColor:{absolute:"0px 0px 0px 0px",borderRadius:"2px",background:"rgba("+a.r+","+a.g+","+a.b+","+a.a+")",boxShadow:"inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)"},hue:{position:"relative",height:"10px",overflow:"hidden"},Hue:{radius:"2px",shadow:"inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)"},alpha:{position:"relative",height:"10px",marginTop:"4px",overflow:"hidden"},Alpha:{radius:"2px",shadow:"inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)"}},y),disableAlpha:{color:{height:"10px"},hue:{height:"10px"},alpha:{display:"none"}}},y),{disableAlpha:b});return n.default.createElement("div",{style:E.picker,className:"sketch-picker "+w},n.default.createElement("div",{style:E.saturation},n.default.createElement(u.Saturation,{style:E.Saturation,hsl:c,hsv:p,onChange:f})),n.default.createElement("div",{style:E.controls,className:"flexbox-fix"},n.default.createElement("div",{style:E.sliders},n.default.createElement("div",{style:E.hue},n.default.createElement(u.Hue,{style:E.Hue,hsl:c,onChange:f})),n.default.createElement("div",{style:E.alpha},n.default.createElement(u.Alpha,{style:E.Alpha,rgb:a,hsl:c,renderers:v,onChange:f}))),n.default.createElement("div",{style:E.color},n.default.createElement(u.Checkboard,null),n.default.createElement("div",{style:E.activeColor}))),n.default.createElement(d.default,{rgb:a,hsl:c,hex:l,onChange:f,disableAlpha:b}),n.default.createElement(s.default,{colors:g,onClick:f,onSwatchHover:h}))};c.propTypes={disableAlpha:l.default.bool,width:l.default.oneOfType([l.default.string,l.default.number]),styles:l.default.object},c.defaultProps={disableAlpha:!1,width:200,styles:{},presetColors:["#D0021B","#F5A623","#F8E71C","#8B572A","#7ED321","#417505","#BD10E0","#9013FE","#4A90E2","#50E3C2","#B8E986","#000000","#4A4A4A","#9B9B9B","#FFFFFF"]},t.default=(0,u.ColorWrap)(c)},89448:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.SketchFields=void 0;var r=i(a(89526)),n=i(a(29790)),l=i(a(53650)),o=a(82111);function i(e){return e&&e.__esModule?e:{default:e}}var u=t.SketchFields=function(e){var t=e.onChange,a=e.rgb,i=e.hsl,u=e.hex,d=e.disableAlpha,s=(0,n.default)({default:{fields:{display:"flex",paddingTop:"4px"},single:{flex:"1",paddingLeft:"6px"},alpha:{flex:"1",paddingLeft:"6px"},double:{flex:"2"},input:{width:"80%",padding:"4px 10% 3px",border:"none",boxShadow:"inset 0 0 0 1px #ccc",fontSize:"11px"},label:{display:"block",textAlign:"center",fontSize:"11px",color:"#222",paddingTop:"3px",paddingBottom:"4px",textTransform:"capitalize"}},disableAlpha:{alpha:{display:"none"}}},{disableAlpha:d}),p=function(e,r){e.hex?l.default.isValidHex(e.hex)&&t({hex:e.hex,source:"hex"},r):e.r||e.g||e.b?t({r:e.r||a.r,g:e.g||a.g,b:e.b||a.b,a:a.a,source:"rgb"},r):e.a&&(e.a<0?e.a=0:e.a>100&&(e.a=100),e.a/=100,t({h:i.h,s:i.s,l:i.l,a:e.a,source:"rgb"},r))};return r.default.createElement("div",{style:s.fields,className:"flexbox-fix"},r.default.createElement("div",{style:s.double},r.default.createElement(o.EditableInput,{style:{input:s.input,label:s.label},label:"hex",value:u.replace("#",""),onChange:p})),r.default.createElement("div",{style:s.single},r.default.createElement(o.EditableInput,{style:{input:s.input,label:s.label},label:"r",value:a.r,onChange:p,dragLabel:"true",dragMax:"255"})),r.default.createElement("div",{style:s.single},r.default.createElement(o.EditableInput,{style:{input:s.input,label:s.label},label:"g",value:a.g,onChange:p,dragLabel:"true",dragMax:"255"})),r.default.createElement("div",{style:s.single},r.default.createElement(o.EditableInput,{style:{input:s.input,label:s.label},label:"b",value:a.b,onChange:p,dragLabel:"true",dragMax:"255"})),r.default.createElement("div",{style:s.alpha},r.default.createElement(o.EditableInput,{style:{input:s.input,label:s.label},label:"a",value:Math.round(100*a.a),onChange:p,dragLabel:"true",dragMax:"100"})))};t.default=u},70520:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.SketchPresetColors=void 0;var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},n=u(a(89526)),l=u(a(2652)),o=u(a(29790)),i=a(82111);function u(e){return e&&e.__esModule?e:{default:e}}var d=t.SketchPresetColors=function(e){var t=e.colors,a=e.onClick,l=void 0===a?function(){}:a,u=e.onSwatchHover,d=(0,o.default)({default:{colors:{margin:"0 -10px",padding:"10px 0 0 10px",borderTop:"1px solid #eee",display:"flex",flexWrap:"wrap",position:"relative"},swatchWrap:{width:"16px",height:"16px",margin:"0 10px 10px 0"},swatch:{borderRadius:"3px",boxShadow:"inset 0 0 0 1px rgba(0,0,0,.15)"}},"no-presets":{colors:{display:"none"}}},{"no-presets":!t||!t.length}),s=function(e,t){l({hex:e,source:"hex"},t)};return n.default.createElement("div",{style:d.colors,className:"flexbox-fix"},t.map((function(e){var t="string"===typeof e?{color:e}:e,a=""+t.color+(t.title||"");return n.default.createElement("div",{key:a,style:d.swatchWrap},n.default.createElement(i.Swatch,r({},t,{style:d.swatch,onClick:s,onHover:u,focusStyle:{boxShadow:"inset 0 0 0 1px rgba(0,0,0,.15), 0 0 4px "+t.color}})))})))};d.propTypes={colors:l.default.arrayOf(l.default.oneOfType([l.default.string,l.default.shape({color:l.default.string,title:l.default.string})])).isRequired},t.default=d},61161:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Slider=void 0;var r=s(a(89526)),n=s(a(2652)),l=s(a(29790)),o=s(a(72739)),i=a(82111),u=s(a(77158)),d=s(a(4303));function s(e){return e&&e.__esModule?e:{default:e}}var p=t.Slider=function(e){var t=e.hsl,a=e.onChange,n=e.pointer,d=e.styles,s=void 0===d?{}:d,p=e.className,c=void 0===p?"":p,f=(0,l.default)((0,o.default)({default:{hue:{height:"12px",position:"relative"},Hue:{radius:"2px"}}},s));return r.default.createElement("div",{style:f.wrap||{},className:"slider-picker "+c},r.default.createElement("div",{style:f.hue},r.default.createElement(i.Hue,{style:f.Hue,hsl:t,pointer:n,onChange:a})),r.default.createElement("div",{style:f.swatches},r.default.createElement(u.default,{hsl:t,onClick:a})))};p.propTypes={styles:n.default.object},p.defaultProps={pointer:d.default,styles:{}},t.default=(0,i.ColorWrap)(p)},4303:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.SliderPointer=void 0;var r=l(a(89526)),n=l(a(29790));function l(e){return e&&e.__esModule?e:{default:e}}var o=t.SliderPointer=function(){var e=(0,n.default)({default:{picker:{width:"14px",height:"14px",borderRadius:"6px",transform:"translate(-7px, -1px)",backgroundColor:"rgb(248, 248, 248)",boxShadow:"0 1px 4px 0 rgba(0, 0, 0, 0.37)"}}});return r.default.createElement("div",{style:e.picker})};t.default=o},92360:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.SliderSwatch=void 0;var r=l(a(89526)),n=l(a(29790));function l(e){return e&&e.__esModule?e:{default:e}}var o=t.SliderSwatch=function(e){var t=e.hsl,a=e.offset,l=e.onClick,o=void 0===l?function(){}:l,i=e.active,u=e.first,d=e.last,s=(0,n.default)({default:{swatch:{height:"12px",background:"hsl("+t.h+", 50%, "+100*a+"%)",cursor:"pointer"}},first:{swatch:{borderRadius:"2px 0 0 2px"}},last:{swatch:{borderRadius:"0 2px 2px 0"}},active:{swatch:{transform:"scaleY(1.8)",borderRadius:"3.6px/2px"}}},{active:i,first:u,last:d});return r.default.createElement("div",{style:s.swatch,onClick:function(e){return o({h:t.h,s:.5,l:a,source:"hsl"},e)}})};t.default=o},77158:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.SliderSwatches=void 0;var r=o(a(89526)),n=o(a(29790)),l=o(a(92360));function o(e){return e&&e.__esModule?e:{default:e}}var i=t.SliderSwatches=function(e){var t=e.onClick,a=e.hsl,o=(0,n.default)({default:{swatches:{marginTop:"20px"},swatch:{boxSizing:"border-box",width:"20%",paddingRight:"1px",float:"left"},clear:{clear:"both"}}}),i=.1;return r.default.createElement("div",{style:o.swatches},r.default.createElement("div",{style:o.swatch},r.default.createElement(l.default,{hsl:a,offset:".80",active:Math.abs(a.l-.8)<i&&Math.abs(a.s-.5)<i,onClick:t,first:!0})),r.default.createElement("div",{style:o.swatch},r.default.createElement(l.default,{hsl:a,offset:".65",active:Math.abs(a.l-.65)<i&&Math.abs(a.s-.5)<i,onClick:t})),r.default.createElement("div",{style:o.swatch},r.default.createElement(l.default,{hsl:a,offset:".50",active:Math.abs(a.l-.5)<i&&Math.abs(a.s-.5)<i,onClick:t})),r.default.createElement("div",{style:o.swatch},r.default.createElement(l.default,{hsl:a,offset:".35",active:Math.abs(a.l-.35)<i&&Math.abs(a.s-.5)<i,onClick:t})),r.default.createElement("div",{style:o.swatch},r.default.createElement(l.default,{hsl:a,offset:".20",active:Math.abs(a.l-.2)<i&&Math.abs(a.s-.5)<i,onClick:t,last:!0})),r.default.createElement("div",{style:o.clear}))};t.default=i},55734:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Swatches=void 0;var r=c(a(89526)),n=c(a(2652)),l=c(a(29790)),o=c(a(34118)),i=c(a(72739)),u=c(a(53650)),d=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t.default=e,t}(a(26321)),s=a(82111),p=c(a(766));function c(e){return e&&e.__esModule?e:{default:e}}var f=t.Swatches=function(e){var t=e.width,a=e.height,n=e.onChange,d=e.onSwatchHover,c=e.colors,f=e.hex,h=e.styles,b=void 0===h?{}:h,g=e.className,v=void 0===g?"":g,x=(0,l.default)((0,i.default)({default:{picker:{width:t,height:a},overflow:{height:a,overflowY:"scroll"},body:{padding:"16px 0 6px 16px"},clear:{clear:"both"}}},b)),y=function(e,t){u.default.isValidHex(e)&&n({hex:e,source:"hex"},t)};return r.default.createElement("div",{style:x.picker,className:"swatches-picker "+v},r.default.createElement(s.Raised,null,r.default.createElement("div",{style:x.overflow},r.default.createElement("div",{style:x.body},(0,o.default)(c,(function(e){return r.default.createElement(p.default,{key:e.toString(),group:e,active:f,onClick:y,onSwatchHover:d})})),r.default.createElement("div",{style:x.clear})))))};f.propTypes={width:n.default.oneOfType([n.default.string,n.default.number]),height:n.default.oneOfType([n.default.string,n.default.number]),colors:n.default.arrayOf(n.default.arrayOf(n.default.string)),styles:n.default.object},f.defaultProps={width:320,height:240,colors:[[d.red[900],d.red[700],d.red[500],d.red[300],d.red[100]],[d.pink[900],d.pink[700],d.pink[500],d.pink[300],d.pink[100]],[d.purple[900],d.purple[700],d.purple[500],d.purple[300],d.purple[100]],[d.deepPurple[900],d.deepPurple[700],d.deepPurple[500],d.deepPurple[300],d.deepPurple[100]],[d.indigo[900],d.indigo[700],d.indigo[500],d.indigo[300],d.indigo[100]],[d.blue[900],d.blue[700],d.blue[500],d.blue[300],d.blue[100]],[d.lightBlue[900],d.lightBlue[700],d.lightBlue[500],d.lightBlue[300],d.lightBlue[100]],[d.cyan[900],d.cyan[700],d.cyan[500],d.cyan[300],d.cyan[100]],[d.teal[900],d.teal[700],d.teal[500],d.teal[300],d.teal[100]],["#194D33",d.green[700],d.green[500],d.green[300],d.green[100]],[d.lightGreen[900],d.lightGreen[700],d.lightGreen[500],d.lightGreen[300],d.lightGreen[100]],[d.lime[900],d.lime[700],d.lime[500],d.lime[300],d.lime[100]],[d.yellow[900],d.yellow[700],d.yellow[500],d.yellow[300],d.yellow[100]],[d.amber[900],d.amber[700],d.amber[500],d.amber[300],d.amber[100]],[d.orange[900],d.orange[700],d.orange[500],d.orange[300],d.orange[100]],[d.deepOrange[900],d.deepOrange[700],d.deepOrange[500],d.deepOrange[300],d.deepOrange[100]],[d.brown[900],d.brown[700],d.brown[500],d.brown[300],d.brown[100]],[d.blueGrey[900],d.blueGrey[700],d.blueGrey[500],d.blueGrey[300],d.blueGrey[100]],["#000000","#525252","#969696","#D9D9D9","#FFFFFF"]],styles:{}},t.default=(0,s.ColorWrap)(f)},53491:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.SwatchesColor=void 0;var r=u(a(89526)),n=u(a(29790)),l=u(a(53650)),o=a(82111),i=u(a(21112));function u(e){return e&&e.__esModule?e:{default:e}}var d=t.SwatchesColor=function(e){var t=e.color,a=e.onClick,u=void 0===a?function(){}:a,d=e.onSwatchHover,s=e.first,p=e.last,c=e.active,f=(0,n.default)({default:{color:{width:"40px",height:"24px",cursor:"pointer",background:t,marginBottom:"1px"},check:{color:l.default.getContrastingColor(t),marginLeft:"8px",display:"none"}},first:{color:{overflow:"hidden",borderRadius:"2px 2px 0 0"}},last:{color:{overflow:"hidden",borderRadius:"0 0 2px 2px"}},active:{check:{display:"block"}},"color-#FFFFFF":{color:{boxShadow:"inset 0 0 0 1px #ddd"},check:{color:"#333"}},transparent:{check:{color:"#333"}}},{first:s,last:p,active:c,"color-#FFFFFF":"#FFFFFF"===t,transparent:"transparent"===t});return r.default.createElement(o.Swatch,{color:t,style:f.color,onClick:u,onHover:d,focusStyle:{boxShadow:"0 0 4px "+t}},r.default.createElement("div",{style:f.check},r.default.createElement(i.default,null)))};t.default=d},766:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.SwatchesGroup=void 0;var r=i(a(89526)),n=i(a(29790)),l=i(a(34118)),o=i(a(53491));function i(e){return e&&e.__esModule?e:{default:e}}var u=t.SwatchesGroup=function(e){var t=e.onClick,a=e.onSwatchHover,i=e.group,u=e.active,d=(0,n.default)({default:{group:{paddingBottom:"10px",width:"40px",float:"left",marginRight:"10px"}}});return r.default.createElement("div",{style:d.group},(0,l.default)(i,(function(e,n){return r.default.createElement(o.default,{key:e,color:e,active:e.toLowerCase()===u,first:0===n,last:n===i.length-1,onClick:t,onSwatchHover:a})})))};t.default=u},30756:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Twitter=void 0;var r=s(a(89526)),n=s(a(2652)),l=s(a(29790)),o=s(a(34118)),i=s(a(72739)),u=s(a(53650)),d=a(82111);function s(e){return e&&e.__esModule?e:{default:e}}var p=t.Twitter=function(e){var t=e.onChange,a=e.onSwatchHover,n=e.hex,s=e.colors,p=e.width,c=e.triangle,f=e.styles,h=void 0===f?{}:f,b=e.className,g=void 0===b?"":b,v=(0,l.default)((0,i.default)({default:{card:{width:p,background:"#fff",border:"0 solid rgba(0,0,0,0.25)",boxShadow:"0 1px 4px rgba(0,0,0,0.25)",borderRadius:"4px",position:"relative"},body:{padding:"15px 9px 9px 15px"},label:{fontSize:"18px",color:"#fff"},triangle:{width:"0px",height:"0px",borderStyle:"solid",borderWidth:"0 9px 10px 9px",borderColor:"transparent transparent #fff transparent",position:"absolute"},triangleShadow:{width:"0px",height:"0px",borderStyle:"solid",borderWidth:"0 9px 10px 9px",borderColor:"transparent transparent rgba(0,0,0,.1) transparent",position:"absolute"},hash:{background:"#F0F0F0",height:"30px",width:"30px",borderRadius:"4px 0 0 4px",float:"left",color:"#98A1A4",display:"flex",alignItems:"center",justifyContent:"center"},input:{width:"100px",fontSize:"14px",color:"#666",border:"0px",outline:"none",height:"28px",boxShadow:"inset 0 0 0 1px #F0F0F0",boxSizing:"content-box",borderRadius:"0 4px 4px 0",float:"left",paddingLeft:"8px"},swatch:{width:"30px",height:"30px",float:"left",borderRadius:"4px",margin:"0 6px 6px 0"},clear:{clear:"both"}},"hide-triangle":{triangle:{display:"none"},triangleShadow:{display:"none"}},"top-left-triangle":{triangle:{top:"-10px",left:"12px"},triangleShadow:{top:"-11px",left:"12px"}},"top-right-triangle":{triangle:{top:"-10px",right:"12px"},triangleShadow:{top:"-11px",right:"12px"}}},h),{"hide-triangle":"hide"===c,"top-left-triangle":"top-left"===c,"top-right-triangle":"top-right"===c}),x=function(e,a){u.default.isValidHex(e)&&t({hex:e,source:"hex"},a)};return r.default.createElement("div",{style:v.card,className:"twitter-picker "+g},r.default.createElement("div",{style:v.triangleShadow}),r.default.createElement("div",{style:v.triangle}),r.default.createElement("div",{style:v.body},(0,o.default)(s,(function(e,t){return r.default.createElement(d.Swatch,{key:t,color:e,hex:e,style:v.swatch,onClick:x,onHover:a,focusStyle:{boxShadow:"0 0 4px "+e}})})),r.default.createElement("div",{style:v.hash},"#"),r.default.createElement(d.EditableInput,{label:null,style:{input:v.input},value:n.replace("#",""),onChange:x}),r.default.createElement("div",{style:v.clear})))};p.propTypes={width:n.default.oneOfType([n.default.string,n.default.number]),triangle:n.default.oneOf(["hide","top-left","top-right"]),colors:n.default.arrayOf(n.default.string),styles:n.default.object},p.defaultProps={width:276,colors:["#FF6900","#FCB900","#7BDCB5","#00D084","#8ED1FC","#0693E3","#ABB8C3","#EB144C","#F78DA7","#9900EF"],triangle:"top-left",styles:{}},t.default=(0,d.ColorWrap)(p)},24526:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.calculateChange=function(e,t,a,r,n){var l=n.clientWidth,o=n.clientHeight,i="number"===typeof e.pageX?e.pageX:e.touches[0].pageX,u="number"===typeof e.pageY?e.pageY:e.touches[0].pageY,d=i-(n.getBoundingClientRect().left+window.pageXOffset),s=u-(n.getBoundingClientRect().top+window.pageYOffset);if("vertical"===a){var p=void 0;if(p=s<0?0:s>o?1:Math.round(100*s/o)/100,t.a!==p)return{h:t.h,s:t.s,l:t.l,a:p,source:"rgb"}}else{var c=void 0;if(r!==(c=d<0?0:d>l?1:Math.round(100*d/l)/100))return{h:t.h,s:t.s,l:t.l,a:c,source:"rgb"}}return null}},62880:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var a={},r=t.render=function(e,t,a,r){if("undefined"===typeof document&&!r)return null;var n=r?new r:document.createElement("canvas");n.width=2*a,n.height=2*a;var l=n.getContext("2d");return l?(l.fillStyle=e,l.fillRect(0,0,n.width,n.height),l.fillStyle=t,l.fillRect(0,0,a,a),l.translate(a,a),l.fillRect(0,0,a,a),n.toDataURL()):null};t.get=function(e,t,n,l){var o=e+"-"+t+"-"+n+(l?"-server":"");if(a[o])return a[o];var i=r(e,t,n,l);return a[o]=i,i}},53650:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.red=t.getContrastingColor=t.isValidHex=t.toState=t.simpleCheckForValidColor=void 0;var r=l(a(60239)),n=l(a(29711));function l(e){return e&&e.__esModule?e:{default:e}}t.simpleCheckForValidColor=function(e){var t=0,a=0;return(0,r.default)(["r","g","b","a","h","s","l","v"],(function(r){if(e[r]&&(t+=1,isNaN(e[r])||(a+=1),"s"===r||"l"===r)){/^\d+%$/.test(e[r])&&(a+=1)}})),t===a&&e};var o=t.toState=function(e,t){var a=e.hex?(0,n.default)(e.hex):(0,n.default)(e),r=a.toHsl(),l=a.toHsv(),o=a.toRgb(),i=a.toHex();return 0===r.s&&(r.h=t||0,l.h=t||0),{hsl:r,hex:"000000"===i&&0===o.a?"transparent":"#"+i,rgb:o,hsv:l,oldHue:e.h||t||r.h,source:e.source}};t.isValidHex=function(e){var t="#"===String(e).charAt(0)?1:0;return e.length!==4+t&&e.length<7+t&&(0,n.default)(e).isValid()},t.getContrastingColor=function(e){if(!e)return"#fff";var t=o(e);return"transparent"===t.hex?"rgba(0,0,0,0.4)":(299*t.rgb.r+587*t.rgb.g+114*t.rgb.b)/1e3>=128?"#000":"#fff"},t.red={hsl:{a:1,h:0,l:.5,s:1},hex:"#ff0000",rgb:{r:255,g:0,b:0,a:1},hsv:{h:0,s:1,v:1,a:1}};t.default=t},48228:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.calculateChange=function(e,t,a,r){var n=r.clientWidth,l=r.clientHeight,o="number"===typeof e.pageX?e.pageX:e.touches[0].pageX,i="number"===typeof e.pageY?e.pageY:e.touches[0].pageY,u=o-(r.getBoundingClientRect().left+window.pageXOffset),d=i-(r.getBoundingClientRect().top+window.pageYOffset);if("vertical"===t){var s=void 0;if(d<0)s=359;else if(d>l)s=0;else{s=360*(-100*d/l+100)/100}if(a.h!==s)return{h:s,s:a.s,l:a.l,a:a.a,source:"rgb"}}else{var p=void 0;if(u<0)p=0;else if(u>n)p=359;else{p=360*(100*u/n)/100}if(a.h!==p)return{h:p,s:a.s,l:a.l,a:a.a,source:"rgb"}}return null}},49686:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.handleFocus=void 0;var r,n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},l=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),o=a(89526),i=(r=o)&&r.__esModule?r:{default:r};function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}function s(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}t.handleFocus=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"span";return function(a){function r(){var e,t,a;u(this,r);for(var n=arguments.length,l=Array(n),o=0;o<n;o++)l[o]=arguments[o];return t=a=d(this,(e=r.__proto__||Object.getPrototypeOf(r)).call.apply(e,[this].concat(l))),a.state={focus:!1},a.handleFocus=function(){return a.setState({focus:!0})},a.handleBlur=function(){return a.setState({focus:!1})},d(a,t)}return s(r,a),l(r,[{key:"render",value:function(){return i.default.createElement(t,{onFocus:this.handleFocus,onBlur:this.handleBlur},i.default.createElement(e,n({},this.props,this.state)))}}]),r}(i.default.Component)}},34655:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.calculateChange=function(e,t,a){var r=a.getBoundingClientRect(),n=r.width,l=r.height,o="number"===typeof e.pageX?e.pageX:e.touches[0].pageX,i="number"===typeof e.pageY?e.pageY:e.touches[0].pageY,u=o-(a.getBoundingClientRect().left+window.pageXOffset),d=i-(a.getBoundingClientRect().top+window.pageYOffset);u<0?u=0:u>n?u=n:d<0?d=0:d>l&&(d=l);var s=100*u/n,p=-100*d/l+100;return{h:t.h,s:s,v:p,a:t.a,source:"rgb"}}},8612:function(e,t,a){t.Lf=void 0;var r=a(12302);var n=a(61517);var l=a(18476);var o=a(86971);var i=a(29838);var u=a(97876);Object.defineProperty(t,"Lf",{enumerable:!0,get:function(){return v(u).default}});var d=a(73879);var s=a(72508);var p=a(48374);var c=a(97554);var f=a(61161);var h=a(55734);var b=a(30756);var g=a(92275);function v(e){return e&&e.__esModule?e:{default:e}}v(o).default}}]);
//# sourceMappingURL=react-color.f61f62aa928115bb03214c2cd2cd4076.js.map