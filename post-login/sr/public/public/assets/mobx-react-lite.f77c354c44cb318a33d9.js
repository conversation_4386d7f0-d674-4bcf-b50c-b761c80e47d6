"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["mobx-react-lite"],{13710:function(e,r,n){n.d(r,{Qj:function(){return S},FY:function(){return m},Pi:function(){return C}});var t=n(59621),o=n(89526);if(!o.useState)throw new Error("mobx-react-lite requires React with Hooks support");if(!t.rC)throw new Error("mobx-react-lite@3 requires mobx at least version 6 to be available");var u=n(73961);function a(e){e()}function i(e){return(0,t.Gf)(e)}var c="undefined"===typeof FinalizationRegistry?void 0:FinalizationRegistry;function f(e){return{reaction:e,mounted:!1,changedBeforeMount:!1,cleanAt:Date.now()+l}}var l=1e4;var s=function(e){var r="function"===typeof Symbol&&Symbol.iterator,n=r&&e[r],t=0;if(n)return n.call(e);if(e&&"number"===typeof e.length)return{next:function(){return e&&t>=e.length&&(e=void 0),{value:e&&e[t++],done:!e}}};throw new TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")};var d=c?function(e){var r=new Map,n=1,t=new e((function(e){var n=r.get(e);n&&(n.reaction.dispose(),r.delete(e))}));return{addReactionToTrack:function(e,o,u){var a=n++;return t.register(u,a,e),e.current=f(o),e.current.finalizationRegistryCleanupToken=a,r.set(a,e.current),e.current},recordReactionAsCommitted:function(e){t.unregister(e),e.current&&e.current.finalizationRegistryCleanupToken&&r.delete(e.current.finalizationRegistryCleanupToken)},forceCleanupTimerToRunNowForTests:function(){},resetCleanupScheduleForTests:function(){}}}(c):function(){var e,r=new Set;function n(){void 0===e&&(e=setTimeout(t,1e4))}function t(){e=void 0;var t=Date.now();r.forEach((function(e){var n=e.current;n&&t>=n.cleanAt&&(n.reaction.dispose(),e.current=null,r.delete(e))})),r.size>0&&n()}return{addReactionToTrack:function(e,t,o){var u;return e.current=f(t),u=e,r.add(u),n(),e.current},recordReactionAsCommitted:function(e){r.delete(e)},forceCleanupTimerToRunNowForTests:function(){e&&(clearTimeout(e),t())},resetCleanupScheduleForTests:function(){var n,t;if(r.size>0){try{for(var o=s(r),u=o.next();!u.done;u=o.next()){var a=u.value,i=a.current;i&&(i.reaction.dispose(),a.current=null)}}catch(c){n={error:c}}finally{try{u&&!u.done&&(t=o.return)&&t.call(o)}finally{if(n)throw n.error}}r.clear()}e&&(clearTimeout(e),e=void 0)}}}(),p=d.addReactionToTrack,v=d.recordReactionAsCommitted,y=(d.resetCleanupScheduleForTests,d.forceCleanupTimerToRunNowForTests,!1);function m(){return y}var h=function(e,r){var n="function"===typeof Symbol&&e[Symbol.iterator];if(!n)return e;var t,o,u=n.call(e),a=[];try{for(;(void 0===r||r-- >0)&&!(t=u.next()).done;)a.push(t.value)}catch(i){o={error:i}}finally{try{t&&!t.done&&(n=u.return)&&n.call(u)}finally{if(o)throw o.error}}return a};function b(e){return"observer"+e}var w=function(){};function T(){return new w}function g(e,r){if(void 0===r&&(r="observed"),m())return e();var n=h(o.useState(T),1)[0],u=h(o.useState(),2)[1],a=function(){return u([])},c=o.useRef(null);if(!c.current)var f=new t.le(b(r),(function(){l.mounted?a():l.changedBeforeMount=!0})),l=p(c,f,n);var s,d,y=c.current.reaction;if(o.useDebugValue(y,i),o.useEffect((function(){return v(c),c.current?(c.current.mounted=!0,c.current.changedBeforeMount&&(c.current.changedBeforeMount=!1,a())):(c.current={reaction:new t.le(b(r),(function(){a()})),mounted:!0,changedBeforeMount:!1,cleanAt:1/0},a()),function(){c.current.reaction.dispose(),c.current=null}}),[]),y.track((function(){try{s=e()}catch(r){d=r}})),d)throw d;return s}var R=function(){return R=Object.assign||function(e){for(var r,n=1,t=arguments.length;n<t;n++)for(var o in r=arguments[n])Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o]);return e},R.apply(this,arguments)};function C(e,r){if(m())return e;var n,t,u,a=R({forwardRef:!1},r),i=e.displayName||e.name,c=function(r,n){return g((function(){return e(r,n)}),i)};return c.displayName=i,e.contextTypes&&(c.contextTypes=e.contextTypes),n=a.forwardRef?(0,o.memo)((0,o.forwardRef)(c)):(0,o.memo)(c),t=e,u=n,Object.keys(t).forEach((function(e){k[e]||Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(t,e))})),n.displayName=i,n}var k={$$typeof:!0,render:!0,compare:!0,type:!0};function S(e){var r=e.children,n=e.render,t=r||n;return"function"!==typeof t?null:g(t)}S.displayName="Observer";var x;(x=u.unstable_batchedUpdates)||(x=a),(0,t.jQ)({reactionScheduler:x})}}]);
//# sourceMappingURL=mobx-react-lite.35b93b33600cb48f3cf3b91173955160.js.map