"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["react-virtuoso"],{26246:function(e,t,o){o.d(t,{OO:function(){return co}});var n=o(89526),r=o(73961);const i=0,l=1,s=2,c=4;function a(e,t){return o=>e(t(o))}function u(e,t){return t(e)}function d(e,t){return o=>e(t,o)}function m(e,t){return()=>e(t)}function f(e,t){return t(e),e}function h(...e){return e}function g(e){e()}function p(e){return()=>e}function v(e){return void 0!==e}function I(){}function T(e,t){return e(l,t)}function x(e,t){e(i,t)}function w(e){e(s)}function S(e){return e(c)}function C(e,t){return T(e,d(t,i))}function E(e,t){const o=e(l,(e=>{o(),t(e)}));return o}function H(){const e=[];return(t,o)=>{switch(t){case s:return void e.splice(0,e.length);case l:return e.push(o),()=>{const t=e.indexOf(o);t>-1&&e.splice(t,1)};case i:return void e.slice().forEach((e=>{e(o)}));default:throw new Error(`unrecognized action ${t}`)}}}function y(e){let t=e;const o=H();return(e,n)=>{switch(e){case l:n(t);break;case i:t=n;break;case c:return t}return o(e,n)}}function b(e){return f(H(),(t=>C(e,t)))}function R(e,t){return f(y(t),(t=>C(e,t)))}function z(e,...t){const o=function(...e){return t=>e.reduceRight(u,t)}(...t);return(t,n)=>{switch(t){case l:return T(e,o(n));case s:return void w(e)}}}function B(e,t){return e===t}function k(e=B){let t;return o=>n=>{e(t,n)||(t=n,o(n))}}function L(e){return t=>o=>{e(o)&&t(o)}}function P(e){return t=>a(t,e)}function F(e){return t=>()=>t(e)}function O(e,t){return o=>n=>o(t=e(t,n))}function M(e){return t=>o=>{e>0?e--:t(o)}}function W(e){let t,o=null;return n=>r=>{o=r,t||(t=setTimeout((()=>{t=void 0,n(o)}),e))}}function A(e){let t,o;return n=>r=>{t=r,o&&clearTimeout(o),o=setTimeout((()=>{n(t)}),e)}}function V(...e){const t=new Array(e.length);let o=0,n=null;const r=Math.pow(2,e.length)-1;return e.forEach(((e,i)=>{const l=Math.pow(2,i);T(e,(e=>{const s=o;o|=l,t[i]=e,s!==r&&o===r&&n&&(n(),n=null)}))})),e=>i=>{const l=()=>e([i].concat(t));o===r?l():n=l}}function D(...e){return function(t,o){switch(t){case l:return function(...e){return()=>{e.map(g)}}(...e.map((e=>T(e,o))));case s:return;default:throw new Error(`unrecognized action ${t}`)}}}function N(e,t=B){return z(e,k(t))}function G(...e){const t=H(),o=new Array(e.length);let n=0;const r=Math.pow(2,e.length)-1;return e.forEach(((e,i)=>{const l=Math.pow(2,i);T(e,(e=>{o[i]=e,n|=l,n===r&&x(t,o)}))})),function(e,i){switch(e){case l:return n===r&&i(o),T(t,i);case s:return w(t);default:throw new Error(`unrecognized action ${e}`)}}}function _(e,t=[],{singleton:o}={singleton:!0}){return{id:U(),constructor:e,dependencies:t,singleton:o}}const U=()=>Symbol();const $="undefined"!==typeof document?n.useLayoutEffect:n.useEffect;function K(e,t,o){const r=Object.keys(t.required||{}),i=Object.keys(t.optional||{}),c=Object.keys(t.methods||{}),a=Object.keys(t.events||{}),u=n.createContext({});function h(e,o){e.propsReady&&x(e.propsReady,!1);for(const n of r){x(e[t.required[n]],o[n])}for(const n of i)if(n in o){x(e[t.optional[n]],o[n])}e.propsReady&&x(e.propsReady,!0)}function g(e){return a.reduce(((o,n)=>(o[n]=function(e){let t,o;const n=()=>t&&t();return function(r,i){switch(r){case l:if(i){if(o===i)return;return n(),o=i,t=T(e,i),t}return n(),I;case s:return n(),void(o=null);default:throw new Error(`unrecognized action ${r}`)}}}(e[t.events[n]]),o)),{})}const v=n.forwardRef(((l,s)=>{const{children:d,...v}=l,[I]=n.useState((()=>f(function(e){const t=new Map,o=({id:e,constructor:n,dependencies:r,singleton:i})=>{if(i&&t.has(e))return t.get(e);const l=n(r.map((e=>o(e))));return i&&t.set(e,l),l};return o(e)}(e),(e=>h(e,v))))),[S]=n.useState(m(g,I));return $((()=>{for(const e of a)e in v&&T(S[e],v[e]);return()=>{Object.values(S).map(w)}}),[v,S,I]),$((()=>{h(I,v)})),n.useImperativeHandle(s,p(function(e){return c.reduce(((o,n)=>(o[n]=o=>{x(e[t.methods[n]],o)},o)),{})}(I))),n.createElement(u.Provider,{value:I},o?n.createElement(o,function(e,t){const o={},n={};let r=0;const i=e.length;for(;r<i;)n[e[r]]=1,r+=1;for(const l in t)n.hasOwnProperty(l)||(o[l]=t[l]);return o}([...r,...i,...a],v),d):d)}));return{Component:v,usePublisher:e=>n.useCallback(d(x,n.useContext(u)[e]),[e]),useEmitterValue:n.version.startsWith("18")?e=>{const t=n.useContext(u)[e],o=n.useCallback((e=>T(t,e)),[t]);return n.useSyncExternalStore(o,(()=>S(t)),(()=>S(t)))}:e=>{const t=n.useContext(u)[e],[o,r]=n.useState(m(S,t));return $((()=>T(t,(e=>{e!==o&&r(p(e))}))),[t,o]),o},useEmitter:(e,t)=>{const o=n.useContext(u)[e];$((()=>T(o,t)),[t,o])}}}const j="undefined"!==typeof document?n.useLayoutEffect:n.useEffect;var q=(e=>(e[e.DEBUG=0]="DEBUG",e[e.INFO=1]="INFO",e[e.WARN=2]="WARN",e[e.ERROR=3]="ERROR",e))(q||{});const Y={0:"debug",1:"log",2:"warn",3:"error"},Z=_((()=>{const e=y(3);return{log:y(((t,o,n=1)=>{var r;n>=(null!=(r=("undefined"===typeof globalThis?window:globalThis).VIRTUOSO_LOG_LEVEL)?r:S(e))&&console[Y[n]]("%creact-virtuoso: %c%s %o","color: #0253b3; font-weight: bold","color: initial",t,o)})),logLevel:e}}),[],{singleton:!0});function J(e,t=!0){const o=n.useRef(null);let r=e=>{};if("undefined"!==typeof ResizeObserver){const i=n.useMemo((()=>new ResizeObserver((t=>{requestAnimationFrame((()=>{const o=t[0].target;null!==o.offsetParent&&e(o)}))}))),[e]);r=e=>{e&&t?(i.observe(e),o.current=e):(o.current&&i.unobserve(o.current),o.current=null)}}return{ref:o,callbackRef:r}}function Q(e,t=!0){return J(e,t).callbackRef}function X(e,t,o,r,i,l,s){const c=n.useCallback((o=>{const n=function(e,t,o,n){const r=e.length;if(0===r)return null;const i=[];for(let l=0;l<r;l++){const r=e.item(l);if(!r||void 0===r.dataset.index)continue;const s=parseInt(r.dataset.index),c=parseFloat(r.dataset.knownSize),a=t(r,o);if(0===a&&n("Zero-sized element, this should not happen",{child:r},q.ERROR),a===c)continue;const u=i[i.length-1];0===i.length||u.size!==a||u.endIndex!==s-1?i.push({startIndex:s,endIndex:s,size:a}):i[i.length-1].endIndex++}return i}(o.children,t,"offsetHeight",i);let c=o.parentElement;for(;!c.dataset.virtuosoScroller;)c=c.parentElement;const a="window"===c.lastElementChild.dataset.viewportType,u=s?s.scrollTop:a?window.pageYOffset||document.documentElement.scrollTop:c.scrollTop,d=s?s.scrollHeight:a?document.documentElement.scrollHeight:c.scrollHeight,m=s?s.offsetHeight:a?window.innerHeight:c.offsetHeight;r({scrollTop:Math.max(u,0),scrollHeight:d,viewportHeight:m}),null==l||l(function(e,t,o){"normal"===t||(null==t?void 0:t.endsWith("px"))||o(`${e} was not resolved to pixel value correctly`,t,q.WARN);if("normal"===t)return 0;return parseInt(null!=t?t:"0",10)}("row-gap",getComputedStyle(o).rowGap,i)),null!==n&&e(n)}),[e,t,i,l,s,r]);return J(c,o)}function ee(e,t){return Math.round(e.getBoundingClientRect()[t])}function te(e,t){return Math.abs(e-t)<1.01}function oe(e,t,o,i=I,l){const s=n.useRef(null),c=n.useRef(null),a=n.useRef(null),u=n.useCallback((o=>{const n=o.target,i=n===window||n===document,l=i?window.pageYOffset||document.documentElement.scrollTop:n.scrollTop,s=i?document.documentElement.scrollHeight:n.scrollHeight,u=i?window.innerHeight:n.offsetHeight,d=()=>{e({scrollTop:Math.max(l,0),scrollHeight:s,viewportHeight:u})};o.suppressFlushSync?d():r.flushSync(d),null!==c.current&&(l===c.current||l<=0||l===s-u)&&(c.current=null,t(!0),a.current&&(clearTimeout(a.current),a.current=null))}),[e,t]);return n.useEffect((()=>{const e=l||s.current;return i(l||s.current),u({target:e,suppressFlushSync:!0}),e.addEventListener("scroll",u,{passive:!0}),()=>{i(null),e.removeEventListener("scroll",u)}}),[s,u,o,i,l]),{scrollerRef:s,scrollByCallback:function(e){s.current.scrollBy(e)},scrollToCallback:function(o){const n=s.current;if(!n||"offsetHeight"in n&&0===n.offsetHeight)return;const r="smooth"===o.behavior;let i,l,u;n===window?(l=Math.max(ee(document.documentElement,"height"),document.documentElement.scrollHeight),i=window.innerHeight,u=document.documentElement.scrollTop):(l=n.scrollHeight,i=ee(n,"height"),u=n.scrollTop);const d=l-i;if(o.top=Math.ceil(Math.max(Math.min(d,o.top),0)),te(i,l)||o.top===u)return e({scrollTop:u,scrollHeight:l,viewportHeight:i}),void(r&&t(!0));r?(c.current=o.top,a.current&&clearTimeout(a.current),a.current=setTimeout((()=>{a.current=null,c.current=null,t(!0)}),1e3)):c.current=null,n.scrollTo(o)}}}const ne=_((()=>{const e=H(),t=H(),o=y(0),n=H(),r=y(0),i=H(),l=H(),s=y(0),c=y(0),a=y(0),u=y(0),d=H(),m=H(),f=y(!1);return C(z(e,P((({scrollTop:e})=>e))),t),C(z(e,P((({scrollHeight:e})=>e))),l),C(t,r),{scrollContainerState:e,scrollTop:t,viewportHeight:i,headerHeight:s,fixedHeaderHeight:c,fixedFooterHeight:a,footerHeight:u,scrollHeight:l,smoothScrollTargetReached:n,scrollTo:d,scrollBy:m,statefulScrollTop:r,deviation:o,scrollingInProgress:f}}),[],{singleton:!0}),re={lvl:0};function ie(e,t,o,n=re,r=re){return{k:e,v:t,lvl:o,l:n,r:r}}function le(e){return e===re}function se(){return re}function ce(e,t){if(le(e))return re;const{k:o,l:n,r:r}=e;if(t===o){if(le(n))return r;if(le(r))return n;{const[t,o]=he(n);return Te(pe(e,{k:t,v:o,l:ge(n)}))}}return Te(pe(e,t<o?{l:ce(n,t)}:{r:ce(r,t)}))}function ae(e,t){if(!le(e))return t===e.k?e.v:t<e.k?ae(e.l,t):ae(e.r,t)}function ue(e,t,o="k"){if(le(e))return[-1/0,void 0];if(Number(e[o])===t)return[e.k,e.v];if(Number(e[o])<t){const n=ue(e.r,t,o);return n[0]===-1/0?[e.k,e.v]:n}return ue(e.l,t,o)}function de(e,t,o){return le(e)?ie(t,o,1):t===e.k?pe(e,{k:t,v:o}):t<e.k?Ie(pe(e,{l:de(e.l,t,o)})):Ie(pe(e,{r:de(e.r,t,o)}))}function me(e,t,o){if(le(e))return[];const{k:n,v:r,l:i,r:l}=e;let s=[];return n>t&&(s=s.concat(me(i,t,o))),n>=t&&n<=o&&s.push({k:n,v:r}),n<=o&&(s=s.concat(me(l,t,o))),s}function fe(e){return le(e)?[]:[...fe(e.l),{k:e.k,v:e.v},...fe(e.r)]}function he(e){return le(e.r)?[e.k,e.v]:he(e.r)}function ge(e){return le(e.r)?e.l:Te(pe(e,{r:ge(e.r)}))}function pe(e,t){return ie(void 0!==t.k?t.k:e.k,void 0!==t.v?t.v:e.v,void 0!==t.lvl?t.lvl:e.lvl,void 0!==t.l?t.l:e.l,void 0!==t.r?t.r:e.r)}function ve(e){return le(e)||e.lvl>e.r.lvl}function Ie(e){return Se(Ce(e))}function Te(e){const{l:t,r:o,lvl:n}=e;if(o.lvl>=n-1&&t.lvl>=n-1)return e;if(n>o.lvl+1){if(ve(t))return Ce(pe(e,{lvl:n-1}));if(le(t)||le(t.r))throw new Error("Unexpected empty nodes");return pe(t.r,{l:pe(t,{r:t.r.l}),r:pe(e,{l:t.r.r,lvl:n-1}),lvl:n})}if(ve(e))return Se(pe(e,{lvl:n-1}));if(le(o)||le(o.l))throw new Error("Unexpected empty nodes");{const t=o.l,r=ve(t)?o.lvl-1:o.lvl;return pe(t,{l:pe(e,{r:t.l,lvl:n-1}),r:Se(pe(o,{l:t.r,lvl:r})),lvl:t.lvl+1})}}function xe(e,t,o){if(le(e))return[];const n=ue(e,t)[0];return we(me(e,n,o),(({k:e,v:t})=>({index:e,value:t})))}function we(e,t){const o=e.length;if(0===o)return[];let{index:n,value:r}=t(e[0]);const i=[];for(let l=1;l<o;l++){const{index:o,value:s}=t(e[l]);i.push({start:n,end:o-1,value:r}),n=o,r=s}return i.push({start:n,end:1/0,value:r}),i}function Se(e){const{r:t,lvl:o}=e;return le(t)||le(t.r)||t.lvl!==o||t.r.lvl!==o?e:pe(t,{l:pe(e,{r:t.l}),lvl:o+1})}function Ce(e){const{l:t}=e;return le(t)||t.lvl!==e.lvl?e:pe(t,{r:pe(e,{l:t.r})})}function Ee(e,t,o,n=0){let r=e.length-1;for(;n<=r;){const i=Math.floor((n+r)/2),l=o(e[i],t);if(0===l)return i;if(-1===l){if(r-n<2)return i-1;r=i-1}else{if(r===n)return i;n=i+1}}throw new Error(`Failed binary finding record in array - ${e.join(",")}, searched for ${t}`)}function He(e,t,o){return e[Ee(e,t,o)]}const ye=_((()=>({recalcInProgress:y(!1)})),[],{singleton:!0});function be(e){const{size:t,startIndex:o,endIndex:n}=e;return e=>e.start===o&&(e.end===n||e.end===1/0)&&e.value===t}function Re(e,t){let o=0,n=0;for(;o<e;)o+=t[n+1]-t[n]-1,n++;return n-(o===e?0:1)}function ze({index:e},t){return t===e?0:t<e?-1:1}function Be({offset:e},t){return t===e?0:t<e?-1:1}function ke(e){return{index:e.index,value:e}}function Le(e,t,o,n=0){return n>0&&(t=Math.max(t,He(e,n,ze).offset)),we(function(e,t,o,n){const r=Ee(e,t,n),i=Ee(e,o,n,r);return e.slice(r,i+1)}(e,t,o,Be),ke)}function Pe(e,t,o,n){let r=e,i=0,l=0,s=0,c=0;if(0!==t){c=Ee(r,t-1,ze);s=r[c].offset;const e=ue(o,t-1);i=e[0],l=e[1],r.length&&r[c].size===ue(o,t)[1]&&(c-=1),r=r.slice(0,c+1)}else r=[];for(const{start:a,value:u}of xe(o,t,1/0)){const e=a-i,t=e*l+s+e*n;r.push({offset:t,size:u,index:a}),i=a,s=t,l=u}return{offsetTree:r,lastIndex:i,lastOffset:s,lastSize:l}}function Fe(e,[t,o,n,r]){t.length>0&&n("received item sizes",t,q.DEBUG);const i=e.sizeTree;let l=i,s=0;if(o.length>0&&le(i)&&2===t.length){const e=t[0].size,n=t[1].size;l=o.reduce(((t,o)=>de(de(t,o,e),o+1,n)),l)}else[l,s]=function(e,t){let o=le(e)?0:1/0;for(const n of t){const{size:t,startIndex:r,endIndex:i}=n;if(o=Math.min(o,r),le(e)){e=de(e,0,t);continue}const l=xe(e,r-1,i+1);if(l.some(be(n)))continue;let s=!1,c=!1;for(const{start:o,end:n,value:a}of l)s?(i>=o||t===a)&&(e=ce(e,o)):(c=a!==t,s=!0),n>i&&i>=o&&a!==t&&(e=de(e,i+1,a));c&&(e=de(e,r,t))}return[e,o]}(l,t);if(l===i)return e;const{offsetTree:c,lastIndex:a,lastSize:u,lastOffset:d}=Pe(e.offsetTree,s,l,r);return{sizeTree:l,offsetTree:c,lastIndex:a,lastOffset:d,lastSize:u,groupOffsetTree:o.reduce(((e,t)=>de(e,t,Oe(t,c,r))),se()),groupIndices:o}}function Oe(e,t,o){if(0===t.length)return 0;const{offset:n,index:r,size:i}=He(t,e,ze),l=e-r,s=i*l+(l-1)*o+n;return s>0?s+o:s}function Me(e,t,o){if(function(e){return"undefined"!==typeof e.groupIndex}(e))return t.groupIndices[e.groupIndex]+1;{let n=We("LAST"===e.index?o:e.index,t);return n=Math.max(0,n,Math.min(o,n)),n}}function We(e,t){if(!Ae(t))return e;let o=0;for(;t.groupIndices[o]<=e+o;)o++;return e+o}function Ae(e){return!le(e.groupOffsetTree)}const Ve={offsetHeight:"height",offsetWidth:"width"},De=_((([{log:e},{recalcInProgress:t}])=>{const o=H(),n=H(),r=R(n,0),i=H(),l=H(),s=y(0),c=y([]),a=y(void 0),u=y(void 0),d=y(((e,t)=>ee(e,Ve[t]))),m=y(void 0),f=y(0),h={offsetTree:[],sizeTree:se(),groupOffsetTree:se(),lastIndex:0,lastOffset:0,lastSize:0,groupIndices:[]},g=R(z(o,V(c,e,f),O(Fe,h),k()),h),p=R(z(c,k(),O(((e,t)=>({prev:e.current,current:t})),{prev:[],current:[]}),P((({prev:e})=>e))),[]);C(z(c,L((e=>e.length>0)),V(g,f),P((([e,t,o])=>{const n=e.reduce(((e,n,r)=>de(e,n,Oe(n,t.offsetTree,o)||r)),se());return{...t,groupIndices:e,groupOffsetTree:n}}))),g),C(z(n,V(g),L((([e,{lastIndex:t}])=>e<t)),P((([e,{lastIndex:t,lastSize:o}])=>[{startIndex:e,endIndex:t,size:o}]))),o),C(a,u);const v=R(z(a,P((e=>void 0===e))),!0);C(z(u,L((e=>void 0!==e&&le(S(g).sizeTree))),P((e=>[{startIndex:0,endIndex:0,size:e}]))),o);const I=b(z(o,V(g),O((({sizes:e},[t,o])=>({changed:o!==e,sizes:o})),{changed:!1,sizes:h}),P((e=>e.changed))));T(z(s,O(((e,t)=>({diff:e.prev-t,prev:t})),{diff:0,prev:0}),P((e=>e.diff))),(e=>{const{groupIndices:o}=S(g);if(e>0)x(t,!0),x(i,e+Re(e,o));else if(e<0){const t=S(p);t.length>0&&(e-=Re(-e,t)),x(l,e)}})),T(z(s,V(e)),(([e,t])=>{e<0&&t("`firstItemIndex` prop should not be set to less than zero. If you don't know the total count, just use a very high value",{firstItemIndex:s},q.ERROR)}));const w=b(i);C(z(i,V(g),P((([e,t])=>{const o=t.groupIndices.length>0,n=[],r=t.lastSize;if(o){const o=ae(t.sizeTree,0);let i=0,l=0;for(;i<e;){const e=t.groupIndices[l],s=t.groupIndices.length===l+1?1/0:t.groupIndices[l+1]-e-1;n.push({startIndex:e,endIndex:e,size:o}),n.push({startIndex:e+1,endIndex:e+1+s-1,size:r}),l++,i+=s+1}const s=fe(t.sizeTree);return i!==e&&s.shift(),s.reduce(((t,{k:o,v:n})=>{let r=t.ranges;return 0!==t.prevSize&&(r=[...t.ranges,{startIndex:t.prevIndex,endIndex:o+e-1,size:t.prevSize}]),{ranges:r,prevIndex:o+e,prevSize:n}}),{ranges:n,prevIndex:e,prevSize:0}).ranges}return fe(t.sizeTree).reduce(((t,{k:o,v:n})=>({ranges:[...t.ranges,{startIndex:t.prevIndex,endIndex:o+e-1,size:t.prevSize}],prevIndex:o+e,prevSize:n})),{ranges:[],prevIndex:0,prevSize:r}).ranges}))),o);const E=b(z(l,V(g,f),P((([e,{offsetTree:t},o])=>Oe(-e,t,o)))));return C(z(l,V(g,f),P((([e,t,o])=>{if(t.groupIndices.length>0){if(le(t.sizeTree))return t;let n=se();const r=S(p);let i=0,l=0,s=0;for(;i<-e;){s=r[l];const e=r[l+1]-s-1;l++,i+=e+1}n=fe(t.sizeTree).reduce(((t,{k:o,v:n})=>de(t,Math.max(0,o+e),n)),n);if(i!==-e){n=de(n,0,ae(t.sizeTree,s));n=de(n,1,ue(t.sizeTree,1-e)[1])}return{...t,sizeTree:n,...Pe(t.offsetTree,0,n,o)}}{const n=fe(t.sizeTree).reduce(((t,{k:o,v:n})=>de(t,Math.max(0,o+e),n)),se());return{...t,sizeTree:n,...Pe(t.offsetTree,0,n,o)}}}))),g),{data:m,totalCount:n,sizeRanges:o,groupIndices:c,defaultItemSize:u,fixedItemSize:a,unshiftWith:i,shiftWith:l,shiftWithOffset:E,beforeUnshiftWith:w,firstItemIndex:s,gap:f,sizes:g,listRefresh:I,statefulTotalCount:r,trackItemSizes:v,itemSize:d}}),h(Z,ye),{singleton:!0}),Ne="undefined"!==typeof document&&"scrollBehavior"in document.documentElement.style;function Ge(e){const t="number"===typeof e?{index:e}:e;return t.align||(t.align="start"),t.behavior&&Ne||(t.behavior="auto"),t.offset||(t.offset=0),t}const _e=_((([{sizes:e,totalCount:t,listRefresh:o,gap:n},{scrollingInProgress:r,viewportHeight:i,scrollTo:l,smoothScrollTargetReached:s,headerHeight:c,footerHeight:a,fixedHeaderHeight:u,fixedFooterHeight:d},{log:m}])=>{const f=H(),h=H(),g=y(0);let p=null,v=null,I=null;function w(){p&&(p(),p=null),I&&(I(),I=null),v&&(clearTimeout(v),v=null),x(r,!1)}return C(z(f,V(e,i,t,g,c,a,m),V(n,u,d),P((([[e,t,n,i,l,c,a,u],d,m,g])=>{const S=Ge(e),{align:C,behavior:H,offset:y}=S,b=i-1,R=Me(S,t,b);let B=Oe(R,t.offsetTree,d)+c;"end"===C?(B+=m+ue(t.sizeTree,R)[1]-n+g,R===b&&(B+=a)):"center"===C?B+=(m+ue(t.sizeTree,R)[1]-n+g)/2:B-=l,y&&(B+=y);const k=t=>{w(),t?(u("retrying to scroll to",{location:e},q.DEBUG),x(f,e)):(x(h,!0),u("list did not change, scroll successful",{},q.DEBUG))};if(w(),"smooth"===H){let e=!1;I=T(o,(t=>{e=e||t})),p=E(s,(()=>{k(e)}))}else p=E(z(o,(L=150,e=>{const t=setTimeout((()=>{e(!1)}),L);return o=>{o&&(e(!0),clearTimeout(t))}})),k);var L;return v=setTimeout((()=>{w()}),1200),x(r,!0),u("scrolling from index to",{index:R,top:B,behavior:H},q.DEBUG),{top:B,behavior:H}}))),l),{scrollToIndex:f,scrollTargetReached:h,topListHeight:g}}),h(De,ne,Z),{singleton:!0});const Ue="up",$e="down",Ke={atBottom:!1,notAtBottomBecause:"NOT_SHOWING_LAST_ITEM",state:{offsetBottom:0,scrollTop:0,viewportHeight:0,scrollHeight:0}},je=_((([{scrollContainerState:e,scrollTop:t,viewportHeight:o,headerHeight:n,footerHeight:r,scrollBy:i}])=>{const l=y(!1),s=y(!0),c=H(),a=H(),u=y(4),d=y(0),m=R(z(D(z(N(t),M(1),F(!0)),z(N(t),M(1),F(!1),A(100))),k()),!1),f=R(z(D(z(i,F(!0)),z(i,F(!1),A(200))),k()),!1);C(z(G(N(t),N(d)),P((([e,t])=>e<=t)),k()),s),C(z(s,W(50)),a);const h=b(z(G(e,N(o),N(n),N(r),N(u)),O(((e,[{scrollTop:t,scrollHeight:o},n,r,i,l])=>{const s={viewportHeight:n,scrollTop:t,scrollHeight:o};if(t+n-o>-l){let o,n;return t>e.state.scrollTop?(o="SCROLLED_DOWN",n=e.state.scrollTop-t):(o="SIZE_DECREASED",n=e.state.scrollTop-t||e.scrollTopDelta),{atBottom:!0,state:s,atBottomBecause:o,scrollTopDelta:n}}let c;return c=s.scrollHeight>e.state.scrollHeight?"SIZE_INCREASED":n<e.state.viewportHeight?"VIEWPORT_HEIGHT_DECREASING":t<e.state.scrollTop?"SCROLLING_UPWARDS":"NOT_FULLY_SCROLLED_TO_LAST_ITEM_BOTTOM",{atBottom:!1,notAtBottomBecause:c,state:s}}),Ke),k(((e,t)=>e&&e.atBottom===t.atBottom)))),g=R(z(e,O(((e,{scrollTop:t,scrollHeight:o,viewportHeight:n})=>{if(te(e.scrollHeight,o))return{scrollTop:t,scrollHeight:o,jump:0,changed:!1};{const r=o-(t+n)<1;return e.scrollTop!==t&&r?{scrollHeight:o,scrollTop:t,jump:e.scrollTop-t,changed:!0}:{scrollHeight:o,scrollTop:t,jump:0,changed:!0}}}),{scrollHeight:0,jump:0,scrollTop:0,changed:!1}),L((e=>e.changed)),P((e=>e.jump))),0);C(z(h,P((e=>e.atBottom))),l),C(z(l,W(50)),c);const p=y($e);C(z(e,P((({scrollTop:e})=>e)),k(),O(((e,t)=>S(f)?{direction:e.direction,prevScrollTop:t}:{direction:t<e.prevScrollTop?Ue:$e,prevScrollTop:t}),{direction:$e,prevScrollTop:0}),P((e=>e.direction))),p),C(z(e,W(50),F("none")),p);const v=y(0);return C(z(m,L((e=>!e)),F(0)),v),C(z(t,W(100),V(m),L((([e,t])=>!!t)),O((([e,t],[o])=>[t,o]),[0,0]),P((([e,t])=>t-e))),v),{isScrolling:m,isAtTop:s,isAtBottom:l,atBottomState:h,atTopStateChange:a,atBottomStateChange:c,scrollDirection:p,atBottomThreshold:u,atTopThreshold:d,scrollVelocity:v,lastJumpDueToItemResize:g}}),h(ne)),qe=_((([{log:e}])=>{const t=y(!1),o=b(z(t,L((e=>e)),k()));return T(t,(t=>{t&&S(e)("props updated",{},q.DEBUG)})),{propsReady:t,didMount:o}}),h(Z),{singleton:!0});function Ye(e,t){0==e?t():requestAnimationFrame((()=>Ye(e-1,t)))}function Ze(e,t){const o=t-1;return"number"===typeof e?e:"LAST"===e.index?o:e.index}const Je=_((([{sizes:e,listRefresh:t,defaultItemSize:o},{scrollTop:n},{scrollToIndex:r,scrollTargetReached:i},{didMount:l}])=>{const s=y(!0),c=y(0),a=y(!0);return C(z(l,V(c),L((([e,t])=>!!t)),F(!1)),s),C(z(l,V(c),L((([e,t])=>!!t)),F(!1)),a),T(z(G(t,l),V(s,e,o,a),L((([[,e],t,{sizeTree:o},n,r])=>e&&(!le(o)||v(n))&&!t&&!r)),V(c)),(([,e])=>{E(i,(()=>{x(a,!0)})),Ye(4,(()=>{E(n,(()=>{x(s,!0)})),x(r,e)}))})),{scrolledToInitialItem:s,initialTopMostItemIndex:c,initialItemFinalLocationReached:a}}),h(De,ne,_e,qe),{singleton:!0});function Qe(e){return!!e&&("smooth"===e?"smooth":"auto")}const Xe=_((([{totalCount:e,listRefresh:t},{isAtBottom:o,atBottomState:n},{scrollToIndex:r},{scrolledToInitialItem:i},{propsReady:l,didMount:s},{log:c},{scrollingInProgress:a}])=>{const u=y(!1),d=H();let m=null;function f(e){x(r,{index:"LAST",align:"end",behavior:e})}function h(e){const t=E(n,(t=>{!e||t.atBottom||"SIZE_INCREASED"!==t.notAtBottomBecause||m||(S(c)("scrolling to bottom due to increased size",{},q.DEBUG),f("auto"))}));setTimeout(t,100)}return T(z(G(z(N(e),M(1)),s),V(N(u),o,i,a),P((([[e,t],o,n,r,i])=>{let l=t&&r,s="auto";return l&&(s=((e,t)=>"function"===typeof e?Qe(e(t)):t&&Qe(e))(o,n||i),l=l&&!!s),{totalCount:e,shouldFollow:l,followOutputBehavior:s}})),L((({shouldFollow:e})=>e))),(({totalCount:e,followOutputBehavior:o})=>{m&&(m(),m=null),m=E(t,(()=>{S(c)("following output to ",{totalCount:e},q.DEBUG),f(o),m=null}))})),T(z(G(N(u),e,l),L((([e,,t])=>e&&t)),O((({value:e},[,t])=>({refreshed:e===t,value:t})),{refreshed:!1,value:0}),L((({refreshed:e})=>e)),V(u,e)),(([,e])=>{S(i)&&h(!1!==e)})),T(d,(()=>{h(!1!==S(u))})),T(G(N(u),n),(([e,t])=>{e&&!t.atBottom&&"VIEWPORT_HEIGHT_DECREASING"===t.notAtBottomBecause&&f("auto")})),{followOutput:u,autoscrollToBottom:d}}),h(De,je,_e,Je,qe,Z,ne));function et(e){return e.reduce(((e,t)=>(e.groupIndices.push(e.totalCount),e.totalCount+=t+1,e)),{totalCount:0,groupIndices:[]})}const tt=_((([{totalCount:e,groupIndices:t,sizes:o},{scrollTop:n,headerHeight:r}])=>{const i=H(),l=H(),s=b(z(i,P(et)));return C(z(s,P((e=>e.totalCount))),e),C(z(s,P((e=>e.groupIndices))),t),C(z(G(n,o,r),L((([e,t])=>Ae(t))),P((([e,t,o])=>ue(t.groupOffsetTree,Math.max(e-o,0),"v")[0])),k(),P((e=>[e]))),l),{groupCounts:i,topItemsIndexes:l}}),h(De,ne));function ot(e,t){return!(!e||e[0]!==t[0]||e[1]!==t[1])}function nt(e,t){return!(!e||e.startIndex!==t.startIndex||e.endIndex!==t.endIndex)}const rt="top",it="bottom",lt="none";function st(e,t,o){return"number"===typeof e?o===Ue&&t===rt||o===$e&&t===it?e:0:o===Ue?t===rt?e.main:e.reverse:t===it?e.main:e.reverse}function ct(e,t){return"number"===typeof e?e:e[t]||0}const at=_((([{scrollTop:e,viewportHeight:t,deviation:o,headerHeight:n,fixedHeaderHeight:r}])=>{const i=H(),l=y(0),s=y(0),c=y(0);return{listBoundary:i,overscan:c,topListHeight:l,increaseViewportBy:s,visibleRange:R(z(G(N(e),N(t),N(n),N(i,ot),N(c),N(l),N(r),N(o),N(s)),P((([e,t,o,[n,r],i,l,s,c,a])=>{const u=e-c,d=l+s,m=Math.max(o-u,0);let f=lt;const h=ct(a,rt),g=ct(a,it);return n-=c,r+=o+s,(n+=o+s)>e+d-h&&(f=Ue),(r-=c)<e-m+t+g&&(f=$e),f!==lt?[Math.max(u-o-st(i,rt,f)-h,0),u-m-s+t+st(i,it,f)+g]:null})),L((e=>null!=e)),k(ot)),[0,0])}}),h(ne),{singleton:!0});const ut={items:[],topItems:[],offsetTop:0,offsetBottom:0,top:0,bottom:0,topListHeight:0,totalCount:0,firstItemIndex:0};function dt(e,t,o){if(0===e.length)return[];if(!Ae(t))return e.map((e=>({...e,index:e.index+o,originalIndex:e.index})));const n=e[0].index,r=e[e.length-1].index,i=[],l=xe(t.groupOffsetTree,n,r);let s,c=0;for(const a of e){let e;(!s||s.end<a.index)&&(s=l.shift(),c=t.groupIndices.indexOf(s.start)),e=a.index===s.start?{type:"group",index:c}:{index:a.index-(c+1)+o,groupIndex:c},i.push({...e,size:a.size,offset:a.offset,originalIndex:a.index,data:a.data})}return i}function mt(e,t,o,n,r,i){const{lastSize:l,lastOffset:s,lastIndex:c}=r;let a=0,u=0;if(e.length>0){a=e[0].offset;const t=e[e.length-1];u=t.offset+t.size}const d=o-c,m=a,f=s+d*l+(d-1)*n-u;return{items:dt(e,r,i),topItems:dt(t,r,i),topListHeight:t.reduce(((e,t)=>t.size+e),0),offsetTop:a,offsetBottom:f,top:m,bottom:u,totalCount:o,firstItemIndex:i}}function ft(e,t,o,n,r,i){let l=0;if(o.groupIndices.length>0)for(const a of o.groupIndices){if(a-l>=e)break;l++}const s=e+l,c=Ze(t,s);return mt(Array.from({length:s}).map(((e,t)=>({index:t+c,size:0,offset:0,data:i[t+c]}))),[],s,r,o,n)}const ht=_((([{sizes:e,totalCount:t,data:o,firstItemIndex:n,gap:r},i,{visibleRange:l,listBoundary:s,topListHeight:c},{scrolledToInitialItem:a,initialTopMostItemIndex:u},{topListHeight:d},m,{didMount:h},{recalcInProgress:g}])=>{const p=y([]),I=y(0),T=H();C(i.topItemsIndexes,p);const x=R(z(G(h,g,N(l,ot),N(t),N(e),N(u),a,N(p),N(n),N(r),o),L((([e,t,,o,,,,,,,n])=>{const r=n&&n.length!==o;return e&&!t&&!r})),P((([,,[e,t],o,n,r,i,l,s,c,a])=>{const u=n,{sizeTree:d,offsetTree:m}=u,h=S(I);if(0===o)return{...ut,totalCount:o};if(0===e&&0===t)return 0===h?{...ut,totalCount:o}:ft(h,r,n,s,c,a||[]);if(le(d)){if(h>0)return null;const e=mt(function(e,t,o){if(Ae(t)){const n=We(e,t);return[{index:ue(t.groupOffsetTree,n)[0],size:0,offset:0},{index:n,size:0,offset:0,data:o&&o[0]}]}return[{index:e,size:0,offset:0,data:o&&o[0]}]}(Ze(r,o),u,a),[],o,c,u,s);return e}const g=[];if(l.length>0){const e=l[0],t=l[l.length-1];let o=0;for(const n of xe(d,e,t)){const r=n.value,i=Math.max(n.start,e),l=Math.min(n.end,t);for(let e=i;e<=l;e++)g.push({index:e,size:r,offset:o,data:a&&a[e]}),o+=r}}if(!i)return mt([],g,o,c,u,s);const p=l.length>0?l[l.length-1]+1:0,v=Le(m,e,t,p);if(0===v.length)return null;const T=o-1;return mt(f([],(o=>{for(const n of v){const r=n.value;let i=r.offset,l=n.start;const s=r.size;if(r.offset<e){l+=Math.floor((e-r.offset+c)/(s+c));const t=l-n.start;i+=t*s+t*c}l<p&&(i+=(p-l)*s,l=p);const u=Math.min(n.end,T);for(let e=l;e<=u&&!(i>=t);e++)o.push({index:e,size:s,offset:i,data:a&&a[e]}),i+=s+c}})),g,o,c,u,s)})),L((e=>null!==e)),k()),ut);C(z(o,L(v),P((e=>null==e?void 0:e.length))),t),C(z(x,P((e=>e.topListHeight))),d),C(d,c),C(z(x,P((e=>[e.top,e.bottom]))),s),C(z(x,P((e=>e.items))),T);return{listState:x,topItemsIndexes:p,endReached:b(z(x,L((({items:e})=>e.length>0)),V(t,o),L((([{items:e},t])=>e[e.length-1].originalIndex===t-1)),P((([,e,t])=>[e-1,t])),k(ot),P((([e])=>e)))),startReached:b(z(x,W(200),L((({items:e,topItems:t})=>e.length>0&&e[0].originalIndex===t.length)),P((({items:e})=>e[0].index)),k())),rangeChanged:b(z(x,L((({items:e})=>e.length>0)),P((({items:e})=>{let t=0,o=e.length-1;for(;"group"===e[t].type&&t<o;)t++;for(;"group"===e[o].type&&o>t;)o--;return{startIndex:e[t].index,endIndex:e[o].index}})),k(nt))),itemsRendered:T,initialItemCount:I,...m}}),h(De,tt,at,Je,_e,je,qe,ye),{singleton:!0}),gt=_((([{sizes:e,firstItemIndex:t,data:o,gap:n},{initialTopMostItemIndex:r},{initialItemCount:i,listState:l},{didMount:s}])=>(C(z(s,V(i),L((([,e])=>0!==e)),V(r,e,t,n,o),P((([[,e],t,o,n,r,i=[]])=>ft(e,t,o,n,r,i)))),l),{})),h(De,Je,ht,qe),{singleton:!0}),pt=_((([{scrollVelocity:e}])=>{const t=y(!1),o=H(),n=y(!1);return C(z(e,V(n,t,o),L((([e,t])=>!!t)),P((([e,t,o,n])=>{const{exit:r,enter:i}=t;if(o){if(r(e,n))return!1}else if(i(e,n))return!0;return o})),k()),t),T(z(G(t,e,o),V(n)),(([[e,t,o],n])=>e&&n&&n.change&&n.change(t,o))),{isSeeking:t,scrollSeekConfiguration:n,scrollVelocity:e,scrollSeekRangeChanged:o}}),h(je),{singleton:!0}),vt=_((([{topItemsIndexes:e}])=>{const t=y(0);return C(z(t,L((e=>e>0)),P((e=>Array.from({length:e}).map(((e,t)=>t))))),e),{topItemCount:t}}),h(ht)),It=_((([{footerHeight:e,headerHeight:t,fixedHeaderHeight:o,fixedFooterHeight:n},{listState:r}])=>{const i=H(),l=R(z(G(e,n,t,o,r),P((([e,t,o,n,r])=>e+t+o+n+r.offsetBottom+r.bottom))),0);return C(N(l),i),{totalListHeight:l,totalListHeightChanged:i}}),h(ne,ht),{singleton:!0});function Tt(e){let t,o=!1;return()=>(o||(o=!0,t=e()),t)}const xt=Tt((()=>/iP(ad|od|hone)/i.test(navigator.userAgent)&&/WebKit/i.test(navigator.userAgent))),wt=_((([{scrollBy:e,scrollTop:t,deviation:o,scrollingInProgress:n},{isScrolling:r,isAtBottom:i,scrollDirection:l,lastJumpDueToItemResize:s},{listState:c},{beforeUnshiftWith:a,shiftWithOffset:u,sizes:d,gap:m},{log:f},{recalcInProgress:h}])=>{const g=b(z(c,V(s),O((([,e,t,o],[{items:n,totalCount:r,bottom:i,offsetBottom:l},s])=>{const c=i+l;let a=0;if(t===r&&e.length>0&&n.length>0){0===n[0].originalIndex&&0===e[0].originalIndex||(a=c-o,0!==a&&(a+=s))}return[a,n,r,c]}),[0,[],0,0]),L((([e])=>0!==e)),V(t,l,n,i,f,h),L((([,e,t,o,,,n])=>!n&&!o&&0!==e&&t===Ue)),P((([[e],,,,,t])=>(t("Upward scrolling compensation",{amount:e},q.DEBUG),e)))));function p(t){t>0?(x(e,{top:-t,behavior:"auto"}),x(o,0)):(x(o,0),x(e,{top:-t,behavior:"auto"}))}return T(z(g,V(o,r)),(([e,t,n])=>{n&&xt()?x(o,t-e):p(-e)})),T(z(G(R(r,!1),o,h),L((([e,t,o])=>!e&&!o&&0!==t)),P((([e,t])=>t)),W(1)),p),C(z(u,P((e=>({top:-e})))),e),T(z(a,V(d,m),P((([e,{lastSize:t,groupIndices:o,sizeTree:n},r])=>{function i(e){return e*(t+r)}if(0===o.length)return i(e);{let t=0;const r=ae(n,0);let l=0,s=0;for(;l<e;){l++,t+=r;let n=o.length===s+1?1/0:o[s+1]-o[s]-1;l+n>e&&(t-=r,n=e-l+1),l+=n,t+=i(n),s++}return t}}))),(t=>{x(o,t),requestAnimationFrame((()=>{x(e,{top:t}),requestAnimationFrame((()=>{x(o,0),x(h,!1)}))}))})),{deviation:o}}),h(ne,je,ht,De,Z,ye)),St=_((([{didMount:e},{scrollTo:t},{listState:o}])=>{const n=y(0);return T(z(e,V(n),L((([,e])=>0!==e)),P((([,e])=>({top:e})))),(e=>{E(z(o,M(1),L((e=>e.items.length>1))),(()=>{requestAnimationFrame((()=>{x(t,e)}))}))})),{initialScrollTop:n}}),h(qe,ne,ht),{singleton:!0}),Ct=_((([{viewportHeight:e},{totalListHeight:t}])=>{const o=y(!1);return{alignToBottom:o,paddingTopAddition:R(z(G(o,e,t),L((([e])=>e)),P((([,e,t])=>Math.max(0,e-t))),W(0),k()),0)}}),h(ne,It),{singleton:!0}),Et=_((([{scrollTo:e,scrollContainerState:t}])=>{const o=H(),n=H(),r=H(),i=y(!1),l=y(void 0);return C(z(G(o,n),P((([{viewportHeight:e,scrollTop:t,scrollHeight:o},{offsetTop:n}])=>({scrollTop:Math.max(0,t-n),scrollHeight:o,viewportHeight:e})))),t),C(z(e,V(n),P((([e,{offsetTop:t}])=>({...e,top:e.top+t})))),r),{useWindowScroll:i,customScrollParent:l,windowScrollContainerState:o,windowViewportRect:n,windowScrollTo:r}}),h(ne)),Ht=({itemTop:e,itemBottom:t,viewportTop:o,viewportBottom:n,locationParams:{behavior:r,align:i,...l}})=>e<o?{...l,behavior:r,align:null!=i?i:"start"}:t>n?{...l,behavior:r,align:null!=i?i:"end"}:null,yt=_((([{sizes:e,totalCount:t,gap:o},{scrollTop:n,viewportHeight:r,headerHeight:i,fixedHeaderHeight:l,fixedFooterHeight:s,scrollingInProgress:c},{scrollToIndex:a}])=>{const u=H();return C(z(u,V(e,r,t,i,l,s,n),V(o),P((([[e,t,o,n,r,i,l,s],a])=>{const{done:u,behavior:d,align:m,calculateViewLocation:f=Ht,...h}=e,g=Me(e,t,n-1),p=Oe(g,t.offsetTree,a)+r+i,v=f({itemTop:p,itemBottom:p+ue(t.sizeTree,g)[1],viewportTop:s+i,viewportBottom:s+o-l,locationParams:{behavior:d,align:m,...h}});return v?u&&E(z(c,L((e=>!1===e)),M(S(c)?1:2)),u):u&&u(),v})),L((e=>null!==e))),a),{scrollIntoView:u}}),h(De,ne,_e,ht,Z),{singleton:!0}),bt=_((([{sizes:e,sizeRanges:t},{scrollTop:o},{initialTopMostItemIndex:n},{didMount:r},{useWindowScroll:i,windowScrollContainerState:l,windowViewportRect:s}])=>{const c=H(),a=y(void 0),u=y(null),d=y(null);return C(l,u),C(s,d),T(z(c,V(e,o,i,u,d)),(([e,t,o,n,r,i])=>{const l=fe(t.sizeTree).map((({k:e,v:t},o,n)=>{const r=n[o+1];return{startIndex:e,endIndex:r?r.k-1:1/0,size:t}}));n&&null!==r&&null!==i&&(o=r.scrollTop-i.offsetTop),e({ranges:l,scrollTop:o})})),C(z(a,L(v),P(Rt)),n),C(z(r,V(a),L((([,e])=>void 0!==e)),k(),P((([,e])=>e.ranges))),t),{getState:c,restoreStateFrom:a}}),h(De,ne,Je,qe,Et));function Rt(e){return{offset:e.scrollTop,index:0,align:"start"}}const zt=_((([e,t,o,n,r,i,l,s,c,a])=>({...e,...t,...o,...n,...r,...i,...l,...s,...c,...a})),h(at,gt,qe,pt,It,St,Ct,Et,yt,Z)),Bt=_((([{totalCount:e,sizeRanges:t,fixedItemSize:o,defaultItemSize:n,trackItemSizes:r,itemSize:i,data:l,firstItemIndex:s,groupIndices:c,statefulTotalCount:a,gap:u,sizes:d},{initialTopMostItemIndex:m,scrolledToInitialItem:f,initialItemFinalLocationReached:h},g,p,v,{listState:I,topItemsIndexes:T,...x},{scrollToIndex:w},S,{topItemCount:E},{groupCounts:H},y])=>(C(x.rangeChanged,y.scrollSeekRangeChanged),C(z(y.windowViewportRect,P((e=>e.visibleHeight))),g.viewportHeight),{totalCount:e,data:l,firstItemIndex:s,sizeRanges:t,initialTopMostItemIndex:m,scrolledToInitialItem:f,initialItemFinalLocationReached:h,topItemsIndexes:T,topItemCount:E,groupCounts:H,fixedItemHeight:o,defaultItemHeight:n,gap:u,...v,statefulTotalCount:a,listState:I,scrollToIndex:w,trackItemSizes:r,itemSize:i,groupIndices:c,...x,...y,...g,sizes:d,...p})),h(De,Je,ne,bt,Xe,ht,_e,wt,vt,tt,zt)),kt="-webkit-sticky",Lt="sticky",Pt=Tt((()=>{if("undefined"===typeof document)return Lt;const e=document.createElement("div");return e.style.position=kt,e.style.position===kt?kt:Lt}));function Ft(e,t){const o=n.useRef(null),r=n.useCallback((n=>{if(null===n||!n.offsetParent)return;const r=n.getBoundingClientRect(),i=r.width;let l,s;if(t){const e=t.getBoundingClientRect(),o=r.top-e.top;l=e.height-Math.max(0,o),s=o+t.scrollTop}else l=window.innerHeight-Math.max(0,r.top),s=r.top+window.pageYOffset;o.current={offsetTop:s,visibleHeight:l,visibleWidth:i},e(o.current)}),[e,t]),{callbackRef:i,ref:l}=J(r),s=n.useCallback((()=>{r(l.current)}),[r,l]);return n.useEffect((()=>{if(t){t.addEventListener("scroll",s);const e=new ResizeObserver((()=>{requestAnimationFrame(s)}));return e.observe(t),()=>{t.removeEventListener("scroll",s),e.unobserve(t)}}return window.addEventListener("scroll",s),window.addEventListener("resize",s),()=>{window.removeEventListener("scroll",s),window.removeEventListener("resize",s)}}),[s,t]),i}const Ot=n.createContext(void 0),Mt=n.createContext(void 0);function Wt(e){return e}const At=_((([e,t])=>({...e,...t})),h(Bt,_((()=>{const e=y((e=>`Item ${e}`)),t=y(null),o=y((e=>`Group ${e}`)),n=y({}),r=y(Wt),i=y("div"),l=y(I),s=(e,t=null)=>R(z(n,P((t=>t[e])),k()),t);return{context:t,itemContent:e,groupContent:o,components:n,computeItemKey:r,headerFooterTag:i,scrollerRef:l,FooterComponent:s("Footer"),HeaderComponent:s("Header"),TopItemListComponent:s("TopItemList"),ListComponent:s("List","div"),ItemComponent:s("Item","div"),GroupComponent:s("Group","div"),ScrollerComponent:s("Scroller","div"),EmptyPlaceholder:s("EmptyPlaceholder"),ScrollSeekPlaceholder:s("ScrollSeekPlaceholder")}})))),Vt=({height:e})=>n.createElement("div",{style:{height:e}}),Dt={position:Pt(),zIndex:1,overflowAnchor:"none"},Nt={overflowAnchor:"none"},Gt=n.memo((function({showTopList:e=!1}){const t=ro("listState"),o=no("sizeRanges"),r=ro("useWindowScroll"),i=ro("customScrollParent"),l=no("windowScrollContainerState"),s=no("scrollContainerState"),c=i||r?l:s,a=ro("itemContent"),u=ro("context"),d=ro("groupContent"),m=ro("trackItemSizes"),f=ro("itemSize"),h=ro("log"),g=no("gap"),{callbackRef:p}=X(o,f,m,e?I:c,h,g,i),[v,T]=n.useState(0);io("deviation",(e=>{v!==e&&T(e)}));const x=ro("EmptyPlaceholder"),w=ro("ScrollSeekPlaceholder")||Vt,S=ro("ListComponent"),C=ro("ItemComponent"),E=ro("GroupComponent"),H=ro("computeItemKey"),y=ro("isSeeking"),b=ro("groupIndices").length>0,R=ro("alignToBottom"),z=ro("initialItemFinalLocationReached"),B=e?{}:{boxSizing:"border-box",paddingTop:t.offsetTop,paddingBottom:t.offsetBottom,marginTop:0!==v?v:R?"auto":0,...z?{}:{visibility:"hidden"}};return!e&&0===t.totalCount&&x?n.createElement(x,Kt(x,u)):n.createElement(S,{...Kt(S,u),ref:p,style:B,"data-testid":e?"virtuoso-top-item-list":"virtuoso-item-list"},(e?t.topItems:t.items).map((e=>{const o=e.originalIndex,r=H(o+t.firstItemIndex,e.data,u);return y?n.createElement(w,{...Kt(w,u),key:r,index:e.index,height:e.size,type:e.type||"item",..."group"===e.type?{}:{groupIndex:e.groupIndex}}):"group"===e.type?n.createElement(E,{...Kt(E,u),key:r,"data-index":o,"data-known-size":e.size,"data-item-index":e.index,style:Dt},d(e.index,u)):n.createElement(C,{...Kt(C,u),...jt(C,e.data),key:r,"data-index":o,"data-known-size":e.size,"data-item-index":e.index,"data-item-group-index":e.groupIndex,style:Nt},b?a(e.index,e.groupIndex,e.data,u):a(e.index,e.data,u))})))})),_t={height:"100%",outline:"none",overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},Ut=e=>({width:"100%",height:"100%",position:"absolute",top:0,...e?{display:"flex",flexDirection:"column"}:{}}),$t={width:"100%",position:Pt(),top:0,zIndex:1};function Kt(e,t){if("string"!==typeof e)return{context:t}}function jt(e,t){return{item:"string"===typeof e?void 0:t}}const qt=n.memo((function(){const e=ro("HeaderComponent"),t=no("headerHeight"),o=ro("headerFooterTag"),r=Q((e=>t(ee(e,"height")))),i=ro("context");return e?n.createElement(o,{ref:r},n.createElement(e,Kt(e,i))):null})),Yt=n.memo((function(){const e=ro("FooterComponent"),t=no("footerHeight"),o=ro("headerFooterTag"),r=Q((e=>t(ee(e,"height")))),i=ro("context");return e?n.createElement(o,{ref:r},n.createElement(e,Kt(e,i))):null}));function Zt({usePublisher:e,useEmitter:t,useEmitterValue:o}){return n.memo((function({style:r,children:i,...l}){const s=e("scrollContainerState"),c=o("ScrollerComponent"),a=e("smoothScrollTargetReached"),u=o("scrollerRef"),d=o("context"),{scrollerRef:m,scrollByCallback:f,scrollToCallback:h}=oe(s,a,c,u);return t("scrollTo",h),t("scrollBy",f),n.createElement(c,{ref:m,style:{..._t,...r},"data-testid":"virtuoso-scroller","data-virtuoso-scroller":!0,tabIndex:0,...l,...Kt(c,d)},i)}))}function Jt({usePublisher:e,useEmitter:t,useEmitterValue:o}){return n.memo((function({style:r,children:i,...l}){const s=e("windowScrollContainerState"),c=o("ScrollerComponent"),a=e("smoothScrollTargetReached"),u=o("totalListHeight"),d=o("deviation"),m=o("customScrollParent"),f=o("context"),{scrollerRef:h,scrollByCallback:g,scrollToCallback:p}=oe(s,a,c,I,m);return j((()=>(h.current=m||window,()=>{h.current=null})),[h,m]),t("windowScrollTo",p),t("scrollBy",g),n.createElement(c,{style:{position:"relative",...r,...0!==u?{height:u+d}:{}},"data-virtuoso-scroller":!0,...l,...Kt(c,f)},i)}))}const Qt=({children:e})=>{const t=n.useContext(Ot),o=no("viewportHeight"),r=no("fixedItemHeight"),i=ro("alignToBottom"),l=Q(a(o,(e=>ee(e,"height"))));return n.useEffect((()=>{t&&(o(t.viewportHeight),r(t.itemHeight))}),[t,o,r]),n.createElement("div",{style:Ut(i),ref:l,"data-viewport-type":"element"},e)},Xt=({children:e})=>{const t=n.useContext(Ot),o=no("windowViewportRect"),r=no("fixedItemHeight"),i=ro("customScrollParent"),l=Ft(o,i),s=ro("alignToBottom");return n.useEffect((()=>{t&&(r(t.itemHeight),o({offsetTop:0,visibleHeight:t.viewportHeight,visibleWidth:100}))}),[t,o,r]),n.createElement("div",{ref:l,style:Ut(s),"data-viewport-type":"window"},e)},eo=({children:e})=>{const t=ro("TopItemListComponent")||"div",o=ro("headerHeight"),r={...$t,marginTop:`${o}px`},i=ro("context");return n.createElement(t,{style:r,...Kt(t,i)},e)},to=n.memo((function(e){const t=ro("useWindowScroll"),o=ro("topItemsIndexes").length>0,r=ro("customScrollParent"),i=r||t?so:lo,l=r||t?Xt:Qt;return n.createElement(i,{...e},o&&n.createElement(eo,null,n.createElement(Gt,{showTopList:!0})),n.createElement(l,null,n.createElement(qt,null),n.createElement(Gt,null),n.createElement(Yt,null)))})),{Component:oo,usePublisher:no,useEmitterValue:ro,useEmitter:io}=K(At,{required:{},optional:{restoreStateFrom:"restoreStateFrom",context:"context",followOutput:"followOutput",itemContent:"itemContent",groupContent:"groupContent",overscan:"overscan",increaseViewportBy:"increaseViewportBy",totalCount:"totalCount",groupCounts:"groupCounts",topItemCount:"topItemCount",firstItemIndex:"firstItemIndex",initialTopMostItemIndex:"initialTopMostItemIndex",components:"components",atBottomThreshold:"atBottomThreshold",atTopThreshold:"atTopThreshold",computeItemKey:"computeItemKey",defaultItemHeight:"defaultItemHeight",fixedItemHeight:"fixedItemHeight",itemSize:"itemSize",scrollSeekConfiguration:"scrollSeekConfiguration",headerFooterTag:"headerFooterTag",data:"data",initialItemCount:"initialItemCount",initialScrollTop:"initialScrollTop",alignToBottom:"alignToBottom",useWindowScroll:"useWindowScroll",customScrollParent:"customScrollParent",scrollerRef:"scrollerRef",logLevel:"logLevel"},methods:{scrollToIndex:"scrollToIndex",scrollIntoView:"scrollIntoView",scrollTo:"scrollTo",scrollBy:"scrollBy",autoscrollToBottom:"autoscrollToBottom",getState:"getState"},events:{isScrolling:"isScrolling",endReached:"endReached",startReached:"startReached",rangeChanged:"rangeChanged",atBottomStateChange:"atBottomStateChange",atTopStateChange:"atTopStateChange",totalListHeightChanged:"totalListHeightChanged",itemsRendered:"itemsRendered",groupIndices:"groupIndices"}},to),lo=Zt({usePublisher:no,useEmitterValue:ro,useEmitter:io}),so=Jt({usePublisher:no,useEmitterValue:ro,useEmitter:io}),co=oo,ao={items:[],offsetBottom:0,offsetTop:0,top:0,bottom:0,itemHeight:0,itemWidth:0},uo={items:[{index:0}],offsetBottom:0,offsetTop:0,top:0,bottom:0,itemHeight:0,itemWidth:0},{round:mo,ceil:fo,floor:ho,min:go,max:po}=Math;function vo(e,t,o){return Array.from({length:t-e+1}).map(((t,n)=>{const r=null===o?null:o[n+e];return{index:n+e,data:r}}))}function Io(e,t){return e&&e.column===t.column&&e.row===t.row}function To(e,t){return e&&e.width===t.width&&e.height===t.height}const xo=_((([{overscan:e,visibleRange:t,listBoundary:o},{scrollTop:n,viewportHeight:r,scrollBy:i,scrollTo:l,smoothScrollTargetReached:s,scrollContainerState:c,footerHeight:a,headerHeight:u},d,m,{propsReady:f,didMount:h},{windowViewportRect:g,useWindowScroll:p,customScrollParent:v,windowScrollContainerState:I,windowScrollTo:w},S])=>{const B=y(0),O=y(0),A=y(ao),D=y({height:0,width:0}),_=y({height:0,width:0}),U=H(),$=H(),K=y(0),j=y(null),q=y({row:0,column:0}),Y=H(),Z=H(),J=y(!1),Q=y(0),X=y(!0),ee=y(!1);T(z(h,V(Q),L((([e,t])=>!!t))),(()=>{x(X,!1),x(O,0)})),T(z(G(h,X,_,D,Q,ee),L((([e,t,o,n,,r])=>e&&!t&&0!==o.height&&0!==n.height&&!r))),(([,,,,e])=>{x(ee,!0),Ye(1,(()=>{x(U,e)})),E(z(n),(()=>{x(o,[0,0]),x(X,!0)}))})),C(z(Z,L((e=>void 0!==e&&null!==e&&e.scrollTop>0)),F(0)),O),T(z(h,V(Z),L((([,e])=>void 0!==e&&null!==e))),(([,e])=>{e&&(x(D,e.viewport),x(_,null==e?void 0:e.item),x(q,e.gap),e.scrollTop>0&&(x(J,!0),E(z(n,M(1)),(e=>{x(J,!1)})),x(l,{top:e.scrollTop})))})),C(z(D,P((({height:e})=>e))),r),C(z(G(N(D,To),N(_,To),N(q,((e,t)=>e&&e.column===t.column&&e.row===t.row)),N(n)),P((([e,t,o,n])=>({viewport:e,item:t,gap:o,scrollTop:n})))),Y),C(z(G(N(B),t,N(q,Io),N(_,To),N(D,To),N(j),N(O),N(J),N(X),N(Q)),L((([,,,,,,,e])=>!e)),P((([e,[t,o],n,r,i,l,s,,c,a])=>{const{row:u,column:d}=n,{height:m,width:f}=r,{width:h}=i;if(0===s&&(0===e||0===h))return ao;if(0===f){const t=Ze(a,e);return function(e){return{...uo,items:e}}(vo(t,0===t?Math.max(s-1,0):t,l))}const g=Co(h,f,d);let p,v;c?0===t&&0===o&&s>0?(p=0,v=s-1):(p=g*ho((t+u)/(m+u)),v=g*fo((o+u)/(m+u))-1,v=go(e-1,po(v,g-1)),p=go(v,po(0,p))):(p=0,v=-1);const I=vo(p,v,l),{top:T,bottom:x}=wo(i,n,r,I),w=fo(e/g);return{items:I,offsetTop:T,offsetBottom:w*m+(w-1)*u-x,top:T,bottom:x,itemHeight:m,itemWidth:f}}))),A),C(z(j,L((e=>null!==e)),P((e=>e.length))),B),C(z(G(D,_,A,q),L((([e,t,{items:o}])=>o.length>0&&0!==t.height&&0!==e.height)),P((([e,t,{items:o},n])=>{const{top:r,bottom:i}=wo(e,n,t,o);return[r,i]})),k(ot)),o);const te=y(!1);C(z(n,V(te),P((([e,t])=>t||0!==e))),te);const oe=b(z(N(A),L((({items:e})=>e.length>0)),V(B,te),L((([{items:e},t,o])=>o&&e[e.length-1].index===t-1)),P((([,e])=>e-1)),k())),ne=b(z(N(A),L((({items:e})=>e.length>0&&0===e[0].index)),F(0),k())),re=b(z(N(A),V(J),L((([{items:e},t])=>e.length>0&&!t)),P((([{items:e}])=>({startIndex:e[0].index,endIndex:e[e.length-1].index}))),k(nt),W(0)));C(re,m.scrollSeekRangeChanged),C(z(U,V(D,_,B,q),P((([e,t,o,n,r])=>{const i=Ge(e),{align:l,behavior:s,offset:c}=i;let a=i.index;"LAST"===a&&(a=n-1),a=po(0,a,go(n-1,a));let u=So(t,r,o,a);return"end"===l?u=mo(u-t.height+o.height):"center"===l&&(u=mo(u-t.height/2+o.height/2)),c&&(u+=c),{top:u,behavior:s}}))),l);const ie=R(z(A,P((e=>e.offsetBottom+e.bottom))),0);return C(z(g,P((e=>({width:e.visibleWidth,height:e.visibleHeight})))),D),{data:j,totalCount:B,viewportDimensions:D,itemDimensions:_,scrollTop:n,scrollHeight:$,overscan:e,scrollBy:i,scrollTo:l,scrollToIndex:U,smoothScrollTargetReached:s,windowViewportRect:g,windowScrollTo:w,useWindowScroll:p,customScrollParent:v,windowScrollContainerState:I,deviation:K,scrollContainerState:c,footerHeight:a,headerHeight:u,initialItemCount:O,gap:q,restoreStateFrom:Z,...m,initialTopMostItemIndex:Q,gridState:A,totalListHeight:ie,...d,startReached:ne,endReached:oe,rangeChanged:re,stateChanged:Y,propsReady:f,stateRestoreInProgress:J,...S}}),h(at,ne,je,pt,qe,Et,Z));function wo(e,t,o,n){const{height:r}=o;if(void 0===r||0===n.length)return{top:0,bottom:0};return{top:So(e,t,o,n[0].index),bottom:So(e,t,o,n[n.length-1].index)+r}}function So(e,t,o,n){const r=Co(e.width,o.width,t.column),i=ho(n/r),l=i*o.height+po(0,i-1)*t.row;return l>0?l+t.row:l}function Co(e,t,o){return po(1,ho((e+o)/(ho(t)+o)))}const Eo=_((([e,t])=>({...e,...t})),h(xo,_((()=>{const e=y((e=>`Item ${e}`)),t=y({}),o=y(null),n=y("virtuoso-grid-item"),r=y("virtuoso-grid-list"),i=y(Wt),l=y("div"),s=y(I),c=(e,o=null)=>R(z(t,P((t=>t[e])),k()),o);return{context:o,itemContent:e,components:t,computeItemKey:i,itemClassName:n,listClassName:r,headerFooterTag:l,scrollerRef:s,FooterComponent:c("Footer"),HeaderComponent:c("Header"),ListComponent:c("List","div"),ItemComponent:c("Item","div"),ScrollerComponent:c("Scroller","div"),ScrollSeekPlaceholder:c("ScrollSeekPlaceholder","div")}})))),Ho=n.memo((function(){const e=Po("gridState"),t=Po("listClassName"),o=Po("itemClassName"),r=Po("itemContent"),i=Po("computeItemKey"),l=Po("isSeeking"),s=Lo("scrollHeight"),c=Po("ItemComponent"),a=Po("ListComponent"),u=Po("ScrollSeekPlaceholder"),d=Po("context"),m=Lo("itemDimensions"),f=Lo("gap"),h=Po("log"),g=Po("stateRestoreInProgress"),p=Q((e=>{const t=e.parentElement.parentElement.scrollHeight;s(t);const o=e.firstChild;if(o){const{width:e,height:t}=o.getBoundingClientRect();m({width:e,height:t})}f({row:Wo("row-gap",getComputedStyle(e).rowGap,h),column:Wo("column-gap",getComputedStyle(e).columnGap,h)})}));return g?null:n.createElement(a,{ref:p,className:t,...Kt(a,d),style:{paddingTop:e.offsetTop,paddingBottom:e.offsetBottom},"data-testid":"virtuoso-item-list"},e.items.map((t=>{const s=i(t.index,t.data,d);return l?n.createElement(u,{key:s,...Kt(u,d),index:t.index,height:e.itemHeight,width:e.itemWidth}):n.createElement(c,{...Kt(c,d),className:o,"data-index":t.index,key:s},r(t.index,t.data,d))})))})),yo=n.memo((function(){const e=Po("HeaderComponent"),t=Lo("headerHeight"),o=Po("headerFooterTag"),r=Q((e=>t(ee(e,"height")))),i=Po("context");return e?n.createElement(o,{ref:r},n.createElement(e,Kt(e,i))):null})),bo=n.memo((function(){const e=Po("FooterComponent"),t=Lo("footerHeight"),o=Po("headerFooterTag"),r=Q((e=>t(ee(e,"height")))),i=Po("context");return e?n.createElement(o,{ref:r},n.createElement(e,Kt(e,i))):null})),Ro=({children:e})=>{const t=n.useContext(Mt),o=Lo("itemDimensions"),r=Lo("viewportDimensions"),i=Q((e=>{r(e.getBoundingClientRect())}));return n.useEffect((()=>{t&&(r({height:t.viewportHeight,width:t.viewportWidth}),o({height:t.itemHeight,width:t.itemWidth}))}),[t,r,o]),n.createElement("div",{style:Ut(!1),ref:i},e)},zo=({children:e})=>{const t=n.useContext(Mt),o=Lo("windowViewportRect"),r=Lo("itemDimensions"),i=Po("customScrollParent"),l=Ft(o,i);return n.useEffect((()=>{t&&(r({height:t.itemHeight,width:t.itemWidth}),o({offsetTop:0,visibleHeight:t.viewportHeight,visibleWidth:t.viewportWidth}))}),[t,o,r]),n.createElement("div",{ref:l,style:Ut(!1)},e)},Bo=n.memo((function({...e}){const t=Po("useWindowScroll"),o=Po("customScrollParent"),r=o||t?Mo:Oo,i=o||t?zo:Ro;return n.createElement(r,{...e},n.createElement(i,null,n.createElement(yo,null),n.createElement(Ho,null),n.createElement(bo,null)))})),{Component:ko,usePublisher:Lo,useEmitterValue:Po,useEmitter:Fo}=K(Eo,{optional:{context:"context",totalCount:"totalCount",overscan:"overscan",itemContent:"itemContent",components:"components",computeItemKey:"computeItemKey",data:"data",initialItemCount:"initialItemCount",scrollSeekConfiguration:"scrollSeekConfiguration",headerFooterTag:"headerFooterTag",listClassName:"listClassName",itemClassName:"itemClassName",useWindowScroll:"useWindowScroll",customScrollParent:"customScrollParent",scrollerRef:"scrollerRef",logLevel:"logLevel",restoreStateFrom:"restoreStateFrom",initialTopMostItemIndex:"initialTopMostItemIndex"},methods:{scrollTo:"scrollTo",scrollBy:"scrollBy",scrollToIndex:"scrollToIndex"},events:{isScrolling:"isScrolling",endReached:"endReached",startReached:"startReached",rangeChanged:"rangeChanged",atBottomStateChange:"atBottomStateChange",atTopStateChange:"atTopStateChange",stateChanged:"stateChanged"}},Bo),Oo=Zt({usePublisher:Lo,useEmitterValue:Po,useEmitter:Fo}),Mo=Jt({usePublisher:Lo,useEmitterValue:Po,useEmitter:Fo});function Wo(e,t,o){return"normal"===t||(null==t?void 0:t.endsWith("px"))||o(`${e} was not resolved to pixel value correctly`,t,q.WARN),"normal"===t?0:parseInt(null!=t?t:"0",10)}const Ao=_((([e,t])=>({...e,...t})),h(Bt,_((()=>{const e=y((e=>n.createElement("td",null,"Item $",e))),t=y(null),o=y(null),r=y(null),i=y({}),l=y(Wt),s=y(I),c=(e,t=null)=>R(z(i,P((t=>t[e])),k()),t);return{context:t,itemContent:e,fixedHeaderContent:o,fixedFooterContent:r,components:i,computeItemKey:l,scrollerRef:s,TableComponent:c("Table","table"),TableHeadComponent:c("TableHead","thead"),TableFooterComponent:c("TableFoot","tfoot"),TableBodyComponent:c("TableBody","tbody"),TableRowComponent:c("TableRow","tr"),ScrollerComponent:c("Scroller","div"),EmptyPlaceholder:c("EmptyPlaceholder"),ScrollSeekPlaceholder:c("ScrollSeekPlaceholder"),FillerRow:c("FillerRow")}})))),Vo=({height:e})=>n.createElement("tr",null,n.createElement("td",{style:{height:e}})),Do=({height:e})=>n.createElement("tr",null,n.createElement("td",{style:{height:e,padding:0,border:0}})),No={overflowAnchor:"none"},Go=n.memo((function(){const e=qo("listState"),t=jo("sizeRanges"),o=qo("useWindowScroll"),r=qo("customScrollParent"),i=jo("windowScrollContainerState"),l=jo("scrollContainerState"),s=r||o?i:l,c=qo("itemContent"),a=qo("trackItemSizes"),u=qo("itemSize"),d=qo("log"),{callbackRef:m,ref:f}=X(t,u,a,s,d,void 0,r),[h,g]=n.useState(0);Yo("deviation",(e=>{h!==e&&(f.current.style.marginTop=`${e}px`,g(e))}));const p=qo("EmptyPlaceholder"),v=qo("ScrollSeekPlaceholder")||Vo,I=qo("FillerRow")||Do,T=qo("TableBodyComponent"),x=qo("TableRowComponent"),w=qo("computeItemKey"),S=qo("isSeeking"),C=qo("paddingTopAddition"),E=qo("firstItemIndex"),H=qo("statefulTotalCount"),y=qo("context");if(0===H&&p)return n.createElement(p,Kt(p,y));const b=e.offsetTop+C+h,R=e.offsetBottom,z=b>0?n.createElement(I,{height:b,key:"padding-top",context:y}):null,B=R>0?n.createElement(I,{height:R,key:"padding-bottom",context:y}):null,k=e.items.map((e=>{const t=e.originalIndex,o=w(t+E,e.data,y);return S?n.createElement(v,{...Kt(v,y),key:o,index:e.index,height:e.size,type:e.type||"item"}):n.createElement(x,{...Kt(x,y),...jt(x,e.data),key:o,"data-index":t,"data-known-size":e.size,"data-item-index":e.index,style:No},c(e.index,e.data,y))}));return n.createElement(T,{ref:m,"data-testid":"virtuoso-item-list",...Kt(T,y)},[z,...k,B])})),_o=({children:e})=>{const t=n.useContext(Ot),o=jo("viewportHeight"),r=jo("fixedItemHeight"),i=Q(a(o,(e=>ee(e,"height"))));return n.useEffect((()=>{t&&(o(t.viewportHeight),r(t.itemHeight))}),[t,o,r]),n.createElement("div",{style:Ut(!1),ref:i,"data-viewport-type":"element"},e)},Uo=({children:e})=>{const t=n.useContext(Ot),o=jo("windowViewportRect"),r=jo("fixedItemHeight"),i=qo("customScrollParent"),l=Ft(o,i);return n.useEffect((()=>{t&&(r(t.itemHeight),o({offsetTop:0,visibleHeight:t.viewportHeight,visibleWidth:100}))}),[t,o,r]),n.createElement("div",{ref:l,style:Ut(!1),"data-viewport-type":"window"},e)},$o=n.memo((function(e){const t=qo("useWindowScroll"),o=qo("customScrollParent"),r=jo("fixedHeaderHeight"),i=jo("fixedFooterHeight"),l=qo("fixedHeaderContent"),s=qo("fixedFooterContent"),c=qo("context"),u=Q(a(r,(e=>ee(e,"height")))),d=Q(a(i,(e=>ee(e,"height")))),m=o||t?Jo:Zo,f=o||t?Uo:_o,h=qo("TableComponent"),g=qo("TableHeadComponent"),p=qo("TableFooterComponent"),v=l?n.createElement(g,{key:"TableHead",style:{zIndex:2,position:"sticky",top:0},ref:u,...Kt(g,c)},l()):null,I=s?n.createElement(p,{key:"TableFoot",style:{zIndex:1,position:"sticky",bottom:0},ref:d,...Kt(p,c)},s()):null;return n.createElement(m,{...e},n.createElement(f,null,n.createElement(h,{style:{borderSpacing:0,overflowAnchor:"none"},...Kt(h,c)},[v,n.createElement(Go,{key:"TableBody"}),I])))})),{Component:Ko,usePublisher:jo,useEmitterValue:qo,useEmitter:Yo}=K(Ao,{required:{},optional:{restoreStateFrom:"restoreStateFrom",context:"context",followOutput:"followOutput",firstItemIndex:"firstItemIndex",itemContent:"itemContent",fixedHeaderContent:"fixedHeaderContent",fixedFooterContent:"fixedFooterContent",overscan:"overscan",increaseViewportBy:"increaseViewportBy",totalCount:"totalCount",topItemCount:"topItemCount",initialTopMostItemIndex:"initialTopMostItemIndex",components:"components",groupCounts:"groupCounts",atBottomThreshold:"atBottomThreshold",atTopThreshold:"atTopThreshold",computeItemKey:"computeItemKey",defaultItemHeight:"defaultItemHeight",fixedItemHeight:"fixedItemHeight",itemSize:"itemSize",scrollSeekConfiguration:"scrollSeekConfiguration",data:"data",initialItemCount:"initialItemCount",initialScrollTop:"initialScrollTop",alignToBottom:"alignToBottom",useWindowScroll:"useWindowScroll",customScrollParent:"customScrollParent",scrollerRef:"scrollerRef",logLevel:"logLevel"},methods:{scrollToIndex:"scrollToIndex",scrollIntoView:"scrollIntoView",scrollTo:"scrollTo",scrollBy:"scrollBy",getState:"getState"},events:{isScrolling:"isScrolling",endReached:"endReached",startReached:"startReached",rangeChanged:"rangeChanged",atBottomStateChange:"atBottomStateChange",atTopStateChange:"atTopStateChange",totalListHeightChanged:"totalListHeightChanged",itemsRendered:"itemsRendered",groupIndices:"groupIndices"}},$o),Zo=Zt({usePublisher:jo,useEmitterValue:qo,useEmitter:Yo}),Jo=Jt({usePublisher:jo,useEmitterValue:qo,useEmitter:Yo})}}]);
//# sourceMappingURL=react-virtuoso.90bb4784b9d4825e9c49e3d636b58cfc.js.map