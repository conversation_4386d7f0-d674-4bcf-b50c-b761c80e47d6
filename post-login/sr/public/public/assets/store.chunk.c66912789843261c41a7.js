(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["store"],{42016:function(e,t,n){var r=n(88267),o=n(97528),i=[n(30810)];e.exports=r.createStore(o,i)},30810:function(e,t,n){e.exports=function(){return n(94609),{}}},94609:function(){"object"!==typeof JSON&&(JSON={}),function(){"use strict";var rx_one=/^[\],:{}\s]*$/,rx_two=/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,rx_three=/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,rx_four=/(?:^|:|,)(?:\s*\[)+/g,rx_escapable=/[\\"\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,rx_dangerous=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,gap,indent,meta,rep;function f(e){return e<10?"0"+e:e}function this_value(){return this.valueOf()}function quote(e){return rx_escapable.lastIndex=0,rx_escapable.test(e)?'"'+e.replace(rx_escapable,(function(e){var t=meta[e];return"string"===typeof t?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)}))+'"':'"'+e+'"'}function str(e,t){var n,r,o,i,a,u=gap,c=t[e];switch(c&&"object"===typeof c&&"function"===typeof c.toJSON&&(c=c.toJSON(e)),"function"===typeof rep&&(c=rep.call(t,e,c)),typeof c){case"string":return quote(c);case"number":return isFinite(c)?String(c):"null";case"boolean":case"null":return String(c);case"object":if(!c)return"null";if(gap+=indent,a=[],"[object Array]"===Object.prototype.toString.apply(c)){for(i=c.length,n=0;n<i;n+=1)a[n]=str(n,c)||"null";return o=0===a.length?"[]":gap?"[\n"+gap+a.join(",\n"+gap)+"\n"+u+"]":"["+a.join(",")+"]",gap=u,o}if(rep&&"object"===typeof rep)for(i=rep.length,n=0;n<i;n+=1)"string"===typeof rep[n]&&(o=str(r=rep[n],c))&&a.push(quote(r)+(gap?": ":":")+o);else for(r in c)Object.prototype.hasOwnProperty.call(c,r)&&(o=str(r,c))&&a.push(quote(r)+(gap?": ":":")+o);return o=0===a.length?"{}":gap?"{\n"+gap+a.join(",\n"+gap)+"\n"+u+"}":"{"+a.join(",")+"}",gap=u,o}}"function"!==typeof Date.prototype.toJSON&&(Date.prototype.toJSON=function(){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+f(this.getUTCMonth()+1)+"-"+f(this.getUTCDate())+"T"+f(this.getUTCHours())+":"+f(this.getUTCMinutes())+":"+f(this.getUTCSeconds())+"Z":null},Boolean.prototype.toJSON=this_value,Number.prototype.toJSON=this_value,String.prototype.toJSON=this_value),"function"!==typeof JSON.stringify&&(meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},JSON.stringify=function(e,t,n){var r;if(gap="",indent="","number"===typeof n)for(r=0;r<n;r+=1)indent+=" ";else"string"===typeof n&&(indent=n);if(rep=t,t&&"function"!==typeof t&&("object"!==typeof t||"number"!==typeof t.length))throw new Error("JSON.stringify");return str("",{"":e})}),"function"!==typeof JSON.parse&&(JSON.parse=function(text,reviver){var j;function walk(e,t){var n,r,o=e[t];if(o&&"object"===typeof o)for(n in o)Object.prototype.hasOwnProperty.call(o,n)&&(void 0!==(r=walk(o,n))?o[n]=r:delete o[n]);return reviver.call(e,t,o)}if(text=String(text),rx_dangerous.lastIndex=0,rx_dangerous.test(text)&&(text=text.replace(rx_dangerous,(function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)}))),rx_one.test(text.replace(rx_two,"@").replace(rx_three,"]").replace(rx_four,"")))return j=eval("("+text+")"),"function"===typeof reviver?walk({"":j},""):j;throw new SyntaxError("JSON.parse")})}()},88267:function(e,t,n){var r=n(41623),o=r.slice,i=r.pluck,a=r.each,u=r.bind,c=r.create,f=r.isList,s=r.isFunction,l=r.isObject;e.exports={createStore:g};var p={version:"2.0.12",enabled:!1,get:function(e,t){var n=this.storage.read(this._namespacePrefix+e);return this._deserialize(n,t)},set:function(e,t){return void 0===t?this.remove(e):(this.storage.write(this._namespacePrefix+e,this._serialize(t)),t)},remove:function(e){this.storage.remove(this._namespacePrefix+e)},each:function(e){var t=this;this.storage.each((function(n,r){e.call(t,t._deserialize(n),(r||"").replace(t._namespaceRegexp,""))}))},clearAll:function(){this.storage.clearAll()},hasNamespace:function(e){return this._namespacePrefix=="__storejs_"+e+"_"},createStore:function(){return g.apply(this,arguments)},addPlugin:function(e){this._addPlugin(e)},namespace:function(e){return g(this.storage,this.plugins,e)}};function g(e,t,n){n||(n=""),e&&!f(e)&&(e=[e]),t&&!f(t)&&(t=[t]);var r=n?"__storejs_"+n+"_":"",g=n?new RegExp("^"+r):null;if(!/^[a-zA-Z0-9_\-]*$/.test(n))throw new Error("store.js namespaces can only have alphanumerics + underscores and dashes");var h={_namespacePrefix:r,_namespaceRegexp:g,_testStorage:function(e){try{var t="__storejs__test__";e.write(t,t);var n=e.read(t)===t;return e.remove(t),n}catch(r){return!1}},_assignPluginFnProp:function(e,t){var n=this[t];this[t]=function(){var t=o(arguments,0),r=this;var i=[function(){if(n)return a(arguments,(function(e,n){t[n]=e})),n.apply(r,t)}].concat(t);return e.apply(r,i)}},_serialize:function(e){return JSON.stringify(e)},_deserialize:function(e,t){if(!e)return t;var n="";try{n=JSON.parse(e)}catch(r){n=e}return void 0!==n?n:t},_addStorage:function(e){this.enabled||this._testStorage(e)&&(this.storage=e,this.enabled=!0)},_addPlugin:function(e){var t=this;if(f(e))a(e,(function(e){t._addPlugin(e)}));else if(!i(this.plugins,(function(t){return e===t}))){if(this.plugins.push(e),!s(e))throw new Error("Plugins must be function values that return objects");var n=e.call(this);if(!l(n))throw new Error("Plugins must return an object of function properties");a(n,(function(n,r){if(!s(n))throw new Error("Bad plugin property: "+r+" from plugin "+e.name+". Plugins should only return functions.");t._assignPluginFnProp(n,r)}))}},addStorage:function(e){!function(){var e="undefined"==typeof console?null:console;e&&(e.warn?e.warn:e.log).apply(e,arguments)}("store.addStorage(storage) is deprecated. Use createStore([storages])"),this._addStorage(e)}},v=c(h,p,{plugins:[]});return v.raw={},a(v,(function(e,t){s(e)&&(v.raw[t]=u(v,e))})),a(e,(function(e){v._addStorage(e)})),a(t,(function(e){v._addPlugin(e)})),v}},41623:function(e,t,n){var r=Object.assign?Object.assign:function(e,t,n,r){for(var o=1;o<arguments.length;o++)c(Object(arguments[o]),(function(t,n){e[n]=t}));return e},o=function(){if(Object.create)return function(e,t,n,o){var i=u(arguments,1);return r.apply(this,[Object.create(e)].concat(i))};{function e(){}return function(t,n,o,i){var a=u(arguments,1);return e.prototype=t,r.apply(this,[new e].concat(a))}}}(),i=String.prototype.trim?function(e){return String.prototype.trim.call(e)}:function(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")},a="undefined"!==typeof window?window:n.g;function u(e,t){return Array.prototype.slice.call(e,t||0)}function c(e,t){f(e,(function(e,n){return t(e,n),!1}))}function f(e,t){if(s(e)){for(var n=0;n<e.length;n++)if(t(e[n],n))return e[n]}else for(var r in e)if(e.hasOwnProperty(r)&&t(e[r],r))return e[r]}function s(e){return null!=e&&"function"!=typeof e&&"number"==typeof e.length}e.exports={assign:r,create:o,trim:i,bind:function(e,t){return function(){return t.apply(e,Array.prototype.slice.call(arguments,0))}},slice:u,each:c,map:function(e,t){var n=s(e)?[]:{};return f(e,(function(e,r){return n[r]=t(e,r),!1})),n},pluck:f,isList:s,isFunction:function(e){return e&&"[object Function]"==={}.toString.call(e)},isObject:function(e){return e&&"[object Object]"==={}.toString.call(e)},Global:a}},97528:function(e,t,n){e.exports=[n(59002),n(41520),n(56677),n(92549),n(37610),n(59178)]},92549:function(e,t,n){var r=n(41623),o=r.Global,i=r.trim;e.exports={name:"cookieStorage",read:function(e){if(!e||!f(e))return null;var t="(?:^|.*;\\s*)"+escape(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=\\s*((?:[^;](?!;))*[^;]?).*";return unescape(a.cookie.replace(new RegExp(t),"$1"))},write:function(e,t){if(!e)return;a.cookie=escape(e)+"="+escape(t)+"; expires=Tue, 19 Jan 2038 03:14:07 GMT; path=/"},each:u,remove:c,clearAll:function(){u((function(e,t){c(t)}))}};var a=o.document;function u(e){for(var t=a.cookie.split(/; ?/g),n=t.length-1;n>=0;n--)if(i(t[n])){var r=t[n].split("="),o=unescape(r[0]);e(unescape(r[1]),o)}}function c(e){e&&f(e)&&(a.cookie=escape(e)+"=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/")}function f(e){return new RegExp("(?:^|;\\s*)"+escape(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=").test(a.cookie)}},59002:function(e,t,n){var r=n(41623).Global;function o(){return r.localStorage}function i(e){return o().getItem(e)}e.exports={name:"localStorage",read:i,write:function(e,t){return o().setItem(e,t)},each:function(e){for(var t=o().length-1;t>=0;t--){var n=o().key(t);e(i(n),n)}},remove:function(e){return o().removeItem(e)},clearAll:function(){return o().clear()}}},59178:function(e){e.exports={name:"memoryStorage",read:function(e){return t[e]},write:function(e,n){t[e]=n},each:function(e){for(var n in t)t.hasOwnProperty(n)&&e(t[n],n)},remove:function(e){delete t[e]},clearAll:function(e){t={}}};var t={}},41520:function(e,t,n){var r=n(41623).Global;e.exports={name:"oldFF-globalStorage",read:function(e){return o[e]},write:function(e,t){o[e]=t},each:i,remove:function(e){return o.removeItem(e)},clearAll:function(){i((function(e,t){delete o[e]}))}};var o=r.globalStorage;function i(e){for(var t=o.length-1;t>=0;t--){var n=o.key(t);e(o[n],n)}}},56677:function(e,t,n){var r=n(41623).Global;e.exports={name:"oldIE-userDataStorage",write:function(e,t){if(u)return;var n=f(e);a((function(e){e.setAttribute(n,t),e.save(o)}))},read:function(e){if(u)return;var t=f(e),n=null;return a((function(e){n=e.getAttribute(t)})),n},each:function(e){a((function(t){for(var n=t.XMLDocument.documentElement.attributes,r=n.length-1;r>=0;r--){var o=n[r];e(t.getAttribute(o.name),o.name)}}))},remove:function(e){var t=f(e);a((function(e){e.removeAttribute(t),e.save(o)}))},clearAll:function(){a((function(e){var t=e.XMLDocument.documentElement.attributes;e.load(o);for(var n=t.length-1;n>=0;n--)e.removeAttribute(t[n].name);e.save(o)}))}};var o="storejs",i=r.document,a=function(){if(!i||!i.documentElement||!i.documentElement.addBehavior)return null;var e,t,n,r="script";try{(t=new ActiveXObject("htmlfile")).open(),t.write("<"+r+">document.w=window</"+r+'><iframe src="/favicon.ico"></iframe>'),t.close(),e=t.w.frames[0].document,n=e.createElement("div")}catch(a){n=i.createElement("div"),e=i.body}return function(t){var r=[].slice.call(arguments,0);r.unshift(n),e.appendChild(n),n.addBehavior("#default#userData"),n.load(o),t.apply(this,r),e.removeChild(n)}}(),u=(r.navigator?r.navigator.userAgent:"").match(/ (MSIE 8|MSIE 9|MSIE 10)\./);var c=new RegExp("[!\"#$%&'()*+,/\\\\:;<=>?@[\\]^`{|}~]","g");function f(e){return e.replace(/^\d/,"___$&").replace(c,"___")}},37610:function(e,t,n){var r=n(41623).Global;function o(){return r.sessionStorage}function i(e){return o().getItem(e)}e.exports={name:"sessionStorage",read:i,write:function(e,t){return o().setItem(e,t)},each:function(e){for(var t=o().length-1;t>=0;t--){var n=o().key(t);e(i(n),n)}},remove:function(e){return o().removeItem(e)},clearAll:function(){return o().clear()}}}}]);
//# sourceMappingURL=store.8d38ba59379ec5091f45cf40d3139649.js.map