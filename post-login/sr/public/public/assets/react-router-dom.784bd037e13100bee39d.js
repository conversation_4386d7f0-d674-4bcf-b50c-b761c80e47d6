"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["react-router-dom"],{97341:function(n,t,e){e.d(t,{VK:function(){return T},rU:function(){return H}});var r=e(62719),o=e(74289),a=e(89526),i=e(17692),c=e(99337),u=e(78109);function f(n){return"/"===n.charAt(0)?n:"/"+n}function s(n){return"/"===n.charAt(0)?n.substr(1):n}function l(n,t){return function(n,t){return 0===n.toLowerCase().indexOf(t.toLowerCase())&&-1!=="/?#".indexOf(n.charAt(t.length))}(n,t)?n.substr(t.length):n}function h(n){return"/"===n.charAt(n.length-1)?n.slice(0,-1):n}function d(n){var t=n.pathname,e=n.search,r=n.hash,o=t||"/";return e&&"?"!==e&&(o+="?"===e.charAt(0)?e:"?"+e),r&&"#"!==r&&(o+="#"===r.charAt(0)?r:"#"+r),o}function v(n,t,e,r){var o;"string"===typeof n?(o=function(n){var t=n||"/",e="",r="",o=t.indexOf("#");-1!==o&&(r=t.substr(o),t=t.substr(0,o));var a=t.indexOf("?");return-1!==a&&(e=t.substr(a),t=t.substr(0,a)),{pathname:t,search:"?"===e?"":e,hash:"#"===r?"":r}}(n),o.state=t):(void 0===(o=(0,i.Z)({},n)).pathname&&(o.pathname=""),o.search?"?"!==o.search.charAt(0)&&(o.search="?"+o.search):o.search="",o.hash?"#"!==o.hash.charAt(0)&&(o.hash="#"+o.hash):o.hash="",void 0!==t&&void 0===o.state&&(o.state=t));try{o.pathname=decodeURI(o.pathname)}catch(a){throw a instanceof URIError?new URIError('Pathname "'+o.pathname+'" could not be decoded. This is likely caused by an invalid percent-encoding.'):a}return e&&(o.key=e),r?o.pathname?"/"!==o.pathname.charAt(0)&&(o.pathname=(0,c.Z)(o.pathname,r.pathname)):o.pathname=r.pathname:o.pathname||(o.pathname="/"),o}function p(){var n=null;var t=[];return{setPrompt:function(t){return n=t,function(){n===t&&(n=null)}},confirmTransitionTo:function(t,e,r,o){if(null!=n){var a="function"===typeof n?n(t,e):n;"string"===typeof a?"function"===typeof r?r(a,o):o(!0):o(!1!==a)}else o(!0)},appendListener:function(n){var e=!0;function r(){e&&n.apply(void 0,arguments)}return t.push(r),function(){e=!1,t=t.filter((function(n){return n!==r}))}},notifyListeners:function(){for(var n=arguments.length,e=new Array(n),r=0;r<n;r++)e[r]=arguments[r];t.forEach((function(n){return n.apply(void 0,e)}))}}}var w=!("undefined"===typeof window||!window.document||!window.document.createElement);function m(n,t){t(window.confirm(n))}var y="popstate",g="hashchange";function O(){try{return window.history.state||{}}catch(n){return{}}}function P(n){void 0===n&&(n={}),w||(0,u.Z)(!1);var t=window.history,e=function(){var n=window.navigator.userAgent;return(-1===n.indexOf("Android 2.")&&-1===n.indexOf("Android 4.0")||-1===n.indexOf("Mobile Safari")||-1!==n.indexOf("Chrome")||-1!==n.indexOf("Windows Phone"))&&window.history&&"pushState"in window.history}(),r=!(-1===window.navigator.userAgent.indexOf("Trident")),o=n,a=o.forceRefresh,c=void 0!==a&&a,s=o.getUserConfirmation,P=void 0===s?m:s,b=o.keyLength,k=void 0===b?6:b,x=n.basename?h(f(n.basename)):"";function A(n){var t=n||{},e=t.key,r=t.state,o=window.location,a=o.pathname+o.search+o.hash;return x&&(a=l(a,x)),v(a,r,e)}function E(){return Math.random().toString(36).substr(2,k)}var C=p();function L(n){(0,i.Z)(B,n),B.length=t.length,C.notifyListeners(B.location,B.action)}function T(n){(function(n){return void 0===n.state&&-1===navigator.userAgent.indexOf("CriOS")})(n)||S(A(n.state))}function R(){S(A(O()))}var Z=!1;function S(n){if(Z)Z=!1,L();else{C.confirmTransitionTo(n,"POP",P,(function(t){t?L({action:"POP",location:n}):function(n){var t=B.location,e=I.indexOf(t.key);-1===e&&(e=0);var r=I.indexOf(n.key);-1===r&&(r=0);var o=e-r;o&&(Z=!0,K(o))}(n)}))}}var U=A(O()),I=[U.key];function H(n){return x+d(n)}function K(n){t.go(n)}var N=0;function _(n){1===(N+=n)&&1===n?(window.addEventListener(y,T),r&&window.addEventListener(g,R)):0===N&&(window.removeEventListener(y,T),r&&window.removeEventListener(g,R))}var F=!1;var B={length:t.length,action:"POP",location:U,createHref:H,push:function(n,r){var o="PUSH",a=v(n,r,E(),B.location);C.confirmTransitionTo(a,o,P,(function(n){if(n){var r=H(a),i=a.key,u=a.state;if(e)if(t.pushState({key:i,state:u},null,r),c)window.location.href=r;else{var f=I.indexOf(B.location.key),s=I.slice(0,f+1);s.push(a.key),I=s,L({action:o,location:a})}else window.location.href=r}}))},replace:function(n,r){var o="REPLACE",a=v(n,r,E(),B.location);C.confirmTransitionTo(a,o,P,(function(n){if(n){var r=H(a),i=a.key,u=a.state;if(e)if(t.replaceState({key:i,state:u},null,r),c)window.location.replace(r);else{var f=I.indexOf(B.location.key);-1!==f&&(I[f]=a.key),L({action:o,location:a})}else window.location.replace(r)}}))},go:K,goBack:function(){K(-1)},goForward:function(){K(1)},block:function(n){void 0===n&&(n=!1);var t=C.setPrompt(n);return F||(_(1),F=!0),function(){return F&&(F=!1,_(-1)),t()}},listen:function(n){var t=C.appendListener(n);return _(1),function(){_(-1),t()}}};return B}var b="hashchange",k={hashbang:{encodePath:function(n){return"!"===n.charAt(0)?n:"!/"+s(n)},decodePath:function(n){return"!"===n.charAt(0)?n.substr(1):n}},noslash:{encodePath:s,decodePath:f},slash:{encodePath:f,decodePath:f}};function x(n){var t=n.indexOf("#");return-1===t?n:n.slice(0,t)}function A(){var n=window.location.href,t=n.indexOf("#");return-1===t?"":n.substring(t+1)}function E(n){window.location.replace(x(window.location.href)+"#"+n)}function C(n){void 0===n&&(n={}),w||(0,u.Z)(!1);var t=window.history,e=(window.navigator.userAgent.indexOf("Firefox"),n),r=e.getUserConfirmation,o=void 0===r?m:r,a=e.hashType,c=void 0===a?"slash":a,s=n.basename?h(f(n.basename)):"",y=k[c],g=y.encodePath,O=y.decodePath;function P(){var n=O(A());return s&&(n=l(n,s)),v(n)}var C=p();function L(n){(0,i.Z)(B,n),B.length=t.length,C.notifyListeners(B.location,B.action)}var T=!1,R=null;function Z(){var n,t,e=A(),r=g(e);if(e!==r)E(r);else{var a=P(),i=B.location;if(!T&&(t=a,(n=i).pathname===t.pathname&&n.search===t.search&&n.hash===t.hash))return;if(R===d(a))return;R=null,function(n){if(T)T=!1,L();else{var t="POP";C.confirmTransitionTo(n,t,o,(function(e){e?L({action:t,location:n}):function(n){var t=B.location,e=H.lastIndexOf(d(t));-1===e&&(e=0);var r=H.lastIndexOf(d(n));-1===r&&(r=0);var o=e-r;o&&(T=!0,K(o))}(n)}))}}(a)}}var S=A(),U=g(S);S!==U&&E(U);var I=P(),H=[d(I)];function K(n){t.go(n)}var N=0;function _(n){1===(N+=n)&&1===n?window.addEventListener(b,Z):0===N&&window.removeEventListener(b,Z)}var F=!1;var B={length:t.length,action:"POP",location:I,createHref:function(n){var t=document.querySelector("base"),e="";return t&&t.getAttribute("href")&&(e=x(window.location.href)),e+"#"+g(s+d(n))},push:function(n,t){var e="PUSH",r=v(n,void 0,void 0,B.location);C.confirmTransitionTo(r,e,o,(function(n){if(n){var t=d(r),o=g(s+t);if(A()!==o){R=t,function(n){window.location.hash=n}(o);var a=H.lastIndexOf(d(B.location)),i=H.slice(0,a+1);i.push(t),H=i,L({action:e,location:r})}else L()}}))},replace:function(n,t){var e="REPLACE",r=v(n,void 0,void 0,B.location);C.confirmTransitionTo(r,e,o,(function(n){if(n){var t=d(r),o=g(s+t);A()!==o&&(R=t,E(o));var a=H.indexOf(d(B.location));-1!==a&&(H[a]=t),L({action:e,location:r})}}))},go:K,goBack:function(){K(-1)},goForward:function(){K(1)},block:function(n){void 0===n&&(n=!1);var t=C.setPrompt(n);return F||(_(1),F=!0),function(){return F&&(F=!1,_(-1)),t()}},listen:function(n){var t=C.appendListener(n);return _(1),function(){_(-1),t()}}};return B}var L=e(71972),T=function(n){function t(){for(var t,e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];return(t=n.call.apply(n,[this].concat(r))||this).history=P(t.props),t}return(0,o.Z)(t,n),t.prototype.render=function(){return a.createElement(r.F0,{history:this.history,children:this.props.children})},t}(a.Component);a.Component;var R=function(n,t){return"function"===typeof n?n(t):n},Z=function(n,t){return"string"===typeof n?v(n,null,null,t):n},S=function(n){return n},U=a.forwardRef;"undefined"===typeof U&&(U=S);var I=U((function(n,t){var e=n.innerRef,r=n.navigate,o=n.onClick,c=(0,L.Z)(n,["innerRef","navigate","onClick"]),u=c.target,f=(0,i.Z)({},c,{onClick:function(n){try{o&&o(n)}catch(t){throw n.preventDefault(),t}n.defaultPrevented||0!==n.button||u&&"_self"!==u||function(n){return!!(n.metaKey||n.altKey||n.ctrlKey||n.shiftKey)}(n)||(n.preventDefault(),r())}});return f.ref=S!==U&&t||e,a.createElement("a",f)}));var H=U((function(n,t){var e=n.component,o=void 0===e?I:e,c=n.replace,f=n.to,s=n.innerRef,l=(0,L.Z)(n,["component","replace","to","innerRef"]);return a.createElement(r.s6.Consumer,null,(function(n){n||(0,u.Z)(!1);var e=n.history,r=Z(R(f,n.location),n.location),h=r?e.createHref(r):"",d=(0,i.Z)({},l,{href:h,navigate:function(){var t=R(f,n.location);(c?e.replace:e.push)(t)}});return S!==U?d.ref=t||s:d.innerRef=s,a.createElement(o,d)}))})),K=function(n){return n},N=a.forwardRef;"undefined"===typeof N&&(N=K);N((function(n,t){var e=n["aria-current"],o=void 0===e?"page":e,c=n.activeClassName,f=void 0===c?"active":c,s=n.activeStyle,l=n.className,h=n.exact,d=n.isActive,v=n.location,p=n.strict,w=n.style,m=n.to,y=n.innerRef,g=(0,L.Z)(n,["aria-current","activeClassName","activeStyle","className","exact","isActive","location","strict","style","to","innerRef"]);return a.createElement(r.s6.Consumer,null,(function(n){n||(0,u.Z)(!1);var e=v||n.location,c=Z(R(m,e),e),O=c.pathname,P=O&&O.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1"),b=P?(0,r.LX)(e.pathname,{path:P,exact:h,strict:p}):null,k=!!(d?d(b,e):b),x=k?function(){for(var n=arguments.length,t=new Array(n),e=0;e<n;e++)t[e]=arguments[e];return t.filter((function(n){return n})).join(" ")}(l,f):l,A=k?(0,i.Z)({},w,{},s):w,E=(0,i.Z)({"aria-current":k&&o||null,className:x,style:A,to:c},g);return K!==N?E.ref=t||y:E.innerRef=y,a.createElement(H,E)}))}))}}]);
//# sourceMappingURL=react-router-dom.49d559cdb0fde6a4990530eb8323862f.js.map