"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["leva"],{1981:function(e,t,n){n.d(t,{Zf:function(){return mo},LI:function(){return Lr},M4:function(){return bo}});var r=n(73961),o=n(89526),i=n(42651),a=n(64525);function l(e,t){if(Object.is(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r=0;r<n.length;r++)if(!Object.prototype.hasOwnProperty.call(t,n[r])||!Object.is(e[n[r]],t[n[r]]))return!1;return!0}var s=n(34353),c=n(86744),u=n(86320),d=n(49090);function p(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}let f;!function(e){e[e.UNSUPPORTED_INPUT=0]="UNSUPPORTED_INPUT",e[e.NO_COMPONENT_FOR_TYPE=1]="NO_COMPONENT_FOR_TYPE",e[e.UNKNOWN_INPUT=2]="UNKNOWN_INPUT",e[e.DUPLICATE_KEYS=3]="DUPLICATE_KEYS",e[e.ALREADY_REGISTERED_TYPE=4]="ALREADY_REGISTERED_TYPE",e[e.CLIPBOARD_ERROR=5]="CLIPBOARD_ERROR",e[e.THEME_ERROR=6]="THEME_ERROR",e[e.PATH_DOESNT_EXIST=7]="PATH_DOESNT_EXIST",e[e.INPUT_TYPE_OVERRIDE=8]="INPUT_TYPE_OVERRIDE",e[e.EMPTY_KEY=9]="EMPTY_KEY"}(f||(f={}));const g={[f.UNSUPPORTED_INPUT]:(e,t)=>[`An input with type \`${e}\` input was found at path \`${t}\` but it's not supported yet.`],[f.NO_COMPONENT_FOR_TYPE]:(e,t)=>[`Type \`${e}\` found at path \`${t}\` can't be displayed in panel because no component supports it yet.`],[f.UNKNOWN_INPUT]:(e,t)=>[`input at path \`${e}\` is not recognized.`,t],[f.DUPLICATE_KEYS]:(e,t,n)=>[`Key \`${e}\` of path \`${t}\` already exists at path \`${n}\`. Even nested keys need to be unique. Rename one of the keys.`],[f.ALREADY_REGISTERED_TYPE]:e=>[`Type ${e} has already been registered. You can't register a component with the same type.`],[f.CLIPBOARD_ERROR]:e=>["Error copying the value",e],[f.THEME_ERROR]:(e,t)=>[`Error accessing the theme \`${e}.${t}\` value.`],[f.PATH_DOESNT_EXIST]:e=>[`Error getting the value at path \`${e}\`. There is probably an error in your \`render\` function.`],[f.PATH_DOESNT_EXIST]:e=>[`Error accessing the value at path \`${e}\``],[f.INPUT_TYPE_OVERRIDE]:(e,t,n)=>[`Input at path \`${e}\` already exists with type: \`${t}\`. Its type cannot be overridden with type \`${n}\`.`],[f.EMPTY_KEY]:()=>["Keys can not be empty, if you want to hide a label use whitespace."]};function h(e,t,...n){const[r,...o]=g[t](...n);console[e]("LEVA: "+r,...o)}const m=h.bind(null,"warn"),v=h.bind(null,"log"),b=["value"],y=["schema"],E=["value"],w=[],$={};function x(e){let{value:t}=e,n=p(e,b);for(let r of w){const e=r(t,n);if(e)return e}}function O(e,t){let{schema:n}=t,r=p(t,y);e in $?m(f.ALREADY_REGISTERED_TYPE,e):(w.push(((t,r)=>n(t,r)&&e)),$[e]=r)}function C(e,t,n,r){const{normalize:o}=$[e];if(o)return o(t,n,r);if("object"!==typeof t||!("value"in t))return{value:t};const{value:i}=t;return{value:i,settings:p(t,E)}}function R(e,t,n){const{format:r}=$[e];return r?r(t,n):t}function S(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function k(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function T(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?k(Object(n),!0).forEach((function(t){S(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const D=(e,t,n)=>e>n?n:e<t?t:e,j=e=>{if(""===e||"number"===typeof e)return e;try{const t=V(e);if(!isNaN(t))return t}catch(t){}return parseFloat(e)},P=Math.log(10);function _(e){let t=Math.abs(+String(e).replace(".",""));if(0===t)return.01;for(;0!==t&&t%10===0;)t/=10;const n=Math.floor(Math.log(t)/P)+1,r=Math.floor(Math.log10(Math.abs(e))),o=Math.pow(10,r-n);return Math.max(o,.001)}const A=(e,t,n)=>{if(n===t)return 0;return(D(e,t,n)-t)/(n-t)},I=(e,t,n)=>e*(n-t)+t,F=/\(([0-9+\-*/^ .]+)\)/,L=/(\d+(?:\.\d+)?) ?\^ ?(\d+(?:\.\d+)?)/,z=/(\d+(?:\.\d+)?) ?\* ?(\d+(?:\.\d+)?)/,N=/(\d+(?:\.\d+)?) ?\/ ?(\d+(?:\.\d+)?)/,M=/(\d+(?:\.\d+)?) ?\+ ?(\d+(?:\.\d+)?)/,U=/(\d+(?:\.\d+)?) ?- ?(\d+(?:\.\d+)?)/;function V(e){if(isNaN(Number(e))){if(F.test(e)){const t=e.replace(F,((e,t)=>String(V(t))));return V(t)}if(L.test(e)){return V(e.replace(L,((e,t,n)=>String(Math.pow(Number(t),Number(n))))))}if(z.test(e)){return V(e.replace(z,((e,t,n)=>String(Number(t)*Number(n)))))}if(N.test(e)){return V(e.replace(N,((e,t,n)=>{if(0!=n)return String(Number(t)/Number(n));throw new Error("Division by zero")})))}if(M.test(e)){return V(e.replace(M,((e,t,n)=>String(Number(t)+Number(n)))))}if(U.test(e)){return V(e.replace(U,((e,t,n)=>String(Number(t)-Number(n)))))}return Number(e)}return Number(e)}function H(e){return"[object Object]"===Object.prototype.toString.call(e)}let W,B;!function(e){e.BUTTON="BUTTON",e.BUTTON_GROUP="BUTTON_GROUP",e.MONITOR="MONITOR",e.FOLDER="FOLDER"}(W||(W={})),function(e){e.SELECT="SELECT",e.IMAGE="IMAGE",e.NUMBER="NUMBER",e.COLOR="COLOR",e.STRING="STRING",e.BOOLEAN="BOOLEAN",e.INTERVAL="INTERVAL",e.VECTOR3D="VECTOR3D",e.VECTOR2D="VECTOR2D"}(B||(B={}));const G=["type","__customInput"],K=["render","label","optional","order","disabled","hint","onChange","onEditStart","onEditEnd","transient"],Y=["type"];function Z(e,t,n={},r){var o,i;if("object"!==typeof e||Array.isArray(e))return{type:r,input:e,options:T({key:t,label:t,optional:!1,disabled:!1,order:0},n)};if("__customInput"in e){const{type:n,__customInput:r}=e;return Z(r,t,p(e,G),n)}const{render:a,label:l,optional:s,order:c=0,disabled:u,hint:d,onChange:f,onEditStart:g,onEditEnd:h,transient:m}=e,v=p(e,K),b=T({render:a,key:t,label:null!==l&&void 0!==l?l:t,hint:d,transient:null!==m&&void 0!==m?m:!!f,onEditStart:g,onEditEnd:h,disabled:u,optional:s,order:c},n);let y,{type:E}=v,w=p(v,Y);return E=null!==r&&void 0!==r?r:E,E in W?{type:E,input:w,options:b}:(r&&H(w)&&"value"in w?y=w.value:y=H($=w)&&0===Object.keys($).length?void 0:w,{type:E,input:y,options:T(T({},b),{},{onChange:f,optional:null!==(o=b.optional)&&void 0!==o&&o,disabled:null!==(i=b.disabled)&&void 0!==i&&i})});var $}function J(e,t,n,r,o){const{value:i,type:a,settings:l}=e;e.value=q({type:a,value:i,settings:l},t,n,r),e.fromPanel=o}const X=function(e,t,n){this.type="LEVA_ERROR",this.message="LEVA: "+e,this.previousValue=t,this.error=n};function q({type:e,value:t,settings:n},r,o,i){const l="SELECT"!==e&&"function"===typeof r?r(t):r;let s;try{s=function(e,t,n,r,o,i){const{sanitize:a}=$[e];return a?a(t,n,r,o,i):t}(e,l,n,t,o,i)}catch(c){throw new X(`The value \`${r}\` did not result in a correct value.`,t,c)}return(0,a.J)(s,t)?t:s}const Q=(e,t,n=!1)=>{let r=0;return function(){const o=arguments,i=n&&!r,a=()=>e.apply(this,o);window.clearTimeout(r),r=window.setTimeout(a,t),i&&a()}},ee=e=>e.shiftKey?5:e.altKey?.2:1;const te=["value"],ne=["min","max"],re=(e,{min:t=-1/0,max:n=1/0,suffix:r})=>{const o=parseFloat(e);if(""===e||isNaN(o))throw Error("Invalid number");const i=D(o,t,n);return r?i+r:i},oe=e=>{let{value:t}=e,n=p(e,te);const{min:r=-1/0,max:o=1/0}=n,i=p(n,ne);let a=parseFloat(t);const l="string"===typeof t?t.substring((""+a).length):void 0;a=D(a,r,o);let s=n.step;s||(Number.isFinite(r)?s=Number.isFinite(o)?+(Math.abs(o-r)/100).toPrecision(1):+(Math.abs(a-r)/100).toPrecision(1):Number.isFinite(o)&&(s=+(Math.abs(o-a)/100).toPrecision(1)));const c=s?10*_(s):_(a);s=s||c/10;return{value:l?a+l:a,settings:T({initialValue:a,step:s,pad:Math.round(D(Math.log10(1/c),0,2)),min:r,max:o,suffix:l},i)}},ie=(e,{step:t,initialValue:n})=>n+Math.round((e-n)/t)*t;var ae=Object.freeze({__proto__:null,schema:e=>{if("number"===typeof e)return!0;if("string"===typeof e){const t=parseFloat(e);if(isNaN(t))return!1;return e.substring((""+t).length).trim().length<4}return!1},sanitize:re,format:(e,{pad:t=0,suffix:n})=>{const r=parseFloat(e).toFixed(t);return n?r+n:r},normalize:oe,sanitizeStep:ie});function le(){return le=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},le.apply(this,arguments)}const se=(0,o.createContext)({});function ce(){return(0,o.useContext)(se)}const ue=(0,o.createContext)(null),de=(0,o.createContext)(null),pe=(0,o.createContext)(null);function fe(){return(0,o.useContext)(de)}function ge(e,t){const[n,r]=e.split(" "),o={};return"none"!==n&&(o.boxShadow=`${t.inset?"inset ":""}0 0 0 $borderWidths${[t.key]} $colors${"default"!==n&&n||t.borderColor}`),r&&(o.backgroundColor=r),o}const he={$inputStyle:()=>e=>ge(e,{key:"$input",borderColor:"$highlight1",inset:!0}),$focusStyle:()=>e=>ge(e,{key:"$focus",borderColor:"$accent2"}),$hoverStyle:()=>e=>ge(e,{key:"$hover",borderColor:"$accent1",inset:!0}),$activeStyle:()=>e=>ge(e,{key:"$active",borderColor:"$accent1",inset:!0})},{styled:me,css:ve,createTheme:be,globalCss:ye,keyframes:Ee}=(0,c.Th)({prefix:"leva",theme:{colors:{elevation1:"#292d39",elevation2:"#181c20",elevation3:"#373c4b",accent1:"#0066dc",accent2:"#007bff",accent3:"#3c93ff",highlight1:"#535760",highlight2:"#8c92a4",highlight3:"#fefefe",vivid1:"#ffcc00",folderWidgetColor:"$highlight2",folderTextColor:"$highlight3",toolTipBackground:"$highlight3",toolTipText:"$elevation2"},radii:{xs:"2px",sm:"3px",lg:"10px"},space:{xs:"3px",sm:"6px",md:"10px",rowGap:"7px",colGap:"7px"},fonts:{mono:"ui-monospace, SFMono-Regular, Menlo, 'Roboto Mono', monospace",sans:"system-ui, sans-serif"},fontSizes:{root:"11px",toolTip:"$root"},sizes:{rootWidth:"280px",controlWidth:"160px",numberInputMinWidth:"38px",scrubberWidth:"8px",scrubberHeight:"16px",rowHeight:"24px",folderTitleHeight:"20px",checkboxSize:"16px",joystickWidth:"100px",joystickHeight:"100px",colorPickerWidth:"$controlWidth",colorPickerHeight:"100px",imagePreviewWidth:"$controlWidth",imagePreviewHeight:"100px",monitorHeight:"60px",titleBarHeight:"39px"},shadows:{level1:"0 0 9px 0 #00000088",level2:"0 4px 14px #00000033"},borderWidths:{root:"0px",input:"1px",focus:"1px",hover:"1px",active:"1px",folder:"1px"},fontWeights:{label:"normal",folder:"normal",button:"normal"}},utils:T(T({},he),{},{$flex:()=>({display:"flex",alignItems:"center"}),$flexCenter:()=>({display:"flex",alignItems:"center",justifyContent:"center"}),$reset:()=>({outline:"none",fontSize:"inherit",fontWeight:"inherit",color:"inherit",fontFamily:"inherit",border:"none",backgroundColor:"transparent",appearance:"none"}),$draggable:()=>({touchAction:"none",WebkitUserDrag:"none",userSelect:"none"}),$focus:e=>({"&:focus":he.$focusStyle()(e)}),$focusWithin:e=>({"&:focus-within":he.$focusStyle()(e)}),$hover:e=>({"&:hover":he.$hoverStyle()(e)}),$active:e=>({"&:active":he.$activeStyle()(e)})})}),we=ye({".leva__panel__dragged":{WebkitUserDrag:"none",userSelect:"none",input:{userSelect:"none"},"*":{cursor:"ew-resize !important"}}});function $e(e,t){const{theme:n}=(0,o.useContext)(ue);if(!(e in n)||!(t in n[e]))return m(f.THEME_ERROR,e,t),"";let r=t;for(;;){let t=n[e][r];if("string"!==typeof t||"$"!==t.charAt(0))return t;r=t.substr(1)}}const xe=me("input",{$reset:"",padding:"0 $sm",width:0,minWidth:0,flex:1,height:"100%",variants:{levaType:{number:{textAlign:"right"}},as:{textarea:{padding:"$sm"}}}}),Oe=me("div",{$draggable:"",height:"100%",$flexCenter:"",position:"relative",padding:"0 $xs",fontSize:"0.8em",opacity:.8,cursor:"default",touchAction:"none",[`& + ${xe}`]:{paddingLeft:0}}),Ce=me(Oe,{cursor:"ew-resize",marginRight:"-$xs",textTransform:"uppercase",opacity:.3,"&:hover":{opacity:1},variants:{dragging:{true:{backgroundColor:"$accent2",opacity:1}}}}),Re=me("div",{$flex:"",position:"relative",borderRadius:"$sm",overflow:"hidden",color:"inherit",height:"$rowHeight",backgroundColor:"$elevation3",$inputStyle:"$elevation1",$hover:"",$focusWithin:"",variants:{textArea:{true:{height:"auto"}}}}),Se=["innerLabel","value","onUpdate","onChange","onKeyDown","type","id","inputType","rows"],ke=["onUpdate"];function Te(e){let{innerLabel:t,value:n,onUpdate:r,onChange:i,onKeyDown:a,type:l,id:s,inputType:c="text",rows:u=0}=e,d=p(e,Se);const{id:f,emitOnEditStart:g,emitOnEditEnd:h,disabled:m}=ce(),v=s||f,b=(0,o.useRef)(null),y=u>0,E=y?"textarea":"input",w=(0,o.useCallback)((e=>t=>{const n=t.currentTarget.value;e(n)}),[]);o.useEffect((()=>{const e=b.current,t=w((e=>{r(e),h()}));return null===e||void 0===e||e.addEventListener("blur",t),()=>null===e||void 0===e?void 0:e.removeEventListener("blur",t)}),[w,r,h]);const $=(0,o.useCallback)((e=>{"Enter"===e.key&&w(r)(e)}),[w,r]),x=Object.assign({as:E},y?{rows:u}:{},d);return o.createElement(Re,{textArea:y},t&&"string"===typeof t?o.createElement(Oe,null,t):t,o.createElement(xe,le({levaType:l,ref:b,id:v,type:c,autoComplete:"off",spellCheck:"false",value:n,onChange:w(i),onFocus:()=>g(),onKeyPress:$,onKeyDown:a,disabled:m},x)))}function De(e){let{onUpdate:t}=e,n=p(e,ke);const r=(0,o.useCallback)((e=>t(j(e))),[t]),i=(0,o.useCallback)((e=>{const n="ArrowUp"===e.key?1:"ArrowDown"===e.key?-1:0;if(n){e.preventDefault();const r=e.altKey?.1:e.shiftKey?10:1;t((e=>parseFloat(e)+n*r))}}),[t]);return o.createElement(Te,le({},n,{onUpdate:r,onKeyDown:i,type:"number"}))}const je=me("div",{}),Pe=me("div",{position:"relative",background:"$elevation2",transition:"height 300ms ease",variants:{fill:{true:{},false:{}},flat:{false:{},true:{}},isRoot:{true:{},false:{paddingLeft:"$md","&::after":{content:'""',position:"absolute",left:0,top:0,width:"$borderWidths$folder",height:"100%",backgroundColor:"$folderWidgetColor",opacity:.4,transform:"translateX(-50%)"}}}},compoundVariants:[{isRoot:!0,fill:!1,css:{overflowY:"auto",maxHeight:"calc(100vh - 20px - $$titleBarHeight)"}},{isRoot:!0,flat:!1,css:{borderRadius:"$lg"}}]}),_e=me("div",{$flex:"",color:"$folderTextColor",userSelect:"none",cursor:"pointer",height:"$folderTitleHeight",fontWeight:"$folder","> svg":{marginLeft:-4,marginRight:4,cursor:"pointer",fill:"$folderWidgetColor",opacity:.6},"&:hover > svg":{fill:"$folderWidgetColor"},[`&:hover + ${Pe}::after`]:{opacity:.6},[`${je}:hover > & + ${Pe}::after`]:{opacity:.6},[`${je}:hover > & > svg`]:{opacity:1}}),Ae=me("div",{position:"relative",display:"grid",gridTemplateColumns:"100%",rowGap:"$rowGap",transition:"opacity 250ms ease",variants:{toggled:{true:{opacity:1,transitionDelay:"250ms"},false:{opacity:0,transitionDelay:"0ms",pointerEvents:"none"}},isRoot:{true:{"& > div":{paddingLeft:"$md",paddingRight:"$md"},"& > div:first-of-type":{paddingTop:"$sm"},"& > div:last-of-type":{paddingBottom:"$sm"},[`> ${je}:not(:first-of-type)`]:{paddingTop:"$sm",marginTop:"$md",borderTop:"$borderWidths$folder solid $colors$elevation1"}}}}}),Ie=me("div",{position:"relative",zIndex:100,display:"grid",rowGap:"$rowGap",gridTemplateRows:"minmax($sizes$rowHeight, max-content)",alignItems:"center",color:"$highlight2",[`${Ae} > &`]:{"&:first-of-type":{marginTop:"$rowGap"},"&:last-of-type":{marginBottom:"$rowGap"}},variants:{disabled:{true:{pointerEvents:"none"},false:{"&:hover,&:focus-within":{color:"$highlight3"}}}}}),Fe=me(Ie,{gridTemplateColumns:"auto $sizes$controlWidth",columnGap:"$colGap"}),Le=me("div",{$flex:"",height:"100%",position:"relative",overflow:"hidden","& > div":{marginLeft:"$colGap",padding:"0 $xs",opacity:.4},"& > div:hover":{opacity:.8},"& > div > svg":{display:"none",cursor:"pointer",width:13,minWidth:13,height:13,backgroundColor:"$elevation2"},"&:hover > div > svg":{display:"block"},variants:{align:{top:{height:"100%",alignItems:"flex-start",paddingTop:"$sm"}}}}),ze=me("input",{$reset:"",height:0,width:0,opacity:0,margin:0,"& + label":{position:"relative",$flexCenter:"",height:"100%",userSelect:"none",cursor:"pointer",paddingLeft:2,paddingRight:"$sm",pointerEvents:"auto"},"& + label:after":{content:'""',width:6,height:6,backgroundColor:"$elevation3",borderRadius:"50%",$activeStyle:""},"&:focus + label:after":{$focusStyle:""},"& + label:active:after":{backgroundColor:"$accent1",$focusStyle:""},"&:checked + label:after":{backgroundColor:"$accent1"}}),Ne=me("label",{fontWeight:"$label",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap","& > svg":{display:"block"}}),Me=me("div",{opacity:1,variants:{disabled:{true:{opacity:.6,pointerEvents:"none",[`& ${Ne}`]:{pointerEvents:"auto"}}}}}),Ue=me("div",{position:"fixed",top:0,bottom:0,right:0,left:0,zIndex:1e3,userSelect:"none"}),Ve=me("div",{background:"$toolTipBackground",fontFamily:"$sans",fontSize:"$toolTip",padding:"$xs $sm",color:"$toolTipText",borderRadius:"$xs",boxShadow:"$level2",maxWidth:260}),He=me(d.Eh,{fill:"$toolTipBackground"});function We({children:e}){const{className:t}=(0,o.useContext)(ue);return o.createElement(i.f,{className:t},e)}const Be=["align"];function Ge(){const{id:e,disable:t,disabled:n}=ce();return o.createElement(o.Fragment,null,o.createElement(ze,{id:e+"__disable",type:"checkbox",checked:!n,onChange:()=>t(!n)}),o.createElement("label",{htmlFor:e+"__disable"}))}function Ke(e){const{id:t,optional:n,hint:r}=ce(),i=e.htmlFor||(t?{htmlFor:t}:null),a=r||"string"!==typeof e.children?null:{title:e.children};return o.createElement(o.Fragment,null,n&&o.createElement(Ge,null),void 0!==r?o.createElement(d.fC,null,o.createElement(d.xz,{asChild:!0},o.createElement(Ne,le({},i,e))),o.createElement(d.VY,{side:"top",sideOffset:2},o.createElement(Ve,null,r,o.createElement(He,null)))):o.createElement(Ne,le({},i,a,e)))}function Ye(e){let{align:t}=e,n=p(e,Be);const{value:r,label:i,key:a,disabled:l}=ce(),{hideCopyButton:s}=(0,o.useContext)(pe),c=!s&&void 0!==a,[u,d]=(0,o.useState)(!1);return o.createElement(Le,{align:t,onPointerLeave:()=>d(!1)},o.createElement(Ke,n),c&&!l&&o.createElement("div",{title:`Click to copy ${"string"===typeof i?i:a} value`},u?o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor"},o.createElement("path",{d:"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"}),o.createElement("path",{fillRule:"evenodd",d:"M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm9.707 5.707a1 1 0 00-1.414-1.414L9 12.586l-1.293-1.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})):o.createElement("svg",{onClick:async()=>{try{await navigator.clipboard.writeText(JSON.stringify({[a]:null!==r&&void 0!==r?r:""})),d(!0)}catch(e){m(f.CLIPBOARD_ERROR,{[a]:r})}},xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor"},o.createElement("path",{d:"M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"}),o.createElement("path",{d:"M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"}))))}const Ze=["toggled"],Je=me("svg",{fill:"currentColor",transition:"transform 350ms ease, fill 250ms ease"});function Xe(e){let{toggled:t}=e,n=p(e,Ze);return o.createElement(Je,le({width:"9",height:"5",viewBox:"0 0 9 5",xmlns:"http://www.w3.org/2000/svg",style:{transform:`rotate(${t?0:-90}deg)`}},n),o.createElement("path",{d:"M3.8 4.4c.4.3 1 .3 1.4 0L8 1.7A1 1 0 007.4 0H1.6a1 1 0 00-.7 1.7l3 2.7z"}))}const qe=["input"];function Qe(e){let{input:t}=e,n=p(e,qe);return t?o.createElement(Fe,n):o.createElement(Ie,n)}function et({value:e,type:t,settings:n,setValue:r}){const[i,l]=(0,o.useState)(R(t,e,n)),s=(0,o.useRef)(e),c=(0,o.useRef)(n);c.current=n;const u=(0,o.useCallback)((e=>l(R(t,e,c.current))),[t]),d=(0,o.useCallback)((e=>{try{r(e)}catch(t){const{type:e,previousValue:n}=t;if("LEVA_ERROR"!==e)throw t;u(n)}}),[u,r]);return(0,o.useEffect)((()=>{(0,a.J)(e,s.current)||u(e),s.current=e}),[e,u]),{displayValue:i,onChange:l,onUpdate:d}}function tt(e,t){const{emitOnEditStart:n,emitOnEditEnd:r}=ce();return(0,u.c0)((t=>{t.first&&(document.body.classList.add("leva__panel__dragged"),null===n||void 0===n||n());const o=e(t);return t.last&&(document.body.classList.remove("leva__panel__dragged"),null===r||void 0===r||r()),o}),t)}function nt(){const e=(0,o.useRef)(null),t=(0,o.useRef)({x:0,y:0}),n=(0,o.useCallback)((n=>{Object.assign(t.current,n),e.current&&(e.current.style.transform=`translate3d(${t.current.x}px, ${t.current.y}px, 0)`)}),[]);return[e,n]}const rt=["__refCount"],ot=(e,t)=>{if(!e[t])return null;return p(e[t],rt)};const it=me("div",{variants:{hasRange:{true:{position:"relative",display:"grid",gridTemplateColumns:"auto $sizes$numberInputMinWidth",columnGap:"$colGap",alignItems:"center"}}}}),at=me("div",{position:"relative",width:"100%",height:2,borderRadius:"$xs",backgroundColor:"$elevation1"}),lt=me("div",{position:"absolute",width:"$scrubberWidth",height:"$scrubberHeight",borderRadius:"$xs",boxShadow:"0 0 0 2px $colors$elevation2",backgroundColor:"$accent2",cursor:"pointer",$active:"none $accent1",$hover:"none $accent3",variants:{position:{left:{borderTopRightRadius:0,borderBottomRightRadius:0,transform:"translateX(calc(-0.5 * ($sizes$scrubberWidth + 4px)))"},right:{borderTopLeftRadius:0,borderBottomLeftRadius:0,transform:"translateX(calc(0.5 * ($sizes$scrubberWidth + 4px)))"}}}}),st=me("div",{position:"relative",$flex:"",height:"100%",cursor:"pointer",touchAction:"none"}),ct=me("div",{position:"absolute",height:"100%",backgroundColor:"$accent2"});function ut({value:e,min:t,max:n,onDrag:r,step:i,initialValue:a}){const l=(0,o.useRef)(null),s=(0,o.useRef)(null),c=(0,o.useRef)(0),u=$e("sizes","scrubberWidth"),d=tt((({event:o,first:d,xy:[p],movement:[f],memo:g})=>{if(d){const{width:r,left:i}=l.current.getBoundingClientRect();c.current=r-parseFloat(u);g=(null===o||void 0===o?void 0:o.target)===s.current?e:I((p-i)/r,t,n)}const h=g+I(f/c.current,0,n-t);return r(ie(h,{step:i,initialValue:a})),g})),p=A(e,t,n);return o.createElement(st,le({ref:l},d()),o.createElement(at,null,o.createElement(ct,{style:{left:0,right:100*(1-p)+"%"}})),o.createElement(lt,{ref:s,style:{left:`calc(${p} * (100% - ${u}))`}}))}const dt=o.memo((({label:e,onUpdate:t,step:n,innerLabelTrim:r})=>{const[i,a]=(0,o.useState)(!1),l=tt((({active:e,delta:[r],event:o,memo:i=0})=>(a(e),i+=r/2,Math.abs(i)>=1&&(t((e=>parseFloat(e)+Math.floor(i)*n*ee(o))),i=0),i)));return o.createElement(Ce,le({dragging:i,title:e.length>1?e:""},l()),e.slice(0,r))}));function pt({label:e,id:t,displayValue:n,onUpdate:r,onChange:i,settings:a,innerLabelTrim:l=1}){const s=l>0&&o.createElement(dt,{label:e,step:a.step,onUpdate:r,innerLabelTrim:l});return o.createElement(De,{id:t,value:String(n),onUpdate:r,onChange:i,innerLabel:s})}const{sanitizeStep:ft}=ae;var gt=T({component:function(){const e=ce(),{label:t,value:n,onUpdate:r,settings:i,id:a}=e,{min:l,max:s}=i,c=s!==1/0&&l!==-1/0;return o.createElement(Qe,{input:!0},o.createElement(Ye,null,t),o.createElement(it,{hasRange:c},c&&o.createElement(ut,le({value:parseFloat(n),onDrag:r},i)),o.createElement(pt,le({},e,{id:a,label:"value",innerLabelTrim:c?0:1}))))}},p(ae,["sanitizeStep"]));var ht=Object.freeze({__proto__:null,schema:(e,t)=>(0,s.Z)().schema({options:(0,s.Z)().passesAnyOf((0,s.Z)().object(),(0,s.Z)().array())}).test(t),sanitize:(e,{values:t})=>{if(t.indexOf(e)<0)throw Error("Selected value doesn't match Select options");return e},format:(e,{values:t})=>t.indexOf(e),normalize:e=>{let t,n,{value:r,options:o}=e;return Array.isArray(o)?(n=o,t=o.map((e=>String(e)))):(n=Object.values(o),t=Object.keys(o)),"value"in e?n.includes(r)||(t.unshift(String(r)),n.unshift(r)):r=n[0],Object.values(o).includes(r)||(o[String(r)]=r),{value:r,settings:{keys:t,values:n}}}});const mt=me("div",{$flexCenter:"",position:"relative","> svg":{pointerEvents:"none",position:"absolute",right:"$md"}}),vt=me("select",{position:"absolute",top:0,left:0,width:"100%",height:"100%",opacity:0}),bt=me("div",{display:"flex",alignItems:"center",width:"100%",height:"$rowHeight",backgroundColor:"$elevation3",borderRadius:"$sm",padding:"0 $sm",cursor:"pointer",[`${vt}:focus + &`]:{$focusStyle:""},[`${vt}:hover + &`]:{$hoverStyle:""}});function yt({displayValue:e,value:t,onUpdate:n,id:r,settings:i,disabled:a}){const{keys:l,values:s}=i,c=(0,o.useRef)();return t===s[e]&&(c.current=l[e]),o.createElement(mt,null,o.createElement(vt,{id:r,value:e,onChange:e=>n(s[Number(e.currentTarget.value)]),disabled:a},l.map(((e,t)=>o.createElement("option",{key:e,value:t},e)))),o.createElement(bt,null,c.current),o.createElement(Xe,{toggled:!0}))}var Et=T({component:function(){const{label:e,value:t,displayValue:n,onUpdate:r,id:i,disabled:a,settings:l}=ce();return o.createElement(Qe,{input:!0},o.createElement(Ye,null,e),o.createElement(yt,{id:i,value:t,displayValue:n,onUpdate:r,settings:l,disabled:a}))}},ht);var wt=Object.freeze({__proto__:null,schema:e=>(0,s.Z)().string().test(e),sanitize:e=>{if("string"!==typeof e)throw Error("Invalid string");return e},normalize:({value:e,editable:t=!0,rows:n=!1})=>({value:e,settings:{editable:t,rows:"number"===typeof n?n:n?5:0}})});const $t=["displayValue","onUpdate","onChange","editable"],xt=me("div",{whiteSpace:"pre-wrap"});function Ot(e){let{displayValue:t,onUpdate:n,onChange:r,editable:i=!0}=e,a=p(e,$t);return i?o.createElement(Te,le({value:t,onUpdate:n,onChange:r},a)):o.createElement(xt,null,t)}var Ct=T({component:function(){const{label:e,settings:t,displayValue:n,onUpdate:r,onChange:i}=ce();return o.createElement(Qe,{input:!0},o.createElement(Ye,null,e),o.createElement(Ot,le({displayValue:n,onUpdate:r,onChange:i},t)))}},wt);var Rt=Object.freeze({__proto__:null,schema:e=>(0,s.Z)().boolean().test(e),sanitize:e=>{if("boolean"!==typeof e)throw Error("Invalid boolean");return e}});const St=me("div",{position:"relative",$flex:"",height:"$rowHeight",input:{$reset:"",height:0,width:0,opacity:0,margin:0},label:{position:"relative",$flexCenter:"",userSelect:"none",cursor:"pointer",height:"$checkboxSize",width:"$checkboxSize",backgroundColor:"$elevation3",borderRadius:"$sm",$hover:""},"input:focus + label":{$focusStyle:""},"input:focus:checked + label, input:checked + label:hover":{$hoverStyle:"$accent3"},"input + label:active":{backgroundColor:"$accent1"},"input:checked + label:active":{backgroundColor:"$accent1"},"label > svg":{display:"none",width:"90%",height:"90%",stroke:"$highlight3"},"input:checked + label":{backgroundColor:"$accent2"},"input:checked + label > svg":{display:"block"}});function kt({value:e,onUpdate:t,id:n,disabled:r}){return o.createElement(St,null,o.createElement("input",{id:n,type:"checkbox",checked:e,onChange:e=>t(e.currentTarget.checked),disabled:r}),o.createElement("label",{htmlFor:n},o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"}))))}var Tt=T({component:function(){const{label:e,value:t,onUpdate:n,disabled:r,id:i}=ce();return o.createElement(Qe,{input:!0},o.createElement(Ye,null,e),o.createElement(kt,{value:t,onUpdate:n,id:i,disabled:r}))}},Rt);const Dt=["locked"];function jt({value:e,id:t,valueKey:n,settings:r,onUpdate:i,innerLabelTrim:a}){const l=(0,o.useRef)(e[n]);l.current=e[n];const s=(0,o.useCallback)((e=>i({[n]:q({type:"NUMBER",value:l.current,settings:r},e)})),[i,r,n]),c=et({type:"NUMBER",value:e[n],settings:r,setValue:s});return o.createElement(pt,{id:t,label:n,value:e[n],displayValue:c.displayValue,onUpdate:c.onUpdate,onChange:c.onChange,settings:r,innerLabelTrim:a})}const Pt=me("div",{display:"grid",columnGap:"$colGap",gridAutoFlow:"column dense",alignItems:"center",variants:{withLock:{true:{gridTemplateColumns:"10px auto","> svg":{cursor:"pointer"}}}}});function _t(e){let{locked:t}=e,n=p(e,Dt);return o.createElement("svg",le({width:"10",height:"10",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"},n),t?o.createElement("path",{d:"M5 4.63601C5 3.76031 5.24219 3.1054 5.64323 2.67357C6.03934 2.24705 6.64582 1.9783 7.5014 1.9783C8.35745 1.9783 8.96306 2.24652 9.35823 2.67208C9.75838 3.10299 10 3.75708 10 4.63325V5.99999H5V4.63601ZM4 5.99999V4.63601C4 3.58148 4.29339 2.65754 4.91049 1.99307C5.53252 1.32329 6.42675 0.978302 7.5014 0.978302C8.57583 0.978302 9.46952 1.32233 10.091 1.99162C10.7076 2.65557 11 3.57896 11 4.63325V5.99999H12C12.5523 5.99999 13 6.44771 13 6.99999V13C13 13.5523 12.5523 14 12 14H3C2.44772 14 2 13.5523 2 13V6.99999C2 6.44771 2.44772 5.99999 3 5.99999H4ZM3 6.99999H12V13H3V6.99999Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}):o.createElement("path",{d:"M9 3.63601C9 2.76044 9.24207 2.11211 9.64154 1.68623C10.0366 1.26502 10.6432 1 11.5014 1C12.4485 1 13.0839 1.30552 13.4722 1.80636C13.8031 2.23312 14 2.84313 14 3.63325H15C15 2.68242 14.7626 1.83856 14.2625 1.19361C13.6389 0.38943 12.6743 0 11.5014 0C10.4294 0 9.53523 0.337871 8.91218 1.0021C8.29351 1.66167 8 2.58135 8 3.63601V6H1C0.447715 6 0 6.44772 0 7V13C0 13.5523 0.447715 14 1 14H10C10.5523 14 11 13.5523 11 13V7C11 6.44772 10.5523 6 10 6H9V3.63601ZM1 7H10V13H1V7Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}))}function At({value:e,onUpdate:t,settings:n,innerLabelTrim:r}){const{id:i,setSettings:a}=ce(),{lock:l,locked:s}=n;return o.createElement(Pt,{withLock:l},l&&o.createElement(_t,{locked:s,onClick:()=>a({locked:!s})}),Object.keys(e).map(((a,l)=>o.createElement(jt,{id:0===l?i:`${i}.${a}`,key:a,valueKey:a,value:e,settings:n[a],onUpdate:t,innerLabelTrim:r}))))}const It=(e,t)=>{const n={};let r=0,o=1/0;Object.entries(e).forEach((([e,i])=>{n[e]=oe(T({value:i},t[e])).settings,r=Math.max(r,n[e].step),o=Math.min(o,n[e].pad)}));for(let i in n){const{step:e,min:a,max:l}=t[i]||{};isFinite(e)||isFinite(a)&&isFinite(l)||(n[i].step=r,n[i].pad=o)}return n},Ft=["lock"],Lt=["value"];function zt(e){const t=(0,s.Z)().array().length(e).every.number();return n=>t.test(n)||(t=>{if(!t||"object"!==typeof t)return!1;const n=Object.values(t);return n.length===e&&n.every((e=>isFinite(e)))})(n)}function Nt(e,t,n){return function(e){return Array.isArray(e)?"array":"object"}(e)===t?e:"array"===t?Object.values(e):function(e,t){return e.reduce(((e,n,r)=>Object.assign(e,{[t[r]]:n})),{})}(e,n)}function Mt(e){return{schema:zt(e.length),normalize:t=>{let{value:n}=t;return function(e,t,n=[]){const{lock:r=!1}=t,o=p(t,Ft),i=Array.isArray(e)?"array":"object",a="object"===i?Object.keys(e):n,l=Nt(e,"object",a),s=(c=o)&&("step"in c||"min"in c||"max"in c)?a.reduce(((e,t)=>Object.assign(e,{[t]:o})),{}):o;var c;return{value:"array"===i?e:l,settings:T(T({},It(l,s)),{},{format:i,keys:a,lock:r,locked:!1})}}(n,p(t,Lt),e)},format:(e,t)=>((e,t)=>Nt(e,"object",t.keys))(e,t),sanitize:(e,t,n)=>((e,t,n)=>{const r=Nt(e,"object",t.keys);for(let a in r)r[a]=re(r[a],t[a]);const o=Object.keys(r);let i={};if(o.length===t.keys.length)i=r;else{const e=Nt(n,"object",t.keys);if(1===o.length&&t.locked){const t=o[0],n=r[t],a=e[t],l=0!==a?n/a:1;for(let r in e)r===t?i[t]=n:i[r]=e[r]*l}else i=T(T({},e),r)}return Nt(i,t.format,t.keys)})(e,t,n)}}var Ut=n(16765),Vt=n(83933),Ht=n(55651),Wt=n(2652),Bt=n.n(Wt),Gt=n(24699),Kt=n(69135);function Yt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Zt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Yt(Object(n),!0).forEach((function(t){Jt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Yt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Jt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Xt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,i=[],a=!0,l=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(s){l=!0,o=s}finally{try{a||null==n.return||n.return()}finally{if(l)throw o}}return i}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return qt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return qt(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Qt="file-invalid-type",en="file-too-large",tn="file-too-small",nn="too-many-files",rn=function(e){e=Array.isArray(e)&&1===e.length?e[0]:e;var t=Array.isArray(e)?"one of ".concat(e.join(", ")):e;return{code:Qt,message:"File type must be ".concat(t)}},on=function(e){return{code:en,message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},an=function(e){return{code:tn,message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},ln={code:nn,message:"Too many files"};function sn(e,t){var n="application/x-moz-file"===e.type||(0,Kt.Z)(e,t);return[n,n?null:rn(t)]}function cn(e,t,n){if(un(e.size))if(un(t)&&un(n)){if(e.size>n)return[!1,on(n)];if(e.size<t)return[!1,an(t)]}else{if(un(t)&&e.size<t)return[!1,an(t)];if(un(n)&&e.size>n)return[!1,on(n)]}return[!0,null]}function un(e){return void 0!==e&&null!==e}function dn(e){var t=e.files,n=e.accept,r=e.minSize,o=e.maxSize,i=e.multiple,a=e.maxFiles;return!(!i&&t.length>1||i&&a>=1&&t.length>a)&&t.every((function(e){var t=Xt(sn(e,n),1)[0],i=Xt(cn(e,r,o),1)[0];return t&&i}))}function pn(e){return"function"===typeof e.isPropagationStopped?e.isPropagationStopped():"undefined"!==typeof e.cancelBubble&&e.cancelBubble}function fn(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,(function(e){return"Files"===e||"application/x-moz-file"===e})):!!e.target&&!!e.target.files}function gn(e){e.preventDefault()}function hn(e){return-1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")}function mn(e){return-1!==e.indexOf("Edge/")}function vn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return hn(e)||mn(e)}function bn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return t.some((function(t){return!pn(e)&&t&&t.apply(void 0,[e].concat(r)),pn(e)}))}}function yn(){return"showOpenFilePicker"in window}function En(e){return e="string"===typeof e?e.split(","):e,[{description:"everything",accept:Array.isArray(e)?e.filter((function(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||/\w+\/[-+.\w]+/g.test(e)})).reduce((function(e,t){return Zt(Zt({},e),{},Jt({},t,[]))}),{}):{}}]}function wn(e){return e instanceof DOMException&&("AbortError"===e.name||e.code===e.ABORT_ERR)}function $n(e){return e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)}var xn=["children"],On=["open"],Cn=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],Rn=["refKey","onChange","onClick"];function Sn(e){return function(e){if(Array.isArray(e))return Dn(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Tn(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function kn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,i=[],a=!0,l=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(s){l=!0,o=s}finally{try{a||null==n.return||n.return()}finally{if(l)throw o}}return i}(e,t)||Tn(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tn(e,t){if(e){if("string"===typeof e)return Dn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Dn(e,t):void 0}}function Dn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function jn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Pn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?jn(Object(n),!0).forEach((function(t){_n(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):jn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function _n(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function An(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var In=(0,o.forwardRef)((function(e,t){var n=e.children,r=zn(An(e,xn)),i=r.open,a=An(r,On);return(0,o.useImperativeHandle)(t,(function(){return{open:i}}),[i]),o.createElement(o.Fragment,null,n(Pn(Pn({},a),{},{open:i})))}));In.displayName="Dropzone";var Fn={disabled:!1,getFilesFromEvent:Gt.R,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!0};In.defaultProps=Fn,In.propTypes={children:Bt().func,accept:Bt().oneOfType([Bt().string,Bt().arrayOf(Bt().string)]),multiple:Bt().bool,preventDropOnDocument:Bt().bool,noClick:Bt().bool,noKeyboard:Bt().bool,noDrag:Bt().bool,noDragEventsBubbling:Bt().bool,minSize:Bt().number,maxSize:Bt().number,maxFiles:Bt().number,disabled:Bt().bool,getFilesFromEvent:Bt().func,onFileDialogCancel:Bt().func,onFileDialogOpen:Bt().func,useFsAccessApi:Bt().bool,onDragEnter:Bt().func,onDragLeave:Bt().func,onDragOver:Bt().func,onDrop:Bt().func,onDropAccepted:Bt().func,onDropRejected:Bt().func,validator:Bt().func};var Ln={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,draggedFiles:[],acceptedFiles:[],fileRejections:[]};function zn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=Pn(Pn({},Fn),e),n=t.accept,r=t.disabled,i=t.getFilesFromEvent,a=t.maxSize,l=t.minSize,s=t.multiple,c=t.maxFiles,u=t.onDragEnter,d=t.onDragLeave,p=t.onDragOver,f=t.onDrop,g=t.onDropAccepted,h=t.onDropRejected,m=t.onFileDialogCancel,v=t.onFileDialogOpen,b=t.useFsAccessApi,y=t.preventDropOnDocument,E=t.noClick,w=t.noKeyboard,$=t.noDrag,x=t.noDragEventsBubbling,O=t.validator,C=(0,o.useMemo)((function(){return"function"===typeof v?v:Mn}),[v]),R=(0,o.useMemo)((function(){return"function"===typeof m?m:Mn}),[m]),S=(0,o.useRef)(null),k=(0,o.useRef)(null),T=(0,o.useReducer)(Nn,Ln),D=kn(T,2),j=D[0],P=D[1],_=j.isFocused,A=j.isFileDialogActive,I=j.draggedFiles,F=(0,o.useRef)("undefined"!==typeof window&&window.isSecureContext&&b&&yn()),L=function(){!F.current&&A&&setTimeout((function(){k.current&&(k.current.files.length||(P({type:"closeDialog"}),R()))}),300)};(0,o.useEffect)((function(){return window.addEventListener("focus",L,!1),function(){window.removeEventListener("focus",L,!1)}}),[k,A,R,F]);var z=(0,o.useRef)([]),N=function(e){S.current&&S.current.contains(e.target)||(e.preventDefault(),z.current=[])};(0,o.useEffect)((function(){return y&&(document.addEventListener("dragover",gn,!1),document.addEventListener("drop",N,!1)),function(){y&&(document.removeEventListener("dragover",gn),document.removeEventListener("drop",N))}}),[S,y]);var M=(0,o.useCallback)((function(e){e.preventDefault(),e.persist(),Q(e),z.current=[].concat(Sn(z.current),[e.target]),fn(e)&&Promise.resolve(i(e)).then((function(t){pn(e)&&!x||(P({draggedFiles:t,isDragActive:!0,type:"setDraggedFiles"}),u&&u(e))}))}),[i,u,x]),U=(0,o.useCallback)((function(e){e.preventDefault(),e.persist(),Q(e);var t=fn(e);if(t&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(n){}return t&&p&&p(e),!1}),[p,x]),V=(0,o.useCallback)((function(e){e.preventDefault(),e.persist(),Q(e);var t=z.current.filter((function(e){return S.current&&S.current.contains(e)})),n=t.indexOf(e.target);-1!==n&&t.splice(n,1),z.current=t,t.length>0||(P({isDragActive:!1,type:"setDraggedFiles",draggedFiles:[]}),fn(e)&&d&&d(e))}),[S,d,x]),H=(0,o.useCallback)((function(e,t){var r=[],o=[];e.forEach((function(e){var t=kn(sn(e,n),2),i=t[0],s=t[1],c=kn(cn(e,l,a),2),u=c[0],d=c[1],p=O?O(e):null;if(i&&u&&!p)r.push(e);else{var f=[s,d];p&&(f=f.concat(p)),o.push({file:e,errors:f.filter((function(e){return e}))})}})),(!s&&r.length>1||s&&c>=1&&r.length>c)&&(r.forEach((function(e){o.push({file:e,errors:[ln]})})),r.splice(0)),P({acceptedFiles:r,fileRejections:o,type:"setFiles"}),f&&f(r,o,t),o.length>0&&h&&h(o,t),r.length>0&&g&&g(r,t)}),[P,s,n,l,a,c,f,g,h,O]),W=(0,o.useCallback)((function(e){e.preventDefault(),e.persist(),Q(e),z.current=[],fn(e)&&Promise.resolve(i(e)).then((function(t){pn(e)&&!x||H(t,e)})),P({type:"reset"})}),[i,H,x]),B=(0,o.useCallback)((function(){if(F.current){P({type:"openDialog"}),C();var e={multiple:s,types:En(n)};window.showOpenFilePicker(e).then((function(e){return i(e)})).then((function(e){H(e,null),P({type:"closeDialog"})})).catch((function(e){wn(e)?(R(e),P({type:"closeDialog"})):$n(e)&&(F.current=!1,k.current&&(k.current.value=null,k.current.click()))}))}else k.current&&(P({type:"openDialog"}),C(),k.current.value=null,k.current.click())}),[P,C,R,b,H,n,s]),G=(0,o.useCallback)((function(e){S.current&&S.current.isEqualNode(e.target)&&(" "!==e.key&&"Enter"!==e.key&&32!==e.keyCode&&13!==e.keyCode||(e.preventDefault(),B()))}),[S,B]),K=(0,o.useCallback)((function(){P({type:"focus"})}),[]),Y=(0,o.useCallback)((function(){P({type:"blur"})}),[]),Z=(0,o.useCallback)((function(){E||(vn()?setTimeout(B,0):B())}),[E,B]),J=function(e){return r?null:e},X=function(e){return w?null:J(e)},q=function(e){return $?null:J(e)},Q=function(e){x&&e.stopPropagation()},ee=(0,o.useMemo)((function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,n=void 0===t?"ref":t,o=e.role,i=e.onKeyDown,a=e.onFocus,l=e.onBlur,s=e.onClick,c=e.onDragEnter,u=e.onDragOver,d=e.onDragLeave,p=e.onDrop,f=An(e,Cn);return Pn(Pn(_n({onKeyDown:X(bn(i,G)),onFocus:X(bn(a,K)),onBlur:X(bn(l,Y)),onClick:J(bn(s,Z)),onDragEnter:q(bn(c,M)),onDragOver:q(bn(u,U)),onDragLeave:q(bn(d,V)),onDrop:q(bn(p,W)),role:"string"===typeof o&&""!==o?o:"button"},n,S),r||w?{}:{tabIndex:0}),f)}}),[S,G,K,Y,Z,M,U,V,W,w,$,r]),te=(0,o.useCallback)((function(e){e.stopPropagation()}),[]),ne=(0,o.useMemo)((function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,r=void 0===t?"ref":t,o=e.onChange,i=e.onClick,a=An(e,Rn),l=_n({accept:n,multiple:s,type:"file",style:{display:"none"},onChange:J(bn(o,W)),onClick:J(bn(i,te)),tabIndex:-1},r,k);return Pn(Pn({},l),a)}}),[k,n,s,W,r]),re=I.length,oe=re>0&&dn({files:I,accept:n,minSize:l,maxSize:a,multiple:s,maxFiles:c}),ie=re>0&&!oe;return Pn(Pn({},j),{},{isDragAccept:oe,isDragReject:ie,isFocused:_&&!r,getRootProps:ee,getInputProps:ne,rootRef:S,inputRef:k,open:J(B)})}function Nn(e,t){switch(t.type){case"focus":return Pn(Pn({},e),{},{isFocused:!0});case"blur":return Pn(Pn({},e),{},{isFocused:!1});case"openDialog":return Pn(Pn({},Ln),{},{isFileDialogActive:!0});case"closeDialog":return Pn(Pn({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":var n=t.isDragActive,r=t.draggedFiles;return Pn(Pn({},e),{},{draggedFiles:r,isDragActive:n});case"setFiles":return Pn(Pn({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections});case"reset":return Pn({},Ln);default:return e}}function Mn(){}function Un(e){let t;const n=new Set,r=(e,r)=>{const o="function"===typeof e?e(t):e;if(o!==t){const e=t;t=r?o:Object.assign({},t,o),n.forEach((n=>n(t,e)))}},o=()=>t,i={setState:r,getState:o,subscribe:(e,r,i)=>r||i?((e,r=o,i=Object.is)=>{console.warn("[DEPRECATED] Please use `subscribeWithSelector` middleware");let a=r(t);function l(){const n=r(t);if(!i(a,n)){const t=a;e(a=n,t)}}return n.add(l),()=>n.delete(l)})(e,r,i):(n.add(e),()=>n.delete(e)),destroy:()=>n.clear()};return t=e(r,o,i),i}const Vn="undefined"===typeof window||!window.navigator||/ServerSideRendering|^Deno\//.test(window.navigator.userAgent)?o.useEffect:o.useLayoutEffect;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;var Hn=n(32597),Wn=n.n(Hn);const Bn=(...e)=>e.filter(Boolean).join(".");function Gn(e,t){return(0,o.useMemo)(e,function(e,t){const n=(0,o.useRef)();return(t?a.J:l)(e,n.current)||(n.current=e),n.current}(t,!0))}function Kn(e,t,n){const r=e.useStore((e=>function(e,t){return Object.entries((n=e,r=t,r.reduce(((e,t)=>(n&&n.hasOwnProperty(t)&&(e[t]=n[t]),e)),{}))).reduce(((e,[,{value:t,disabled:n,key:r}])=>(e[r]=n?void 0:t,e)),{});var n,r}(T(T({},n),e.data),t)),l);return r}function Yn(e=3){const t=(0,o.useRef)(null),n=(0,o.useRef)(null),[r,i]=(0,o.useState)(!1),a=(0,o.useCallback)((()=>i(!0)),[]),l=(0,o.useCallback)((()=>i(!1)),[]);return(0,o.useLayoutEffect)((()=>{if(r){const{bottom:r,top:o,left:i}=t.current.getBoundingClientRect(),{height:a}=n.current.getBoundingClientRect(),l=r+a>window.innerHeight-40?"up":"down";n.current.style.position="fixed",n.current.style.zIndex="10000",n.current.style.left=i+"px","down"===l?n.current.style.top=r+e+"px":n.current.style.bottom=window.innerHeight-o+e+"px"}}),[e,r]),{popinRef:t,wrapperRef:n,shown:r,show:a,hide:l}}(0,Ut.l7)([Vt.Z]);const Zn={rgb:"toRgb",hsl:"toHsl",hsv:"toHsv",hex:"toHex"};s.Z.extend({color:()=>e=>(0,Ut.Vi)(e).isValid()});function Jn(e,{format:t,hasAlpha:n,isString:r}){const o=e[Zn[t]+(r&&"hex"!==t?"String":"")]();return"object"!==typeof o||n?o:function(e,t){const n=T({},e);return t.forEach((t=>t in e&&delete n[t])),n}(o,["a"])}const Xn=(e,t)=>{const n=(0,Ut.Vi)(e);if(!n.isValid())throw Error("Invalid color");return Jn(n,t)};var qn=Object.freeze({__proto__:null,schema:e=>(0,s.Z)().color().test(e),sanitize:Xn,format:(e,t)=>Jn((0,Ut.Vi)(e),T(T({},t),{},{isString:!0,format:"hex"})),normalize:({value:e})=>{const t=(0,Ut.y6)(e),n={format:"name"===t?"hex":t,hasAlpha:"object"===typeof e?"a"in e:"hex"===t&&8===e.length||/^(rgba)|(hsla)|(hsva)/.test(e),isString:"string"===typeof e};return{value:Xn(e,n),settings:n}}});const Qn=me("div",{position:"relative",boxSizing:"border-box",borderRadius:"$sm",overflow:"hidden",cursor:"pointer",height:"$rowHeight",width:"$rowHeight",backgroundColor:"#fff",backgroundImage:'url(\'data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>\')',$inputStyle:"",$hover:"",zIndex:1,variants:{active:{true:{$inputStyle:"$accent1"}}},"&::before":{content:'""',position:"absolute",top:0,bottom:0,right:0,left:0,backgroundColor:"currentColor",zIndex:1}}),er=me("div",{position:"relative",display:"grid",gridTemplateColumns:"$sizes$rowHeight auto",columnGap:"$colGap",alignItems:"center"}),tr=me("div",{width:"$colorPickerWidth",height:"$colorPickerHeight",".react-colorful":{width:"100%",height:"100%",boxShadow:"$level2",cursor:"crosshair"},".react-colorful__saturation":{borderRadius:"$sm $sm 0 0"},".react-colorful__alpha, .react-colorful__hue":{height:10},".react-colorful__last-control":{borderRadius:"0 0 $sm $sm"},".react-colorful__pointer":{height:12,width:12}});function nr(e,t){return"rgb"!==t?(0,Ut.Vi)(e).toRgb():e}function rr({value:e,displayValue:t,settings:n,onUpdate:r}){const{emitOnEditStart:i,emitOnEditEnd:a}=ce(),{format:l,hasAlpha:s}=n,{popinRef:c,wrapperRef:u,shown:d,show:p,hide:f}=Yn(),g=(0,o.useRef)(0),[h,m]=(0,o.useState)((()=>nr(e,l))),v=s?Ht.Jg:Ht.Ts,b=()=>{f(),a(),window.clearTimeout(g.current)};return(0,o.useEffect)((()=>()=>window.clearTimeout(g.current)),[]),o.createElement(o.Fragment,null,o.createElement(Qn,{ref:c,active:d,onClick:()=>(m(nr(e,l)),p(),void i()),style:{color:t}}),d&&o.createElement(We,null,o.createElement(Ue,{onPointerUp:b}),o.createElement(tr,{ref:u,onMouseEnter:()=>window.clearTimeout(g.current),onMouseLeave:e=>0===e.buttons&&void(g.current=window.setTimeout(b,500))},o.createElement(v,{color:h,onChange:r}))))}var or=T({component:function(){const{value:e,displayValue:t,label:n,onChange:r,onUpdate:i,settings:a}=ce();return o.createElement(Qe,{input:!0},o.createElement(Ye,null,n),o.createElement(er,null,o.createElement(rr,{value:e,displayValue:t,onChange:r,onUpdate:i,settings:a}),o.createElement(Te,{value:t,onChange:r,onUpdate:i})))}},qn);var ir=T({component:function(){const{label:e,displayValue:t,onUpdate:n,settings:r}=ce();return o.createElement(Qe,{input:!0},o.createElement(Ye,null,e),o.createElement(At,{value:t,settings:r,onUpdate:n}))}},Mt(["x","y","z"]));const ar=me("div",{$flexCenter:"",position:"relative",backgroundColor:"$elevation3",borderRadius:"$sm",cursor:"pointer",height:"$rowHeight",width:"$rowHeight",touchAction:"none",$draggable:"",$hover:"","&:active":{cursor:"none"},"&::after":{content:'""',backgroundColor:"$accent2",height:4,width:4,borderRadius:2}}),lr=me("div",{$flexCenter:"",width:"$joystickWidth",height:"$joystickHeight",borderRadius:"$sm",boxShadow:"$level2",position:"fixed",zIndex:1e4,overflow:"hidden",$draggable:"",transform:"translate(-50%, -50%)",variants:{isOutOfBounds:{true:{backgroundColor:"$elevation1"},false:{backgroundColor:"$elevation3"}}},"> div":{position:"absolute",$flexCenter:"",borderStyle:"solid",borderWidth:1,borderColor:"$highlight1",backgroundColor:"$elevation3",width:"80%",height:"80%","&::after,&::before":{content:'""',position:"absolute",zindex:10,backgroundColor:"$highlight1"},"&::before":{width:"100%",height:1},"&::after":{height:"100%",width:1}},"> span":{position:"relative",zindex:100,width:10,height:10,backgroundColor:"$accent2",borderRadius:"50%"}});function sr({value:e,settings:t,onUpdate:n}){const r=(0,o.useRef)(),i=(0,o.useRef)(0),a=(0,o.useRef)(0),l=(0,o.useRef)(1),[s,c]=(0,o.useState)(!1),[u,d]=(0,o.useState)(!1),[p,f]=nt(),g=(0,o.useRef)(null),h=(0,o.useRef)(null);(0,o.useLayoutEffect)((()=>{if(s){const{top:e,left:t,width:n,height:r}=g.current.getBoundingClientRect();h.current.style.left=t+n/2+"px",h.current.style.top=e+r/2+"px"}}),[s]);const{keys:[m,v],joystick:b}=t,y="invertY"===b?1:-1,{[m]:{step:E},[v]:{step:w}}=t,$=$e("sizes","joystickWidth"),x=$e("sizes","joystickHeight"),O=.8*parseFloat($)/2,C=.8*parseFloat(x)/2,R=(0,o.useCallback)((()=>{r.current||(d(!0),i.current&&f({x:i.current*O}),a.current&&f({y:a.current*-C}),r.current=window.setInterval((()=>{n((e=>{const t=E*i.current*l.current,n=y*w*a.current*l.current;return Array.isArray(e)?{[m]:e[0]+t,[v]:e[1]+n}:{[m]:e[m]+t,[v]:e[v]+n}}))}),16))}),[O,C,n,f,E,w,m,v,y]),S=(0,o.useCallback)((()=>{window.clearTimeout(r.current),r.current=void 0,d(!1)}),[]);(0,o.useEffect)((()=>{function e(e){l.current=ee(e)}return window.addEventListener("keydown",e),window.addEventListener("keyup",e),()=>{window.clearTimeout(r.current),window.removeEventListener("keydown",e),window.removeEventListener("keyup",e)}}),[]);const k=tt((({first:t,active:r,delta:[o,s],movement:[u,d]})=>{t&&c(!0);const p=D(u,-O,O),g=D(d,-C,C);i.current=Math.abs(u)>Math.abs(p)?Math.sign(u-p):0,a.current=Math.abs(d)>Math.abs(g)?Math.sign(g-d):0;let h=e[m],b=e[v];r?(i.current||(h+=o*E*l.current,f({x:p})),a.current||(b-=y*s*w*l.current,f({y:g})),i.current||a.current?R():S(),n({[m]:h,[v]:b})):(c(!1),i.current=0,a.current=0,f({x:0,y:0}),S())}));return o.createElement(ar,le({ref:g},k()),s&&o.createElement(We,null,o.createElement(lr,{ref:h,isOutOfBounds:u},o.createElement("div",null),o.createElement("span",{ref:p}))))}const cr=me("div",{display:"grid",columnGap:"$colGap",variants:{withJoystick:{true:{gridTemplateColumns:"$sizes$rowHeight auto"},false:{gridTemplateColumns:"auto"}}}});const ur=["joystick"],dr=Mt(["x","y"]);var pr=T(T({component:function(){const{label:e,displayValue:t,onUpdate:n,settings:r}=ce();return o.createElement(Qe,{input:!0},o.createElement(Ye,null,e),o.createElement(cr,{withJoystick:!!r.joystick},r.joystick&&o.createElement(sr,{value:t,settings:r,onUpdate:n}),o.createElement(At,{value:t,settings:r,onUpdate:n})))}},dr),{},{normalize:e=>{let{joystick:t=!0}=e,n=p(e,ur);const{value:r,settings:o}=dr.normalize(n);return{value:r,settings:T(T({},o),{},{joystick:t})}}});var fr=Object.freeze({__proto__:null,sanitize:e=>{if(void 0!==e){if(e instanceof File)try{return URL.createObjectURL(e)}catch(t){return}if("string"===typeof e&&0===e.indexOf("blob:"))return e;throw Error("Invalid image format [undefined | blob |\xa0File].")}},schema:(e,t)=>"object"===typeof t&&"image"in t,normalize:({image:e})=>({value:e})});const gr=me("div",{position:"relative",display:"grid",gridTemplateColumns:"$sizes$rowHeight auto 20px",columnGap:"$colGap",alignItems:"center"}),hr=me("div",{$flexCenter:"",overflow:"hidden",height:"$rowHeight",background:"$elevation3",textAlign:"center",color:"inherit",borderRadius:"$sm",outline:"none",userSelect:"none",cursor:"pointer",$inputStyle:"",$hover:"",$focusWithin:"",$active:"$accent1 $elevation1",variants:{isDragAccept:{true:{$inputStyle:"$accent1",backgroundColor:"$elevation1"}}}}),mr=me("div",{boxSizing:"border-box",borderRadius:"$sm",height:"$rowHeight",width:"$rowHeight",$inputStyle:"",backgroundSize:"cover",backgroundPosition:"center",variants:{hasImage:{true:{cursor:"pointer",$hover:"",$active:""}}}}),vr=me("div",{$flexCenter:"",width:"$imagePreviewWidth",height:"$imagePreviewHeight",borderRadius:"$sm",boxShadow:"$level2",pointerEvents:"none",$inputStyle:"",backgroundSize:"cover",backgroundPosition:"center"}),br=me("div",{fontSize:"0.8em",height:"100%",padding:"$rowGap $md"}),yr=me("div",{$flexCenter:"",top:"0",right:"0",marginRight:"$sm",height:"100%",cursor:"pointer",variants:{disabled:{true:{color:"$elevation3",cursor:"default"}}},"&::after,&::before":{content:'""',position:"absolute",height:2,width:10,borderRadius:1,backgroundColor:"currentColor"},"&::after":{transform:"rotate(45deg)"},"&::before":{transform:"rotate(-45deg)"}});var Er=T({component:function(){const{label:e,value:t,onUpdate:n,disabled:r}=ce(),{popinRef:i,wrapperRef:a,shown:l,show:s,hide:c}=Yn(),u=(0,o.useCallback)((e=>{e.length&&n(e[0])}),[n]),d=(0,o.useCallback)((e=>{e.stopPropagation(),n(void 0)}),[n]),{getRootProps:p,getInputProps:f,isDragAccept:g}=zn({maxFiles:1,accept:"image/*",onDrop:u,disabled:r});return o.createElement(Qe,{input:!0},o.createElement(Ye,null,e),o.createElement(gr,null,o.createElement(mr,{ref:i,hasImage:!!t,onPointerDown:()=>!!t&&s(),onPointerUp:c,style:{backgroundImage:t?`url(${t})`:"none"}}),l&&!!t&&o.createElement(We,null,o.createElement(Ue,{onPointerUp:c,style:{cursor:"pointer"}}),o.createElement(vr,{ref:a,style:{backgroundImage:`url(${t})`}})),o.createElement(hr,p({isDragAccept:g}),o.createElement("input",f()),o.createElement(br,null,g?"drop image":"click or drop")),o.createElement(yr,{onClick:d,disabled:!t})))}},fr);const wr=(0,s.Z)().number(),$r=e=>({min:e[0],max:e[1]}),xr=(e,{bounds:[t,n]},r)=>{const o=Array.isArray(e)?$r(e):e,i={min:r[0],max:r[1]},{min:a,max:l}=T(T({},i),o);return[D(Number(a),t,Math.max(t,l)),D(Number(l),Math.min(n,a),n)]};var Or=Object.freeze({__proto__:null,schema:(e,t)=>(0,s.Z)().array().length(2).every.number().test(e)&&(0,s.Z)().schema({min:wr,max:wr}).test(t),format:$r,sanitize:xr,normalize:({value:e,min:t,max:n})=>{const r={min:t,max:n},o=[t,n],i=T(T({},It($r(e),{min:r,max:r})),{},{bounds:o});return{value:xr($r(e),i,e),settings:i}}});const Cr=["value","bounds","onDrag"],Rr=["bounds"],Sr=me("div",{display:"grid",columnGap:"$colGap",gridTemplateColumns:"auto calc($sizes$numberInputMinWidth * 2 + $space$rowGap)"});function kr(e){let{value:t,bounds:[n,r],onDrag:i}=e,a=p(e,Cr);const l=(0,o.useRef)(null),s=(0,o.useRef)(null),c=(0,o.useRef)(null),u=(0,o.useRef)(0),d=$e("sizes","scrubberWidth"),f=tt((({event:e,first:o,xy:[p],movement:[f],memo:g={}})=>{if(o){const{width:o,left:i}=l.current.getBoundingClientRect();u.current=o-parseFloat(d);const a=(null===e||void 0===e?void 0:e.target)===s.current||(null===e||void 0===e?void 0:e.target)===c.current;g.pos=I((p-i)/o,n,r);const f=Math.abs(g.pos-t.min)-Math.abs(g.pos-t.max);g.key=f<0||0===f&&g.pos<=t.min?"min":"max",a&&(g.pos=t[g.key])}const h=g.pos+I(f/u.current,0,r-n);return i({[g.key]:ft(h,a[g.key])}),g})),g=`calc(${A(t.min,n,r)} * (100% - ${d} - 8px) + 4px)`,h=`calc(${1-A(t.max,n,r)} * (100% - ${d} - 8px) + 4px)`;return o.createElement(st,le({ref:l},f()),o.createElement(at,null,o.createElement(ct,{style:{left:g,right:h}})),o.createElement(lt,{position:"left",ref:s,style:{left:g}}),o.createElement(lt,{position:"right",ref:c,style:{right:h}}))}var Tr=T({component:function(){const{label:e,displayValue:t,onUpdate:n,settings:r}=ce(),i=p(r,Rr);return o.createElement(o.Fragment,null,o.createElement(Qe,{input:!0},o.createElement(Ye,null,e),o.createElement(Sr,null,o.createElement(kr,le({value:t},r,{onDrag:n})),o.createElement(At,{value:t,settings:i,onUpdate:n,innerLabelTrim:0}))))}},Or);const Dr=["type","value"],jr=["onChange","transient","onEditStart","onEditEnd"],Pr=function(){const e=function(e){const t="function"===typeof e?Un(e):e,n=(e=t.getState,n=Object.is)=>{const[,r]=(0,o.useReducer)((e=>e+1),0),i=t.getState(),a=(0,o.useRef)(i),l=(0,o.useRef)(e),s=(0,o.useRef)(n),c=(0,o.useRef)(!1),u=(0,o.useRef)();let d;void 0===u.current&&(u.current=e(i));let p=!1;(a.current!==i||l.current!==e||s.current!==n||c.current)&&(d=e(i),p=!n(u.current,d)),Vn((()=>{p&&(u.current=d),a.current=i,l.current=e,s.current=n,c.current=!1}));const f=(0,o.useRef)(i);Vn((()=>{const e=()=>{try{const e=t.getState(),n=l.current(e);s.current(u.current,n)||(a.current=e,u.current=n,r())}catch(e){c.current=!0,r()}},n=t.subscribe(e);return t.getState()!==f.current&&e(),n}),[]);const g=p?d:u.current;return(0,o.useDebugValue)(g),g};return Object.assign(n,t),n[Symbol.iterator]=function(){console.warn("[useStore, api] = create() is deprecated and will be removed in v4");const e=[n,t];return{next(){const t=e.length<=0;return{value:e.shift(),done:t}}}},n}((t=()=>({data:{}}),(e,n,r)=>{const o=r.subscribe;return r.subscribe=(e,t,n)=>{let i=e;if(t){const o=(null==n?void 0:n.equalityFn)||Object.is;let a=e(r.getState());i=n=>{const r=e(n);if(!o(a,r)){const e=a;t(a=r,e)}},(null==n?void 0:n.fireImmediately)&&t(a,a)}return o(i)},t(e,n,r)}));var t;const n=(()=>{const e=new Map;return{on:(t,n)=>{let r=e.get(t);void 0===r&&(r=new Set,e.set(t,r)),r.add(n)},off:(t,n)=>{const r=e.get(t);void 0!==r&&(r.delete(n),0===r.size&&e.delete(t))},emit:(t,...n)=>{const r=e.get(t);if(void 0!==r)for(const e of r)e(...n)}}})();this.storeId="_"+Math.random().toString(36).substr(2,9),this.useStore=e;const r={},i=new Set;this.getVisiblePaths=()=>{const e=this.getData(),t=Object.keys(e),n=[];Object.entries(r).forEach((([e,r])=>{r.render&&t.some((t=>0===t.indexOf(e)))&&!r.render(this.get)&&n.push(e+".")}));const o=[];return i.forEach((t=>{t in e&&e[t].__refCount>0&&n.every((e=>-1===t.indexOf(e)))&&(!e[t].render||e[t].render(this.get))&&o.push(t)})),o},this.setOrderedPaths=e=>{e.forEach((e=>i.add(e)))},this.orderPaths=e=>(this.setOrderedPaths(e),e),this.disposePaths=t=>{e.setState((e=>{const n=e.data;return t.forEach((e=>{if(e in n){const t=n[e];t.__refCount--,0===t.__refCount&&t.type in W&&delete n[e]}})),{data:n}}))},this.dispose=()=>{e.setState((()=>({data:{}})))},this.getFolderSettings=e=>r[e]||{},this.getData=()=>e.getState().data,this.addData=(t,n)=>{e.setState((e=>{const r=e.data;return Object.entries(t).forEach((([e,t])=>{let o=r[e];if(o){const{type:e,value:r}=t,i=p(t,Dr);e!==o.type?m(f.INPUT_TYPE_OVERRIDE,e):((0===o.__refCount||n)&&Object.assign(o,i),o.__refCount++)}else r[e]=T(T({},t),{},{__refCount:1})})),{data:r}}))},this.setValueAtPath=(t,n,r)=>{e.setState((e=>{const o=e.data;return J(o[t],n,t,this,r),{data:o}}))},this.setSettingsAtPath=(t,n)=>{e.setState((e=>{const r=e.data;return r[t].settings=T(T({},r[t].settings),n),{data:r}}))},this.disableInputAtPath=(t,n)=>{e.setState((e=>{const r=e.data;return r[t].disabled=n,{data:r}}))},this.set=(t,n)=>{e.setState((e=>{const r=e.data;return Object.entries(t).forEach((([e,t])=>{try{J(r[e],t,void 0,void 0,n)}catch(o){0}})),{data:r}}))},this.getInput=e=>{try{return this.getData()[e]}catch(t){m(f.PATH_DOESNT_EXIST,e)}},this.get=e=>{var t;return null===(t=this.getInput(e))||void 0===t?void 0:t.value},this.emitOnEditStart=e=>{n.emit(`onEditStart:${e}`,this.get(e),e,T(T({},this.getInput(e)),{},{get:this.get}))},this.emitOnEditEnd=e=>{n.emit(`onEditEnd:${e}`,this.get(e),e,T(T({},this.getInput(e)),{},{get:this.get}))},this.subscribeToEditStart=(e,t)=>{const r=`onEditStart:${e}`;return n.on(r,t),()=>n.off(r,t)},this.subscribeToEditEnd=(e,t)=>{const r=`onEditEnd:${e}`;return n.on(r,t),()=>n.off(r,t)};const a=(e,t,n)=>{const o={};return Object.entries(e).forEach((([e,i])=>{if(""===e)return m(f.EMPTY_KEY);let l=Bn(t,e);if(i.type===W.FOLDER){const e=a(i.schema,l,n);Object.assign(o,e),l in r||(r[l]=i.settings)}else if(e in n)m(f.DUPLICATE_KEYS,e,l,n[e].path);else{const t=function(e,t,n,r){const o=Z(e,t),{type:i,input:a,options:l}=o;if(i)return i in W?o:{type:i,input:C(i,a,n,r),options:l};let s=x(a);return s?{type:s,input:C(s,a,n,r),options:l}:(s=x({value:a}),!!s&&{type:s,input:C(s,{value:a},n,r),options:l})}(i,e,l,o);if(t){const{type:r,options:i,input:a}=t,{onChange:s,transient:c,onEditStart:u,onEditEnd:d}=i,f=p(i,jr);o[l]=T(T(T({type:r},f),a),{},{fromPanel:!0}),n[e]={path:l,onChange:s,transient:c,onEditStart:u,onEditEnd:d}}else m(f.UNKNOWN_INPUT,l,i)}})),o};this.getDataFromSchema=e=>{const t={};return[a(e,"",t),t]}},_r=new Pr;const Ar={collapsed:!1};function Ir(e,t){return{type:W.FOLDER,schema:e,settings:T(T({},Ar),t)}}const Fr={disabled:!1};function Lr(e,t){return{type:W.BUTTON,onClick:e,settings:T(T({},Fr),t)}}const zr=e=>"__levaInput"in e,Nr=["type","label","path","valueKey","value","settings","setValue","disabled"];function Mr(e){let{type:t,label:n,path:r,valueKey:i,value:a,settings:l,setValue:s,disabled:c}=e,u=p(e,Nr);const{displayValue:d,onChange:g,onUpdate:h}=et({type:t,value:a,settings:l,setValue:s}),v=$[t].component;return v?o.createElement(se.Provider,{value:T({key:i,path:r,id:""+r,label:n,displayValue:d,value:a,onChange:g,onUpdate:h,settings:l,setValue:s,disabled:c},u)},o.createElement(Me,{disabled:c},o.createElement(v,null))):(m(f.NO_COMPONENT_FOR_TYPE,t,r),null)}const Ur=me("button",{display:"block",$reset:"",fontWeight:"$button",height:"$rowHeight",borderStyle:"none",borderRadius:"$sm",backgroundColor:"$elevation1",color:"$highlight1","&:not(:disabled)":{color:"$highlight3",backgroundColor:"$accent2",cursor:"pointer",$hover:"$accent3",$active:"$accent3 $accent1",$focus:""}});const Vr=me("div",{$flex:"",justifyContent:"flex-end",gap:"$colGap"}),Hr=me("button",{$reset:"",cursor:"pointer",borderRadius:"$xs","&:hover":{backgroundColor:"$elevation3"}});const Wr=me("canvas",{height:"$monitorHeight",width:"100%",display:"block",borderRadius:"$sm"});const Br=(0,o.forwardRef)((function({initialValue:e},t){const n=$e("colors","highlight3"),r=$e("colors","elevation2"),i=$e("colors","highlight1"),[a,l]=(0,o.useMemo)((()=>[(0,Ut.Vi)(i).alpha(.4).toRgbString(),(0,Ut.Vi)(i).alpha(.1).toRgbString()]),[i]),s=(0,o.useRef)([e]),c=(0,o.useRef)(e),u=(0,o.useRef)(e),d=(0,o.useRef)(),p=(0,o.useCallback)(((e,t)=>{if(!e)return;const{width:o,height:i}=e,d=new Path2D,p=o/100,f=.05*i;for(let n=0;n<s.current.length;n++){const e=p*n,t=i-A(s.current[n],c.current,u.current)*(i-2*f)-f;d.lineTo(e,t)}t.clearRect(0,0,o,i);const g=new Path2D(d);g.lineTo(p*(s.current.length+1),i),g.lineTo(0,i),g.lineTo(0,0);const h=t.createLinearGradient(0,0,0,i);h.addColorStop(0,a),h.addColorStop(1,l),t.fillStyle=h,t.fill(g),t.strokeStyle=r,t.lineJoin="round",t.lineWidth=14,t.stroke(d),t.strokeStyle=n,t.lineWidth=2,t.stroke(d)}),[n,r,a,l]),[f,g]=function(e){const t=(0,o.useRef)(null),n=(0,o.useRef)(null),r=(0,o.useRef)(!1);return(0,o.useEffect)((()=>{const o=Q((()=>{t.current.width=t.current.offsetWidth*window.devicePixelRatio,t.current.height=t.current.offsetHeight*window.devicePixelRatio,e(t.current,n.current)}),250);return window.addEventListener("resize",o),r.current||(o(),r.current=!0),()=>window.removeEventListener("resize",o)}),[e]),(0,o.useEffect)((()=>{n.current=t.current.getContext("2d")}),[]),[t,n]}(p);return(0,o.useImperativeHandle)(t,(()=>({frame:e=>{(void 0===c.current||e<c.current)&&(c.current=e),(void 0===u.current||e>u.current)&&(u.current=e),function(e,t){e.push(t),e.length>100&&e.shift()}(s.current,e),d.current=requestAnimationFrame((()=>p(f.current,g.current)))}})),[f,g,p]),(0,o.useEffect)((()=>()=>cancelAnimationFrame(d.current)),[]),o.createElement(Wr,{ref:f})})),Gr=e=>Number.isFinite(e)?e.toPrecision(2):e.toString(),Kr=(0,o.forwardRef)((function({initialValue:e},t){const[n,r]=(0,o.useState)(Gr(e));return(0,o.useImperativeHandle)(t,(()=>({frame:e=>r(Gr(e))})),[]),o.createElement("div",null,n)}));function Yr(e){return"function"===typeof e?e():e.current}const Zr=["type","label","key"],Jr={[W.BUTTON]:function({onClick:e,settings:t,label:n}){const r=fe();return o.createElement(Qe,null,o.createElement(Ur,{disabled:t.disabled,onClick:()=>e(r.get)},n))},[W.BUTTON_GROUP]:function(e){const{label:t,opts:n}=(({label:e,opts:t})=>{let n="string"===typeof e&&""===e.trim()?null:e,r=t;return"object"===typeof t.opts&&(void 0!==r.label&&(n=t.label),r=t.opts),{label:n,opts:r}})(e),r=fe();return o.createElement(Qe,{input:!!t},t&&o.createElement(Ye,null,t),o.createElement(Vr,null,Object.entries(n).map((([e,t])=>o.createElement(Hr,{key:e,onClick:()=>t(r.get)},e)))))},[W.MONITOR]:function({label:e,objectOrFn:t,settings:n}){const r=(0,o.useRef)(),i=(0,o.useRef)(Yr(t));return(0,o.useEffect)((()=>{const e=window.setInterval((()=>{var e;document.hidden||null===(e=r.current)||void 0===e||e.frame(Yr(t))}),n.interval);return()=>window.clearInterval(e)}),[t,n.interval]),o.createElement(Qe,{input:!0},o.createElement(Ye,{align:"top"},e),n.graph?o.createElement(Br,{ref:r,initialValue:i.current}):o.createElement(Kr,{ref:r,initialValue:i.current}))}},Xr=o.memo((({path:e})=>{const[t,{set:n,setSettings:r,disable:i,storeId:a,emitOnEditStart:s,emitOnEditEnd:c}]=function(e){const t=fe(),[n,r]=(0,o.useState)(ot(t.getData(),e)),i=(0,o.useCallback)((n=>t.setValueAtPath(e,n,!0)),[e,t]),a=(0,o.useCallback)((n=>t.setSettingsAtPath(e,n)),[e,t]),s=(0,o.useCallback)((n=>t.disableInputAtPath(e,n)),[e,t]),c=(0,o.useCallback)((()=>t.emitOnEditStart(e)),[e,t]),u=(0,o.useCallback)((()=>t.emitOnEditEnd(e)),[e,t]);return(0,o.useEffect)((()=>{r(ot(t.getData(),e));const n=t.useStore.subscribe((t=>ot(t.data,e)),r,{equalityFn:l});return()=>n()}),[t,e]),[n,{set:i,setSettings:a,disable:s,storeId:t.storeId,emitOnEditStart:c,emitOnEditEnd:u}]}(e);if(!t)return null;const{type:u,label:d,key:g}=t,h=p(t,Zr);if(u in W){const t=Jr[u];return o.createElement(t,le({label:d,path:e},h))}return u in $?o.createElement(Mr,le({key:a+e,type:u,label:d,storeId:a,path:e,valueKey:g,setValue:n,setSettings:r,disable:i,emitOnEditStart:s,emitOnEditEnd:c},h)):(v(f.UNSUPPORTED_INPUT,u,e),null)}));function qr({toggle:e,toggled:t,name:n}){return o.createElement(_e,{onClick:()=>e()},o.createElement(Xe,{toggled:t}),o.createElement("div",null,n))}const Qr=({name:e,path:t,tree:n})=>{const r=fe(),i=Bn(t,e),{collapsed:a,color:l}=r.getFolderSettings(i),[s,c]=(0,o.useState)(!a),u=(0,o.useRef)(null),d=$e("colors","folderWidgetColor"),p=$e("colors","folderTextColor");return(0,o.useLayoutEffect)((()=>{u.current.style.setProperty("--leva-colors-folderWidgetColor",l||d),u.current.style.setProperty("--leva-colors-folderTextColor",l||p)}),[l,d,p]),o.createElement(je,{ref:u},o.createElement(qr,{name:e,toggled:s,toggle:()=>c((e=>!e))}),o.createElement(eo,{parent:i,tree:n,toggled:s}))},eo=o.memo((({isRoot:e=!1,fill:t=!1,flat:n=!1,parent:r,tree:i,toggled:a})=>{const{wrapperRef:l,contentRef:s}=function(e){const t=(0,o.useRef)(null),n=(0,o.useRef)(null),r=(0,o.useRef)(!0);return(0,o.useLayoutEffect)((()=>{e||(t.current.style.height="0px",t.current.style.overflow="hidden")}),[]),(0,o.useEffect)((()=>{if(r.current)return void(r.current=!1);let o;const i=t.current,a=()=>{e&&(i.style.removeProperty("height"),i.style.removeProperty("overflow"),n.current.scrollIntoView({behavior:"smooth",block:"nearest"}))};i.addEventListener("transitionend",a,{once:!0});const{height:l}=n.current.getBoundingClientRect();return i.style.height=l+"px",e||(i.style.overflow="hidden",o=window.setTimeout((()=>i.style.height="0px"),50)),()=>{i.removeEventListener("transitionend",a),clearTimeout(o)}}),[e]),{wrapperRef:t,contentRef:n}}(a),c=fe(),u=([e,t])=>{var n;return(zr(t)?null===(n=c.getInput(t.path))||void 0===n?void 0:n.order:c.getFolderSettings(Bn(r,e)).order)||0},d=Object.entries(i).sort(((e,t)=>u(e)-u(t)));return o.createElement(Pe,{ref:l,isRoot:e,fill:t,flat:n},o.createElement(Ae,{ref:s,isRoot:e,toggled:a},d.map((([e,t])=>zr(t)?o.createElement(Xr,{key:t.path,valueKey:t.valueKey,path:t.path}):o.createElement(Qr,{key:e,name:e,path:r,tree:t})))))})),to=me("div",{position:"relative",fontFamily:"$mono",fontSize:"$root",color:"$rootText",backgroundColor:"$elevation1",variants:{fill:{false:{position:"fixed",top:"10px",right:"10px",zIndex:1e3,width:"$rootWidth"},true:{position:"relative",width:"100%"}},flat:{false:{borderRadius:"$lg",boxShadow:"$level1"}},oneLineLabels:{true:{[`${Fe}`]:{gridTemplateColumns:"auto",gridAutoColumns:"minmax(max-content, 1fr)",gridAutoRows:"minmax($sizes$rowHeight), auto)",rowGap:0,columnGap:0,marginTop:"$rowGap"}}},hideTitleBar:{true:{$$titleBarHeight:"0px"},false:{$$titleBarHeight:"$sizes$titleBarHeight"}}},"&,*,*:after,*:before":{boxSizing:"border-box"},"*::selection":{backgroundColor:"$accent2"}}),no=me("i",{$flexCenter:"",width:40,userSelect:"none",cursor:"pointer","> svg":{fill:"$highlight1",transition:"transform 350ms ease, fill 250ms ease"},"&:hover > svg":{fill:"$highlight3"},variants:{active:{true:{"> svg":{fill:"$highlight2"}}}}}),ro=me("div",{display:"flex",alignItems:"stretch",justifyContent:"space-between",height:"$titleBarHeight",variants:{mode:{drag:{cursor:"grab"}}}}),oo=me("div",{$flex:"",position:"relative",width:"100%",overflow:"hidden",transition:"height 250ms ease",color:"$highlight3",paddingLeft:"$md",[`> ${no}`]:{height:30},variants:{toggled:{true:{height:30},false:{height:0}}}}),io=me("input",{$reset:"",flex:1,position:"relative",height:30,width:"100%",backgroundColor:"transparent",fontSize:"10px",borderRadius:"$root","&:focus":{},"&::placeholder":{color:"$highlight2"}}),ao=me("div",{touchAction:"none",$flexCenter:"",flex:1,"> svg":{fill:"$highlight1"},color:"$highlight1",variants:{drag:{true:{$draggable:"","> svg":{transition:"fill 250ms ease"},"&:hover":{color:"$highlight3"},"&:hover > svg":{fill:"$highlight3"}}},filterEnabled:{false:{paddingRight:40}}}}),lo=o.forwardRef((({setFilter:e,toggle:t},n)=>{const[r,i]=(0,o.useState)(""),a=(0,o.useMemo)((()=>Q(e,250)),[e]);return(0,o.useEffect)((()=>{a(r)}),[r,a]),o.createElement(o.Fragment,null,o.createElement(io,{ref:n,value:r,placeholder:"[Open filter with CMD+SHIFT+L]",onPointerDown:e=>e.stopPropagation(),onChange:e=>{const n=e.currentTarget.value;t(!0),i(n)}}),o.createElement(no,{onClick:()=>(e(""),void i("")),style:{visibility:r?"visible":"hidden"}},o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",height:"14",width:"14",viewBox:"0 0 20 20",fill:"currentColor"},o.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"}))))}));function so({setFilter:e,onDrag:t,onDragStart:n,onDragEnd:r,toggle:i,toggled:a,title:l,drag:s,filterEnabled:c,from:u}){const[d,p]=(0,o.useState)(!1),f=(0,o.useRef)(null);(0,o.useEffect)((()=>{var e,t;d?null===(e=f.current)||void 0===e||e.focus():null===(t=f.current)||void 0===t||t.blur()}),[d]);const g=tt((({offset:[e,o],first:i,last:a})=>{t({x:e,y:o}),i&&n({x:e,y:o}),a&&r({x:e,y:o})}),{filterTaps:!0,from:({offset:[e,t]})=>[(null===u||void 0===u?void 0:u.x)||e,(null===u||void 0===u?void 0:u.y)||t]});return(0,o.useEffect)((()=>{const e=e=>{"L"===e.key&&e.shiftKey&&e.metaKey&&p((e=>!e))};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)}),[]),o.createElement(o.Fragment,null,o.createElement(ro,{mode:s?"drag":void 0},o.createElement(no,{active:!a,onClick:()=>i()},o.createElement(Xe,{toggled:a,width:12,height:8})),o.createElement(ao,le({},s?g():{},{drag:s,filterEnabled:c}),void 0===l&&s?o.createElement("svg",{width:"20",height:"10",viewBox:"0 0 28 14",xmlns:"http://www.w3.org/2000/svg"},o.createElement("circle",{cx:"2",cy:"2",r:"2"}),o.createElement("circle",{cx:"14",cy:"2",r:"2"}),o.createElement("circle",{cx:"26",cy:"2",r:"2"}),o.createElement("circle",{cx:"2",cy:"12",r:"2"}),o.createElement("circle",{cx:"14",cy:"12",r:"2"}),o.createElement("circle",{cx:"26",cy:"12",r:"2"})):l),c&&o.createElement(no,{active:d,onClick:()=>p((e=>!e))},o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",height:"20",viewBox:"0 0 20 20"},o.createElement("path",{d:"M9 9a2 2 0 114 0 2 2 0 01-4 0z"}),o.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a4 4 0 00-3.446 6.032l-2.261 2.26a1 1 0 101.414 1.415l2.261-2.261A4 4 0 1011 5z",clipRule:"evenodd"})))),o.createElement(oo,{toggled:d},o.createElement(lo,{ref:f,setFilter:e,toggle:i})))}const co=["store","hidden","theme","collapsed"];function uo(e){let{store:t,hidden:n=!1,theme:r,collapsed:i=!1}=e,a=p(e,co);const l=Gn((()=>function(e){const t={colors:{elevation1:"#292d39",elevation2:"#181c20",elevation3:"#373c4b",accent1:"#0066dc",accent2:"#007bff",accent3:"#3c93ff",highlight1:"#535760",highlight2:"#8c92a4",highlight3:"#fefefe",vivid1:"#ffcc00",folderWidgetColor:"$highlight2",folderTextColor:"$highlight3",toolTipBackground:"$highlight3",toolTipText:"$elevation2"},radii:{xs:"2px",sm:"3px",lg:"10px"},space:{xs:"3px",sm:"6px",md:"10px",rowGap:"7px",colGap:"7px"},fonts:{mono:"ui-monospace, SFMono-Regular, Menlo, 'Roboto Mono', monospace",sans:"system-ui, sans-serif"},fontSizes:{root:"11px",toolTip:"$root"},sizes:{rootWidth:"280px",controlWidth:"160px",numberInputMinWidth:"38px",scrubberWidth:"8px",scrubberHeight:"16px",rowHeight:"24px",folderTitleHeight:"20px",checkboxSize:"16px",joystickWidth:"100px",joystickHeight:"100px",colorPickerWidth:"$controlWidth",colorPickerHeight:"100px",imagePreviewWidth:"$controlWidth",imagePreviewHeight:"100px",monitorHeight:"60px",titleBarHeight:"39px"},shadows:{level1:"0 0 9px 0 #00000088",level2:"0 4px 14px #00000033"},borderWidths:{root:"0px",input:"1px",focus:"1px",hover:"1px",active:"1px",folder:"1px"},fontWeights:{label:"normal",folder:"normal",button:"normal"}};if(!e)return{theme:t,className:""};Object.keys(e).forEach((n=>{Object.assign(t[n],e[n])}));const n=be(t);return{theme:t,className:n.className}}(r)),[r]),[s,c]=(0,o.useState)(!i),u="object"===typeof i?!i.collapsed:s,d=(0,o.useMemo)((()=>"object"===typeof i?e=>{"function"===typeof e?i.onChange(!e(!i.collapsed)):i.onChange(!e)}:c),[i]);return!t||n?null:o.createElement(ue.Provider,{value:l},o.createElement(po,le({store:t},a,{toggled:u,setToggle:d,rootClass:l.className})))}const po=o.memo((({store:e,rootClass:t,fill:n=!1,flat:r=!1,neverHide:i=!1,oneLineLabels:a=!1,titleBar:s={title:void 0,drag:!0,filter:!0,position:void 0,onDrag:void 0,onDragStart:void 0,onDragEnd:void 0},hideCopyButton:c=!1,toggled:u,setToggle:d})=>{var p,f;const g=(e=>{const[t,n]=(0,o.useState)(e.getVisiblePaths());return(0,o.useEffect)((()=>{n(e.getVisiblePaths());const t=e.useStore.subscribe(e.getVisiblePaths,n,{equalityFn:l});return()=>t()}),[e]),t})(e),[h,m]=(0,o.useState)(""),v=(0,o.useMemo)((()=>((e,t)=>{const n={},r=t?t.toLowerCase():null;return e.forEach((e=>{const[t,o]=function(e){const t=e.split(".");return[t.pop(),t.join(".")||void 0]}(e);(!r||t.toLowerCase().indexOf(r)>-1)&&Wn()(n,o,{[t]:{__levaInput:!0,path:e}})})),n})(g,h)),[g,h]),[b,y]=nt(),E=i||g.length>0,w="object"===typeof s&&s.title||void 0,$="object"!==typeof s||(null===(p=s.drag)||void 0===p||p),x="object"!==typeof s||(null===(f=s.filter)||void 0===f||f),O="object"===typeof s&&s.position||void 0,C="object"===typeof s&&s.onDrag||void 0,R="object"===typeof s&&s.onDragStart||void 0,S="object"===typeof s&&s.onDragEnd||void 0;return o.useEffect((()=>{y({x:null===O||void 0===O?void 0:O.x,y:null===O||void 0===O?void 0:O.y})}),[O,y]),we(),o.createElement(pe.Provider,{value:{hideCopyButton:c}},o.createElement(to,{ref:b,className:t,fill:n,flat:r,oneLineLabels:a,hideTitleBar:!s,style:{display:E?"block":"none"}},s&&o.createElement(so,{onDrag:e=>{y(e),null===C||void 0===C||C(e)},onDragStart:e=>null===R||void 0===R?void 0:R(e),onDragEnd:e=>null===S||void 0===S?void 0:S(e),setFilter:m,toggle:e=>d((t=>null!==e&&void 0!==e?e:!t)),toggled:u,title:w,drag:$,filterEnabled:x,from:O}),E&&o.createElement(de.Provider,{value:e},o.createElement(eo,{isRoot:!0,fill:n,flat:r,tree:v,toggled:u}))))})),fo=["isRoot"];let go=!1,ho=null;function mo(e){let{isRoot:t=!1}=e,n=p(e,fo);return(0,o.useEffect)((()=>(go=!0,!t&&ho&&(ho.remove(),ho=null),()=>{t||(go=!1)})),[t]),o.createElement(uo,le({store:_r},n))}function vo(e){(0,o.useEffect)((()=>{e&&!go&&(ho||(ho=document.getElementById("leva__root")||Object.assign(document.createElement("div"),{id:"leva__root"}),document.body&&(document.body.appendChild(ho),function(e,t){const n=console.error;console.error=()=>{},r.render(e,t),console.error=n}(o.createElement(mo,{isRoot:!0}),ho))),go=!0)}),[e])}function bo(e,t,n,r,i){const{folderName:a,schema:s,folderSettings:c,hookSettings:u,deps:d}=function(e,t,n,r,o){let i,a,l,s,c;return"string"===typeof e?(a=e,i=t,Array.isArray(n)?c=n:n&&("store"in n?(s=n,c=r):(l=n,Array.isArray(r)?c=r:(s=r,c=o)))):(i=e,Array.isArray(t)?c=t:(s=t,c=n)),{schema:i,folderName:a,folderSettings:l,hookSettings:s,deps:c||[]}}(e,t,n,r,i),p="function"===typeof s,f=(0,o.useRef)(!1),g=(0,o.useRef)(!0),h=Gn((()=>{f.current=!0;const e="function"===typeof s?s():s;return a?{[a]:Ir(e,c)}:e}),d);vo(!(null!==u&&void 0!==u&&u.store));const[m]=(0,o.useState)((()=>(null===u||void 0===u?void 0:u.store)||_r)),[v,b]=(0,o.useMemo)((()=>m.getDataFromSchema(h)),[m,h]),[y,E,w,$,x]=(0,o.useMemo)((()=>{const e=[],t=[],n={},r={},o={};return Object.values(b).forEach((({path:i,onChange:a,onEditStart:l,onEditEnd:s,transient:c})=>{e.push(i),a?(n[i]=a,c||t.push(i)):t.push(i),l&&(r[i]=l),s&&(o[i]=s)})),[e,t,n,r,o]}),[b]),O=(0,o.useMemo)((()=>m.orderPaths(y)),[y,m]),C=Kn(m,E,v),R=(0,o.useCallback)((e=>{const t=Object.entries(e).reduce(((e,[t,n])=>Object.assign(e,{[b[t].path]:n})),{});m.set(t,!1)}),[m,b]),S=(0,o.useCallback)((e=>m.get(b[e].path)),[m,b]);return(0,o.useEffect)((()=>{const e=!g.current&&f.current;return m.addData(v,e),g.current=!1,f.current=!1,()=>m.disposePaths(O)}),[m,O,v]),(0,o.useEffect)((()=>{const e=[];return Object.entries(w).forEach((([t,n])=>{n(m.get(t),t,T({initial:!0,get:m.get},m.getInput(t)));const r=m.useStore.subscribe((e=>{const n=e.data[t];return[n.disabled?void 0:n.value,n]}),(([e,r])=>n(e,t,T({initial:!1,get:m.get},r))),{equalityFn:l});e.push(r)})),()=>e.forEach((e=>e()))}),[m,w]),(0,o.useEffect)((()=>{const e=[];return Object.entries($).forEach((([t,n])=>e.push(m.subscribeToEditStart(t,n)))),Object.entries(x).forEach((([t,n])=>e.push(m.subscribeToEditEnd(t,n)))),()=>e.forEach((e=>e()))}),[$,x,m]),p?[C,R,S]:C}O(B.SELECT,Et),O(B.IMAGE,Er),O(B.NUMBER,gt),O(B.COLOR,or),O(B.STRING,Ct),O(B.BOOLEAN,Tt),O(B.INTERVAL,Tr),O(B.VECTOR3D,ir),O(B.VECTOR2D,pr)},69135:function(e,t){t.Z=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(","),r=e.name||"",o=(e.type||"").toLowerCase(),i=o.replace(/\/.*$/,"");return n.some((function(e){var t=e.trim().toLowerCase();return"."===t.charAt(0)?r.toLowerCase().endsWith(t):t.endsWith("/*")?i===t.replace(/\/.*$/,""):o===t}))}return!0}}}]);
//# sourceMappingURL=leva.8719053a91b28b5b5f155260f98d5651.js.map