"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["recharts"],{48218:function(t,e,r){r.d(e,{$:function(){return V}});var n=r(89526),i=r(90512),o=r(68059),a=r(47184),c=r.n(a),u=r(51391),l=r.n(u),s=r(61452),f=r(65370),p=r(32214),d=r(34324),y=r(16171),h=r(9410),v=r(59509),m=r(36530),b=r(33790),g=r(78109),O=r(69531),x=["x","y"];function w(t){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},w(t)}function j(){return j=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},j.apply(this,arguments)}function S(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function P(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?S(Object(r),!0).forEach((function(e){A(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function A(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=w(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=w(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==w(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function k(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function E(t,e){var r=t.x,n=t.y,i=k(t,x),o="".concat(r),a=parseInt(o,10),c="".concat(n),u=parseInt(c,10),l="".concat(e.height||i.height),s=parseInt(l,10),f="".concat(e.width||i.width),p=parseInt(f,10);return P(P(P(P(P({},e),i),a?{x:a}:{}),u?{y:u}:{}),{},{height:s,width:p,name:e.name,radius:e.radius})}function T(t){return n.createElement(O.bn,j({shapeType:"rectangle",propTransformer:E,activeClassName:"recharts-active-bar"},t))}var I,C=["value","background"];function D(t){return D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},D(t)}function M(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function N(){return N=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},N.apply(this,arguments)}function L(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function B(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?L(Object(r),!0).forEach((function(e){K(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):L(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function R(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,X(n.key),n)}}function z(t,e,r){return e=_(e),function(t,e){if(e&&("object"===D(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return W(t)}(t,F()?Reflect.construct(e,r||[],_(t).constructor):e.apply(t,r))}function F(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(F=function(){return!!t})()}function _(t){return _=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},_(t)}function W(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Z(t,e){return Z=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Z(t,e)}function K(t,e,r){return(e=X(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function X(t){var e=function(t,e){if("object"!=D(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=D(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==D(e)?e:String(e)}var V=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return K(W(t=z(this,e,[].concat(n))),"state",{isAnimationFinished:!1}),K(W(t),"id",(0,y.EL)("recharts-bar-")),K(W(t),"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()})),K(W(t),"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()})),t}var r,a,u;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Z(t,e)}(e,t),r=e,u=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],(a=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,i=r.shape,o=r.dataKey,a=r.activeIndex,c=r.activeBar,u=(0,h.L6)(this.props,!1);return t&&t.map((function(t,r){var l=r===a,f=l?c:i,p=B(B(B({},u),t),{},{isActive:l,option:f,index:r,dataKey:o,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return n.createElement(s.m,N({className:"recharts-bar-rectangle"},(0,b.bw)(e.props,t,r),{key:"rectangle-".concat(null===t||void 0===t?void 0:t.x,"-").concat(null===t||void 0===t?void 0:t.y,"-").concat(null===t||void 0===t?void 0:t.value)}),n.createElement(T,p))}))}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,i=e.layout,a=e.isAnimationActive,c=e.animationBegin,u=e.animationDuration,l=e.animationEasing,f=e.animationId,p=this.state.prevData;return n.createElement(o.ZP,{begin:c,duration:u,isActive:a,easing:l,from:{t:0},to:{t:1},key:"bar-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(e){var o=e.t,a=r.map((function(t,e){var r=p&&p[e];if(r){var n=(0,y.k4)(r.x,t.x),a=(0,y.k4)(r.y,t.y),c=(0,y.k4)(r.width,t.width),u=(0,y.k4)(r.height,t.height);return B(B({},t),{},{x:n(o),y:a(o),width:c(o),height:u(o)})}if("horizontal"===i){var l=(0,y.k4)(0,t.height)(o);return B(B({},t),{},{y:t.y+t.height-l,height:l})}var s=(0,y.k4)(0,t.width)(o);return B(B({},t),{},{width:s})}));return n.createElement(s.m,null,t.renderRectanglesStatically(a))}))}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return!(r&&e&&e.length)||n&&c()(n,e)?this.renderRectanglesStatically(e):this.renderRectanglesWithAnimation()}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,i=e.dataKey,o=e.activeIndex,a=(0,h.L6)(this.props.background,!1);return r.map((function(e,r){e.value;var c=e.background,u=M(e,C);if(!c)return null;var l=B(B(B(B(B({},u),{},{fill:"#eee"},c),a),(0,b.bw)(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:i,index:r,key:"background-bar-".concat(r),className:"recharts-bar-background-rectangle"});return n.createElement(T,N({option:t.props.background,isActive:r===o},l))}))}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,i=r.data,o=r.xAxis,a=r.yAxis,c=r.layout,u=r.children,l=(0,h.NN)(u,f.W);if(!l)return null;var p="vertical"===c?i[0].height/2:i[0].width/2,d=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:(0,m.F$)(t,e)}},y={clipPath:t?"url(#clipPath-".concat(e,")"):null};return n.createElement(s.m,y,l.map((function(t){return n.cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:i,xAxis:o,yAxis:a,layout:c,offset:p,dataPointFormatter:d})})))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,o=t.className,a=t.xAxis,c=t.yAxis,u=t.left,f=t.top,p=t.width,y=t.height,h=t.isAnimationActive,v=t.background,m=t.id;if(e||!r||!r.length)return null;var b=this.state.isAnimationFinished,g=(0,i.Z)("recharts-bar",o),O=a&&a.allowDataOverflow,x=c&&c.allowDataOverflow,w=O||x,j=l()(m)?this.id:m;return n.createElement(s.m,{className:g},O||x?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(j)},n.createElement("rect",{x:O?u:u-p/2,y:x?f:f-y/2,width:O?p:2*p,height:x?y:2*y}))):null,n.createElement(s.m,{className:"recharts-bar-rectangles",clipPath:w?"url(#clipPath-".concat(j,")"):null},v?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(w,j),(!h||b)&&d.e.renderCallByParent(this.props,r))}}])&&R(r.prototype,a),u&&R(r,u),Object.defineProperty(r,"prototype",{writable:!1}),e}(n.PureComponent);I=V,K(V,"displayName","Bar"),K(V,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!v.x.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),K(V,"getComposedData",(function(t){var e=t.props,r=t.item,n=t.barPosition,i=t.bandSize,o=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,d=t.offset,v=(0,m.Bu)(n,r);if(!v)return null;var b=e.layout,O=r.props,x=O.dataKey,w=O.children,j=O.minPointSize,S="horizontal"===b?a:o,P=l?S.scale.domain():null,A=(0,m.Yj)({numericAxis:S}),k=(0,h.NN)(w,p.b),E=f.map((function(t,e){var n,f,p,d,h,O;l?n=(0,m.Vv)(l[s+e],P):(n=(0,m.F$)(t,x),Array.isArray(n)||(n=[A,n]));var w=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"===typeof t)return t;var i="number"===typeof r;return i?t(r,n):(i||(0,g.Z)(!1),e)}}(j,I.defaultProps.minPointSize)(n[1],e);if("horizontal"===b){var S,E=[a.scale(n[0]),a.scale(n[1])],T=E[0],C=E[1];f=(0,m.Fy)({axis:o,ticks:c,bandSize:i,offset:v.offset,entry:t,index:e}),p=null!==(S=null!==C&&void 0!==C?C:T)&&void 0!==S?S:void 0,d=v.size;var D=T-C;if(h=Number.isNaN(D)?0:D,O={x:f,y:a.y,width:d,height:a.height},Math.abs(w)>0&&Math.abs(h)<Math.abs(w)){var M=(0,y.uY)(h||w)*(Math.abs(w)-Math.abs(h));p-=M,h+=M}}else{var N=[o.scale(n[0]),o.scale(n[1])],L=N[0],R=N[1];if(f=L,p=(0,m.Fy)({axis:a,ticks:u,bandSize:i,offset:v.offset,entry:t,index:e}),d=R-L,h=v.size,O={x:o.x,y:p,width:o.width,height:h},Math.abs(w)>0&&Math.abs(d)<Math.abs(w))d+=(0,y.uY)(d||w)*(Math.abs(w)-Math.abs(d))}return B(B(B({},t),{},{x:f,y:p,width:d,height:h,value:l?n:n[1],payload:t,background:O},k&&k[e]&&k[e].props),{},{tooltipPayload:[(0,m.Qo)(r,t)],tooltipPosition:{x:f+d/2,y:p+h/2}})}));return B({data:E,layout:b},d)}))},88974:function(t,e,r){r.d(e,{B:function(){return M}});var n=r(89526),i=r(90512),o=r(35406),a=r(39277),c=r.n(a),u=r(58120),l=r.n(u),s=r(61452),f=r(49266),p=r(36530),d=r(16171);function y(t){return y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(t)}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function v(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach((function(e){m(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function m(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var b=["Webkit","Moz","O","ms"],g=r(9410);function O(t){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(t)}function x(){return x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},x.apply(this,arguments)}function w(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?w(Object(r),!0).forEach((function(e){I(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function S(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,C(n.key),n)}}function P(t,e,r){return e=k(e),function(t,e){if(e&&("object"===O(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return E(t)}(t,A()?Reflect.construct(e,r||[],k(t).constructor):e.apply(t,r))}function A(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(A=function(){return!!t})()}function k(t){return k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},k(t)}function E(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function T(t,e){return T=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},T(t,e)}function I(t,e,r){return(e=C(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function C(t){var e=function(t,e){if("object"!=O(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=O(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==O(e)?e:String(e)}var D=function(t){return t.changedTouches&&!!t.changedTouches.length},M=function(t){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),I(E(r=P(this,e,[t])),"handleDrag",(function(t){r.leaveTimer&&(clearTimeout(r.leaveTimer),r.leaveTimer=null),r.state.isTravellerMoving?r.handleTravellerMove(t):r.state.isSlideMoving&&r.handleSlideDrag(t)})),I(E(r),"handleTouchMove",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&r.handleDrag(t.changedTouches[0])})),I(E(r),"handleDragEnd",(function(){r.setState({isTravellerMoving:!1,isSlideMoving:!1},(function(){var t=r.props,e=t.endIndex,n=t.onDragEnd,i=t.startIndex;null===n||void 0===n||n({endIndex:e,startIndex:i})})),r.detachDragEndListener()})),I(E(r),"handleLeaveWrapper",(function(){(r.state.isTravellerMoving||r.state.isSlideMoving)&&(r.leaveTimer=window.setTimeout(r.handleDragEnd,r.props.leaveTimeOut))})),I(E(r),"handleEnterSlideOrTraveller",(function(){r.setState({isTextActive:!0})})),I(E(r),"handleLeaveSlideOrTraveller",(function(){r.setState({isTextActive:!1})})),I(E(r),"handleSlideDragStart",(function(t){var e=D(t)?t.changedTouches[0]:t;r.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:e.pageX}),r.attachDragEndListener()})),r.travellerDragStartHandlers={startX:r.handleTravellerDragStart.bind(E(r),"startX"),endX:r.handleTravellerDragStart.bind(E(r),"endX")},r.state={},r}var r,a,u;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&T(t,e)}(e,t),r=e,u=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,i=t.width,o=t.height,a=t.stroke,c=Math.floor(r+o/2)-1;return n.createElement(n.Fragment,null,n.createElement("rect",{x:e,y:r,width:i,height:o,fill:a,stroke:"none"}),n.createElement("line",{x1:e+1,y1:c,x2:e+i-1,y2:c,fill:"none",stroke:"#fff"}),n.createElement("line",{x1:e+1,y1:c+2,x2:e+i-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,r){return n.isValidElement(t)?n.cloneElement(t,r):c()(t)?t(r):e.renderDefaultTraveller(r)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,i=t.x,a=t.travellerWidth,c=t.updateId,u=t.startIndex,s=t.endIndex;if(r!==e.prevData||c!==e.prevUpdateId)return j({prevData:r,prevTravellerWidth:a,prevUpdateId:c,prevX:i,prevWidth:n},r&&r.length?function(t){var e=t.data,r=t.startIndex,n=t.endIndex,i=t.x,a=t.width,c=t.travellerWidth;if(!e||!e.length)return{};var u=e.length,s=(0,o.x)().domain(l()(0,u)).range([i,i+a-c]),f=s.domain().map((function(t){return s(t)}));return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(r),endX:s(n),scale:s,scaleValues:f}}({data:r,width:n,x:i,travellerWidth:a,startIndex:u,endIndex:s}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||i!==e.prevX||a!==e.prevTravellerWidth)){e.scale.range([i,i+n-a]);var f=e.scale.domain().map((function(t){return e.scale(t)}));return{prevData:r,prevTravellerWidth:a,prevUpdateId:c,prevX:i,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:f}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=0,n=t.length-1;n-r>1;){var i=Math.floor((r+n)/2);t[i]>e?n=i:r=i}return e>=t[n]?n:r}}],(a=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var r=t.startX,n=t.endX,i=this.state.scaleValues,o=this.props,a=o.gap,c=o.data.length-1,u=Math.min(r,n),l=Math.max(r,n),s=e.getIndexInRange(i,u),f=e.getIndexInRange(i,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,i=e.dataKey,o=(0,p.F$)(r[t],i,t);return c()(n)?n(o,t):o}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,i=e.endX,o=this.props,a=o.x,c=o.width,u=o.travellerWidth,l=o.startIndex,s=o.endIndex,f=o.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-i,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-i));var d=this.getIndex({startX:n+p,endX:i+p});d.startIndex===l&&d.endIndex===s||!f||f(d),this.setState({startX:n+p,endX:i+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=D(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,i=e.endX,o=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,d=c.data,y={startX:this.state.startX,endX:this.state.endX},h=t.pageX-r;h>0?h=Math.min(h,u+l-s-a):h<0&&(h=Math.max(h,u-a)),y[n]=a+h;var v=this.getIndex(y),m=v.startIndex,b=v.endIndex;this.setState(I(I({},n,a+h),"brushMoveStartX",t.pageX),(function(){f&&function(){var t=d.length-1;return"startX"===n&&(i>o?m%p===0:b%p===0)||i<o&&b===t||"endX"===n&&(i>o?b%p===0:m%p===0)||i>o&&b===t}()&&f(v)}))}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,i=n.scaleValues,o=n.startX,a=n.endX,c=this.state[e],u=i.indexOf(c);if(-1!==u){var l=u+t;if(!(-1===l||l>=i.length)){var s=i[l];"startX"===e&&s>=a||"endX"===e&&s<=o||this.setState(I({},e,s),(function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))}))}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,i=t.width,o=t.height,a=t.fill,c=t.stroke;return n.createElement("rect",{stroke:c,fill:a,x:e,y:r,width:i,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,i=t.width,o=t.height,a=t.data,c=t.children,u=t.padding,l=n.Children.only(c);return l?n.cloneElement(l,{x:e,y:r,width:i,height:o,margin:u,compact:!0,data:a}):null}},{key:"renderTravellerLayer",value:function(t,r){var i=this,o=this.props,a=o.y,c=o.travellerWidth,u=o.height,l=o.traveller,f=o.ariaLabel,p=o.data,d=o.startIndex,y=o.endIndex,h=Math.max(t,this.props.x),v=j(j({},(0,g.L6)(this.props,!1)),{},{x:h,y:a,width:c,height:u}),m=f||"Min value: ".concat(p[d].name,", Max value: ").concat(p[y].name);return n.createElement(s.m,{tabIndex:0,role:"slider","aria-label":m,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[r],onTouchStart:this.travellerDragStartHandlers[r],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,r))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},e.renderTraveller(l,v))}},{key:"renderSlide",value:function(t,e){var r=this.props,i=r.y,o=r.height,a=r.stroke,c=r.travellerWidth,u=Math.min(t,e)+c,l=Math.max(Math.abs(e-t)-c,0);return n.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:u,y:i,width:l,height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,i=t.y,o=t.height,a=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,p=u.endX,d={pointerEvents:"none",fill:c};return n.createElement(s.m,{className:"recharts-brush-texts"},n.createElement(f.x,x({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,p)-5,y:i+o/2},d),this.getTextOfTick(e)),n.createElement(f.x,x({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,p)+a+5,y:i+o/2},d),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,o=t.children,a=t.x,c=t.y,u=t.width,l=t.height,f=t.alwaysShowText,p=this.state,y=p.startX,h=p.endX,g=p.isTextActive,O=p.isSlideMoving,x=p.isTravellerMoving,w=p.isTravellerFocused;if(!e||!e.length||!(0,d.hj)(a)||!(0,d.hj)(c)||!(0,d.hj)(u)||!(0,d.hj)(l)||u<=0||l<=0)return null;var j=(0,i.Z)("recharts-brush",r),S=1===n.Children.count(o),P=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,(function(t){return t.toUpperCase()})),n=b.reduce((function(t,n){return v(v({},t),{},m({},n+r,e))}),{});return n[t]=e,n}("userSelect","none");return n.createElement(s.m,{className:j,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:P},this.renderBackground(),S&&this.renderPanorama(),this.renderSlide(y,h),this.renderTravellerLayer(y,"startX"),this.renderTravellerLayer(h,"endX"),(g||O||x||w||f)&&this.renderText())}}])&&S(r.prototype,a),u&&S(r,u),Object.defineProperty(r,"prototype",{writable:!1}),e}(n.PureComponent);I(M,"displayName","Brush"),I(M,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1})},27871:function(t,e,r){r.d(e,{O:function(){return D}});var n=r(89526),i=r(39277),o=r.n(i),a=r(80089),c=r.n(a),u=r(90512),l=r(68201),s=r(61452),f=r(49266),p=r(43774),d=r(16171),y=r(33790),h=r(9410),v=r(37561),m=["viewBox"],b=["viewBox"],g=["ticks"];function O(t){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(t)}function x(){return x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},x.apply(this,arguments)}function w(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?w(Object(r),!0).forEach((function(e){I(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function S(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function P(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,C(n.key),n)}}function A(t,e,r){return e=E(e),function(t,e){if(e&&("object"===O(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,k()?Reflect.construct(e,r||[],E(t).constructor):e.apply(t,r))}function k(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(k=function(){return!!t})()}function E(t){return E=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},E(t)}function T(t,e){return T=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},T(t,e)}function I(t,e,r){return(e=C(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function C(t){var e=function(t,e){if("object"!=O(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=O(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==O(e)?e:String(e)}var D=function(t){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(r=A(this,e,[t])).state={fontSize:"",letterSpacing:""},r}var r,i,a;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&T(t,e)}(e,t),r=e,a=[{key:"renderTickItem",value:function(t,e,r){return n.isValidElement(t)?n.cloneElement(t,e):o()(t)?t(e):n.createElement(f.x,x({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],(i=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=S(t,m),i=this.props,o=i.viewBox,a=S(i,b);return!(0,l.w)(r,o)||!(0,l.w)(n,a)||!(0,l.w)(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,i,o,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,y=c.tickSize,h=c.mirror,v=c.tickMargin,m=h?-1:1,b=t.tickSize||y,g=(0,d.hj)(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(i=l+ +!h*f)-m*b)-m*v,o=g;break;case"left":n=i=t.coordinate,o=(e=(r=u+ +!h*s)-m*b)-m*v,a=g;break;case"right":n=i=t.coordinate,o=(e=(r=u+ +h*s)+m*b)+m*v,a=g;break;default:e=r=t.coordinate,a=(n=(i=l+ +h*f)+m*b)+m*v,o=g}return{line:{x1:e,y1:n,x2:r,y2:i},tick:{x:o,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,i=t.width,o=t.height,a=t.orientation,l=t.mirror,s=t.axisLine,f=j(j(j({},(0,h.L6)(this.props,!1)),(0,h.L6)(s,!1)),{},{fill:"none"});if("top"===a||"bottom"===a){var p=+("top"===a&&!l||"bottom"===a&&l);f=j(j({},f),{},{x1:e,y1:r+p*o,x2:e+i,y2:r+p*o})}else{var d=+("left"===a&&!l||"right"===a&&l);f=j(j({},f),{},{x1:e+d*i,y1:r,x2:e+d*i,y2:r+o})}return n.createElement("line",x({},f,{className:(0,u.Z)("recharts-cartesian-axis-line",c()(s,"className"))}))}},{key:"renderTicks",value:function(t,r,i){var a=this,l=this.props,f=l.tickLine,p=l.stroke,d=l.tick,m=l.tickFormatter,b=l.unit,g=(0,v.f)(j(j({},this.props),{},{ticks:t}),r,i),O=this.getTickTextAnchor(),w=this.getTickVerticalAnchor(),S=(0,h.L6)(this.props,!1),P=(0,h.L6)(d,!1),A=j(j({},S),{},{fill:"none"},(0,h.L6)(f,!1)),k=g.map((function(t,r){var i=a.getTickLineCoord(t),l=i.line,h=i.tick,v=j(j(j(j({textAnchor:O,verticalAnchor:w},S),{},{stroke:"none",fill:p},P),h),{},{index:r,payload:t,visibleTicksCount:g.length,tickFormatter:m});return n.createElement(s.m,x({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},(0,y.bw)(a.props,t,r)),f&&n.createElement("line",x({},A,l,{className:(0,u.Z)("recharts-cartesian-axis-tick-line",c()(f,"className"))})),d&&e.renderTickItem(d,v,"".concat(o()(m)?m(t.value,r):t.value).concat(b||"")))}));return n.createElement("g",{className:"recharts-cartesian-axis-ticks"},k)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,i=e.width,a=e.height,c=e.ticksGenerator,l=e.className;if(e.hide)return null;var f=this.props,d=f.ticks,y=S(f,g),h=d;return o()(c)&&(h=d&&d.length>0?c(this.props):c(y)),i<=0||a<=0||!h||!h.length?null:n.createElement(s.m,{className:(0,u.Z)("recharts-cartesian-axis",l),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(h,this.state.fontSize,this.state.letterSpacing),p._.renderCallByParent(this.props))}}])&&P(r.prototype,i),a&&P(r,a),Object.defineProperty(r,"prototype",{writable:!1}),e}(n.Component);I(D,"displayName","CartesianAxis"),I(D,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},42333:function(t,e,r){r.d(e,{q:function(){return I}});var n=r(89526),i=r(39277),o=r.n(i),a=r(78706),c=r(16171),u=r(9410),l=r(36530),s=r(37561),f=r(27871),p=r(86545),d=["x1","y1","x2","y2","key"],y=["offset"];function h(t){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},h(t)}function v(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function m(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?v(Object(r),!0).forEach((function(e){b(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function b(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=h(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=h(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==h(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function g(){return g=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},g.apply(this,arguments)}function O(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var x=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,i=t.x,o=t.y,a=t.width,c=t.height;return n.createElement("rect",{x:i,y:o,width:a,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function w(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if(o()(t))r=t(e);else{var i=e.x1,a=e.y1,c=e.x2,l=e.y2,s=e.key,f=O(e,d),p=(0,u.L6)(f,!1),h=(p.offset,O(p,y));r=n.createElement("line",g({},h,{x1:i,y1:a,x2:c,y2:l,fill:"none",key:s}))}return r}function j(t){var e=t.x,r=t.width,i=t.horizontal,o=void 0===i||i,a=t.horizontalPoints;if(!o||!a||!a.length)return null;var c=a.map((function(n,i){var a=m(m({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(i),index:i});return w(o,a)}));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function S(t){var e=t.y,r=t.height,i=t.vertical,o=void 0===i||i,a=t.verticalPoints;if(!o||!a||!a.length)return null;var c=a.map((function(n,i){var a=m(m({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(i),index:i});return w(o,a)}));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function P(t){var e=t.horizontalFill,r=t.fillOpacity,i=t.x,o=t.y,a=t.width,c=t.height,u=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=u.map((function(t){return Math.round(t+o-o)})).sort((function(t,e){return t-e}));o!==s[0]&&s.unshift(0);var f=s.map((function(t,u){var l=!s[u+1]?o+c-t:s[u+1]-t;if(l<=0)return null;var f=u%e.length;return n.createElement("rect",{key:"react-".concat(u),y:t,x:i,height:l,width:a,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})}));return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function A(t){var e=t.vertical,r=void 0===e||e,i=t.verticalFill,o=t.fillOpacity,a=t.x,c=t.y,u=t.width,l=t.height,s=t.verticalPoints;if(!r||!i||!i.length)return null;var f=s.map((function(t){return Math.round(t+a-a)})).sort((function(t,e){return t-e}));a!==f[0]&&f.unshift(0);var p=f.map((function(t,e){var r=!f[e+1]?a+u-t:f[e+1]-t;if(r<=0)return null;var s=e%i.length;return n.createElement("rect",{key:"react-".concat(e),x:t,y:c,width:r,height:l,stroke:"none",fill:i[s],fillOpacity:o,className:"recharts-cartesian-grid-bg"})}));return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},p)}var k=function(t,e){var r=t.xAxis,n=t.width,i=t.height,o=t.offset;return(0,l.Rf)((0,s.f)(m(m(m({},f.O.defaultProps),r),{},{ticks:(0,l.uY)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.left,o.left+o.width,e)},E=function(t,e){var r=t.yAxis,n=t.width,i=t.height,o=t.offset;return(0,l.Rf)((0,s.f)(m(m(m({},f.O.defaultProps),r),{},{ticks:(0,l.uY)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.top,o.top+o.height,e)},T={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function I(t){var e,r,i,u,l,s,f=(0,p.zn)(),d=(0,p.Mw)(),y=(0,p.qD)(),v=m(m({},t),{},{stroke:null!==(e=t.stroke)&&void 0!==e?e:T.stroke,fill:null!==(r=t.fill)&&void 0!==r?r:T.fill,horizontal:null!==(i=t.horizontal)&&void 0!==i?i:T.horizontal,horizontalFill:null!==(u=t.horizontalFill)&&void 0!==u?u:T.horizontalFill,vertical:null!==(l=t.vertical)&&void 0!==l?l:T.vertical,verticalFill:null!==(s=t.verticalFill)&&void 0!==s?s:T.verticalFill,x:(0,c.hj)(t.x)?t.x:y.left,y:(0,c.hj)(t.y)?t.y:y.top,width:(0,c.hj)(t.width)?t.width:y.width,height:(0,c.hj)(t.height)?t.height:y.height}),b=v.x,O=v.y,w=v.width,I=v.height,C=v.syncWithTicks,D=v.horizontalValues,M=v.verticalValues,N=(0,p.CW)(),L=(0,p.Nf)();if(!(0,c.hj)(w)||w<=0||!(0,c.hj)(I)||I<=0||!(0,c.hj)(b)||b!==+b||!(0,c.hj)(O)||O!==+O)return null;var B=v.verticalCoordinatesGenerator||k,R=v.horizontalCoordinatesGenerator||E,z=v.horizontalPoints,F=v.verticalPoints;if((!z||!z.length)&&o()(R)){var _=D&&D.length,W=R({yAxis:L?m(m({},L),{},{ticks:_?D:L.ticks}):void 0,width:f,height:d,offset:y},!!_||C);(0,a.Z)(Array.isArray(W),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(h(W),"]")),Array.isArray(W)&&(z=W)}if((!F||!F.length)&&o()(B)){var Z=M&&M.length,K=B({xAxis:N?m(m({},N),{},{ticks:Z?M:N.ticks}):void 0,width:f,height:d,offset:y},!!Z||C);(0,a.Z)(Array.isArray(K),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(h(K),"]")),Array.isArray(K)&&(F=K)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(x,{fill:v.fill,fillOpacity:v.fillOpacity,x:v.x,y:v.y,width:v.width,height:v.height}),n.createElement(j,g({},v,{offset:y,horizontalPoints:z,xAxis:N,yAxis:L})),n.createElement(S,g({},v,{offset:y,verticalPoints:F,xAxis:N,yAxis:L})),n.createElement(P,g({},v,{horizontalPoints:z})),n.createElement(A,g({},v,{verticalPoints:F})))}I.displayName="CartesianGrid"},65370:function(t,e,r){r.d(e,{W:function(){return p}});var n=r(89526),i=r(78109),o=r(61452),a=r(9410),c=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function u(){return u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},u.apply(this,arguments)}function l(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return s(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function f(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function p(t){var e=t.offset,r=t.layout,s=t.width,p=t.dataKey,d=t.data,y=t.dataPointFormatter,h=t.xAxis,v=t.yAxis,m=f(t,c),b=(0,a.L6)(m,!1);"x"===t.direction&&"number"!==h.type&&(0,i.Z)(!1);var g=d.map((function(t){var i=y(t,p),a=i.x,c=i.y,f=i.value,d=i.errorVal;if(!d)return null;var m,g,O=[];if(Array.isArray(d)){var x=l(d,2);m=x[0],g=x[1]}else m=g=d;if("vertical"===r){var w=h.scale,j=c+e,S=j+s,P=j-s,A=w(f-m),k=w(f+g);O.push({x1:k,y1:S,x2:k,y2:P}),O.push({x1:A,y1:j,x2:k,y2:j}),O.push({x1:A,y1:S,x2:A,y2:P})}else if("horizontal"===r){var E=v.scale,T=a+e,I=T-s,C=T+s,D=E(f-m),M=E(f+g);O.push({x1:I,y1:M,x2:C,y2:M}),O.push({x1:T,y1:D,x2:T,y2:M}),O.push({x1:I,y1:D,x2:C,y2:D})}return n.createElement(o.m,u({className:"recharts-errorBar",key:"bar-".concat(O.map((function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)})))},b),O.map((function(t){return n.createElement("line",u({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))})))}));return n.createElement(o.m,{className:"recharts-errorBars"},g)}p.defaultProps={stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"},p.displayName="ErrorBar"},93264:function(t,e,r){r.d(e,{z:function(){return x}});var n=r(89526),i=r(39277),o=r.n(i),a=r(90512),c=r(61452),u=r(43774),l=r(87210),s=r(94694),f=r(16171),p=r(78706),d=r(33951),y=r(9410);function h(t){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},h(t)}function v(){return v=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},v.apply(this,arguments)}function m(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function b(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?m(Object(r),!0).forEach((function(e){g(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function g(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=h(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=h(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==h(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var O=function(t,e,r,n,i){var o=i.x1,a=i.x2,c=i.y1,u=i.y2,f=i.xAxis,p=i.yAxis;if(!f||!p)return null;var d=(0,l.Ky)({x:f.scale,y:p.scale}),y={x:t?d.x.apply(o,{position:"start"}):d.x.rangeMin,y:r?d.y.apply(c,{position:"start"}):d.y.rangeMin},h={x:e?d.x.apply(a,{position:"end"}):d.x.rangeMax,y:n?d.y.apply(u,{position:"end"}):d.y.rangeMax};return!(0,s.B)(i,"discard")||d.isInRange(y)&&d.isInRange(h)?(0,l.O1)(y,h):null};function x(t){var e=t.x1,r=t.x2,i=t.y1,o=t.y2,l=t.className,d=t.alwaysShow,h=t.clipPathId;(0,p.Z)(void 0===d,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var v=(0,f.P2)(e),m=(0,f.P2)(r),g=(0,f.P2)(i),w=(0,f.P2)(o),j=t.shape;if(!v&&!m&&!g&&!w&&!j)return null;var S=O(v,m,g,w,t);if(!S&&!j)return null;var P=(0,s.B)(t,"hidden")?"url(#".concat(h,")"):void 0;return n.createElement(c.m,{className:(0,a.Z)("recharts-reference-area",l)},x.renderRect(j,b(b({clipPath:P},(0,y.L6)(t,!0)),S)),u._.renderCallByParent(t,S))}x.displayName="ReferenceArea",x.defaultProps={isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1},x.renderRect=function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):o()(t)?t(e):n.createElement(d.A,v({},e,{className:"recharts-reference-area-rect"}))}},7629:function(t,e,r){r.d(e,{q:function(){return x}});var n=r(89526),i=r(39277),o=r.n(i),a=r(90512),c=r(61452),u=r(96963),l=r(43774),s=r(16171),f=r(94694),p=r(87210),d=r(78706),y=r(9410);function h(t){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},h(t)}function v(){return v=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},v.apply(this,arguments)}function m(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function b(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?m(Object(r),!0).forEach((function(e){g(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function g(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=h(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=h(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==h(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var O=function(t){var e=t.x,r=t.y,n=t.xAxis,i=t.yAxis,o=(0,p.Ky)({x:n.scale,y:i.scale}),a=o.apply({x:e,y:r},{bandAware:!0});return(0,f.B)(t,"discard")&&!o.isInRange(a)?null:a};function x(t){var e=t.x,r=t.y,i=t.r,o=t.alwaysShow,u=t.clipPathId,p=(0,s.P2)(e),h=(0,s.P2)(r);if((0,d.Z)(void 0===o,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!p||!h)return null;var v=O(t);if(!v)return null;var m=v.x,g=v.y,w=t.shape,j=t.className,S=b(b({clipPath:(0,f.B)(t,"hidden")?"url(#".concat(u,")"):void 0},(0,y.L6)(t,!0)),{},{cx:m,cy:g});return n.createElement(c.m,{className:(0,a.Z)("recharts-reference-dot",j)},x.renderDot(w,S),l._.renderCallByParent(t,{x:m-i,y:g-i,width:2*i,height:2*i}))}x.displayName="ReferenceDot",x.defaultProps={isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1},x.renderDot=function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):o()(t)?t(e):n.createElement(u.o,v({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))}},48586:function(t,e,r){r.d(e,{d:function(){return A}});var n=r(89526),i=r(39277),o=r.n(i),a=r(60479),c=r.n(a),u=r(90512),l=r(61452),s=r(43774),f=r(94694),p=r(16171),d=r(87210),y=r(78706),h=r(9410),v=r(86545);function m(t){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},m(t)}function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function g(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach((function(e){O(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function O(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=m(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=m(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==m(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function x(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return w(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return w(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function j(){return j=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},j.apply(this,arguments)}var S=function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):o()(t)?t(e):n.createElement("line",j({},e,{className:"recharts-reference-line-line"}))},P=function(t,e,r,n,i,o,a,u,l){var s=i.x,p=i.y,d=i.width,y=i.height;if(r){var h=l.y,v=t.y.apply(h,{position:o});if((0,f.B)(l,"discard")&&!t.y.isInRange(v))return null;var m=[{x:s+d,y:v},{x:s,y:v}];return"left"===u?m.reverse():m}if(e){var b=l.x,g=t.x.apply(b,{position:o});if((0,f.B)(l,"discard")&&!t.x.isInRange(g))return null;var O=[{x:g,y:p+y},{x:g,y:p}];return"top"===a?O.reverse():O}if(n){var x=l.segment.map((function(e){return t.apply(e,{position:o})}));return(0,f.B)(l,"discard")&&c()(x,(function(e){return!t.isInRange(e)}))?null:x}return null};function A(t){var e=t.x,r=t.y,i=t.segment,o=t.xAxisId,a=t.yAxisId,c=t.shape,m=t.className,b=t.alwaysShow,O=(0,v.sp)(),w=(0,v.bH)(o),j=(0,v.Ud)(a),A=(0,v.d2)();if(!O||!A)return null;(0,y.Z)(void 0===b,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var k=(0,d.Ky)({x:w.scale,y:j.scale}),E=(0,p.P2)(e),T=(0,p.P2)(r),I=i&&2===i.length,C=P(k,E,T,I,A,t.position,w.orientation,j.orientation,t);if(!C)return null;var D=x(C,2),M=D[0],N=M.x,L=M.y,B=D[1],R=B.x,z=B.y,F=g(g({clipPath:(0,f.B)(t,"hidden")?"url(#".concat(O,")"):void 0},(0,h.L6)(t,!0)),{},{x1:N,y1:L,x2:R,y2:z});return n.createElement(l.m,{className:(0,u.Z)("recharts-reference-line",m)},S(c,F),s._.renderCallByParent(t,(0,d._b)({x1:N,y1:L,x2:R,y2:z})))}A.displayName="ReferenceLine",A.defaultProps={isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"}},23007:function(t,e,r){r.d(e,{K:function(){return l}});var n=r(89526),i=r(90512),o=r(86545),a=r(27871),c=r(36530);function u(){return u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},u.apply(this,arguments)}var l=function(t){var e=t.xAxisId,r=(0,o.zn)(),l=(0,o.Mw)(),s=(0,o.bH)(e);return null==s?null:n.createElement(a.O,u({},s,{className:(0,i.Z)("recharts-".concat(s.axisType," ").concat(s.axisType),s.className),viewBox:{x:0,y:0,width:r,height:l},ticksGenerator:function(t){return(0,c.uY)(t,!0)}}))};l.displayName="XAxis",l.defaultProps={allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0}},58104:function(t,e,r){r.d(e,{B:function(){return l}});var n=r(89526),i=r(90512),o=r(86545),a=r(27871),c=r(36530);function u(){return u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},u.apply(this,arguments)}var l=function(t){var e=t.yAxisId,r=(0,o.zn)(),l=(0,o.Mw)(),s=(0,o.Ud)(e);return null==s?null:n.createElement(a.O,u({},s,{className:(0,i.Z)("recharts-".concat(s.axisType," ").concat(s.axisType),s.className),viewBox:{x:0,y:0,width:r,height:l},ticksGenerator:function(t){return(0,c.uY)(t,!0)}}))};l.displayName="YAxis",l.defaultProps={allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1}},37561:function(t,e,r){r.d(e,{f:function(){return h}});var n=r(39277),i=r.n(n),o=r(16171),a=r(99875),c=r(59509),u=r(87210);function l(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],i=0;i<t.length;i+=e){if(void 0!==r&&!0!==r(t[i]))return;n.push(t[i])}return n}function s(t,e,r,n,i){if(t*e<t*n||t*e>t*i)return!1;var o=r();return t*(e-t*o/2-n)>=0&&t*(e+t*o/2-i)<=0}function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach((function(e){y(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function y(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h(t,e,r){var n=t.tick,f=t.ticks,p=t.viewBox,y=t.minTickGap,h=t.orientation,v=t.interval,m=t.tickFormatter,b=t.unit,g=t.angle;if(!f||!f.length||!n)return[];if((0,o.hj)(v)||c.x.isSsr)return function(t,e){return l(t,e+1)}(f,"number"===typeof v&&(0,o.hj)(v)?v:0);var O=[],x="top"===h||"bottom"===h?"width":"height",w=b&&"width"===x?(0,a.xE)(b,{fontSize:e,letterSpacing:r}):{width:0,height:0},j=function(t,n){var o=i()(m)?m(t.value,n):t.value;return"width"===x?function(t,e,r){var n={width:t.width+e.width,height:t.height+e.height};return(0,u.xE)(n,r)}((0,a.xE)(o,{fontSize:e,letterSpacing:r}),w,g):(0,a.xE)(o,{fontSize:e,letterSpacing:r})[x]},S=f.length>=2?(0,o.uY)(f[1].coordinate-f[0].coordinate):1,P=function(t,e,r){var n="width"===r,i=t.x,o=t.y,a=t.width,c=t.height;return 1===e?{start:n?i:o,end:n?i+a:o+c}:{start:n?i+a:o+c,end:n?i:o}}(p,S,x);return"equidistantPreserveStart"===v?function(t,e,r,n,i){for(var o,a=(n||[]).slice(),c=e.start,u=e.end,f=0,p=1,d=c,y=function(){var e=null===n||void 0===n?void 0:n[f];if(void 0===e)return{v:l(n,p)};var o,a=f,y=function(){return void 0===o&&(o=r(e,a)),o},h=e.coordinate,v=0===f||s(t,h,y,d,u);v||(f=0,d=c,p+=1),v&&(d=h+t*(y()/2+i),f+=p)};p<=a.length;)if(o=y())return o.v;return[]}(S,P,j,f,y):(O="preserveStart"===v||"preserveStartEnd"===v?function(t,e,r,n,i,o){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(o){var f=n[c-1],p=r(f,c-1),y=t*(f.coordinate+t*p/2-l);a[c-1]=f=d(d({},f),{},{tickCoord:y>0?f.coordinate-y*t:f.coordinate}),s(t,f.tickCoord,(function(){return p}),u,l)&&(l=f.tickCoord-t*(p/2+i),a[c-1]=d(d({},f),{},{isShow:!0}))}for(var h=o?c-1:c,v=function(e){var n,o=a[e],c=function(){return void 0===n&&(n=r(o,e)),n};if(0===e){var f=t*(o.coordinate-t*c()/2-u);a[e]=o=d(d({},o),{},{tickCoord:f<0?o.coordinate-f*t:o.coordinate})}else a[e]=o=d(d({},o),{},{tickCoord:o.coordinate});s(t,o.tickCoord,c,u,l)&&(u=o.tickCoord+t*(c()/2+i),a[e]=d(d({},o),{},{isShow:!0}))},m=0;m<h;m++)v(m);return a}(S,P,j,f,y,"preserveStartEnd"===v):function(t,e,r,n,i){for(var o=(n||[]).slice(),a=o.length,c=e.start,u=e.end,l=function(e){var n,l=o[e],f=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var p=t*(l.coordinate+t*f()/2-u);o[e]=l=d(d({},l),{},{tickCoord:p>0?l.coordinate-p*t:l.coordinate})}else o[e]=l=d(d({},l),{},{tickCoord:l.coordinate});s(t,l.tickCoord,f,c,u)&&(u=l.tickCoord-t*(f()/2+i),o[e]=d(d({},l),{},{isShow:!0}))},f=a-1;f>=0;f--)l(f);return o}(S,P,j,f,y),O.filter((function(t){return t.isShow})))}},79416:function(t,e,r){r.d(e,{v:function(){return u}});var n=r(88259),i=r(48218),o=r(23007),a=r(58104),c=r(87210),u=(0,n.z)({chartName:"BarChart",GraphicalChild:i.$,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:o.K},{axisType:"yAxis",AxisComp:a.B}],formatAxisMap:c.t9})},77434:function(t,e,r){r.d(e,{u:function(){return u}});var n=r(88259),i=r(10562),o=r(85322),a=r(80072),c=r(86246),u=(0,n.z)({chartName:"PieChart",GraphicalChild:c.b,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:i.I},{axisType:"radiusAxis",AxisComp:o.S}],formatAxisMap:a.t9,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}})},88259:function(t,e,r){r.d(e,{z:function(){return zt}});var n=r(89526),i=r(51391),o=r.n(i),a=r(39277),c=r.n(a),u=r(58120),l=r.n(u),s=r(80089),f=r.n(s),p=r(65853),d=r.n(p),y=r(38172),h=r.n(y),v=r(90512),m=r(78109),b=r(93386),g=r(61452),O=r(18170),x=r(71015),w=r(96963),j=r(33951),S=r(9410),P=r(88974),A=r(99875),k=r(16171),E=r(36530),T=r(65436),I=r(7629),C=r(48586),D=r(93264),M=r(94694);function N(t){return function(t){if(Array.isArray(t))return L(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return L(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return L(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function L(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var B=function(t,e,r,n,i){var o=(0,S.NN)(t,C.d),a=(0,S.NN)(t,I.q),c=[].concat(N(o),N(a)),u=(0,S.NN)(t,D.z),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce((function(t,e){if(e.props[l]===r&&(0,M.B)(e.props,"extendDomain")&&(0,k.hj)(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t}),f)),u.length){var p="".concat(s,"1"),d="".concat(s,"2");f=u.reduce((function(t,e){if(e.props[l]===r&&(0,M.B)(e.props,"extendDomain")&&(0,k.hj)(e.props[p])&&(0,k.hj)(e.props[d])){var n=e.props[p],i=e.props[d];return[Math.min(t[0],n,i),Math.max(t[1],n,i)]}return t}),f)}return i&&i.length&&(f=i.reduce((function(t,e){return(0,k.hj)(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t}),f)),f},R=r(80072),z=r(68201),F=r(33034),_=new(r.n(F)()),W="recharts.syncMouseEvents",Z=r(33790);function K(t){return K="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},K(t)}function X(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,G(n.key),n)}}function V(t,e,r){return(e=G(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function G(t){var e=function(t,e){if("object"!=K(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=K(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==K(e)?e:String(e)}var Y=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),V(this,"activeIndex",0),V(this,"coordinateList",[]),V(this,"layout","horizontal")}var e,r,n;return e=t,(r=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,i=t.container,o=void 0===i?null:i,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!==n&&void 0!==n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!==o&&void 0!==o?o:this.container,this.layout=null!==c&&void 0!==c?c:this.layout,this.offset=null!==l&&void 0!==l?l:this.offset,this.mouseHandlerCallback=null!==f&&void 0!==f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){var t,e;if("horizontal"===this.layout&&0!==this.coordinateList.length){var r=this.container.getBoundingClientRect(),n=r.x,i=r.y,o=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,u=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=n+a+c,s=i+this.offset.top+o/2+u;this.mouseHandlerCallback({pageX:l,pageY:s})}}}])&&X(e.prototype,r),n&&X(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();var U=r(69531),H=r(92147),$=r(74791);function q(t){var e=t.cx,r=t.cy,n=t.radius,i=t.startAngle,o=t.endAngle;return{points:[(0,R.op)(e,r,n,i),(0,R.op)(e,r,n,o)],cx:e,cy:r,radius:n,startAngle:i,endAngle:o}}var Q=r(61001);function J(t,e,r){var n,i,o,a;if("horizontal"===t)o=n=e.x,i=r.top,a=r.top+r.height;else if("vertical"===t)a=i=e.y,n=r.left,o=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return q(e);var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=(0,R.op)(c,u,l,f),d=(0,R.op)(c,u,s,f);n=p.x,i=p.y,o=d.x,a=d.y}return[{x:n,y:i},{x:o,y:a}]}function tt(t){return tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},tt(t)}function et(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function rt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?et(Object(r),!0).forEach((function(e){nt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):et(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function nt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=tt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tt(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function it(t){var e,r=t.element,i=t.tooltipEventType,o=t.isActive,a=t.activeCoordinate,c=t.activePayload,u=t.offset,l=t.activeTooltipIndex,s=t.tooltipAxisBandSize,f=t.layout,p=t.chartName;if(!r||!r.props.cursor||!o||!a||"ScatterChart"!==p&&"axis"!==i)return null;var d=H.H;if("ScatterChart"===p)e=a,d=$.X;else if("BarChart"===p)e=function(t,e,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===t?e.x-i:r.left+.5,y:"horizontal"===t?r.top+.5:e.y-i,width:"horizontal"===t?n:r.width-1,height:"horizontal"===t?r.height-1:n}}(f,a,u,s),d=j.A;else if("radial"===f){var y=q(a),h=y.cx,m=y.cy,b=y.radius;e={cx:h,cy:m,startAngle:y.startAngle,endAngle:y.endAngle,innerRadius:b,outerRadius:b},d=Q.L}else e={points:J(f,a,u)},d=H.H;var g=rt(rt(rt(rt({stroke:"#ccc",pointerEvents:"none"},u),e),(0,S.L6)(r.props.cursor,!1)),{},{payload:c,payloadIndex:l,className:(0,v.Z)("recharts-tooltip-cursor",r.props.cursor.className)});return(0,n.isValidElement)(r.props.cursor)?(0,n.cloneElement)(r.props.cursor,g):(0,n.createElement)(d,g)}var ot=r(86545),at=["item"],ct=["children","className","width","height","style","compact","title","desc"];function ut(t){return ut="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ut(t)}function lt(){return lt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},lt.apply(this,arguments)}function st(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||gt(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ft(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function pt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,St(n.key),n)}}function dt(t,e,r){return e=ht(e),function(t,e){if(e&&("object"===ut(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return vt(t)}(t,yt()?Reflect.construct(e,r||[],ht(t).constructor):e.apply(t,r))}function yt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(yt=function(){return!!t})()}function ht(t){return ht=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ht(t)}function vt(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function mt(t,e){return mt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},mt(t,e)}function bt(t){return function(t){if(Array.isArray(t))return Ot(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||gt(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function gt(t,e){if(t){if("string"===typeof t)return Ot(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ot(t,e):void 0}}function Ot(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function xt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function wt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?xt(Object(r),!0).forEach((function(e){jt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function jt(t,e,r){return(e=St(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function St(t){var e=function(t,e){if("object"!=ut(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ut(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ut(e)?e:String(e)}var Pt={xAxis:["bottom","top"],yAxis:["left","right"]},At={width:"100%",height:"100%"},kt={x:0,y:0};function Et(t){return t}var Tt=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,i=e.dataEndIndex,o=(null!==r&&void 0!==r?r:[]).reduce((function(t,e){var r=e.props.data;return r&&r.length?[].concat(bt(t),bt(r)):t}),[]);return o.length>0?o:t&&t.length&&(0,k.hj)(n)&&(0,k.hj)(i)?t.slice(n,i+1):[]};function It(t){return"number"===t?[0,"auto"]:void 0}var Ct=function(t,e,r,n){var i=t.graphicalItems,o=t.tooltipAxis,a=Tt(e,t);return r<0||!i||!i.length||r>=a.length?null:i.reduce((function(i,c){var u,l,s=null!==(u=c.props.data)&&void 0!==u?u:e;if(s&&t.dataStartIndex+t.dataEndIndex!==0&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),o.dataKey&&!o.allowDuplicatedCategory){var f=void 0===s?a:s;l=(0,k.Ap)(f,o.dataKey,n)}else l=s&&s[r]||a[r];return l?[].concat(bt(i),[(0,E.Qo)(c,l)]):i}),[])},Dt=function(t,e,r,n){var i=n||{x:t.chartX,y:t.chartY},o=function(t,e){return"horizontal"===e?t.x:"vertical"===e?t.y:"centric"===e?t.angle:t.radius}(i,r),a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=(0,E.VO)(o,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=Ct(t,e,l,s),p=function(t,e,r,n){var i=e.find((function(t){return t&&t.index===r}));if(i){if("horizontal"===t)return{x:i.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:i.coordinate};if("centric"===t){var o=i.coordinate,a=n.radius;return wt(wt(wt({},n),(0,R.op)(n.cx,n.cy,a,o)),{},{angle:o,radius:a})}var c=i.coordinate,u=n.angle;return wt(wt(wt({},n),(0,R.op)(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return kt}(r,a,l,i);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},Mt=function(t,e){var r=e.axes,n=e.graphicalItems,i=e.axisType,a=e.axisIdKey,c=e.stackGroups,u=e.dataStartIndex,s=e.dataEndIndex,f=t.layout,p=t.children,d=t.stackOffset,y=(0,E.NA)(f,i);return r.reduce((function(e,r){var h,v=r.props,m=v.type,b=v.dataKey,g=v.allowDataOverflow,O=v.allowDuplicatedCategory,x=v.scale,w=v.ticks,j=v.includeHidden,S=r.props[a];if(e[S])return e;var P,A,T,I=Tt(t.data,{graphicalItems:n.filter((function(t){return t.props[a]===S})),dataStartIndex:u,dataEndIndex:s}),C=I.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null===t||void 0===t?void 0:t[0],i=null===t||void 0===t?void 0:t[1];if(n&&i&&(0,k.hj)(n)&&(0,k.hj)(i))return!0}return!1})(r.props.domain,g,m)&&(P=(0,E.LG)(r.props.domain,null,g),!y||"number"!==m&&"auto"===x||(T=(0,E.gF)(I,b,"category")));var D=It(m);if(!P||0===P.length){var M,N=null!==(M=r.props.domain)&&void 0!==M?M:D;if(b){if(P=(0,E.gF)(I,b,m),"category"===m&&y){var L=(0,k.bv)(P);O&&L?(A=P,P=l()(0,C)):O||(P=(0,E.ko)(N,P,r).reduce((function(t,e){return t.indexOf(e)>=0?t:[].concat(bt(t),[e])}),[]))}else if("category"===m)P=O?P.filter((function(t){return""!==t&&!o()(t)})):(0,E.ko)(N,P,r).reduce((function(t,e){return t.indexOf(e)>=0||""===e||o()(e)?t:[].concat(bt(t),[e])}),[]);else if("number"===m){var R=(0,E.ZI)(I,n.filter((function(t){return t.props[a]===S&&(j||!t.props.hide)})),b,i,f);R&&(P=R)}!y||"number"!==m&&"auto"===x||(T=(0,E.gF)(I,b,"category"))}else P=y?l()(0,C):c&&c[S]&&c[S].hasStack&&"number"===m?"expand"===d?[0,1]:(0,E.EB)(c[S].stackGroups,u,s):(0,E.s6)(I,n.filter((function(t){return t.props[a]===S&&(j||!t.props.hide)})),m,f,!0);if("number"===m)P=B(p,P,S,i,w),N&&(P=(0,E.LG)(N,P,g));else if("category"===m&&N){var z=N;P.every((function(t){return z.indexOf(t)>=0}))&&(P=z)}}return wt(wt({},e),{},jt({},S,wt(wt({},r.props),{},{axisType:i,domain:P,categoricalDomain:T,duplicateDomain:A,originalDomain:null!==(h=r.props.domain)&&void 0!==h?h:D,isCategorical:y,layout:f})))}),{})},Nt=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,i=e.AxisComp,o=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.children,p="".concat(n,"Id"),d=(0,S.NN)(s,i),y={};return d&&d.length?y=Mt(t,{axes:d,graphicalItems:o,axisType:n,axisIdKey:p,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):o&&o.length&&(y=function(t,e){var r=e.graphicalItems,n=e.Axis,i=e.axisType,o=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.layout,p=t.children,d=Tt(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),y=d.length,h=(0,E.NA)(s,i),v=-1;return r.reduce((function(t,e){var m,b=e.props[o],g=It("number");return t[b]?t:(v++,h?m=l()(0,y):a&&a[b]&&a[b].hasStack?(m=(0,E.EB)(a[b].stackGroups,c,u),m=B(p,m,b,i)):(m=(0,E.LG)(g,(0,E.s6)(d,r.filter((function(t){return t.props[o]===b&&!t.props.hide})),"number",s),n.defaultProps.allowDataOverflow),m=B(p,m,b,i)),wt(wt({},t),{},jt({},b,wt(wt({axisType:i},n.defaultProps),{},{hide:!0,orientation:f()(Pt,"".concat(i,".").concat(v%2),null),domain:m,originalDomain:g,isCategorical:h,layout:s}))))}),{})}(t,{Axis:i,graphicalItems:o,axisType:n,axisIdKey:p,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),y},Lt=function(t){var e=t.children,r=t.defaultShowTooltip,n=(0,S.sP)(e,P.B),i=0,o=0;return t.data&&0!==t.data.length&&(o=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(i=n.props.startIndex),n.props.endIndex>=0&&(o=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:i,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:Boolean(r)}},Bt=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},Rt=function(t,e){return"xAxis"===e?t[e].width:"yAxis"===e?t[e].height:void 0},zt=function(t){var e,r=t.chartName,i=t.GraphicalChild,a=t.defaultTooltipEventType,u=void 0===a?"axis":a,l=t.validateTooltipEventTypes,s=void 0===l?["axis"]:l,p=t.axisComponents,y=t.legendContent,I=t.formatAxisMap,C=t.defaultProps,D=function(t,e){var r=e.graphicalItems,n=e.stackGroups,i=e.offset,a=e.updateId,c=e.dataStartIndex,u=e.dataEndIndex,l=t.barSize,s=t.layout,f=t.barGap,d=t.barCategoryGap,y=t.maxBarSize,h=Bt(s),v=h.numericAxisName,b=h.cateAxisName,g=function(t){return!(!t||!t.length)&&t.some((function(t){var e=(0,S.Gf)(t&&t.type);return e&&e.indexOf("Bar")>=0}))}(r),O=[];return r.forEach((function(r,h){var x=Tt(t.data,{graphicalItems:[r],dataStartIndex:c,dataEndIndex:u}),w=r.props,j=w.dataKey,P=w.maxBarSize,A=r.props["".concat(v,"Id")],k=r.props["".concat(b,"Id")],T=p.reduce((function(t,n){var i=e["".concat(n.axisType,"Map")],o=r.props["".concat(n.axisType,"Id")];i&&i[o]||"zAxis"===n.axisType||(0,m.Z)(!1);var a=i[o];return wt(wt({},t),{},jt(jt({},n.axisType,a),"".concat(n.axisType,"Ticks"),(0,E.uY)(a)))}),{}),I=T[b],C=T["".concat(b,"Ticks")],D=n&&n[A]&&n[A].hasStack&&(0,E.O3)(r,n[A].stackGroups),M=(0,S.Gf)(r.type).indexOf("Bar")>=0,N=(0,E.zT)(I,C),L=[],B=g&&(0,E.pt)({barSize:l,stackGroups:n,totalSize:Rt(T,b)});if(M){var R,z,F=o()(P)?y:P,_=null!==(R=null!==(z=(0,E.zT)(I,C,!0))&&void 0!==z?z:F)&&void 0!==R?R:0;L=(0,E.qz)({barGap:f,barCategoryGap:d,bandSize:_!==N?_:N,sizeList:B[k],maxBarSize:F}),_!==N&&(L=L.map((function(t){return wt(wt({},t),{},{position:wt(wt({},t.position),{},{offset:t.position.offset-_/2})})})))}var W=r&&r.type&&r.type.getComposedData;W&&O.push({props:wt(wt({},W(wt(wt({},T),{},{displayedData:x,props:t,dataKey:j,item:r,bandSize:N,barPosition:L,offset:i,stackedData:D,layout:s,dataStartIndex:c,dataEndIndex:u}))),{},jt(jt(jt({key:r.key||"item-".concat(h)},v,T[v]),b,T[b]),"animationId",a)),childIndex:(0,S.$R)(r,t.children),item:r})})),O},M=function(t,e){var n=t.props,o=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!(0,S.TT)({props:n}))return null;var u=n.children,l=n.layout,s=n.stackOffset,y=n.data,h=n.reverseStackOrder,v=Bt(l),m=v.numericAxisName,b=v.cateAxisName,g=(0,S.NN)(u,i),O=(0,E.wh)(y,g,"".concat(m,"Id"),"".concat(b,"Id"),s,h),w=p.reduce((function(t,e){var r="".concat(e.axisType,"Map");return wt(wt({},t),{},jt({},r,Nt(n,wt(wt({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&O,dataStartIndex:o,dataEndIndex:a}))))}),{}),j=function(t,e){var r=t.props,n=t.graphicalItems,i=t.xAxisMap,o=void 0===i?{}:i,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,p=r.margin||{},d=(0,S.sP)(s,P.B),y=(0,S.sP)(s,x.D),h=Object.keys(c).reduce((function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:wt(wt({},t),{},jt({},n,t[n]+r.width))}),{left:p.left||0,right:p.right||0}),v=Object.keys(o).reduce((function(t,e){var r=o[e],n=r.orientation;return r.mirror||r.hide?t:wt(wt({},t),{},jt({},n,f()(t,"".concat(n))+r.height))}),{top:p.top||0,bottom:p.bottom||0}),m=wt(wt({},v),h),b=m.bottom;d&&(m.bottom+=d.props.height||P.B.defaultProps.height),y&&e&&(m=(0,E.By)(m,n,r,e));var g=u-m.left-m.right,O=l-m.top-m.bottom;return wt(wt({brushBottom:b},m),{},{width:Math.max(g,0),height:Math.max(O,0)})}(wt(wt({},w),{},{props:n,graphicalItems:g}),null===e||void 0===e?void 0:e.legendBBox);Object.keys(w).forEach((function(t){w[t]=I(n,w[t],j,t.replace("Map",""),r)}));var A=function(t){var e=(0,k.Kt)(t),r=(0,E.uY)(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:d()(r,(function(t){return t.coordinate})),tooltipAxis:e,tooltipAxisBandSize:(0,E.zT)(e,r)}}(w["".concat(b,"Map")]),T=D(n,wt(wt({},w),{},{dataStartIndex:o,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:O,offset:j}));return wt(wt({formattedGraphicalItems:T,graphicalItems:g,offset:j,stackGroups:O},A),w)};return e=function(t){function e(t){var i,a,u;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),jt(vt(u=dt(this,e,[t])),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),jt(vt(u),"accessibilityManager",new Y),jt(vt(u),"handleLegendBBoxUpdate",(function(t){if(t){var e=u.state,r=e.dataStartIndex,n=e.dataEndIndex,i=e.updateId;u.setState(wt({legendBBox:t},M({props:u.props,dataStartIndex:r,dataEndIndex:n,updateId:i},wt(wt({},u.state),{},{legendBBox:t}))))}})),jt(vt(u),"handleReceiveSyncEvent",(function(t,e,r){if(u.props.syncId===t){if(r===u.eventEmitterSymbol&&"function"!==typeof u.props.syncMethod)return;u.applySyncEvent(e)}})),jt(vt(u),"handleBrushChange",(function(t){var e=t.startIndex,r=t.endIndex;if(e!==u.state.dataStartIndex||r!==u.state.dataEndIndex){var n=u.state.updateId;u.setState((function(){return wt({dataStartIndex:e,dataEndIndex:r},M({props:u.props,dataStartIndex:e,dataEndIndex:r,updateId:n},u.state))})),u.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}})),jt(vt(u),"handleMouseEnter",(function(t){var e=u.getMouseInfo(t);if(e){var r=wt(wt({},e),{},{isTooltipActive:!0});u.setState(r),u.triggerSyncEvent(r);var n=u.props.onMouseEnter;c()(n)&&n(r,t)}})),jt(vt(u),"triggeredAfterMouseMove",(function(t){var e=u.getMouseInfo(t),r=e?wt(wt({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};u.setState(r),u.triggerSyncEvent(r);var n=u.props.onMouseMove;c()(n)&&n(r,t)})),jt(vt(u),"handleItemMouseEnter",(function(t){u.setState((function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}}))})),jt(vt(u),"handleItemMouseLeave",(function(){u.setState((function(){return{isTooltipActive:!1}}))})),jt(vt(u),"handleMouseMove",(function(t){t.persist(),u.throttleTriggeredAfterMouseMove(t)})),jt(vt(u),"handleMouseLeave",(function(t){u.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};u.setState(e),u.triggerSyncEvent(e);var r=u.props.onMouseLeave;c()(r)&&r(e,t)})),jt(vt(u),"handleOuterEvent",(function(t){var e,r=(0,S.Bh)(t),n=f()(u.props,"".concat(r));r&&c()(n)&&n(null!==(e=/.*touch.*/i.test(r)?u.getMouseInfo(t.changedTouches[0]):u.getMouseInfo(t))&&void 0!==e?e:{},t)})),jt(vt(u),"handleClick",(function(t){var e=u.getMouseInfo(t);if(e){var r=wt(wt({},e),{},{isTooltipActive:!0});u.setState(r),u.triggerSyncEvent(r);var n=u.props.onClick;c()(n)&&n(r,t)}})),jt(vt(u),"handleMouseDown",(function(t){var e=u.props.onMouseDown;c()(e)&&e(u.getMouseInfo(t),t)})),jt(vt(u),"handleMouseUp",(function(t){var e=u.props.onMouseUp;c()(e)&&e(u.getMouseInfo(t),t)})),jt(vt(u),"handleTouchMove",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.throttleTriggeredAfterMouseMove(t.changedTouches[0])})),jt(vt(u),"handleTouchStart",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.handleMouseDown(t.changedTouches[0])})),jt(vt(u),"handleTouchEnd",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.handleMouseUp(t.changedTouches[0])})),jt(vt(u),"triggerSyncEvent",(function(t){void 0!==u.props.syncId&&_.emit(W,u.props.syncId,t,u.eventEmitterSymbol)})),jt(vt(u),"applySyncEvent",(function(t){var e=u.props,r=e.layout,n=e.syncMethod,i=u.state.updateId,o=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)u.setState(wt({dataStartIndex:o,dataEndIndex:a},M({props:u.props,dataStartIndex:o,dataEndIndex:a,updateId:i},u.state)));else if(void 0!==t.activeTooltipIndex){var c=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=u.state,p=f.offset,d=f.tooltipTicks;if(!p)return;if("function"===typeof n)s=n(d,t);else if("value"===n){s=-1;for(var y=0;y<d.length;y++)if(d[y].value===t.activeLabel){s=y;break}}var h=wt(wt({},p),{},{x:p.left,y:p.top}),v=Math.min(c,h.x+h.width),m=Math.min(l,h.y+h.height),b=d[s]&&d[s].value,g=Ct(u.state,u.props.data,s),O=d[s]?{x:"horizontal"===r?d[s].coordinate:v,y:"horizontal"===r?m:d[s].coordinate}:kt;u.setState(wt(wt({},t),{},{activeLabel:b,activeCoordinate:O,activePayload:g,activeTooltipIndex:s}))}else u.setState(t)})),jt(vt(u),"renderCursor",(function(t){var e,i=u.state,o=i.isTooltipActive,a=i.activeCoordinate,c=i.activePayload,l=i.offset,s=i.activeTooltipIndex,f=i.tooltipAxisBandSize,p=u.getTooltipEventType(),d=null!==(e=t.props.active)&&void 0!==e?e:o,y=u.props.layout,h=t.key||"_recharts-cursor";return n.createElement(it,{key:h,activeCoordinate:a,activePayload:c,activeTooltipIndex:s,chartName:r,element:t,isActive:d,layout:y,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})})),jt(vt(u),"renderPolarAxis",(function(t,e,r){var i=f()(t,"type.axisType"),o=f()(u.state,"".concat(i,"Map")),a=o&&o[t.props["".concat(i,"Id")]];return(0,n.cloneElement)(t,wt(wt({},a),{},{className:(0,v.Z)(i,a.className),key:t.key||"".concat(e,"-").concat(r),ticks:(0,E.uY)(a,!0)}))})),jt(vt(u),"renderPolarGrid",(function(t){var e=t.props,r=e.radialLines,i=e.polarAngles,o=e.polarRadius,a=u.state,c=a.radiusAxisMap,l=a.angleAxisMap,s=(0,k.Kt)(c),f=(0,k.Kt)(l),p=f.cx,d=f.cy,y=f.innerRadius,h=f.outerRadius;return(0,n.cloneElement)(t,{polarAngles:Array.isArray(i)?i:(0,E.uY)(f,!0).map((function(t){return t.coordinate})),polarRadius:Array.isArray(o)?o:(0,E.uY)(s,!0).map((function(t){return t.coordinate})),cx:p,cy:d,innerRadius:y,outerRadius:h,key:t.key||"polar-grid",radialLines:r})})),jt(vt(u),"renderLegend",(function(){var t=u.state.formattedGraphicalItems,e=u.props,r=e.children,i=e.width,o=e.height,a=u.props.margin||{},c=i-(a.left||0)-(a.right||0),l=(0,T.z)({children:r,formattedGraphicalItems:t,legendWidth:c,legendContent:y});if(!l)return null;var s=l.item,f=ft(l,at);return(0,n.cloneElement)(s,wt(wt({},f),{},{chartWidth:i,chartHeight:o,margin:a,onBBoxUpdate:u.handleLegendBBoxUpdate}))})),jt(vt(u),"renderTooltip",(function(){var t,e=u.props,r=e.children,i=e.accessibilityLayer,o=(0,S.sP)(r,O.u);if(!o)return null;var a=u.state,c=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,d=null!==(t=o.props.active)&&void 0!==t?t:c;return(0,n.cloneElement)(o,{viewBox:wt(wt({},p),{},{x:p.left,y:p.top}),active:d,label:f,payload:d?s:[],coordinate:l,accessibilityLayer:i})})),jt(vt(u),"renderBrush",(function(t){var e=u.props,r=e.margin,i=e.data,o=u.state,a=o.offset,c=o.dataStartIndex,l=o.dataEndIndex,s=o.updateId;return(0,n.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:(0,E.DO)(u.handleBrushChange,t.props.onChange),data:i,x:(0,k.hj)(t.props.x)?t.props.x:a.left,y:(0,k.hj)(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:(0,k.hj)(t.props.width)?t.props.width:a.width,startIndex:c,endIndex:l,updateId:"brush-".concat(s)})})),jt(vt(u),"renderReferenceElement",(function(t,e,r){if(!t)return null;var i=vt(u).clipPathId,o=u.state,a=o.xAxisMap,c=o.yAxisMap,l=o.offset,s=t.props,f=s.xAxisId,p=s.yAxisId;return(0,n.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[f],yAxis:c[p],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:i})})),jt(vt(u),"renderActivePoints",(function(t){var r=t.item,n=t.activePoint,i=t.basePoint,o=t.childIndex,a=t.isRange,c=[],u=r.props.key,l=r.item.props,s=l.activeDot,f=wt(wt({index:o,dataKey:l.dataKey,cx:n.x,cy:n.y,r:4,fill:(0,E.fk)(r.item),strokeWidth:2,stroke:"#fff",payload:n.payload,value:n.value,key:"".concat(u,"-activePoint-").concat(o)},(0,S.L6)(s,!1)),(0,Z.Ym)(s));return c.push(e.renderActiveDot(s,f)),i?c.push(e.renderActiveDot(s,wt(wt({},f),{},{cx:i.x,cy:i.y,key:"".concat(u,"-basePoint-").concat(o)}))):a&&c.push(null),c})),jt(vt(u),"renderGraphicChild",(function(t,e,r){var i=u.filterFormatItem(t,e,r);if(!i)return null;var a=u.getTooltipEventType(),c=u.state,l=c.isTooltipActive,s=c.tooltipAxis,f=c.activeTooltipIndex,p=c.activeLabel,d=u.props.children,y=(0,S.sP)(d,O.u),h=i.props,v=h.points,m=h.isRange,b=h.baseLine,g=i.item.props,x=g.activeDot,w=g.hide,j=g.activeBar,P=g.activeShape,A=Boolean(!w&&l&&y&&(x||j||P)),T={};"axis"!==a&&y&&"click"===y.props.trigger?T={onClick:(0,E.DO)(u.handleItemMouseEnter,t.props.onClick)}:"axis"!==a&&(T={onMouseLeave:(0,E.DO)(u.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:(0,E.DO)(u.handleItemMouseEnter,t.props.onMouseEnter)});var I=(0,n.cloneElement)(t,wt(wt({},i.props),T));if(A){if(!(f>=0)){var C,D=(null!==(C=u.getItemByXY(u.state.activeCoordinate))&&void 0!==C?C:{graphicalItem:I}).graphicalItem,M=D.item,N=void 0===M?t:M,L=D.childIndex,B=wt(wt(wt({},i.props),T),{},{activeIndex:L});return[(0,n.cloneElement)(N,B),null,null]}var R,z;if(s.dataKey&&!s.allowDuplicatedCategory){var F="function"===typeof s.dataKey?function(t){return"function"===typeof s.dataKey?s.dataKey(t.payload):null}:"payload.".concat(s.dataKey.toString());R=(0,k.Ap)(v,F,p),z=m&&b&&(0,k.Ap)(b,F,p)}else R=null===v||void 0===v?void 0:v[f],z=m&&b&&b[f];if(P||j){var _=void 0!==t.props.activeIndex?t.props.activeIndex:f;return[(0,n.cloneElement)(t,wt(wt(wt({},i.props),T),{},{activeIndex:_})),null,null]}if(!o()(R))return[I].concat(bt(u.renderActivePoints({item:i,activePoint:R,basePoint:z,childIndex:f,isRange:m})))}return m?[I,null,null]:[I,null]})),jt(vt(u),"renderCustomized",(function(t,e,r){return(0,n.cloneElement)(t,wt(wt({key:"recharts-customized-".concat(r)},u.props),u.state))})),jt(vt(u),"renderMap",{CartesianGrid:{handler:Et,once:!0},ReferenceArea:{handler:u.renderReferenceElement},ReferenceLine:{handler:Et},ReferenceDot:{handler:u.renderReferenceElement},XAxis:{handler:Et},YAxis:{handler:Et},Brush:{handler:u.renderBrush,once:!0},Bar:{handler:u.renderGraphicChild},Line:{handler:u.renderGraphicChild},Area:{handler:u.renderGraphicChild},Radar:{handler:u.renderGraphicChild},RadialBar:{handler:u.renderGraphicChild},Scatter:{handler:u.renderGraphicChild},Pie:{handler:u.renderGraphicChild},Funnel:{handler:u.renderGraphicChild},Tooltip:{handler:u.renderCursor,once:!0},PolarGrid:{handler:u.renderPolarGrid,once:!0},PolarAngleAxis:{handler:u.renderPolarAxis},PolarRadiusAxis:{handler:u.renderPolarAxis},Customized:{handler:u.renderCustomized}}),u.clipPathId="".concat(null!==(i=t.id)&&void 0!==i?i:(0,k.EL)("recharts"),"-clip"),u.throttleTriggeredAfterMouseMove=h()(u.triggeredAfterMouseMove,null!==(a=t.throttleDelay)&&void 0!==a?a:1e3/60),u.state={},u}var i,a,l;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&mt(t,e)}(e,t),i=e,a=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,i=t.layout,o=(0,S.sP)(e,O.u);if(o){var a=o.props.defaultIndex;if(!("number"!==typeof a||a<0||a>this.state.tooltipTicks.length)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=Ct(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===i?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find((function(t){return"Scatter"===t.item.type.name}));p&&(f=wt(wt({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var d={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(d),this.renderCursor(o),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){return this.props.accessibilityLayer?(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin&&this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}}),null):null;var r,n}},{key:"componentDidUpdate",value:function(t){(0,S.rL)([(0,S.sP)(t.children,O.u)],[(0,S.sP)(this.props.children,O.u)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=(0,S.sP)(this.props.children,O.u);if(t&&"boolean"===typeof t.props.shared){var e=t.props.shared?"axis":"item";return s.indexOf(e)>=0?e:u}return u}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n=(0,A.os)(r),i={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},o=r.width/e.offsetWidth||1,a=this.inRange(i.chartX,i.chartY,o);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap;if("axis"!==this.getTooltipEventType()&&u&&l){var s=(0,k.Kt)(u).scale,f=(0,k.Kt)(l).scale,p=s&&s.invert?s.invert(i.chartX):null,d=f&&f.invert?f.invert(i.chartY):null;return wt(wt({},i),{},{xValue:p,yValue:d})}var y=Dt(this.state,this.props.data,this.props.layout,a);return y?wt(wt({},i),y):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,i=t/r,o=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return i>=a.left&&i<=a.left+a.width&&o>=a.top&&o<=a.top+a.height?{x:i,y:o}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;if(u&&l){var s=(0,k.Kt)(u);return(0,R.z3)({x:i,y:o},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=(0,S.sP)(t,O.u),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd}),wt(wt({},(0,Z.Ym)(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){_.on(W,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){_.removeListener(W,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,i=0,o=n.length;i<o;i++){var a=n[i];if(a.item===t||a.props.key===t.key||e===(0,S.Gf)(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,i=e.top,o=e.height,a=e.width;return n.createElement("defs",null,n.createElement("clipPath",{id:t},n.createElement("rect",{x:r,y:i,height:o,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce((function(t,e){var r=st(e,2),n=r[0],i=r[1];return wt(wt({},t),{},jt({},n,i.scale))}),{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce((function(t,e){var r=st(e,2),n=r[0],i=r[1];return wt(wt({},t),{},jt({},n,i.scale))}),{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var i=0,o=r.length;i<o;i++){var a=r[i],c=a.props,u=a.item,l=(0,S.Gf)(u.type);if("Bar"===l){var s=(c.data||[]).find((function(e){return(0,j.X)(t,e)}));if(s)return{graphicalItem:a,payload:s}}else if("RadialBar"===l){var f=(c.data||[]).find((function(e){return(0,R.z3)(t,e)}));if(f)return{graphicalItem:a,payload:f}}else if((0,U.lT)(a,n)||(0,U.V$)(a,n)||(0,U.w7)(a,n)){var p=(0,U.a3)({graphicalItem:a,activeTooltipItem:n,itemData:u.props.data}),d=void 0===u.props.activeIndex?p:u.props.activeIndex;return{graphicalItem:wt(wt({},a),{},{childIndex:d}),payload:(0,U.w7)(a,n)?u.props.data[p]:a.props.data[p]}}}return null}},{key:"render",value:function(){var t=this;if(!(0,S.TT)(this))return null;var e,r,i=this.props,o=i.children,a=i.className,c=i.width,u=i.height,l=i.style,s=i.compact,f=i.title,p=i.desc,d=ft(i,ct),y=(0,S.L6)(d,!1);if(s)return n.createElement(ot.br,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement(b.T,lt({},y,{width:c,height:u,title:f,desc:p}),this.renderClipPath(),(0,S.eu)(o,this.renderMap)));this.props.accessibilityLayer&&(y.tabIndex=null!==(e=this.props.tabIndex)&&void 0!==e?e:0,y.role=null!==(r=this.props.role)&&void 0!==r?r:"application",y.onKeyDown=function(e){t.accessibilityManager.keyboardEvent(e)},y.onFocus=function(){t.accessibilityManager.focus()});var h=this.parseEventsOfWrapper();return n.createElement(ot.br,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement("div",lt({className:(0,v.Z)("recharts-wrapper",a),style:wt({position:"relative",cursor:"default",width:c,height:u},l)},h,{ref:function(e){t.container=e}}),n.createElement(b.T,lt({},y,{width:c,height:u,title:f,desc:p,style:At}),this.renderClipPath(),(0,S.eu)(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],a&&pt(i.prototype,a),l&&pt(i,l),Object.defineProperty(i,"prototype",{writable:!1}),e}(n.Component),jt(e,"displayName",r),jt(e,"defaultProps",wt({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},C)),jt(e,"getDerivedStateFromProps",(function(t,e){var r=t.dataKey,n=t.data,i=t.children,a=t.width,c=t.height,u=t.layout,l=t.stackOffset,s=t.margin,f=e.dataStartIndex,p=e.dataEndIndex;if(void 0===e.updateId){var d=Lt(t);return wt(wt(wt({},d),{},{updateId:0},M(wt(wt({props:t},d),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:c,prevLayout:u,prevStackOffset:l,prevMargin:s,prevChildren:i})}if(r!==e.prevDataKey||n!==e.prevData||a!==e.prevWidth||c!==e.prevHeight||u!==e.prevLayout||l!==e.prevStackOffset||!(0,z.w)(s,e.prevMargin)){var y=Lt(t),h={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=wt(wt({},Dt(e,n,u)),{},{updateId:e.updateId+1}),m=wt(wt(wt({},y),h),v);return wt(wt(wt({},m),M(wt({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:c,prevLayout:u,prevStackOffset:l,prevMargin:s,prevChildren:i})}if(!(0,S.rL)(i,e.prevChildren)){var b,g,O,x,w=(0,S.sP)(i,P.B),j=w&&null!==(b=null===(g=w.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:f,A=w&&null!==(O=null===(x=w.props)||void 0===x?void 0:x.endIndex)&&void 0!==O?O:p,k=j!==f||A!==p,E=!o()(n)&&!k?e.updateId:e.updateId+1;return wt(wt({updateId:E},M(wt(wt({props:t},e),{},{updateId:E,dataStartIndex:j,dataEndIndex:A}),e)),{},{prevChildren:i,dataStartIndex:j,dataEndIndex:A})}return null})),jt(e,"renderActiveDot",(function(t,e){var r;return r=(0,n.isValidElement)(t)?(0,n.cloneElement)(t,e):c()(t)?t(e):n.createElement(w.o,e),n.createElement(g.m,{className:"recharts-active-dot",key:e.key},r)})),e}},32214:function(t,e,r){r.d(e,{b:function(){return n}});var n=function(t){return null};n.displayName="Cell"},58935:function(t,e,r){r.d(e,{g:function(){return w}});var n=r(89526),i=r(39277),o=r.n(i),a=r(90512),c=r(78706),u=r(93386),l=r(71746),s=r(33790);function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(){return p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},p.apply(this,arguments)}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function y(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,O(n.key),n)}}function h(t,e,r){return e=m(e),function(t,e){if(e&&("object"===f(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,v()?Reflect.construct(e,r||[],m(t).constructor):e.apply(t,r))}function v(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(v=function(){return!!t})()}function m(t){return m=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},m(t)}function b(t,e){return b=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},b(t,e)}function g(t,e,r){return(e=O(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function O(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:String(e)}var x=32,w=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),h(this,e,arguments)}var r,i,f;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&b(t,e)}(e,t),r=e,i=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=16,i=x/6,o=x/3,a=t.inactive?e:t.color;if("plainline"===t.type)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:a,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:r,x2:x,y2:r,className:"recharts-legend-icon"});if("line"===t.type)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:a,d:"M0,".concat(r,"h").concat(o,"\n            A").concat(i,",").concat(i,",0,1,1,").concat(2*o,",").concat(r,"\n            H").concat(x,"M").concat(2*o,",").concat(r,"\n            A").concat(i,",").concat(i,",0,1,1,").concat(o,",").concat(r),className:"recharts-legend-icon"});if("rect"===t.type)return n.createElement("path",{stroke:"none",fill:a,d:"M0,".concat(4,"h").concat(x,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(t.legendIcon)){var c=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach((function(e){g(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},t);return delete c.legendIcon,n.cloneElement(t.legendIcon,c)}return n.createElement(l.v,{fill:a,cx:r,cy:r,size:x,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,i=e.iconSize,l=e.layout,f=e.formatter,d=e.inactiveColor,y={x:0,y:0,width:x,height:x},h={display:"horizontal"===l?"inline-block":"block",marginRight:10},v={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map((function(e,r){var l=e.formatter||f,m=(0,a.Z)(g(g({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var b=o()(e.value)?null:e.value;(0,c.Z)(!o()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var O=e.inactive?d:e.color;return n.createElement("li",p({className:m,style:h,key:"legend-item-".concat(r)},(0,s.bw)(t.props,e,r)),n.createElement(u.T,{width:i,height:i,viewBox:y,style:v},t.renderIcon(e)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:O}},l?l(b,e,r):b))}))}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,i=t.align;if(!e||!e.length)return null;var o={padding:0,margin:0,textAlign:"horizontal"===r?i:"left"};return n.createElement("ul",{className:"recharts-default-legend",style:o},this.renderItems())}}],i&&y(r.prototype,i),f&&y(r,f),Object.defineProperty(r,"prototype",{writable:!1}),e}(n.PureComponent);g(w,"displayName","Legend"),g(w,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"})},69100:function(t,e,r){r.d(e,{x:function(){return b}});var n=r(89526),i=r(65853),o=r.n(i),a=r(51391),c=r.n(a),u=r(90512),l=r(16171);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function f(){return f=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},f.apply(this,arguments)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return d(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function y(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?y(Object(r),!0).forEach((function(e){v(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function v(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function m(t){return Array.isArray(t)&&(0,l.P2)(t[0])&&(0,l.P2)(t[1])?t.join(" ~ "):t}var b=function(t){var e=t.separator,r=void 0===e?" : ":e,i=t.contentStyle,a=void 0===i?{}:i,s=t.itemStyle,d=void 0===s?{}:s,y=t.labelStyle,v=void 0===y?{}:y,b=t.payload,g=t.formatter,O=t.itemSorter,x=t.wrapperClassName,w=t.labelClassName,j=t.label,S=t.labelFormatter,P=t.accessibilityLayer,A=void 0!==P&&P,k=h({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),E=h({margin:0},v),T=!c()(j),I=T?j:"",C=(0,u.Z)("recharts-default-tooltip",x),D=(0,u.Z)("recharts-tooltip-label",w);T&&S&&void 0!==b&&null!==b&&(I=S(j,b));var M=A?{role:"status","aria-live":"assertive"}:{};return n.createElement("div",f({className:C,style:k},M),n.createElement("p",{className:D,style:E},n.isValidElement(I)?I:"".concat(I)),function(){if(b&&b.length){var t=(O?o()(b,O):b).map((function(t,e){if("none"===t.type)return null;var i=h({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},d),o=t.formatter||g||m,a=t.value,c=t.name,u=a,s=c;if(o&&null!=u&&null!=s){var f=o(a,c,t,e,b);if(Array.isArray(f)){var y=p(f,2);u=y[0],s=y[1]}else u=f}return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:i},(0,l.P2)(s)?n.createElement("span",{className:"recharts-tooltip-item-name"},s):null,(0,l.P2)(s)?n.createElement("span",{className:"recharts-tooltip-item-separator"},r):null,n.createElement("span",{className:"recharts-tooltip-item-value"},u),n.createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))}));return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())}},43774:function(t,e,r){r.d(e,{_:function(){return T}});var n=r(89526),i=r(51391),o=r.n(i),a=r(39277),c=r.n(a),u=r(23619),l=r.n(u),s=r(90512),f=r(49266),p=r(9410),d=r(16171),y=r(80072);function h(t){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},h(t)}var v=["offset"];function m(t){return function(t){if(Array.isArray(t))return b(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return b(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return b(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function O(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?O(Object(r),!0).forEach((function(e){w(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function w(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=h(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=h(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==h(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function j(){return j=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},j.apply(this,arguments)}var S=function(t){var e=t.value,r=t.formatter,n=o()(t.children)?e:t.children;return c()(r)?r(n):n},P=function(t,e,r){var i,a,c=t.position,u=t.viewBox,l=t.offset,f=t.className,p=u,h=p.cx,v=p.cy,m=p.innerRadius,b=p.outerRadius,g=p.startAngle,O=p.endAngle,x=p.clockWise,w=(m+b)/2,S=function(t,e){return(0,d.uY)(e-t)*Math.min(Math.abs(e-t),360)}(g,O),P=S>=0?1:-1;"insideStart"===c?(i=g+P*l,a=x):"insideEnd"===c?(i=O-P*l,a=!x):"end"===c&&(i=O+P*l,a=x),a=S<=0?a:!a;var A=(0,y.op)(h,v,w,i),k=(0,y.op)(h,v,w,i+359*(a?1:-1)),E="M".concat(A.x,",").concat(A.y,"\n    A").concat(w,",").concat(w,",0,1,").concat(a?0:1,",\n    ").concat(k.x,",").concat(k.y),T=o()(t.id)?(0,d.EL)("recharts-radial-line-"):t.id;return n.createElement("text",j({},r,{dominantBaseline:"central",className:(0,s.Z)("recharts-radial-bar-label",f)}),n.createElement("defs",null,n.createElement("path",{id:T,d:E})),n.createElement("textPath",{xlinkHref:"#".concat(T)},e))},A=function(t){var e=t.viewBox,r=t.offset,n=t.position,i=e,o=i.cx,a=i.cy,c=i.innerRadius,u=i.outerRadius,l=(i.startAngle+i.endAngle)/2;if("outside"===n){var s=(0,y.op)(o,a,u+r,l),f=s.x;return{x:f,y:s.y,textAnchor:f>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:a,textAnchor:"middle",verticalAnchor:"end"};var p=(c+u)/2,d=(0,y.op)(o,a,p,l);return{x:d.x,y:d.y,textAnchor:"middle",verticalAnchor:"middle"}},k=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,i=t.position,o=e,a=o.x,c=o.y,u=o.width,s=o.height,f=s>=0?1:-1,p=f*n,y=f>0?"end":"start",h=f>0?"start":"end",v=u>=0?1:-1,m=v*n,b=v>0?"end":"start",g=v>0?"start":"end";if("top"===i)return x(x({},{x:a+u/2,y:c-f*n,textAnchor:"middle",verticalAnchor:y}),r?{height:Math.max(c-r.y,0),width:u}:{});if("bottom"===i)return x(x({},{x:a+u/2,y:c+s+p,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(c+s),0),width:u}:{});if("left"===i){var O={x:a-m,y:c+s/2,textAnchor:b,verticalAnchor:"middle"};return x(x({},O),r?{width:Math.max(O.x-r.x,0),height:s}:{})}if("right"===i){var w={x:a+u+m,y:c+s/2,textAnchor:g,verticalAnchor:"middle"};return x(x({},w),r?{width:Math.max(r.x+r.width-w.x,0),height:s}:{})}var j=r?{width:u,height:s}:{};return"insideLeft"===i?x({x:a+m,y:c+s/2,textAnchor:g,verticalAnchor:"middle"},j):"insideRight"===i?x({x:a+u-m,y:c+s/2,textAnchor:b,verticalAnchor:"middle"},j):"insideTop"===i?x({x:a+u/2,y:c+p,textAnchor:"middle",verticalAnchor:h},j):"insideBottom"===i?x({x:a+u/2,y:c+s-p,textAnchor:"middle",verticalAnchor:y},j):"insideTopLeft"===i?x({x:a+m,y:c+p,textAnchor:g,verticalAnchor:h},j):"insideTopRight"===i?x({x:a+u-m,y:c+p,textAnchor:b,verticalAnchor:h},j):"insideBottomLeft"===i?x({x:a+m,y:c+s-p,textAnchor:g,verticalAnchor:y},j):"insideBottomRight"===i?x({x:a+u-m,y:c+s-p,textAnchor:b,verticalAnchor:y},j):l()(i)&&((0,d.hj)(i.x)||(0,d.hU)(i.x))&&((0,d.hj)(i.y)||(0,d.hU)(i.y))?x({x:a+(0,d.h1)(i.x,u),y:c+(0,d.h1)(i.y,s),textAnchor:"end",verticalAnchor:"end"},j):x({x:a+u/2,y:c+s/2,textAnchor:"middle",verticalAnchor:"middle"},j)},E=function(t){return"cx"in t&&(0,d.hj)(t.cx)};function T(t){var e,r=t.offset,i=x({offset:void 0===r?5:r},g(t,v)),a=i.viewBox,u=i.position,l=i.value,d=i.children,y=i.content,h=i.className,m=void 0===h?"":h,b=i.textBreakAll;if(!a||o()(l)&&o()(d)&&!(0,n.isValidElement)(y)&&!c()(y))return null;if((0,n.isValidElement)(y))return(0,n.cloneElement)(y,i);if(c()(y)){if(e=(0,n.createElement)(y,i),(0,n.isValidElement)(e))return e}else e=S(i);var O=E(a),w=(0,p.L6)(i,!0);if(O&&("insideStart"===u||"insideEnd"===u||"end"===u))return P(i,e,w);var T=O?A(i):k(i);return n.createElement(f.x,j({className:(0,s.Z)("recharts-label",m)},w,T,{breakAll:b}),e)}T.displayName="Label";var I=function(t){var e=t.cx,r=t.cy,n=t.angle,i=t.startAngle,o=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,y=t.left,h=t.width,v=t.height,m=t.clockWise,b=t.labelViewBox;if(b)return b;if((0,d.hj)(h)&&(0,d.hj)(v)){if((0,d.hj)(s)&&(0,d.hj)(f))return{x:s,y:f,width:h,height:v};if((0,d.hj)(p)&&(0,d.hj)(y))return{x:p,y:y,width:h,height:v}}return(0,d.hj)(s)&&(0,d.hj)(f)?{x:s,y:f,width:0,height:0}:(0,d.hj)(e)&&(0,d.hj)(r)?{cx:e,cy:r,startAngle:i||n||0,endAngle:o||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:m}:t.viewBox?t.viewBox:{}};T.parseViewBox=I,T.renderCallByParent=function(t,e){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&r&&!t.label)return null;var i=t.children,o=I(t),a=(0,p.NN)(i,T).map((function(t,r){return(0,n.cloneElement)(t,{viewBox:e||o,key:"label-".concat(r)})}));if(!r)return a;var u=function(t,e){return t?!0===t?n.createElement(T,{key:"label-implicit",viewBox:e}):(0,d.P2)(t)?n.createElement(T,{key:"label-implicit",viewBox:e,value:t}):(0,n.isValidElement)(t)?t.type===T?(0,n.cloneElement)(t,{key:"label-implicit",viewBox:e}):n.createElement(T,{key:"label-implicit",content:t,viewBox:e}):c()(t)?n.createElement(T,{key:"label-implicit",content:t,viewBox:e}):l()(t)?n.createElement(T,j({viewBox:e},t,{key:"label-implicit"})):null:null}(t.label,e||o);return[u].concat(m(a))}},34324:function(t,e,r){r.d(e,{e:function(){return k}});var n=r(89526),i=r(51391),o=r.n(i),a=r(23619),c=r.n(a),u=r(39277),l=r.n(u),s=r(80275),f=r.n(s),p=r(43774),d=r(61452),y=r(9410),h=r(36530);function v(t){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(t)}var m=["valueAccessor"],b=["data","dataKey","clockWise","id","textBreakAll"];function g(t){return function(t){if(Array.isArray(t))return O(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return O(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return O(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function x(){return x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},x.apply(this,arguments)}function w(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?w(Object(r),!0).forEach((function(e){S(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function S(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function P(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var A=function(t){return Array.isArray(t.value)?f()(t.value):t.value};function k(t){var e=t.valueAccessor,r=void 0===e?A:e,i=P(t,m),a=i.data,c=i.dataKey,u=i.clockWise,l=i.id,s=i.textBreakAll,f=P(i,b);return a&&a.length?n.createElement(d.m,{className:"recharts-label-list"},a.map((function(t,e){var i=o()(c)?r(t,e):(0,h.F$)(t&&t.payload,c),a=o()(l)?{}:{id:"".concat(l,"-").concat(e)};return n.createElement(p._,x({},(0,y.L6)(t,!0),f,a,{parentViewBox:t.parentViewBox,value:i,textBreakAll:s,viewBox:p._.parseViewBox(o()(u)?t:j(j({},t),{},{clockWise:u})),key:"label-".concat(e),index:e}))}))):null}k.displayName="LabelList",k.renderCallByParent=function(t,e){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&r&&!t.label)return null;var i=t.children,o=(0,y.NN)(i,k).map((function(t,r){return(0,n.cloneElement)(t,{data:e,key:"labelList-".concat(r)})}));return r?[function(t,e){return t?!0===t?n.createElement(k,{key:"labelList-implicit",data:e}):n.isValidElement(t)||l()(t)?n.createElement(k,{key:"labelList-implicit",data:e,content:t}):c()(t)?n.createElement(k,x({data:e},t,{key:"labelList-implicit"})):null:null}(t.label,e)].concat(g(o)):o}},71015:function(t,e,r){r.d(e,{D:function(){return x}});var n=r(89526),i=r(58935),o=r(16171),a=r(56062);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}var u=["ref"];function l(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function s(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l(Object(r),!0).forEach((function(e){m(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function f(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,b(n.key),n)}}function p(t,e,r){return e=y(e),function(t,e){if(e&&("object"===c(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return h(t)}(t,d()?Reflect.construct(e,r||[],y(t).constructor):e.apply(t,r))}function d(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(d=function(){return!!t})()}function y(t){return y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},y(t)}function h(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function v(t,e){return v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},v(t,e)}function m(t,e,r){return(e=b(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function b(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:String(e)}function g(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function O(t){return t.value}var x=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return m(h(t=p(this,e,[].concat(n))),"lastBoundingBox",{width:-1,height:-1}),t}var r,c,l;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&v(t,e)}(e,t),r=e,l=[{key:"getWithHeight",value:function(t,e){var r=t.props.layout;return"vertical"===r&&(0,o.hj)(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],(c=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):-1===this.lastBoundingBox.width&&-1===this.lastBoundingBox.height||(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?s({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,i=n.layout,o=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===o&&"vertical"===i?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===o?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),s(s({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,o=e.width,c=e.height,l=e.wrapperStyle,f=e.payloadUniqBy,p=e.payload,d=s(s({position:"absolute",width:o||"auto",height:c||"auto"},this.getDefaultPosition(l)),l);return n.createElement("div",{className:"recharts-legend-wrapper",style:d,ref:function(e){t.wrapperNode=e}},function(t,e){if(n.isValidElement(t))return n.cloneElement(t,e);if("function"===typeof t)return n.createElement(t,e);e.ref;var r=g(e,u);return n.createElement(i.g,r)}(r,s(s({},this.props),{},{payload:(0,a.z)(p,f,O)})))}}])&&f(r.prototype,c),l&&f(r,l),Object.defineProperty(r,"prototype",{writable:!1}),e}(n.PureComponent);m(x,"displayName","Legend"),m(x,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"})},71824:function(t,e,r){r.d(e,{h:function(){return m}});var n=r(90512),i=r(89526),o=r(38172),a=r.n(o),c=r(338),u=r(16171),l=r(78706),s=r(9410);function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach((function(e){y(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function y(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return v(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return v(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function v(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var m=(0,i.forwardRef)((function(t,e){var r=t.aspect,o=t.initialDimension,f=void 0===o?{width:-1,height:-1}:o,p=t.width,y=void 0===p?"100%":p,v=t.height,m=void 0===v?"100%":v,b=t.minWidth,g=void 0===b?0:b,O=t.minHeight,x=t.maxHeight,w=t.children,j=t.debounce,S=void 0===j?0:j,P=t.id,A=t.className,k=t.onResize,E=t.style,T=void 0===E?{}:E,I=(0,i.useRef)(null),C=(0,i.useRef)();C.current=k,(0,i.useImperativeHandle)(e,(function(){return Object.defineProperty(I.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),I.current},configurable:!0})}));var D=h((0,i.useState)({containerWidth:f.width,containerHeight:f.height}),2),M=D[0],N=D[1],L=(0,i.useCallback)((function(t,e){N((function(r){var n=Math.round(t),i=Math.round(e);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}}))}),[]);(0,i.useEffect)((function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,i=r.height;L(n,i),null===(e=C.current)||void 0===e||e.call(C,n,i)};S>0&&(t=a()(t,S,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=I.current.getBoundingClientRect(),n=r.width,i=r.height;return L(n,i),e.observe(I.current),function(){e.disconnect()}}),[L,S]);var B=(0,i.useMemo)((function(){var t=M.containerWidth,e=M.containerHeight;if(t<0||e<0)return null;(0,l.Z)((0,u.hU)(y)||(0,u.hU)(m),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",y,m),(0,l.Z)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,u.hU)(y)?t:y,o=(0,u.hU)(m)?e:m;r&&r>0&&(n?o=n/r:o&&(n=o*r),x&&o>x&&(o=x)),(0,l.Z)(n>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,o,y,m,g,O,r);var a=!Array.isArray(w)&&(0,c.isElement)(w)&&(0,s.Gf)(w.type).endsWith("Chart");return i.Children.map(w,(function(t){return(0,c.isElement)(t)?(0,i.cloneElement)(t,d({width:n,height:o},a?{style:d({height:"100%",width:"100%",maxHeight:o,maxWidth:n},t.props.style)}:{})):t}))}),[r,w,m,x,O,g,M,y]);return i.createElement("div",{id:P?"".concat(P):void 0,className:(0,n.Z)("recharts-responsive-container",A),style:d(d({},T),{},{width:y,height:m,minWidth:g,minHeight:O,maxHeight:x}),ref:I},B)}))},49266:function(t,e,r){r.d(e,{x:function(){return F}});var n=r(89526),i=r(51391),o=r.n(i),a=r(90512),c=r(16171),u=r(59509),l=r(9410),s=r(99875);function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return d(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function y(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h(n.key),n)}}function h(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:String(e)}var v=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,m=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,b=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,g=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,O={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},x=Object.keys(O),w="NaN";var j=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.num=e,this.unit=r,this.num=e,this.unit=r,Number.isNaN(e)&&(this.unit=""),""===r||b.test(r)||(this.num=NaN,this.unit=""),x.includes(r)&&(this.num=function(t,e){return t*O[e]}(e,r),this.unit="px")}var e,r,n;return e=t,n=[{key:"parse",value:function(e){var r,n=p(null!==(r=g.exec(e))&&void 0!==r?r:[],3),i=n[1],o=n[2];return new t(parseFloat(i),null!==o&&void 0!==o?o:"")}}],(r=[{key:"add",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}])&&y(e.prototype,r),n&&y(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();function S(t){if(t.includes(w))return w;for(var e=t;e.includes("*")||e.includes("/");){var r,n=p(null!==(r=v.exec(e))&&void 0!==r?r:[],4),i=n[1],o=n[2],a=n[3],c=j.parse(null!==i&&void 0!==i?i:""),u=j.parse(null!==a&&void 0!==a?a:""),l="*"===o?c.multiply(u):c.divide(u);if(l.isNaN())return w;e=e.replace(v,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=p(null!==(s=m.exec(e))&&void 0!==s?s:[],4),d=f[1],y=f[2],h=f[3],b=j.parse(null!==d&&void 0!==d?d:""),g=j.parse(null!==h&&void 0!==h?h:""),O="+"===y?b.add(g):b.subtract(g);if(O.isNaN())return w;e=e.replace(m,O.toString())}return e}var P=/\(([^()]*)\)/;function A(t){var e=t.replace(/\s+/g,"");return e=function(t){for(var e=t;e.includes("(");){var r=p(P.exec(e),2)[1];e=e.replace(P,S(r))}return e}(e),e=S(e)}function k(t){var e=function(t){try{return A(t)}catch(e){return w}}(t.slice(5,-1));return e===w?"":e}var E=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],T=["dx","dy","angle","className","breakAll"];function I(){return I=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},I.apply(this,arguments)}function C(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function D(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return M(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return M(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function M(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var N=/[ \f\n\r\t\v\u2028\u2029]+/,L=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var i=[];return o()(e)||(i=r?e.toString().split(""):e.toString().split(N)),{wordsWithComputedWidth:i.map((function(t){return{word:t,width:(0,s.xE)(t,n).width}})),spaceWidth:r?0:(0,s.xE)("\xa0",n).width}}catch(a){return null}},B=function(t){return[{words:o()(t)?[]:t.toString().split(N)}]},R=function(t){var e=t.width,r=t.scaleToFit,n=t.children,i=t.style,o=t.breakAll,a=t.maxLines;if((e||r)&&!u.x.isSsr){var l=L({breakAll:o,children:n,style:i});return l?function(t,e,r,n,i){var o=t.maxLines,a=t.children,u=t.style,l=t.breakAll,s=(0,c.hj)(o),f=a,p=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce((function(t,e){var o=e.word,a=e.width,c=t[t.length-1];if(c&&(null==n||i||c.width+a+r<Number(n)))c.words.push(o),c.width+=a+r;else{var u={words:[o],width:a};t.push(u)}return t}),[])},d=p(e);if(!s)return d;for(var y,h=function(t){var e=f.slice(0,t),r=L({breakAll:l,style:u,children:e+"\u2026"}).wordsWithComputedWidth,i=p(r),a=i.length>o||function(t){return t.reduce((function(t,e){return t.width>e.width?t:e}))}(i).width>Number(n);return[a,i]},v=0,m=f.length-1,b=0;v<=m&&b<=f.length-1;){var g=Math.floor((v+m)/2),O=D(h(g-1),2),x=O[0],w=O[1],j=D(h(g),1)[0];if(x||j||(v=g+1),x&&j&&(m=g-1),!x&&j){y=w;break}b++}return y||d}({breakAll:o,children:n,maxLines:a,style:i},l.wordsWithComputedWidth,l.spaceWidth,e,r):B(n)}return B(n)},z="#808080",F=function(t){var e=t.x,r=void 0===e?0:e,i=t.y,o=void 0===i?0:i,u=t.lineHeight,s=void 0===u?"1em":u,f=t.capHeight,p=void 0===f?"0.71em":f,d=t.scaleToFit,y=void 0!==d&&d,h=t.textAnchor,v=void 0===h?"start":h,m=t.verticalAnchor,b=void 0===m?"end":m,g=t.fill,O=void 0===g?z:g,x=C(t,E),w=(0,n.useMemo)((function(){return R({breakAll:x.breakAll,children:x.children,maxLines:x.maxLines,scaleToFit:y,style:x.style,width:x.width})}),[x.breakAll,x.children,x.maxLines,y,x.style,x.width]),j=x.dx,S=x.dy,P=x.angle,A=x.className,D=x.breakAll,M=C(x,T);if(!(0,c.P2)(r)||!(0,c.P2)(o))return null;var N,L=r+((0,c.hj)(j)?j:0),B=o+((0,c.hj)(S)?S:0);switch(b){case"start":N=k("calc(".concat(p,")"));break;case"middle":N=k("calc(".concat((w.length-1)/2," * -").concat(s," + (").concat(p," / 2))"));break;default:N=k("calc(".concat(w.length-1," * -").concat(s,")"))}var F=[];if(y){var _=w[0].width,W=x.width;F.push("scale(".concat(((0,c.hj)(W)?W/_:1)/_,")"))}return P&&F.push("rotate(".concat(P,", ").concat(L,", ").concat(B,")")),F.length&&(M.transform=F.join(" ")),n.createElement("text",I({},(0,l.L6)(M,!0),{x:L,y:B,className:(0,a.Z)("recharts-text",A),textAnchor:v,fill:O.includes("url")?z:O}),w.map((function(t,e){var r=t.words.join(D?"":" ");return n.createElement("tspan",{x:L,dy:0===e?N:s,key:r},r)})))}},18170:function(t,e,r){r.d(e,{u:function(){return z}});var n=r(89526),i=r(69100),o=r(90512),a=r(16171);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var l="recharts-tooltip-wrapper",s={visibility:"hidden"};function f(t){var e=t.coordinate,r=t.translateX,n=t.translateY;return(0,o.Z)(l,u(u(u(u({},"".concat(l,"-right"),(0,a.hj)(r)&&e&&(0,a.hj)(e.x)&&r>=e.x),"".concat(l,"-left"),(0,a.hj)(r)&&e&&(0,a.hj)(e.x)&&r<e.x),"".concat(l,"-bottom"),(0,a.hj)(n)&&e&&(0,a.hj)(e.y)&&n>=e.y),"".concat(l,"-top"),(0,a.hj)(n)&&e&&(0,a.hj)(e.y)&&n<e.y))}function p(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,i=t.offsetTopLeft,o=t.position,c=t.reverseDirection,u=t.tooltipDimension,l=t.viewBox,s=t.viewBoxDimension;if(o&&(0,a.hj)(o[n]))return o[n];var f=r[n]-u-i,p=r[n]+i;return e[n]?c[n]?f:p:c[n]?f<l[n]?Math.max(p,l[n]):Math.max(f,l[n]):p+u>l[n]+s?Math.max(f,l[n]):Math.max(p,l[n])}function d(t){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},d(t)}function y(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?y(Object(r),!0).forEach((function(e){w(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function v(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,j(n.key),n)}}function m(t,e,r){return e=g(e),function(t,e){if(e&&("object"===d(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return O(t)}(t,b()?Reflect.construct(e,r||[],g(t).constructor):e.apply(t,r))}function b(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(b=function(){return!!t})()}function g(t){return g=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},g(t)}function O(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function x(t,e){return x=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},x(t,e)}function w(t,e,r){return(e=j(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function j(t){var e=function(t,e){if("object"!=d(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=d(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==d(e)?e:String(e)}var S=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return w(O(t=m(this,e,[].concat(n))),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),w(O(t),"handleKeyDown",(function(e){var r,n,i,o;"Escape"===e.key&&t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(i=null===(o=t.props.coordinate)||void 0===o?void 0:o.y)&&void 0!==i?i:0}})})),t}var r,i,o;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&x(t,e)}(e,t),r=e,(i=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else-1===this.state.lastBoundingBox.width&&-1===this.state.lastBoundingBox.height||this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)===this.state.dismissedAtCoordinate.x&&(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)===this.state.dismissedAtCoordinate.y||(this.state.dismissed=!1))}},{key:"render",value:function(){var t=this,e=this.props,r=e.active,i=e.allowEscapeViewBox,o=e.animationDuration,a=e.animationEasing,c=e.children,u=e.coordinate,l=e.hasPayload,d=e.isAnimationActive,y=e.offset,v=e.position,m=e.reverseDirection,b=e.useTranslate3d,g=e.viewBox,O=e.wrapperStyle,x=function(t){var e,r,n=t.allowEscapeViewBox,i=t.coordinate,o=t.offsetTopLeft,a=t.position,c=t.reverseDirection,u=t.tooltipBox,l=t.useTranslate3d,d=t.viewBox;return{cssProperties:u.height>0&&u.width>0&&i?function(t){var e=t.translateX,r=t.translateY;return{transform:t.useTranslate3d?"translate3d(".concat(e,"px, ").concat(r,"px, 0)"):"translate(".concat(e,"px, ").concat(r,"px)")}}({translateX:e=p({allowEscapeViewBox:n,coordinate:i,key:"x",offsetTopLeft:o,position:a,reverseDirection:c,tooltipDimension:u.width,viewBox:d,viewBoxDimension:d.width}),translateY:r=p({allowEscapeViewBox:n,coordinate:i,key:"y",offsetTopLeft:o,position:a,reverseDirection:c,tooltipDimension:u.height,viewBox:d,viewBoxDimension:d.height}),useTranslate3d:l}):s,cssClasses:f({translateX:e,translateY:r,coordinate:i})}}({allowEscapeViewBox:i,coordinate:u,offsetTopLeft:y,position:v,reverseDirection:m,tooltipBox:this.state.lastBoundingBox,useTranslate3d:b,viewBox:g}),w=x.cssClasses,j=x.cssProperties,S=h(h({transition:d&&r?"transform ".concat(o,"ms ").concat(a):void 0},j),{},{pointerEvents:"none",visibility:!this.state.dismissed&&r&&l?"visible":"hidden",position:"absolute",top:0,left:0},O);return n.createElement("div",{tabIndex:-1,className:w,style:S,ref:function(e){t.wrapperNode=e}},c)}}])&&v(r.prototype,i),o&&v(r,o),Object.defineProperty(r,"prototype",{writable:!1}),e}(n.PureComponent),P=r(59509),A=r(56062);function k(t){return k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},k(t)}function E(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function T(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?E(Object(r),!0).forEach((function(e){L(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function I(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,B(n.key),n)}}function C(t,e,r){return e=M(e),function(t,e){if(e&&("object"===k(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,D()?Reflect.construct(e,r||[],M(t).constructor):e.apply(t,r))}function D(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(D=function(){return!!t})()}function M(t){return M=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},M(t)}function N(t,e){return N=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},N(t,e)}function L(t,e,r){return(e=B(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function B(t){var e=function(t,e){if("object"!=k(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=k(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==k(e)?e:String(e)}function R(t){return t.dataKey}var z=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),C(this,e,arguments)}var r,o,a;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&N(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){var t=this,e=this.props,r=e.active,o=e.allowEscapeViewBox,a=e.animationDuration,c=e.animationEasing,u=e.content,l=e.coordinate,s=e.filterNull,f=e.isAnimationActive,p=e.offset,d=e.payload,y=e.payloadUniqBy,h=e.position,v=e.reverseDirection,m=e.useTranslate3d,b=e.viewBox,g=e.wrapperStyle,O=null!==d&&void 0!==d?d:[];s&&O.length&&(O=(0,A.z)(d.filter((function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)})),y,R));var x=O.length>0;return n.createElement(S,{allowEscapeViewBox:o,animationDuration:a,animationEasing:c,isAnimationActive:f,active:r,coordinate:l,hasPayload:x,offset:p,position:h,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):"function"===typeof t?n.createElement(t,e):n.createElement(i.x,e)}(u,T(T({},this.props),{},{payload:O})))}}])&&I(r.prototype,o),a&&I(r,a),Object.defineProperty(r,"prototype",{writable:!1}),e}(n.PureComponent);L(z,"displayName","Tooltip"),L(z,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!P.x.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}})},61452:function(t,e,r){r.d(e,{m:function(){return l}});var n=r(89526),i=r(90512),o=r(9410),a=["children","className"];function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(this,arguments)}function u(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var l=n.forwardRef((function(t,e){var r=t.children,l=t.className,s=u(t,a),f=(0,i.Z)("recharts-layer",l);return n.createElement("g",c({className:f},(0,o.L6)(s,!0),{ref:e}),r)}))},93386:function(t,e,r){r.d(e,{T:function(){return l}});var n=r(89526),i=r(90512),o=r(9410),a=["children","width","height","viewBox","className","style","title","desc"];function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(this,arguments)}function u(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function l(t){var e=t.children,r=t.width,l=t.height,s=t.viewBox,f=t.className,p=t.style,d=t.title,y=t.desc,h=u(t,a),v=s||{width:r,height:l,x:0,y:0},m=(0,i.Z)("recharts-surface",f);return n.createElement("svg",c({},(0,o.L6)(h,!0,"svg"),{className:m,width:r,height:l,style:p,viewBox:"".concat(v.x," ").concat(v.y," ").concat(v.width," ").concat(v.height)}),n.createElement("title",null,d),n.createElement("desc",null,y),e)}},86545:function(t,e,r){r.d(e,{br:function(){return g},CW:function(){return w},Mw:function(){return E},zn:function(){return k},sp:function(){return O},qD:function(){return A},d2:function(){return P},bH:function(){return x},Ud:function(){return S},Nf:function(){return j}});var n=r(89526),i=r(78109),o=r(92210),a=r.n(o),c=r(84168),u=r.n(c),l=r(54883),s=r.n(l)()((function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}}),(function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")})),f=r(16171);var p=(0,n.createContext)(void 0),d=(0,n.createContext)(void 0),y=(0,n.createContext)(void 0),h=(0,n.createContext)({}),v=(0,n.createContext)(void 0),m=(0,n.createContext)(0),b=(0,n.createContext)(0),g=function(t){var e=t.state,r=e.xAxisMap,i=e.yAxisMap,o=e.offset,a=t.clipPathId,c=t.children,u=t.width,l=t.height,f=s(o);return n.createElement(p.Provider,{value:r},n.createElement(d.Provider,{value:i},n.createElement(h.Provider,{value:o},n.createElement(y.Provider,{value:f},n.createElement(v.Provider,{value:a},n.createElement(m.Provider,{value:l},n.createElement(b.Provider,{value:u},c)))))))},O=function(){return(0,n.useContext)(v)};var x=function(t){var e=(0,n.useContext)(p);null==e&&(0,i.Z)(!1);var r=e[t];return null==r&&(0,i.Z)(!1),r},w=function(){var t=(0,n.useContext)(p);return(0,f.Kt)(t)},j=function(){var t=(0,n.useContext)(d);return a()(t,(function(t){return u()(t.domain,Number.isFinite)}))||(0,f.Kt)(t)},S=function(t){var e=(0,n.useContext)(d);null==e&&(0,i.Z)(!1);var r=e[t];return null==r&&(0,i.Z)(!1),r},P=function(){return(0,n.useContext)(y)},A=function(){return(0,n.useContext)(h)},k=function(){return(0,n.useContext)(b)},E=function(){return(0,n.useContext)(m)}},40076:function(t,e,r){r.r(e),r.d(e,{Area:function(){return Me},AreaChart:function(){return mn},Bar:function(){return Ne.$},BarChart:function(){return ir.v},Brush:function(){return Xt.B},CartesianAxis:function(){return Ut.O},CartesianGrid:function(){return Ht.q},Cell:function(){return s.b},ComposedChart:function(){return gn},Cross:function(){return k.X},Curve:function(){return j.H},Customized:function(){return x},DefaultLegendContent:function(){return a.g},DefaultTooltipContent:function(){return u.x},Dot:function(){return A.o},ErrorBar:function(){return $t.W},Funnel:function(){return ei},FunnelChart:function(){return ri},Global:function(){return it.x},Label:function(){return p._},LabelList:function(){return d.e},Layer:function(){return i.m},Legend:function(){return o.D},Line:function(){return de},LineChart:function(){return nr},Pie:function(){return Y.b},PieChart:function(){return or.u},PolarAngleAxis:function(){return G.I},PolarGrid:function(){return X},PolarRadiusAxis:function(){return V.S},Polygon:function(){return P.m},Radar:function(){return bt},RadarChart:function(){return hn},RadialBar:function(){return Kt},RadialBarChart:function(){return bn},Rectangle:function(){return S.A},ReferenceArea:function(){return Yt.z},ReferenceDot:function(){return Gt.q},ReferenceLine:function(){return Vt.d},ResponsiveContainer:function(){return l.h},Sankey:function(){return yn},Scatter:function(){return Qe},ScatterChart:function(){return vn},Sector:function(){return w.L},SunburstChart:function(){return Tn},Surface:function(){return n.T},Symbols:function(){return E.v},Text:function(){return f.x},Tooltip:function(){return c.u},Trapezoid:function(){return ni.Z},Treemap:function(){return Dr},XAxis:function(){return Je.K},YAxis:function(){return tr.B},ZAxis:function(){return Le}});var n=r(93386),i=r(61452),o=r(71015),a=r(58935),c=r(18170),u=r(69100),l=r(71824),s=r(32214),f=r(49266),p=r(43774),d=r(34324),y=r(89526),h=r(39277),v=r.n(h),m=r(78706),b=["component"];function g(t){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},g(t)}function O(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function x(t){var e,r=t.component,n=O(t,b);return(0,y.isValidElement)(r)?e=(0,y.cloneElement)(r,n):v()(r)?e=(0,y.createElement)(r,n):(0,m.Z)(!1,"Customized's props `component` must be React.element or Function, but got %s.",g(r)),y.createElement(i.m,{className:"recharts-customized-wrapper"},e)}x.displayName="Customized";var w=r(61001),j=r(92147),S=r(33951),P=r(31234),A=r(96963),k=r(74791),E=r(71746),T=r(90512),I=r(80072),C=r(9410),D=["cx","cy","innerRadius","outerRadius","gridType","radialLines"];function M(t){return M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},M(t)}function N(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function L(){return L=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},L.apply(this,arguments)}function B(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function R(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?B(Object(r),!0).forEach((function(e){z(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function z(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=M(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=M(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==M(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var F=function(t,e,r,n){var i="";return n.forEach((function(n,o){var a=(0,I.op)(e,r,t,n);i+=o?"L ".concat(a.x,",").concat(a.y):"M ".concat(a.x,",").concat(a.y)})),i+="Z"},_=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.polarAngles,a=t.radialLines;if(!o||!o.length||!a)return null;var c=R({stroke:"#ccc"},(0,C.L6)(t,!1));return y.createElement("g",{className:"recharts-polar-grid-angle"},o.map((function(t){var o=(0,I.op)(e,r,n,t),a=(0,I.op)(e,r,i,t);return y.createElement("line",L({},c,{key:"line-".concat(t),x1:o.x,y1:o.y,x2:a.x,y2:a.y}))})))},W=function(t){var e=t.cx,r=t.cy,n=t.radius,i=t.index,o=R(R({stroke:"#ccc"},(0,C.L6)(t,!1)),{},{fill:"none"});return y.createElement("circle",L({},o,{className:(0,T.Z)("recharts-polar-grid-concentric-circle",t.className),key:"circle-".concat(i),cx:e,cy:r,r:n}))},Z=function(t){var e=t.radius,r=t.index,n=R(R({stroke:"#ccc"},(0,C.L6)(t,!1)),{},{fill:"none"});return y.createElement("path",L({},n,{className:(0,T.Z)("recharts-polar-grid-concentric-polygon",t.className),key:"path-".concat(r),d:F(e,t.cx,t.cy,t.polarAngles)}))},K=function(t){var e=t.polarRadius,r=t.gridType;return e&&e.length?y.createElement("g",{className:"recharts-polar-grid-concentric"},e.map((function(e,n){var i=n;return"circle"===r?y.createElement(W,L({key:i},t,{radius:e,index:n})):y.createElement(Z,L({key:i},t,{radius:e,index:n}))}))):null},X=function(t){var e=t.cx,r=void 0===e?0:e,n=t.cy,i=void 0===n?0:n,o=t.innerRadius,a=void 0===o?0:o,c=t.outerRadius,u=void 0===c?0:c,l=t.gridType,s=void 0===l?"polygon":l,f=t.radialLines,p=void 0===f||f,d=N(t,D);return u<=0?null:y.createElement("g",{className:"recharts-polar-grid"},y.createElement(_,L({cx:r,cy:i,innerRadius:a,outerRadius:u,gridType:s,radialLines:p},d)),y.createElement(K,L({cx:r,cy:i,innerRadius:a,outerRadius:u,gridType:s,radialLines:p},d)))};X.displayName="PolarGrid";var V=r(85322),G=r(10562),Y=r(86246),U=r(68059),H=r(51391),$=r.n(H),q=r(80275),Q=r.n(q),J=r(48e3),tt=r.n(J),et=r(47184),rt=r.n(et),nt=r(16171),it=r(59509),ot=r(36530);function at(t){return at="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},at(t)}function ct(){return ct=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ct.apply(this,arguments)}function ut(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function lt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ut(Object(r),!0).forEach((function(e){vt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ut(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function st(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,mt(n.key),n)}}function ft(t,e,r){return e=dt(e),function(t,e){if(e&&("object"===at(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return yt(t)}(t,pt()?Reflect.construct(e,r||[],dt(t).constructor):e.apply(t,r))}function pt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(pt=function(){return!!t})()}function dt(t){return dt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},dt(t)}function yt(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function ht(t,e){return ht=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ht(t,e)}function vt(t,e,r){return(e=mt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function mt(t){var e=function(t,e){if("object"!=at(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=at(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==at(e)?e:String(e)}var bt=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return vt(yt(t=ft(this,e,[].concat(n))),"state",{isAnimationFinished:!1}),vt(yt(t),"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),v()(e)&&e()})),vt(yt(t),"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),v()(e)&&e()})),vt(yt(t),"handleMouseEnter",(function(e){var r=t.props.onMouseEnter;r&&r(t.props,e)})),vt(yt(t),"handleMouseLeave",(function(e){var r=t.props.onMouseLeave;r&&r(t.props,e)})),t}var r,n,o;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ht(t,e)}(e,t),r=e,o=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"renderDotItem",value:function(t,e){return y.isValidElement(t)?y.cloneElement(t,e):v()(t)?t(e):y.createElement(A.o,ct({},e,{className:(0,T.Z)("recharts-radar-dot","boolean"!==typeof t?t.className:"")}))}}],(n=[{key:"renderDots",value:function(t){var r=this.props,n=r.dot,o=r.dataKey,a=(0,C.L6)(this.props,!1),c=(0,C.L6)(n,!0),u=t.map((function(t,r){var i=lt(lt(lt({key:"dot-".concat(r),r:3},a),c),{},{dataKey:o,cx:t.x,cy:t.y,index:r,payload:t});return e.renderDotItem(n,i)}));return y.createElement(i.m,{className:"recharts-radar-dots"},u)}},{key:"renderPolygonStatically",value:function(t){var e,r=this.props,n=r.shape,o=r.dot,a=r.isRange,c=r.baseLinePoints,u=r.connectNulls;return e=y.isValidElement(n)?y.cloneElement(n,lt(lt({},this.props),{},{points:t})):v()(n)?n(lt(lt({},this.props),{},{points:t})):y.createElement(P.m,ct({},(0,C.L6)(this.props,!0),{onMouseEnter:this.handleMouseEnter,onMouseLeave:this.handleMouseLeave,points:t,baseLinePoints:a?c:null,connectNulls:u})),y.createElement(i.m,{className:"recharts-radar-polygon"},e,o?this.renderDots(t):null)}},{key:"renderPolygonWithAnimation",value:function(){var t=this,e=this.props,r=e.points,n=e.isAnimationActive,i=e.animationBegin,o=e.animationDuration,a=e.animationEasing,c=e.animationId,u=this.state.prevPoints;return y.createElement(U.ZP,{begin:i,duration:o,isActive:n,easing:a,from:{t:0},to:{t:1},key:"radar-".concat(c),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(e){var n=e.t,i=u&&u.length/r.length,o=r.map((function(t,e){var r=u&&u[Math.floor(e*i)];if(r){var o=(0,nt.k4)(r.x,t.x),a=(0,nt.k4)(r.y,t.y);return lt(lt({},t),{},{x:o(n),y:a(n)})}var c=(0,nt.k4)(t.cx,t.x),l=(0,nt.k4)(t.cy,t.y);return lt(lt({},t),{},{x:c(n),y:l(n)})}));return t.renderPolygonStatically(o)}))}},{key:"renderPolygon",value:function(){var t=this.props,e=t.points,r=t.isAnimationActive,n=t.isRange,i=this.state.prevPoints;return!(r&&e&&e.length)||n||i&&rt()(i,e)?this.renderPolygonStatically(e):this.renderPolygonWithAnimation()}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.className,n=t.points,o=t.isAnimationActive;if(e||!n||!n.length)return null;var a=this.state.isAnimationFinished,c=(0,T.Z)("recharts-radar",r);return y.createElement(i.m,{className:c},this.renderPolygon(),(!o||a)&&d.e.renderCallByParent(this.props,n))}}])&&st(r.prototype,n),o&&st(r,o),Object.defineProperty(r,"prototype",{writable:!1}),e}(y.PureComponent);vt(bt,"displayName","Radar"),vt(bt,"defaultProps",{angleAxisId:0,radiusAxisId:0,hide:!1,activeDot:!0,dot:!1,legendType:"rect",isAnimationActive:!it.x.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),vt(bt,"getComposedData",(function(t){var e=t.radiusAxis,r=t.angleAxis,n=t.displayedData,i=t.dataKey,o=t.bandSize,a=r.cx,c=r.cy,u=!1,l=[],s="number"!==r.type&&null!==o&&void 0!==o?o:0;n.forEach((function(t,n){var o=(0,ot.F$)(t,r.dataKey,n),f=(0,ot.F$)(t,i),p=r.scale(o)+s,d=Array.isArray(f)?Q()(f):f,y=$()(d)?void 0:e.scale(d);Array.isArray(f)&&f.length>=2&&(u=!0),l.push(lt(lt({},(0,I.op)(a,c,y,p)),{},{name:o,value:f,cx:a,cy:c,radius:y,angle:p,payload:t}))}));var f=[];return u&&l.forEach((function(t){if(Array.isArray(t.value)){var r=tt()(t.value),n=$()(r)?void 0:e.scale(r);f.push(lt(lt({},t),{},{radius:n},(0,I.op)(a,c,n,t.angle)))}else f.push(t)})),{points:l,isRange:u,baseLinePoints:f}}));var gt=r(69531);function Ot(t){return Ot="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ot(t)}function xt(){return xt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},xt.apply(this,arguments)}function wt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function jt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?wt(Object(r),!0).forEach((function(e){St(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):wt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function St(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Ot(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ot(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Ot(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Pt(t){return"string"===typeof t?parseInt(t,10):t}function At(t,e){var r="".concat(e.cx||t.cx),n=Number(r),i="".concat(e.cy||t.cy),o=Number(i);return jt(jt(jt({},e),t),{},{cx:n,cy:o})}function kt(t){return y.createElement(gt.bn,xt({shapeType:"sector",propTransformer:At},t))}var Et=r(33790),Tt=["shape","activeShape","activeIndex","cornerRadius"],It=["value","background"];function Ct(t){return Ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ct(t)}function Dt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Mt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Dt(Object(r),!0).forEach((function(e){Wt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Dt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Nt(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function Lt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Zt(n.key),n)}}function Bt(t,e,r){return e=zt(e),function(t,e){if(e&&("object"===Ct(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Ft(t)}(t,Rt()?Reflect.construct(e,r||[],zt(t).constructor):e.apply(t,r))}function Rt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Rt=function(){return!!t})()}function zt(t){return zt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},zt(t)}function Ft(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function _t(t,e){return _t=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},_t(t,e)}function Wt(t,e,r){return(e=Zt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Zt(t){var e=function(t,e){if("object"!=Ct(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ct(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Ct(e)?e:String(e)}var Kt=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return Wt(Ft(t=Bt(this,e,[].concat(n))),"state",{isAnimationFinished:!1}),Wt(Ft(t),"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),v()(e)&&e()})),Wt(Ft(t),"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),v()(e)&&e()})),t}var r,n,o;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_t(t,e)}(e,t),r=e,o=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],(n=[{key:"getDeltaAngle",value:function(){var t=this.props,e=t.startAngle,r=t.endAngle;return(0,nt.uY)(r-e)*Math.min(Math.abs(r-e),360)}},{key:"renderSectorsStatically",value:function(t){var e=this,r=this.props,n=r.shape,i=r.activeShape,o=r.activeIndex,a=r.cornerRadius,c=Nt(r,Tt),u=(0,C.L6)(c,!1);return t.map((function(t,r){var l=r===o,s=Mt(Mt(Mt(Mt({},u),{},{cornerRadius:Pt(a)},t),(0,Et.bw)(e.props,t,r)),{},{key:"sector-".concat(r),className:"recharts-radial-bar-sector ".concat(t.className),forceCornerRadius:c.forceCornerRadius,cornerIsExternal:c.cornerIsExternal,isActive:l,option:l?i:n});return y.createElement(kt,s)}))}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.isAnimationActive,o=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,l=this.state.prevData;return y.createElement(U.ZP,{begin:o,duration:a,isActive:n,easing:c,from:{t:0},to:{t:1},key:"radialBar-".concat(u),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(e){var n=e.t,o=r.map((function(t,e){var r=l&&l[e];if(r){var i=(0,nt.k4)(r.startAngle,t.startAngle),o=(0,nt.k4)(r.endAngle,t.endAngle);return Mt(Mt({},t),{},{startAngle:i(n),endAngle:o(n)})}var a=t.endAngle,c=t.startAngle,u=(0,nt.k4)(c,a);return Mt(Mt({},t),{},{endAngle:u(n)})}));return y.createElement(i.m,null,t.renderSectorsStatically(o))}))}},{key:"renderSectors",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return!(r&&e&&e.length)||n&&rt()(n,e)?this.renderSectorsStatically(e):this.renderSectorsWithAnimation()}},{key:"renderBackground",value:function(t){var e=this,r=this.props.cornerRadius,n=(0,C.L6)(this.props.background,!1);return t.map((function(t,i){t.value;var o=t.background,a=Nt(t,It);if(!o)return null;var c=Mt(Mt(Mt(Mt(Mt({cornerRadius:Pt(r)},a),{},{fill:"#eee"},o),n),(0,Et.bw)(e.props,t,i)),{},{index:i,key:"sector-".concat(i),className:(0,T.Z)("recharts-radial-bar-background-sector",null===n||void 0===n?void 0:n.className),option:o,isActive:!1});return y.createElement(kt,c)}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,o=t.background,a=t.isAnimationActive;if(e||!r||!r.length)return null;var c=this.state.isAnimationFinished,u=(0,T.Z)("recharts-area",n);return y.createElement(i.m,{className:u},o&&y.createElement(i.m,{className:"recharts-radial-bar-background"},this.renderBackground(r)),y.createElement(i.m,{className:"recharts-radial-bar-sectors"},this.renderSectors()),(!a||c)&&d.e.renderCallByParent(Mt({},this.props),r))}}])&&Lt(r.prototype,n),o&&Lt(r,o),Object.defineProperty(r,"prototype",{writable:!1}),e}(y.PureComponent);Wt(Kt,"displayName","RadialBar"),Wt(Kt,"defaultProps",{angleAxisId:0,radiusAxisId:0,minPointSize:0,hide:!1,legendType:"rect",data:[],isAnimationActive:!it.x.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease",forceCornerRadius:!1,cornerIsExternal:!1}),Wt(Kt,"getComposedData",(function(t){var e=t.item,r=t.props,n=t.radiusAxis,i=t.radiusAxisTicks,o=t.angleAxis,a=t.angleAxisTicks,c=t.displayedData,u=t.dataKey,l=t.stackedData,f=t.barPosition,p=t.bandSize,d=t.dataStartIndex,y=(0,ot.Bu)(f,e);if(!y)return null;var h=o.cx,v=o.cy,m=r.layout,b=e.props,g=b.children,O=b.minPointSize,x="radial"===m?o:n,w=l?x.scale.domain():null,j=(0,ot.Yj)({numericAxis:x}),S=(0,C.NN)(g,s.b);return{data:c.map((function(t,c){var s,f,b,g,x,P;if(l?s=(0,ot.Vv)(l[d+c],w):(s=(0,ot.F$)(t,u),Array.isArray(s)||(s=[j,s])),"radial"===m){f=(0,ot.Fy)({axis:n,ticks:i,bandSize:p,offset:y.offset,entry:t,index:c}),x=o.scale(s[1]),g=o.scale(s[0]),b=f+y.size;var A=x-g;if(Math.abs(O)>0&&Math.abs(A)<Math.abs(O))x+=(0,nt.uY)(A||O)*(Math.abs(O)-Math.abs(A));P={background:{cx:h,cy:v,innerRadius:f,outerRadius:b,startAngle:r.startAngle,endAngle:r.endAngle}}}else{f=n.scale(s[0]),b=n.scale(s[1]),x=(g=(0,ot.Fy)({axis:o,ticks:a,bandSize:p,offset:y.offset,entry:t,index:c}))+y.size;var k=b-f;if(Math.abs(O)>0&&Math.abs(k)<Math.abs(O))b+=(0,nt.uY)(k||O)*(Math.abs(O)-Math.abs(k))}return Mt(Mt(Mt(Mt({},t),P),{},{payload:t,value:l?s:s[1],cx:h,cy:v,innerRadius:f,outerRadius:b,startAngle:g,endAngle:x},S&&S[c]&&S[c].props),{},{tooltipPayload:[(0,ot.Qo)(e,t)],tooltipPosition:(0,I.op)(h,v,(f+b)/2,(g+x)/2)})})),layout:m}}));var Xt=r(88974),Vt=r(48586),Gt=r(7629),Yt=r(93264),Ut=r(27871),Ht=r(42333),$t=r(65370),qt=["type","layout","connectNulls","ref"];function Qt(t){return Qt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qt(t)}function Jt(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function te(){return te=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},te.apply(this,arguments)}function ee(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function re(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ee(Object(r),!0).forEach((function(e){fe(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ee(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ne(t){return function(t){if(Array.isArray(t))return ie(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return ie(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ie(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ie(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function oe(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pe(n.key),n)}}function ae(t,e,r){return e=ue(e),function(t,e){if(e&&("object"===Qt(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return le(t)}(t,ce()?Reflect.construct(e,r||[],ue(t).constructor):e.apply(t,r))}function ce(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(ce=function(){return!!t})()}function ue(t){return ue=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ue(t)}function le(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function se(t,e){return se=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},se(t,e)}function fe(t,e,r){return(e=pe(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pe(t){var e=function(t,e){if("object"!=Qt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Qt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Qt(e)?e:String(e)}var de=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return fe(le(t=ae(this,e,[].concat(n))),"state",{isAnimationFinished:!0,totalLength:0}),fe(le(t),"generateSimpleStrokeDasharray",(function(t,e){return"".concat(e,"px ").concat(t-e,"px")})),fe(le(t),"getStrokeDasharray",(function(r,n,i){var o=i.reduce((function(t,e){return t+e}));if(!o)return t.generateSimpleStrokeDasharray(n,r);for(var a=Math.floor(r/o),c=r%o,u=n-r,l=[],s=0,f=0;s<i.length;f+=i[s],++s)if(f+i[s]>c){l=[].concat(ne(i.slice(0,s)),[c-f]);break}var p=l.length%2===0?[0,u]:[u];return[].concat(ne(e.repeat(i,a)),ne(l),p).map((function(t){return"".concat(t,"px")})).join(", ")})),fe(le(t),"id",(0,nt.EL)("recharts-line-")),fe(le(t),"pathRef",(function(e){t.mainCurve=e})),fe(le(t),"handleAnimationEnd",(function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()})),fe(le(t),"handleAnimationStart",(function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()})),t}var r,n,o;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&se(t,e)}(e,t),r=e,o=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"repeat",value:function(t,e){for(var r=t.length%2!==0?[].concat(ne(t),[0]):t,n=[],i=0;i<e;++i)n=[].concat(ne(n),ne(r));return n}},{key:"renderDotItem",value:function(t,e){var r;if(y.isValidElement(t))r=y.cloneElement(t,e);else if(v()(t))r=t(e);else{var n=(0,T.Z)("recharts-line-dot","boolean"!==typeof t?t.className:"");r=y.createElement(A.o,te({},e,{className:n}))}return r}}],(n=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();this.setState({totalLength:t})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();t!==this.state.totalLength&&this.setState({totalLength:t})}}},{key:"getTotalLength",value:function(){var t=this.mainCurve;try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(e){return 0}}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.points,o=r.xAxis,a=r.yAxis,c=r.layout,u=r.children,l=(0,C.NN)(u,$t.W);if(!l)return null;var s=function(t,e){return{x:t.x,y:t.y,value:t.value,errorVal:(0,ot.F$)(t.payload,e)}},f={clipPath:t?"url(#clipPath-".concat(e,")"):null};return y.createElement(i.m,f,l.map((function(t){return y.cloneElement(t,{key:"bar-".concat(t.props.dataKey),data:n,xAxis:o,yAxis:a,layout:c,dataPointFormatter:s})})))}},{key:"renderDots",value:function(t,r,n){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var o=this.props,a=o.dot,c=o.points,u=o.dataKey,l=(0,C.L6)(this.props,!1),s=(0,C.L6)(a,!0),f=c.map((function(t,r){var n=re(re(re({key:"dot-".concat(r),r:3},l),s),{},{value:t.value,dataKey:u,cx:t.x,cy:t.y,index:r,payload:t.payload});return e.renderDotItem(a,n)})),p={clipPath:t?"url(#clipPath-".concat(r?"":"dots-").concat(n,")"):null};return y.createElement(i.m,te({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(t,e,r,n){var i=this.props,o=i.type,a=i.layout,c=i.connectNulls,u=(i.ref,Jt(i,qt)),l=re(re(re({},(0,C.L6)(u,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:e?"url(#clipPath-".concat(r,")"):null,points:t},n),{},{type:o,layout:a,connectNulls:c});return y.createElement(j.H,te({},l,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(t,e){var r=this,n=this.props,i=n.points,o=n.strokeDasharray,a=n.isAnimationActive,c=n.animationBegin,u=n.animationDuration,l=n.animationEasing,s=n.animationId,f=n.animateNewValues,p=n.width,d=n.height,h=this.state,v=h.prevPoints,m=h.totalLength;return y.createElement(U.ZP,{begin:c,duration:u,isActive:a,easing:l,from:{t:0},to:{t:1},key:"line-".concat(s),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(n){var a=n.t;if(v){var c=v.length/i.length,u=i.map((function(t,e){var r=Math.floor(e*c);if(v[r]){var n=v[r],i=(0,nt.k4)(n.x,t.x),o=(0,nt.k4)(n.y,t.y);return re(re({},t),{},{x:i(a),y:o(a)})}if(f){var u=(0,nt.k4)(2*p,t.x),l=(0,nt.k4)(d/2,t.y);return re(re({},t),{},{x:u(a),y:l(a)})}return re(re({},t),{},{x:t.x,y:t.y})}));return r.renderCurveStatically(u,t,e)}var l,s=(0,nt.k4)(0,m)(a);if(o){var y="".concat(o).split(/[,\s]+/gim).map((function(t){return parseFloat(t)}));l=r.getStrokeDasharray(s,m,y)}else l=r.generateSimpleStrokeDasharray(m,s);return r.renderCurveStatically(i,t,e,{strokeDasharray:l})}))}},{key:"renderCurve",value:function(t,e){var r=this.props,n=r.points,i=r.isAnimationActive,o=this.state,a=o.prevPoints,c=o.totalLength;return i&&n&&n.length&&(!a&&c>0||!rt()(a,n))?this.renderCurveWithAnimation(t,e):this.renderCurveStatically(n,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,n=e.dot,o=e.points,a=e.className,c=e.xAxis,u=e.yAxis,l=e.top,s=e.left,f=e.width,p=e.height,h=e.isAnimationActive,v=e.id;if(r||!o||!o.length)return null;var m=this.state.isAnimationFinished,b=1===o.length,g=(0,T.Z)("recharts-line",a),O=c&&c.allowDataOverflow,x=u&&u.allowDataOverflow,w=O||x,j=$()(v)?this.id:v,S=null!==(t=(0,C.L6)(n,!1))&&void 0!==t?t:{r:3,strokeWidth:2},P=S.r,A=void 0===P?3:P,k=S.strokeWidth,E=void 0===k?2:k,I=((0,C.$k)(n)?n:{}).clipDot,D=void 0===I||I,M=2*A+E;return y.createElement(i.m,{className:g},O||x?y.createElement("defs",null,y.createElement("clipPath",{id:"clipPath-".concat(j)},y.createElement("rect",{x:O?s:s-f/2,y:x?l:l-p/2,width:O?f:2*f,height:x?p:2*p})),!D&&y.createElement("clipPath",{id:"clipPath-dots-".concat(j)},y.createElement("rect",{x:s-M/2,y:l-M/2,width:f+M,height:p+M}))):null,!b&&this.renderCurve(w,j),this.renderErrorBar(w,j),(b||n)&&this.renderDots(w,D,j),(!h||m)&&d.e.renderCallByParent(this.props,o))}}])&&oe(r.prototype,n),o&&oe(r,o),Object.defineProperty(r,"prototype",{writable:!1}),e}(y.PureComponent);fe(de,"displayName","Line"),fe(de,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!it.x.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),fe(de,"getComposedData",(function(t){var e=t.props,r=t.xAxis,n=t.yAxis,i=t.xAxisTicks,o=t.yAxisTicks,a=t.dataKey,c=t.bandSize,u=t.displayedData,l=t.offset,s=e.layout;return re({points:u.map((function(t,e){var u=(0,ot.F$)(t,a);return"horizontal"===s?{x:(0,ot.Hv)({axis:r,ticks:i,bandSize:c,entry:t,index:e}),y:$()(u)?null:n.scale(u),value:u,payload:t}:{x:$()(u)?null:r.scale(u),y:(0,ot.Hv)({axis:n,ticks:o,bandSize:c,entry:t,index:e}),value:u,payload:t}})),layout:s},l)}));var ye,he=r(14019),ve=r.n(he),me=r(35813),be=r.n(me),ge=["layout","type","stroke","connectNulls","isRange","ref"];function Oe(t){return Oe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Oe(t)}function xe(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function we(){return we=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},we.apply(this,arguments)}function je(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Se(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?je(Object(r),!0).forEach((function(e){Ce(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):je(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Pe(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,De(n.key),n)}}function Ae(t,e,r){return e=Ee(e),function(t,e){if(e&&("object"===Oe(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Te(t)}(t,ke()?Reflect.construct(e,r||[],Ee(t).constructor):e.apply(t,r))}function ke(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(ke=function(){return!!t})()}function Ee(t){return Ee=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Ee(t)}function Te(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Ie(t,e){return Ie=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Ie(t,e)}function Ce(t,e,r){return(e=De(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function De(t){var e=function(t,e){if("object"!=Oe(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Oe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Oe(e)?e:String(e)}var Me=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return Ce(Te(t=Ae(this,e,[].concat(n))),"state",{isAnimationFinished:!0}),Ce(Te(t),"id",(0,nt.EL)("recharts-area-")),Ce(Te(t),"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),v()(e)&&e()})),Ce(Te(t),"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),v()(e)&&e()})),t}var r,n,o;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ie(t,e)}(e,t),r=e,o=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,curBaseLine:t.baseLine,prevPoints:e.curPoints,prevBaseLine:e.curBaseLine}:t.points!==e.curPoints||t.baseLine!==e.curBaseLine?{curPoints:t.points,curBaseLine:t.baseLine}:null}}],(n=[{key:"renderDots",value:function(t,r,n){var o=this.props.isAnimationActive,a=this.state.isAnimationFinished;if(o&&!a)return null;var c=this.props,u=c.dot,l=c.points,s=c.dataKey,f=(0,C.L6)(this.props,!1),p=(0,C.L6)(u,!0),d=l.map((function(t,r){var n=Se(Se(Se({key:"dot-".concat(r),r:3},f),p),{},{dataKey:s,cx:t.x,cy:t.y,index:r,value:t.value,payload:t.payload});return e.renderDotItem(u,n)})),h={clipPath:t?"url(#clipPath-".concat(r?"":"dots-").concat(n,")"):null};return y.createElement(i.m,we({className:"recharts-area-dots"},h),d)}},{key:"renderHorizontalRect",value:function(t){var e=this.props,r=e.baseLine,n=e.points,i=e.strokeWidth,o=n[0].x,a=n[n.length-1].x,c=t*Math.abs(o-a),u=ve()(n.map((function(t){return t.y||0})));return(0,nt.hj)(r)&&"number"===typeof r?u=Math.max(r,u):r&&Array.isArray(r)&&r.length&&(u=Math.max(ve()(r.map((function(t){return t.y||0}))),u)),(0,nt.hj)(u)?y.createElement("rect",{x:o<a?o:o-c,y:0,width:c,height:Math.floor(u+(i?parseInt("".concat(i),10):1))}):null}},{key:"renderVerticalRect",value:function(t){var e=this.props,r=e.baseLine,n=e.points,i=e.strokeWidth,o=n[0].y,a=n[n.length-1].y,c=t*Math.abs(o-a),u=ve()(n.map((function(t){return t.x||0})));return(0,nt.hj)(r)&&"number"===typeof r?u=Math.max(r,u):r&&Array.isArray(r)&&r.length&&(u=Math.max(ve()(r.map((function(t){return t.x||0}))),u)),(0,nt.hj)(u)?y.createElement("rect",{x:0,y:o<a?o:o-c,width:u+(i?parseInt("".concat(i),10):1),height:Math.floor(c)}):null}},{key:"renderClipRect",value:function(t){return"vertical"===this.props.layout?this.renderVerticalRect(t):this.renderHorizontalRect(t)}},{key:"renderAreaStatically",value:function(t,e,r,n){var o=this.props,a=o.layout,c=o.type,u=o.stroke,l=o.connectNulls,s=o.isRange,f=(o.ref,xe(o,ge));return y.createElement(i.m,{clipPath:r?"url(#clipPath-".concat(n,")"):null},y.createElement(j.H,we({},(0,C.L6)(f,!0),{points:t,connectNulls:l,type:c,baseLine:e,layout:a,stroke:"none",className:"recharts-area-area"})),"none"!==u&&y.createElement(j.H,we({},(0,C.L6)(this.props,!1),{className:"recharts-area-curve",layout:a,type:c,connectNulls:l,fill:"none",points:t})),"none"!==u&&s&&y.createElement(j.H,we({},(0,C.L6)(this.props,!1),{className:"recharts-area-curve",layout:a,type:c,connectNulls:l,fill:"none",points:e})))}},{key:"renderAreaWithAnimation",value:function(t,e){var r=this,n=this.props,o=n.points,a=n.baseLine,c=n.isAnimationActive,u=n.animationBegin,l=n.animationDuration,s=n.animationEasing,f=n.animationId,p=this.state,d=p.prevPoints,h=p.prevBaseLine;return y.createElement(U.ZP,{begin:u,duration:l,isActive:c,easing:s,from:{t:0},to:{t:1},key:"area-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(n){var c=n.t;if(d){var u,l=d.length/o.length,s=o.map((function(t,e){var r=Math.floor(e*l);if(d[r]){var n=d[r],i=(0,nt.k4)(n.x,t.x),o=(0,nt.k4)(n.y,t.y);return Se(Se({},t),{},{x:i(c),y:o(c)})}return t}));return u=(0,nt.hj)(a)&&"number"===typeof a?(0,nt.k4)(h,a)(c):$()(a)||be()(a)?(0,nt.k4)(h,0)(c):a.map((function(t,e){var r=Math.floor(e*l);if(h[r]){var n=h[r],i=(0,nt.k4)(n.x,t.x),o=(0,nt.k4)(n.y,t.y);return Se(Se({},t),{},{x:i(c),y:o(c)})}return t})),r.renderAreaStatically(s,u,t,e)}return y.createElement(i.m,null,y.createElement("defs",null,y.createElement("clipPath",{id:"animationClipPath-".concat(e)},r.renderClipRect(c))),y.createElement(i.m,{clipPath:"url(#animationClipPath-".concat(e,")")},r.renderAreaStatically(o,a,t,e)))}))}},{key:"renderArea",value:function(t,e){var r=this.props,n=r.points,i=r.baseLine,o=r.isAnimationActive,a=this.state,c=a.prevPoints,u=a.prevBaseLine,l=a.totalLength;return o&&n&&n.length&&(!c&&l>0||!rt()(c,n)||!rt()(u,i))?this.renderAreaWithAnimation(t,e):this.renderAreaStatically(n,i,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,n=e.dot,o=e.points,a=e.className,c=e.top,u=e.left,l=e.xAxis,s=e.yAxis,f=e.width,p=e.height,h=e.isAnimationActive,v=e.id;if(r||!o||!o.length)return null;var m=this.state.isAnimationFinished,b=1===o.length,g=(0,T.Z)("recharts-area",a),O=l&&l.allowDataOverflow,x=s&&s.allowDataOverflow,w=O||x,j=$()(v)?this.id:v,S=null!==(t=(0,C.L6)(n,!1))&&void 0!==t?t:{r:3,strokeWidth:2},P=S.r,A=void 0===P?3:P,k=S.strokeWidth,E=void 0===k?2:k,I=((0,C.$k)(n)?n:{}).clipDot,D=void 0===I||I,M=2*A+E;return y.createElement(i.m,{className:g},O||x?y.createElement("defs",null,y.createElement("clipPath",{id:"clipPath-".concat(j)},y.createElement("rect",{x:O?u:u-f/2,y:x?c:c-p/2,width:O?f:2*f,height:x?p:2*p})),!D&&y.createElement("clipPath",{id:"clipPath-dots-".concat(j)},y.createElement("rect",{x:u-M/2,y:c-M/2,width:f+M,height:p+M}))):null,b?null:this.renderArea(w,j),(n||b)&&this.renderDots(w,D,j),(!h||m)&&d.e.renderCallByParent(this.props,o))}}])&&Pe(r.prototype,n),o&&Pe(r,o),Object.defineProperty(r,"prototype",{writable:!1}),e}(y.PureComponent);ye=Me,Ce(Me,"displayName","Area"),Ce(Me,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!it.x.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),Ce(Me,"getBaseValue",(function(t,e,r,n){var i=t.layout,o=t.baseValue,a=e.props.baseValue,c=null!==a&&void 0!==a?a:o;if((0,nt.hj)(c)&&"number"===typeof c)return c;var u="horizontal"===i?n:r,l=u.scale.domain();if("number"===u.type){var s=Math.max(l[0],l[1]),f=Math.min(l[0],l[1]);return"dataMin"===c?f:"dataMax"===c||s<0?s:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===c?l[0]:"dataMax"===c?l[1]:l[0]})),Ce(Me,"getComposedData",(function(t){var e,r=t.props,n=t.item,i=t.xAxis,o=t.yAxis,a=t.xAxisTicks,c=t.yAxisTicks,u=t.bandSize,l=t.dataKey,s=t.stackedData,f=t.dataStartIndex,p=t.displayedData,d=t.offset,y=r.layout,h=s&&s.length,v=ye.getBaseValue(r,n,i,o),m="horizontal"===y,b=!1,g=p.map((function(t,e){var r;h?r=s[f+e]:(r=(0,ot.F$)(t,l),Array.isArray(r)?b=!0:r=[v,r]);var n=null==r[1]||h&&null==(0,ot.F$)(t,l);return m?{x:(0,ot.Hv)({axis:i,ticks:a,bandSize:u,entry:t,index:e}),y:n?null:o.scale(r[1]),value:r,payload:t}:{x:n?null:i.scale(r[1]),y:(0,ot.Hv)({axis:o,ticks:c,bandSize:u,entry:t,index:e}),value:r,payload:t}}));return e=h||b?g.map((function(t){var e=Array.isArray(t.value)?t.value[0]:null;return m?{x:t.x,y:null!=e&&null!=t.y?o.scale(e):null}:{x:null!=e?i.scale(e):null,y:t.y}})):m?o.scale(v):i.scale(v),Se({points:g,baseLine:e,layout:y,isRange:b},d)})),Ce(Me,"renderDotItem",(function(t,e){var r;if(y.isValidElement(t))r=y.cloneElement(t,e);else if(v()(t))r=t(e);else{var n=(0,T.Z)("recharts-area-dot","boolean"!==typeof t?t.className:"");r=y.createElement(A.o,we({},e,{className:n}))}return r}));var Ne=r(48218),Le=function(){return null};Le.displayName="ZAxis",Le.defaultProps={zAxisId:0,range:[64,64],scale:"auto",type:"number"};var Be=["option","isActive"];function Re(){return Re=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Re.apply(this,arguments)}function ze(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function Fe(t){var e=t.option,r=t.isActive,n=ze(t,Be);return"string"===typeof e?y.createElement(gt.bn,Re({option:y.createElement(E.v,Re({type:e},n)),isActive:r,shapeType:"symbols"},n)):y.createElement(gt.bn,Re({option:e,isActive:r,shapeType:"symbols"},n))}function _e(t){return _e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_e(t)}function We(){return We=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},We.apply(this,arguments)}function Ze(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ke(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ze(Object(r),!0).forEach((function(e){$e(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ze(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Xe(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,qe(n.key),n)}}function Ve(t,e,r){return e=Ye(e),function(t,e){if(e&&("object"===_e(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Ue(t)}(t,Ge()?Reflect.construct(e,r||[],Ye(t).constructor):e.apply(t,r))}function Ge(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Ge=function(){return!!t})()}function Ye(t){return Ye=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Ye(t)}function Ue(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function He(t,e){return He=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},He(t,e)}function $e(t,e,r){return(e=qe(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function qe(t){var e=function(t,e){if("object"!=_e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=_e(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==_e(e)?e:String(e)}var Qe=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return $e(Ue(t=Ve(this,e,[].concat(n))),"state",{isAnimationFinished:!1}),$e(Ue(t),"handleAnimationEnd",(function(){t.setState({isAnimationFinished:!0})})),$e(Ue(t),"handleAnimationStart",(function(){t.setState({isAnimationFinished:!1})})),$e(Ue(t),"id",(0,nt.EL)("recharts-scatter-")),t}var r,n,o;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&He(t,e)}(e,t),r=e,o=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}}],(n=[{key:"renderSymbolsStatically",value:function(t){var e=this,r=this.props,n=r.shape,o=r.activeShape,a=r.activeIndex,c=(0,C.L6)(this.props,!1);return t.map((function(t,r){var u=a===r,l=u?o:n,s=Ke(Ke({key:"symbol-".concat(r)},c),t);return y.createElement(i.m,We({className:"recharts-scatter-symbol"},(0,Et.bw)(e.props,t,r),{key:"symbol-".concat(null===t||void 0===t?void 0:t.cx,"-").concat(null===t||void 0===t?void 0:t.cy,"-").concat(null===t||void 0===t?void 0:t.size,"-").concat(r),role:"img"}),y.createElement(Fe,We({option:l,isActive:u},s)))}))}},{key:"renderSymbolsWithAnimation",value:function(){var t=this,e=this.props,r=e.points,n=e.isAnimationActive,o=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,l=this.state.prevPoints;return y.createElement(U.ZP,{begin:o,duration:a,isActive:n,easing:c,from:{t:0},to:{t:1},key:"pie-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(e){var n=e.t,o=r.map((function(t,e){var r=l&&l[e];if(r){var i=(0,nt.k4)(r.cx,t.cx),o=(0,nt.k4)(r.cy,t.cy),a=(0,nt.k4)(r.size,t.size);return Ke(Ke({},t),{},{cx:i(n),cy:o(n),size:a(n)})}var c=(0,nt.k4)(0,t.size);return Ke(Ke({},t),{},{size:c(n)})}));return y.createElement(i.m,null,t.renderSymbolsStatically(o))}))}},{key:"renderSymbols",value:function(){var t=this.props,e=t.points,r=t.isAnimationActive,n=this.state.prevPoints;return!(r&&e&&e.length)||n&&rt()(n,e)?this.renderSymbolsStatically(e):this.renderSymbolsWithAnimation()}},{key:"renderErrorBar",value:function(){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var t=this.props,e=t.points,r=t.xAxis,n=t.yAxis,i=t.children,o=(0,C.NN)(i,$t.W);return o?o.map((function(t,i){var o=t.props,a=o.direction,c=o.dataKey;return y.cloneElement(t,{key:"".concat(a,"-").concat(c,"-").concat(e[i]),data:e,xAxis:r,yAxis:n,layout:"x"===a?"vertical":"horizontal",dataPointFormatter:function(t,e){return{x:t.cx,y:t.cy,value:"x"===a?+t.node.x:+t.node.y,errorVal:(0,ot.F$)(t,e)}}})})):null}},{key:"renderLine",value:function(){var t,e,r=this.props,n=r.points,o=r.line,a=r.lineType,c=r.lineJointType,u=(0,C.L6)(this.props,!1),l=(0,C.L6)(o,!1);if("joint"===a)t=n.map((function(t){return{x:t.cx,y:t.cy}}));else if("fitting"===a){var s=(0,nt.wr)(n),f=s.xmin,p=s.xmax,d=s.a,h=s.b,m=function(t){return d*t+h};t=[{x:f,y:m(f)},{x:p,y:m(p)}]}var b=Ke(Ke(Ke({},u),{},{fill:"none",stroke:u&&u.fill},l),{},{points:t});return e=y.isValidElement(o)?y.cloneElement(o,b):v()(o)?o(b):y.createElement(j.H,We({},b,{type:c})),y.createElement(i.m,{className:"recharts-scatter-line",key:"recharts-scatter-line"},e)}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.points,n=t.line,o=t.className,a=t.xAxis,c=t.yAxis,u=t.left,l=t.top,s=t.width,f=t.height,p=t.id,h=t.isAnimationActive;if(e||!r||!r.length)return null;var v=this.state.isAnimationFinished,m=(0,T.Z)("recharts-scatter",o),b=a&&a.allowDataOverflow,g=c&&c.allowDataOverflow,O=b||g,x=$()(p)?this.id:p;return y.createElement(i.m,{className:m,clipPath:O?"url(#clipPath-".concat(x,")"):null},b||g?y.createElement("defs",null,y.createElement("clipPath",{id:"clipPath-".concat(x)},y.createElement("rect",{x:b?u:u-s/2,y:g?l:l-f/2,width:b?s:2*s,height:g?f:2*f}))):null,n&&this.renderLine(),this.renderErrorBar(),y.createElement(i.m,{key:"recharts-scatter-symbols"},this.renderSymbols()),(!h||v)&&d.e.renderCallByParent(this.props,r))}}])&&Xe(r.prototype,n),o&&Xe(r,o),Object.defineProperty(r,"prototype",{writable:!1}),e}(y.PureComponent);$e(Qe,"displayName","Scatter"),$e(Qe,"defaultProps",{xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!it.x.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"}),$e(Qe,"getComposedData",(function(t){var e=t.xAxis,r=t.yAxis,n=t.zAxis,i=t.item,o=t.displayedData,a=t.xAxisTicks,c=t.yAxisTicks,u=t.offset,l=i.props.tooltipType,f=(0,C.NN)(i.props.children,s.b),p=$()(e.dataKey)?i.props.dataKey:e.dataKey,d=$()(r.dataKey)?i.props.dataKey:r.dataKey,y=n&&n.dataKey,h=n?n.range:Le.defaultProps.range,v=h&&h[0],m=e.scale.bandwidth?e.scale.bandwidth():0,b=r.scale.bandwidth?r.scale.bandwidth():0,g=o.map((function(t,o){var u=(0,ot.F$)(t,p),s=(0,ot.F$)(t,d),h=!$()(y)&&(0,ot.F$)(t,y)||"-",g=[{name:$()(e.dataKey)?i.props.name:e.name||e.dataKey,unit:e.unit||"",value:u,payload:t,dataKey:p,type:l},{name:$()(r.dataKey)?i.props.name:r.name||r.dataKey,unit:r.unit||"",value:s,payload:t,dataKey:d,type:l}];"-"!==h&&g.push({name:n.name||n.dataKey,unit:n.unit||"",value:h,payload:t,dataKey:y,type:l});var O=(0,ot.Hv)({axis:e,ticks:a,bandSize:m,entry:t,index:o,dataKey:p}),x=(0,ot.Hv)({axis:r,ticks:c,bandSize:b,entry:t,index:o,dataKey:d}),w="-"!==h?n.scale(h):v,j=Math.sqrt(Math.max(w,0)/Math.PI);return Ke(Ke({},t),{},{cx:O,cy:x,x:O-j,y:x-j,xAxis:e,yAxis:r,zAxis:n,width:2*j,height:2*j,size:w,node:{x:u,y:s,z:h},tooltipPayload:g,tooltipPosition:{x:O,y:x},payload:t},f&&f[o]&&f[o].props)}));return Ke({points:g},u)}));var Je=r(23007),tr=r(58104),er=r(88259),rr=r(87210),nr=(0,er.z)({chartName:"LineChart",GraphicalChild:de,axisComponents:[{axisType:"xAxis",AxisComp:Je.K},{axisType:"yAxis",AxisComp:tr.B}],formatAxisMap:rr.t9}),ir=r(79416),or=r(77434),ar=r(38863),cr=r.n(ar),ur=r(80089),lr=r.n(ur),sr=["#1890FF","#66B5FF","#41D9C7","#2FC25B","#6EDB8F","#9AE65C","#FACC14","#E6965C","#57AD71","#223273","#738AE6","#7564CC","#8543E0","#A877ED","#5C8EE6","#13C2C2","#70E0E0","#5CA3E6","#3436C7","#8082FF","#DD81E6","#F04864","#FA7D92","#D598D9"],fr=r(99875),pr=["width","height","className","style","children","type"];function dr(t){return dr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dr(t)}function yr(){return yr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},yr.apply(this,arguments)}function hr(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function vr(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Pr(n.key),n)}}function mr(t,e,r){return e=gr(e),function(t,e){if(e&&("object"===dr(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Or(t)}(t,br()?Reflect.construct(e,r||[],gr(t).constructor):e.apply(t,r))}function br(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(br=function(){return!!t})()}function gr(t){return gr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},gr(t)}function Or(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function xr(t,e){return xr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},xr(t,e)}function wr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function jr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?wr(Object(r),!0).forEach((function(e){Sr(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):wr(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Sr(t,e,r){return(e=Pr(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Pr(t){var e=function(t,e){if("object"!=dr(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dr(e)?e:String(e)}var Ar="value",kr=function t(e){var r,n=e.depth,i=e.node,o=e.index,a=e.valueKey,c=i.children,u=n+1,l=c&&c.length?c.map((function(e,r){return t({depth:u,node:e,index:r,valueKey:a})})):null;return r=c&&c.length?l.reduce((function(t,e){return t+e[Ar]}),0):be()(i[a])||i[a]<=0?0:i[a],jr(jr({},i),{},Sr(Sr(Sr({children:l},Ar,r),"depth",n),"index",o))},Er=function(t,e,r){var n=e*e,i=t.area*t.area,o=t.reduce((function(t,e){return{min:Math.min(t.min,e.area),max:Math.max(t.max,e.area)}}),{min:1/0,max:0}),a=o.min,c=o.max;return i?Math.max(n*c*r/i,i/(n*a*r)):1/0},Tr=function(t,e,r,n){return e===r.width?function(t,e,r,n){var i=e?Math.round(t.area/e):0;(n||i>r.height)&&(i=r.height);for(var o,a=r.x,c=0,u=t.length;c<u;c++)(o=t[c]).x=a,o.y=r.y,o.height=i,o.width=Math.min(i?Math.round(o.area/i):0,r.x+r.width-a),a+=o.width;return o.width+=r.x+r.width-a,jr(jr({},r),{},{y:r.y+i,height:r.height-i})}(t,e,r,n):function(t,e,r,n){var i=e?Math.round(t.area/e):0;(n||i>r.width)&&(i=r.width);for(var o,a=r.y,c=0,u=t.length;c<u;c++)(o=t[c]).x=r.x,o.y=a,o.width=i,o.height=Math.min(i?Math.round(o.area/i):0,r.y+r.height-a),a+=o.height;return o&&(o.height+=r.y+r.height-a),jr(jr({},r),{},{x:r.x+i,width:r.width-i})}(t,e,r,n)},Ir=function t(e,r){var n=e.children;if(n&&n.length){var i,o,a=function(t){return{x:t.x,y:t.y,width:t.width,height:t.height}}(e),c=[],u=1/0,l=Math.min(a.width,a.height),s=function(t,e){var r=e<0?0:e;return t.map((function(t){var e=t[Ar]*r;return jr(jr({},t),{},{area:be()(e)||e<=0?0:e})}))}(n,a.width*a.height/e[Ar]),f=s.slice();for(c.area=0;f.length>0;)c.push(i=f[0]),c.area+=i.area,(o=Er(c,l,r))<=u?(f.shift(),u=o):(c.area-=c.pop().area,a=Tr(c,l,a,!1),l=Math.min(a.width,a.height),c.length=c.area=0,u=1/0);return c.length&&(a=Tr(c,l,a,!0),c.length=c.area=0),jr(jr({},e),{},{children:s.map((function(e){return t(e,r)}))})}return e},Cr={isTooltipActive:!1,isAnimationFinished:!1,activeNode:null,formatRoot:null,currentRoot:null,nestIndex:[]},Dr=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return Sr(Or(t=mr(this,e,[].concat(n))),"state",jr({},Cr)),Sr(Or(t),"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),v()(e)&&e()})),Sr(Or(t),"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),v()(e)&&e()})),t}var r,o,a;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&xr(t,e)}(e,t),r=e,a=[{key:"getDerivedStateFromProps",value:function(t,e){if(t.data!==e.prevData||t.type!==e.prevType||t.width!==e.prevWidth||t.height!==e.prevHeight||t.dataKey!==e.prevDataKey||t.aspectRatio!==e.prevAspectRatio){var r=kr({depth:0,node:{children:t.data,x:0,y:0,width:t.width,height:t.height},index:0,valueKey:t.dataKey}),n=Ir(r,t.aspectRatio);return jr(jr({},e),{},{formatRoot:n,currentRoot:r,nestIndex:[r],prevAspectRatio:t.aspectRatio,prevData:t.data,prevWidth:t.width,prevHeight:t.height,prevDataKey:t.dataKey,prevType:t.type})}return null}},{key:"renderContentItem",value:function(t,e,r,n){if(y.isValidElement(t))return y.cloneElement(t,e);if(v()(t))return t(e);var i=e.x,o=e.y,a=e.width,c=e.height,u=e.index,l=null;a>10&&c>10&&e.children&&"nest"===r&&(l=y.createElement(P.m,{points:[{x:i+2,y:o+c/2},{x:i+6,y:o+c/2+3},{x:i+2,y:o+c/2+6}]}));var s=null,f=(0,fr.xE)(e.name);a>20&&c>20&&f.width<a&&f.height<c&&(s=y.createElement("text",{x:i+8,y:o+c/2+7,fontSize:14},e.name));var p=n||sr;return y.createElement("g",null,y.createElement(S.A,yr({fill:e.depth<2?p[u%p.length]:"rgba(255,255,255,0)",stroke:"#fff"},cr()(e,"children"),{role:"img"})),l,s)}}],(o=[{key:"handleMouseEnter",value:function(t,e){e.persist();var r=this.props,n=r.onMouseEnter,i=r.children;(0,C.sP)(i,c.u)?this.setState({isTooltipActive:!0,activeNode:t},(function(){n&&n(t,e)})):n&&n(t,e)}},{key:"handleMouseLeave",value:function(t,e){e.persist();var r=this.props,n=r.onMouseLeave,i=r.children;(0,C.sP)(i,c.u)?this.setState({isTooltipActive:!1,activeNode:null},(function(){n&&n(t,e)})):n&&n(t,e)}},{key:"handleClick",value:function(t){var e=this.props,r=e.onClick;if("nest"===e.type&&t.children){var n=this.props,i=n.width,o=n.height,a=n.dataKey,c=n.aspectRatio,u=kr({depth:0,node:jr(jr({},t),{},{x:0,y:0,width:i,height:o}),index:0,valueKey:a}),l=Ir(u,c),s=this.state.nestIndex;s.push(t),this.setState({formatRoot:l,currentRoot:u,nestIndex:s})}r&&r(t)}},{key:"handleNestIndex",value:function(t,e){var r=this.state.nestIndex,n=this.props,i=n.width,o=n.height,a=n.dataKey,c=n.aspectRatio,u=kr({depth:0,node:jr(jr({},t),{},{x:0,y:0,width:i,height:o}),index:0,valueKey:a}),l=Ir(u,c);r=r.slice(0,e+1),this.setState({formatRoot:l,currentRoot:t,nestIndex:r})}},{key:"renderItem",value:function(t,e,r){var n=this,o=this.props,a=o.isAnimationActive,c=o.animationBegin,u=o.animationDuration,l=o.animationEasing,s=o.isUpdateAnimationActive,f=o.type,p=o.animationId,d=o.colorPanel,h=this.state.isAnimationFinished,v=e.width,m=e.height,b=e.x,g=e.y,O=e.depth,x=parseInt("".concat((2*Math.random()-1)*v),10),w={};return(r||"nest"===f)&&(w={onMouseEnter:this.handleMouseEnter.bind(this,e),onMouseLeave:this.handleMouseLeave.bind(this,e),onClick:this.handleClick.bind(this,e)}),a?y.createElement(U.ZP,{begin:c,duration:u,isActive:a,easing:l,key:"treemap-".concat(p),from:{x:b,y:g,width:v,height:m},to:{x:b,y:g,width:v,height:m},onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(r){var o=r.x,p=r.y,v=r.width,m=r.height;return y.createElement(U.ZP,{from:"translate(".concat(x,"px, ").concat(x,"px)"),to:"translate(0, 0)",attributeName:"transform",begin:c,easing:l,isActive:a,duration:u},y.createElement(i.m,w,O>2&&!h?null:n.constructor.renderContentItem(t,jr(jr({},e),{},{isAnimationActive:a,isUpdateAnimationActive:!s,width:v,height:m,x:o,y:p}),f,d)))})):y.createElement(i.m,w,this.constructor.renderContentItem(t,jr(jr({},e),{},{isAnimationActive:!1,isUpdateAnimationActive:!1,width:v,height:m,x:b,y:g}),f,d))}},{key:"renderNode",value:function(t,e){var r=this,n=this.props,o=n.content,a=n.type,c=jr(jr(jr({},(0,C.L6)(this.props,!1)),e),{},{root:t}),u=!e.children||!e.children.length;return!(this.state.currentRoot.children||[]).filter((function(t){return t.depth===e.depth&&t.name===e.name})).length&&t.depth&&"nest"===a?null:y.createElement(i.m,{key:"recharts-treemap-node-".concat(c.x,"-").concat(c.y,"-").concat(c.name),className:"recharts-treemap-depth-".concat(e.depth)},this.renderItem(o,c,u),e.children&&e.children.length?e.children.map((function(t){return r.renderNode(e,t)})):null)}},{key:"renderAllNodes",value:function(){var t=this.state.formatRoot;return t?this.renderNode(t,t):null}},{key:"renderTooltip",value:function(){var t=this.props,e=t.children,r=t.nameKey,n=(0,C.sP)(e,c.u);if(!n)return null;var i=this.props,o=i.width,a=i.height,u=this.state,l=u.isTooltipActive,s=u.activeNode,f={x:0,y:0,width:o,height:a},p=s?{x:s.x+s.width/2,y:s.y+s.height/2}:null,d=l&&s?[{payload:s,name:(0,ot.F$)(s,r,""),value:(0,ot.F$)(s,Ar)}]:[];return y.cloneElement(n,{viewBox:f,active:l,coordinate:p,label:"",payload:d})}},{key:"renderNestIndex",value:function(){var t=this,e=this.props,r=e.nameKey,n=e.nestIndexContent,i=this.state.nestIndex;return y.createElement("div",{className:"recharts-treemap-nest-index-wrapper",style:{marginTop:"8px",textAlign:"center"}},i.map((function(e,i){var o=lr()(e,r,"root"),a=null;return y.isValidElement(n)&&(a=y.cloneElement(n,e,i)),a=v()(n)?n(e,i):o,y.createElement("div",{onClick:t.handleNestIndex.bind(t,e,i),key:"nest-index-".concat((0,nt.EL)()),className:"recharts-treemap-nest-index-box",style:{cursor:"pointer",display:"inline-block",padding:"0 7px",background:"#000",color:"#fff",marginRight:"3px"}},a)})))}},{key:"render",value:function(){if(!(0,C.TT)(this))return null;var t=this.props,e=t.width,r=t.height,i=t.className,o=t.style,a=t.children,c=t.type,u=hr(t,pr),l=(0,C.L6)(u,!1);return y.createElement("div",{className:(0,T.Z)("recharts-wrapper",i),style:jr(jr({},o),{},{position:"relative",cursor:"default",width:e,height:r}),role:"region"},y.createElement(n.T,yr({},l,{width:e,height:"nest"===c?r-30:r}),this.renderAllNodes(),(0,C.hQ)(a)),this.renderTooltip(),"nest"===c&&this.renderNestIndex())}}])&&vr(r.prototype,o),a&&vr(r,a),Object.defineProperty(r,"prototype",{writable:!1}),e}(y.PureComponent);Sr(Dr,"displayName","Treemap"),Sr(Dr,"defaultProps",{aspectRatio:.5*(1+Math.sqrt(5)),dataKey:"value",type:"flat",isAnimationActive:!it.x.isSsr,isUpdateAnimationActive:!it.x.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"linear"});var Mr=r(58399),Nr=r.n(Mr),Lr=r(73398),Br=r.n(Lr),Rr=r(61224),zr=r.n(Rr),Fr=r(68201),_r=["width","height","className","style","children"],Wr=["sourceX","sourceY","sourceControlX","targetX","targetY","targetControlX","linkWidth"];function Zr(t){return Zr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zr(t)}function Kr(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function Xr(){return Xr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Xr.apply(this,arguments)}function Vr(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tn(n.key),n)}}function Gr(t,e,r){return e=Ur(e),function(t,e){if(e&&("object"===Zr(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Hr(t)}(t,Yr()?Reflect.construct(e,r||[],Ur(t).constructor):e.apply(t,r))}function Yr(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Yr=function(){return!!t})()}function Ur(t){return Ur=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Ur(t)}function Hr(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function $r(t,e){return $r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},$r(t,e)}function qr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Qr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?qr(Object(r),!0).forEach((function(e){Jr(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):qr(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Jr(t,e,r){return(e=tn(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tn(t){var e=function(t,e){if("object"!=Zr(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Zr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Zr(e)?e:String(e)}var en={x:0,y:0},rn=function(t){return t.y+t.dy/2},nn=function(t){return t&&t.value||0},on=function(t,e){return e.reduce((function(e,r){return e+nn(t[r])}),0)},an=function(t,e,r){return r.reduce((function(r,n){var i=e[n],o=t[i.source];return r+rn(o)*nn(e[n])}),0)},cn=function(t,e,r){return r.reduce((function(r,n){var i=e[n],o=t[i.target];return r+rn(o)*nn(e[n])}),0)},un=function(t,e){return t.y-e.y},ln=function t(e,r){for(var n=r.targetNodes,i=0,o=n.length;i<o;i++){var a=e[n[i]];a&&(a.depth=Math.max(r.depth+1,a.depth),t(e,a))}},sn=function(t,e,r){for(var n=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],i=0,o=t.length;i<o;i++){var a=t[i],c=a.length;n&&a.sort(un);for(var u=0,l=0;l<c;l++){var s=a[l],f=u-s.y;f>0&&(s.y+=f),u=s.y+s.dy+r}u=e+r;for(var p=c-1;p>=0;p--){var d=a[p],y=d.y+d.dy+r-u;if(!(y>0))break;d.y-=y,u=d.y}}},fn=function(t,e,r,n){for(var i=0,o=e.length;i<o;i++)for(var a=e[i],c=0,u=a.length;c<u;c++){var l=a[c];if(l.sourceLinks.length){var s=on(r,l.sourceLinks),f=an(t,r,l.sourceLinks)/s;l.y+=(f-rn(l))*n}}},pn=function(t,e,r,n){for(var i=e.length-1;i>=0;i--)for(var o=e[i],a=0,c=o.length;a<c;a++){var u=o[a];if(u.targetLinks.length){var l=on(r,u.targetLinks),s=cn(t,r,u.targetLinks)/l;u.y+=(s-rn(u))*n}}},dn=function(t){var e=t.data,r=t.width,n=t.height,i=t.iterations,o=t.nodeWidth,a=t.nodePadding,c=t.sort,u=e.links,l=function(t,e,r){for(var n=t.nodes,i=t.links,o=n.map((function(t,e){var r=function(t,e){for(var r=[],n=[],i=[],o=[],a=0,c=t.length;a<c;a++){var u=t[a];u.source===e&&(i.push(u.target),o.push(a)),u.target===e&&(r.push(u.source),n.push(a))}return{sourceNodes:r,sourceLinks:n,targetLinks:o,targetNodes:i}}(i,e);return Qr(Qr(Qr({},t),r),{},{value:Math.max(on(i,r.sourceLinks),on(i,r.targetLinks)),depth:0})})),a=0,c=o.length;a<c;a++){var u=o[a];u.sourceNodes.length||ln(o,u)}var l=Nr()(o,(function(t){return t.depth})).depth;if(l>=1)for(var s=(e-r)/l,f=0,p=o.length;f<p;f++){var d=o[f];d.targetNodes.length||(d.depth=l),d.x=d.depth*s,d.dx=r}return{tree:o,maxDepth:l}}(e,r,o),s=l.tree,f=function(t){for(var e=[],r=0,n=t.length;r<n;r++){var i=t[r];e[i.depth]||(e[i.depth]=[]),e[i.depth].push(i)}return e}(s),p=function(t,e,r,n){for(var i=Br()(t.map((function(t){return(e-(t.length-1)*r)/zr()(t,nn)}))),o=0,a=t.length;o<a;o++)for(var c=0,u=t[o].length;c<u;c++){var l=t[o][c];l.y=c,l.dy=l.value*i}return n.map((function(t){return Qr(Qr({},t),{},{dy:nn(t)*i})}))}(f,n,a,u);sn(f,n,a,c);for(var d=1,y=1;y<=i;y++)pn(s,f,p,d*=.99),sn(f,n,a,c),fn(s,f,p,d),sn(f,n,a,c);return function(t,e){for(var r=0,n=t.length;r<n;r++){var i=t[r],o=0,a=0;i.targetLinks.sort((function(r,n){return t[e[r].target].y-t[e[n].target].y})),i.sourceLinks.sort((function(r,n){return t[e[r].source].y-t[e[n].source].y}));for(var c=0,u=i.targetLinks.length;c<u;c++){var l=e[i.targetLinks[c]];l&&(l.sy=o,o+=l.dy)}for(var s=0,f=i.sourceLinks.length;s<f;s++){var p=e[i.sourceLinks[s]];p&&(p.ty=a,a+=p.dy)}}}(s,p),{nodes:s,links:p}},yn=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return Jr(Hr(t=Gr(this,e,[].concat(n))),"state",{activeElement:null,activeElementType:null,isTooltipActive:!1,nodes:[],links:[]}),t}var r,o,a;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&$r(t,e)}(e,t),r=e,a=[{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,i=t.height,o=t.margin,a=t.iterations,c=t.nodeWidth,u=t.nodePadding,l=t.sort;if(r!==e.prevData||n!==e.prevWidth||i!==e.prevHeight||!(0,Fr.w)(o,e.prevMargin)||a!==e.prevIterations||c!==e.prevNodeWidth||u!==e.prevNodePadding||l!==e.sort){var s=n-(o&&o.left||0)-(o&&o.right||0),f=i-(o&&o.top||0)-(o&&o.bottom||0),p=dn({data:r,width:s,height:f,iterations:a,nodeWidth:c,nodePadding:u,sort:l}),d=p.links,y=p.nodes;return Qr(Qr({},e),{},{nodes:y,links:d,prevData:r,prevWidth:a,prevHeight:i,prevMargin:o,prevNodePadding:u,prevNodeWidth:c,prevIterations:a,prevSort:l})}return null}},{key:"renderLinkItem",value:function(t,e){if(y.isValidElement(t))return y.cloneElement(t,e);if(v()(t))return t(e);var r=e.sourceX,n=e.sourceY,i=e.sourceControlX,o=e.targetX,a=e.targetY,c=e.targetControlX,u=e.linkWidth,l=Kr(e,Wr);return y.createElement("path",Xr({className:"recharts-sankey-link",d:"\n          M".concat(r,",").concat(n,"\n          C").concat(i,",").concat(n," ").concat(c,",").concat(a," ").concat(o,",").concat(a,"\n        "),fill:"none",stroke:"#333",strokeWidth:u,strokeOpacity:"0.2"},(0,C.L6)(l,!1)))}},{key:"renderNodeItem",value:function(t,e){return y.isValidElement(t)?y.cloneElement(t,e):v()(t)?t(e):y.createElement(S.A,Xr({className:"recharts-sankey-node",fill:"#0088fe",fillOpacity:"0.8"},(0,C.L6)(e,!1),{role:"img"}))}}],(o=[{key:"handleMouseEnter",value:function(t,e,r){var n=this.props,i=n.onMouseEnter,o=n.children,a=(0,C.sP)(o,c.u);a?this.setState((function(r){return"hover"===a.props.trigger?Qr(Qr({},r),{},{activeElement:t,activeElementType:e,isTooltipActive:!0}):r}),(function(){i&&i(t,e,r)})):i&&i(t,e,r)}},{key:"handleMouseLeave",value:function(t,e,r){var n=this.props,i=n.onMouseLeave,o=n.children,a=(0,C.sP)(o,c.u);a?this.setState((function(t){return"hover"===a.props.trigger?Qr(Qr({},t),{},{activeElement:void 0,activeElementType:void 0,isTooltipActive:!1}):t}),(function(){i&&i(t,e,r)})):i&&i(t,e,r)}},{key:"handleClick",value:function(t,e,r){var n=this.props,i=n.onClick,o=n.children,a=(0,C.sP)(o,c.u);a&&"click"===a.props.trigger&&(this.state.isTooltipActive?this.setState((function(t){return Qr(Qr({},t),{},{activeElement:void 0,activeElementType:void 0,isTooltipActive:!1})})):this.setState((function(r){return Qr(Qr({},r),{},{activeElement:t,activeElementType:e,isTooltipActive:!0})}))),i&&i(t,e,r)}},{key:"renderLinks",value:function(t,e){var r=this,n=this.props,o=n.linkCurvature,a=n.link,c=n.margin,u=lr()(c,"top")||0,l=lr()(c,"left")||0;return y.createElement(i.m,{className:"recharts-sankey-links",key:"recharts-sankey-links"},t.map((function(t,n){var c=t.sy,s=t.ty,f=t.dy,p=e[t.source],d=e[t.target],h=p.x+p.dx+l,v=d.x+l,m=function(t,e){var r=+t,n=e-r;return function(t){return r+n*t}}(h,v),b=m(o),g=m(1-o),O=Qr({sourceX:h,targetX:v,sourceY:p.y+c+f/2+u,targetY:d.y+s+f/2+u,sourceControlX:b,targetControlX:g,sourceRelativeY:c,targetRelativeY:s,linkWidth:f,index:n,payload:Qr(Qr({},t),{},{source:p,target:d})},(0,C.L6)(a,!1)),x={onMouseEnter:r.handleMouseEnter.bind(r,O,"link"),onMouseLeave:r.handleMouseLeave.bind(r,O,"link"),onClick:r.handleClick.bind(r,O,"link")};return y.createElement(i.m,Xr({key:"link-".concat(t.source,"-").concat(t.target,"-").concat(t.value)},x),r.constructor.renderLinkItem(a,O))})))}},{key:"renderNodes",value:function(t){var e=this,r=this.props,n=r.node,o=r.margin,a=lr()(o,"top")||0,c=lr()(o,"left")||0;return y.createElement(i.m,{className:"recharts-sankey-nodes",key:"recharts-sankey-nodes"},t.map((function(t,r){var o=t.x,u=t.y,l=t.dx,s=t.dy,f=Qr(Qr({},(0,C.L6)(n,!1)),{},{x:o+c,y:u+a,width:l,height:s,index:r,payload:t}),p={onMouseEnter:e.handleMouseEnter.bind(e,f,"node"),onMouseLeave:e.handleMouseLeave.bind(e,f,"node"),onClick:e.handleClick.bind(e,f,"node")};return y.createElement(i.m,Xr({key:"node-".concat(t.x,"-").concat(t.y,"-").concat(t.value)},p),e.constructor.renderNodeItem(n,f))})))}},{key:"renderTooltip",value:function(){var t=this.props,e=t.children,r=t.width,n=t.height,i=t.nameKey,o=(0,C.sP)(e,c.u);if(!o)return null;var a,u=this.state,l=u.isTooltipActive,s=u.activeElement,f=u.activeElementType,p={x:0,y:0,width:r,height:n},d=s?(a=s,"node"===f?{x:a.x+a.width/2,y:a.y+a.height/2}:{x:(a.sourceX+a.targetX)/2,y:(a.sourceY+a.targetY)/2}):en,h=s?function(t,e,r){var n=t.payload;if("node"===e)return[{payload:t,name:(0,ot.F$)(n,r,""),value:(0,ot.F$)(n,"value")}];if(n.source&&n.target){var i=(0,ot.F$)(n.source,r,""),o=(0,ot.F$)(n.target,r,"");return[{payload:t,name:"".concat(i," - ").concat(o),value:(0,ot.F$)(n,"value")}]}return[]}(s,f,i):[];return y.cloneElement(o,{viewBox:p,active:l,coordinate:d,label:"",payload:h})}},{key:"render",value:function(){if(!(0,C.TT)(this))return null;var t=this.props,e=t.width,r=t.height,i=t.className,o=t.style,a=t.children,c=Kr(t,_r),u=this.state,l=u.links,s=u.nodes,f=(0,C.L6)(c,!1);return y.createElement("div",{className:(0,T.Z)("recharts-wrapper",i),style:Qr(Qr({},o),{},{position:"relative",cursor:"default",width:e,height:r}),role:"region"},y.createElement(n.T,Xr({},f,{width:e,height:r}),(0,C.hQ)(a),this.renderLinks(l,s),this.renderNodes(s)),this.renderTooltip())}}])&&Vr(r.prototype,o),a&&Vr(r,a),Object.defineProperty(r,"prototype",{writable:!1}),e}(y.PureComponent);Jr(yn,"displayName","Sankey"),Jr(yn,"defaultProps",{nameKey:"name",dataKey:"value",nodePadding:10,nodeWidth:10,linkCurvature:.5,iterations:32,margin:{top:5,right:5,bottom:5,left:5},sort:!0});var hn=(0,er.z)({chartName:"RadarChart",GraphicalChild:bt,axisComponents:[{axisType:"angleAxis",AxisComp:G.I},{axisType:"radiusAxis",AxisComp:V.S}],formatAxisMap:I.t9,defaultProps:{layout:"centric",startAngle:90,endAngle:-270,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),vn=(0,er.z)({chartName:"ScatterChart",GraphicalChild:Qe,defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],axisComponents:[{axisType:"xAxis",AxisComp:Je.K},{axisType:"yAxis",AxisComp:tr.B},{axisType:"zAxis",AxisComp:Le}],formatAxisMap:rr.t9}),mn=(0,er.z)({chartName:"AreaChart",GraphicalChild:Me,axisComponents:[{axisType:"xAxis",AxisComp:Je.K},{axisType:"yAxis",AxisComp:tr.B}],formatAxisMap:rr.t9}),bn=(0,er.z)({chartName:"RadialBarChart",GraphicalChild:Kt,legendContent:"children",defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"angleAxis",AxisComp:G.I},{axisType:"radiusAxis",AxisComp:V.S}],formatAxisMap:I.t9,defaultProps:{layout:"radial",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),gn=(0,er.z)({chartName:"ComposedChart",GraphicalChild:[de,Me,Ne.$,Qe],axisComponents:[{axisType:"xAxis",AxisComp:Je.K},{axisType:"yAxis",AxisComp:tr.B},{axisType:"zAxis",AxisComp:Le}],formatAxisMap:rr.t9}),On=r(8919);function xn(){return xn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},xn.apply(this,arguments)}function wn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||Sn(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function jn(t){return function(t){if(Array.isArray(t))return Pn(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||Sn(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Sn(t,e){if(t){if("string"===typeof t)return Pn(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Pn(t,e):void 0}}function Pn(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var An={fontWeight:"bold",paintOrder:"stroke fill",fontSize:".75rem",stroke:"#FFF",fill:"black",pointerEvents:"none"};function kn(t){if(!t.children||0===t.children.length)return 1;var e=t.children.map((function(t){return kn(t)}));return 1+Math.max.apply(Math,jn(e))}var En,Tn=function(t){var e=t.className,r=t.data,o=t.children,a=t.width,u=t.height,l=t.padding,s=void 0===l?2:l,p=t.dataKey,d=void 0===p?"value":p,h=t.ringPadding,v=void 0===h?2:h,m=t.innerRadius,b=void 0===m?50:m,g=t.fill,O=void 0===g?"#333":g,x=t.stroke,j=void 0===x?"#FFF":x,S=t.textOptions,P=void 0===S?An:S,A=t.outerRadius,k=void 0===A?Math.min(a,u)/2:A,E=t.cx,D=void 0===E?a/2:E,M=t.cy,N=void 0===M?u/2:M,L=t.startAngle,B=void 0===L?0:L,R=t.endAngle,z=void 0===R?360:R,F=t.onClick,_=t.onMouseEnter,W=t.onMouseLeave,Z=wn((0,y.useState)(!1),2),K=Z[0],X=Z[1],V=wn((0,y.useState)(null),2),G=V[0],Y=V[1],U=(0,On.Z)([0,r[d]],[0,z]),H=(k-b)/kn(r),$=[],q=new Map([]);function Q(t,e){_&&_(t,e),Y(t),X(!0)}function J(t,e){W&&W(t,e),Y(null),X(!1)}function tt(t){F&&F(t)}!function t(e,r){var n=r.radius,i=r.innerR,o=r.initialAngle,a=r.childColor,c=o;e&&e.forEach((function(e){var r,o,u=U(e[d]),l=c,p=null!==(r=null!==(o=null===e||void 0===e?void 0:e.fill)&&void 0!==o?o:a)&&void 0!==r?r:O,h=(0,I.op)(0,0,i+n/2,-(l+u-u/2)),m=h.x,b=h.y;c+=u,$.push(y.createElement("g",{"aria-label":e.name,tabIndex:0},y.createElement(w.L,{onClick:function(){return tt(e)},onMouseEnter:function(t){return Q(e,t)},onMouseLeave:function(t){return J(e,t)},fill:p,stroke:j,strokeWidth:s,startAngle:l,endAngle:l+u,innerRadius:i,outerRadius:i+n,cx:D,cy:N}),y.createElement(f.x,xn({},P,{alignmentBaseline:"middle",textAnchor:"middle",x:m+D,y:N-b}),e[d])));var g=(0,I.op)(D,N,i+n/2,l),x=g.x,S=g.y;return q.set(e.name,{x:x,y:S}),t(e.children,{radius:n,innerR:i+n+v,initialAngle:l,childColor:p})}))}(r.children,{radius:H,innerR:b,initialAngle:B});var et=(0,T.Z)("recharts-sunburst",e);return y.createElement("div",{className:(0,T.Z)("recharts-wrapper",e),style:{position:"relative",width:a,height:u},role:"region"},y.createElement(n.T,{width:a,height:u},o,y.createElement(i.m,{className:et},$)),function(){var t=(0,C.sP)([o],c.u);if(!t||!G)return null;var e={x:0,y:0,width:a,height:u};return y.cloneElement(t,{viewBox:e,coordinate:q.get(G.name),payload:[G],active:K})}())},In=r(47315),Cn=r.n(In),Dn=r(72139),Mn=r.n(Dn);function Nn(t){return Nn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nn(t)}function Ln(){return Ln=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ln.apply(this,arguments)}function Bn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Rn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Bn(Object(r),!0).forEach((function(e){zn(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Bn(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function zn(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Nn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Nn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Nn(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Fn(t,e){var r="".concat(e.x||t.x),n=parseInt(r,10),i="".concat(e.y||t.y),o=parseInt(i,10),a="".concat((null===e||void 0===e?void 0:e.height)||(null===t||void 0===t?void 0:t.height)),c=parseInt(a,10);return Rn(Rn(Rn({},e),(0,gt.Ob)(t)),{},{height:c,x:n,y:o})}function _n(t){return y.createElement(gt.bn,Ln({shapeType:"trapezoid",propTransformer:Fn},t))}function Wn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return Zn(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Zn(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Zn(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Kn(t){return Kn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kn(t)}function Xn(){return Xn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Xn.apply(this,arguments)}function Vn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Gn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Vn(Object(r),!0).forEach((function(e){Jn(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Vn(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Yn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ti(n.key),n)}}function Un(t,e,r){return e=$n(e),function(t,e){if(e&&("object"===Kn(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return qn(t)}(t,Hn()?Reflect.construct(e,r||[],$n(t).constructor):e.apply(t,r))}function Hn(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Hn=function(){return!!t})()}function $n(t){return $n=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},$n(t)}function qn(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Qn(t,e){return Qn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Qn(t,e)}function Jn(t,e,r){return(e=ti(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ti(t){var e=function(t,e){if("object"!=Kn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Kn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Kn(e)?e:String(e)}var ei=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return Jn(qn(t=Un(this,e,[].concat(n))),"state",{isAnimationFinished:!1}),Jn(qn(t),"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),v()(e)&&e()})),Jn(qn(t),"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),v()(e)&&e()})),t}var r,n,o;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Qn(t,e)}(e,t),r=e,o=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curTrapezoids:t.trapezoids,prevTrapezoids:e.curTrapezoids}:t.trapezoids!==e.curTrapezoids?{curTrapezoids:t.trapezoids}:null}}],(n=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"renderTrapezoidsStatically",value:function(t){var e=this,r=this.props,n=r.shape,o=r.activeShape;return t.map((function(t,r){var a=e.isActiveIndex(r)?o:n,c=Gn(Gn({},t),{},{isActive:e.isActiveIndex(r),stroke:t.stroke});return y.createElement(i.m,Xn({className:"recharts-funnel-trapezoid"},(0,Et.bw)(e.props,t,r),{key:"trapezoid-".concat(null===t||void 0===t?void 0:t.x,"-").concat(null===t||void 0===t?void 0:t.y,"-").concat(null===t||void 0===t?void 0:t.name,"-").concat(null===t||void 0===t?void 0:t.value),role:"img"}),y.createElement(_n,Xn({option:a},c)))}))}},{key:"renderTrapezoidsWithAnimation",value:function(){var t=this,e=this.props,r=e.trapezoids,n=e.isAnimationActive,o=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,l=this.state.prevTrapezoids;return y.createElement(U.ZP,{begin:o,duration:a,isActive:n,easing:c,from:{t:0},to:{t:1},key:"funnel-".concat(u),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(e){var n=e.t,o=r.map((function(t,e){var r=l&&l[e];if(r){var i=(0,nt.k4)(r.x,t.x),o=(0,nt.k4)(r.y,t.y),a=(0,nt.k4)(r.upperWidth,t.upperWidth),c=(0,nt.k4)(r.lowerWidth,t.lowerWidth),u=(0,nt.k4)(r.height,t.height);return Gn(Gn({},t),{},{x:i(n),y:o(n),upperWidth:a(n),lowerWidth:c(n),height:u(n)})}var s=(0,nt.k4)(t.x+t.upperWidth/2,t.x),f=(0,nt.k4)(t.y+t.height/2,t.y),p=(0,nt.k4)(0,t.upperWidth),d=(0,nt.k4)(0,t.lowerWidth),y=(0,nt.k4)(0,t.height);return Gn(Gn({},t),{},{x:s(n),y:f(n),upperWidth:p(n),lowerWidth:d(n),height:y(n)})}));return y.createElement(i.m,null,t.renderTrapezoidsStatically(o))}))}},{key:"renderTrapezoids",value:function(){var t=this.props,e=t.trapezoids,r=t.isAnimationActive,n=this.state.prevTrapezoids;return!(r&&e&&e.length)||n&&rt()(n,e)?this.renderTrapezoidsStatically(e):this.renderTrapezoidsWithAnimation()}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.trapezoids,n=t.className,o=t.isAnimationActive,a=this.state.isAnimationFinished;if(e||!r||!r.length)return null;var c=(0,T.Z)("recharts-trapezoids",n);return y.createElement(i.m,{className:c},this.renderTrapezoids(),(!o||a)&&d.e.renderCallByParent(this.props,r))}}])&&Yn(r.prototype,n),o&&Yn(r,o),Object.defineProperty(r,"prototype",{writable:!1}),e}(y.PureComponent);En=ei,Jn(ei,"displayName","Funnel"),Jn(ei,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",labelLine:!0,hide:!1,isAnimationActive:!it.x.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",lastShapeType:"triangle"}),Jn(ei,"getRealFunnelData",(function(t){var e=t.props,r=e.data,n=e.children,i=(0,C.L6)(t.props,!1),o=(0,C.NN)(n,s.b);return r&&r.length?r.map((function(t,e){return Gn(Gn(Gn({payload:t},i),t),o&&o[e]&&o[e].props)})):o&&o.length?o.map((function(t){return Gn(Gn({},i),t.props)})):[]})),Jn(ei,"getRealWidthHeight",(function(t,e){var r=t.props.width,n=e.width,i=e.height,o=e.left,a=e.right,c=e.top,u=e.bottom,l=i,s=n;return Cn()(r)?s=r:Mn()(r)&&(s=s*parseFloat(r)/100),{realWidth:s-o-a-50,realHeight:l-u-c,offsetX:(n-s)/2,offsetY:(i-l)/2}})),Jn(ei,"getComposedData",(function(t){var e=t.item,r=t.offset,n=En.getRealFunnelData(e),i=e.props,o=i.dataKey,a=i.nameKey,c=i.tooltipType,u=i.lastShapeType,l=i.reversed,s=r.left,f=r.top,p=En.getRealWidthHeight(e,r),d=p.realHeight,y=p.realWidth,h=p.offsetX,v=p.offsetY,m=Math.max.apply(null,n.map((function(t){return(0,ot.F$)(t,o,0)}))),b=n.length,g=d/b,O={x:r.left,y:r.top,width:r.width,height:r.height},x=n.map((function(t,e){var r,i=(0,ot.F$)(t,o,0),l=(0,ot.F$)(t,a,e),p=i;if(e!==b-1)(r=(0,ot.F$)(n[e+1],o,0))instanceof Array&&(r=Wn(r,1)[0]);else if(i instanceof Array&&2===i.length){var d=Wn(i,2);p=d[0],r=d[1]}else r="rectangle"===u?p:0;var x=(m-p)*y/(2*m)+f+25+h,w=g*e+s+v,j=p/m*y,S=r/m*y,P=[{name:l,value:p,payload:t,dataKey:o,type:c}],A={x:x+j/2,y:w+g/2};return Gn(Gn({x:x,y:w,width:Math.max(j,S),upperWidth:j,lowerWidth:S,height:g,name:l,val:p,tooltipPayload:P,tooltipPosition:A},cr()(t,"width")),{},{payload:t,parentViewBox:O,labelViewBox:{x:x+(j-S)/4,y:w,width:Math.abs(j-S)/2+Math.min(j,S),height:g}})}));return l&&(x=x.map((function(t,e){var r=t.y-e*g+(b-1-e)*g;return Gn(Gn({},t),{},{upperWidth:t.lowerWidth,lowerWidth:t.upperWidth,x:t.x-(t.lowerWidth-t.upperWidth)/2,y:t.y-e*g+(b-1-e)*g,tooltipPosition:Gn(Gn({},t.tooltipPosition),{},{y:r+g/2}),labelViewBox:Gn(Gn({},t.labelViewBox),{},{y:r})})}))),{trapezoids:x,data:n}}));var ri=(0,er.z)({chartName:"FunnelChart",GraphicalChild:ei,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",axisComponents:[],defaultProps:{layout:"centric"}}),ni=r(64397)},86246:function(t,e,r){r.d(e,{b:function(){return W}});var n,i=r(89526),o=r(68059),a=r(80089),c=r.n(a),u=r(47184),l=r.n(u),s=r(51391),f=r.n(s),p=r(39277),d=r.n(p),y=r(90512),h=r(61452),v=r(92147),m=r(49266),b=r(43774),g=r(34324),O=r(32214),x=r(9410),w=r(59509),j=r(80072),S=r(16171),P=r(36530),A=r(78706),k=r(33790),E=r(69531);function T(t){return T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},T(t)}function I(){return I=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},I.apply(this,arguments)}function C(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function D(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?C(Object(r),!0).forEach((function(e){F(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function M(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,_(n.key),n)}}function N(t,e,r){return e=B(e),function(t,e){if(e&&("object"===T(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return R(t)}(t,L()?Reflect.construct(e,r||[],B(t).constructor):e.apply(t,r))}function L(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(L=function(){return!!t})()}function B(t){return B=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},B(t)}function R(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function z(t,e){return z=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},z(t,e)}function F(t,e,r){return(e=_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _(t){var e=function(t,e){if("object"!=T(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=T(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==T(e)?e:String(e)}var W=function(t){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),F(R(r=N(this,e,[t])),"pieRef",null),F(R(r),"sectorRefs",[]),F(R(r),"id",(0,S.EL)("recharts-pie-")),F(R(r),"handleAnimationEnd",(function(){var t=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),d()(t)&&t()})),F(R(r),"handleAnimationStart",(function(){var t=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),d()(t)&&t()})),r.state={isAnimationFinished:!t.isAnimationActive,prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,sectorToFocus:0},r}var r,n,a;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&z(t,e)}(e,t),r=e,a=[{key:"getDerivedStateFromProps",value:function(t,e){return e.prevIsAnimationActive!==t.isAnimationActive?{prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:[],isAnimationFinished:!0}:t.isAnimationActive&&t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:e.curSectors,isAnimationFinished:!0}:t.sectors!==e.curSectors?{curSectors:t.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(t,e){return t>e?"start":t<e?"end":"middle"}},{key:"renderLabelLineItem",value:function(t,e){if(i.isValidElement(t))return i.cloneElement(t,e);if(d()(t))return t(e);var r=(0,y.Z)("recharts-pie-label-line","boolean"!==typeof t?t.className:"");return i.createElement(v.H,I({},e,{type:"linear",className:r}))}},{key:"renderLabelItem",value:function(t,e,r){if(i.isValidElement(t))return i.cloneElement(t,e);var n=r;if(d()(t)&&(n=t(e),i.isValidElement(n)))return n;var o=(0,y.Z)("recharts-pie-label-text","boolean"===typeof t||d()(t)?"":t.className);return i.createElement(m.x,I({},e,{alignmentBaseline:"middle",className:o}),n)}}],(n=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"hasActiveIndex",value:function(){var t=this.props.activeIndex;return Array.isArray(t)?0!==t.length:t||0===t}},{key:"renderLabels",value:function(t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.label,o=r.labelLine,a=r.dataKey,c=r.valueKey,u=(0,x.L6)(this.props,!1),l=(0,x.L6)(n,!1),s=(0,x.L6)(o,!1),p=n&&n.offsetRadius||20,d=t.map((function(t,r){var d=(t.startAngle+t.endAngle)/2,y=(0,j.op)(t.cx,t.cy,t.outerRadius+p,d),v=D(D(D(D({},u),t),{},{stroke:"none"},l),{},{index:r,textAnchor:e.getTextAnchor(y.x,t.cx)},y),m=D(D(D(D({},u),t),{},{fill:"none",stroke:t.fill},s),{},{index:r,points:[(0,j.op)(t.cx,t.cy,t.outerRadius,d),y],key:"line"}),b=a;return f()(a)&&f()(c)?b="value":f()(a)&&(b=c),i.createElement(h.m,{key:"label-".concat(t.startAngle,"-").concat(t.endAngle,"-").concat(t.midAngle,"-").concat(r)},o&&e.renderLabelLineItem(o,m),e.renderLabelItem(n,v,(0,P.F$)(t,b)))}));return i.createElement(h.m,{className:"recharts-pie-labels"},d)}},{key:"renderSectorsStatically",value:function(t){var e=this,r=this.props,n=r.activeShape,o=r.blendStroke,a=r.inactiveShape;return t.map((function(r,c){if(0===(null===r||void 0===r?void 0:r.startAngle)&&0===(null===r||void 0===r?void 0:r.endAngle)&&1!==t.length)return null;var u=e.isActiveIndex(c),l=a&&e.hasActiveIndex()?a:null,s=u?n:l,f=D(D({},r),{},{stroke:o?r.fill:r.stroke,tabIndex:-1});return i.createElement(h.m,I({ref:function(t){t&&!e.sectorRefs.includes(t)&&e.sectorRefs.push(t)},tabIndex:-1,className:"recharts-pie-sector"},(0,k.bw)(e.props,r,c),{key:"sector-".concat(null===r||void 0===r?void 0:r.startAngle,"-").concat(null===r||void 0===r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(c)}),i.createElement(E.bn,I({option:s,isActive:u,shapeType:"sector"},f)))}))}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,r=e.sectors,n=e.isAnimationActive,a=e.animationBegin,u=e.animationDuration,l=e.animationEasing,s=e.animationId,f=this.state,p=f.prevSectors,d=f.prevIsAnimationActive;return i.createElement(o.ZP,{begin:a,duration:u,isActive:n,easing:l,from:{t:0},to:{t:1},key:"pie-".concat(s,"-").concat(d),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(e){var n=e.t,o=[],a=(r&&r[0]).startAngle;return r.forEach((function(t,e){var r=p&&p[e],i=e>0?c()(t,"paddingAngle",0):0;if(r){var u=(0,S.k4)(r.endAngle-r.startAngle,t.endAngle-t.startAngle),l=D(D({},t),{},{startAngle:a+i,endAngle:a+u(n)+i});o.push(l),a=l.endAngle}else{var s=t.endAngle,f=t.startAngle,d=(0,S.k4)(0,s-f)(n),y=D(D({},t),{},{startAngle:a+i,endAngle:a+d+i});o.push(y),a=y.endAngle}})),i.createElement(h.m,null,t.renderSectorsStatically(o))}))}},{key:"attachKeyboardHandlers",value:function(t){var e=this;t.onkeydown=function(t){if(!t.altKey)switch(t.key){case"ArrowLeft":var r=++e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[r].focus(),e.setState({sectorToFocus:r});break;case"ArrowRight":var n=--e.state.sectorToFocus<0?e.sectorRefs.length-1:e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[n].focus(),e.setState({sectorToFocus:n});break;case"Escape":e.sectorRefs[e.state.sectorToFocus].blur(),e.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var t=this.props,e=t.sectors,r=t.isAnimationActive,n=this.state.prevSectors;return!(r&&e&&e.length)||n&&l()(n,e)?this.renderSectorsStatically(e):this.renderSectorsWithAnimation()}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var t=this,e=this.props,r=e.hide,n=e.sectors,o=e.className,a=e.label,c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.isAnimationActive,p=this.state.isAnimationFinished;if(r||!n||!n.length||!(0,S.hj)(c)||!(0,S.hj)(u)||!(0,S.hj)(l)||!(0,S.hj)(s))return null;var d=(0,y.Z)("recharts-pie",o);return i.createElement(h.m,{tabIndex:this.props.rootTabIndex,className:d,ref:function(e){t.pieRef=e}},this.renderSectors(),a&&this.renderLabels(n),b._.renderCallByParent(this.props,null,!1),(!f||p)&&g.e.renderCallByParent(this.props,n,!1))}}])&&M(r.prototype,n),a&&M(r,a),Object.defineProperty(r,"prototype",{writable:!1}),e}(i.PureComponent);n=W,F(W,"displayName","Pie"),F(W,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!w.x.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),F(W,"parseDeltaAngle",(function(t,e){return(0,S.uY)(e-t)*Math.min(Math.abs(e-t),360)})),F(W,"getRealPieData",(function(t){var e=t.props,r=e.data,n=e.children,i=(0,x.L6)(t.props,!1),o=(0,x.NN)(n,O.b);return r&&r.length?r.map((function(t,e){return D(D(D({payload:t},i),t),o&&o[e]&&o[e].props)})):o&&o.length?o.map((function(t){return D(D({},i),t.props)})):[]})),F(W,"parseCoordinateOfPie",(function(t,e){var r=e.top,n=e.left,i=e.width,o=e.height,a=(0,j.$4)(i,o);return{cx:n+(0,S.h1)(t.props.cx,i,i/2),cy:r+(0,S.h1)(t.props.cy,o,o/2),innerRadius:(0,S.h1)(t.props.innerRadius,a,0),outerRadius:(0,S.h1)(t.props.outerRadius,a,.8*a),maxRadius:t.props.maxRadius||Math.sqrt(i*i+o*o)/2}})),F(W,"getComposedData",(function(t){var e=t.item,r=t.offset,i=n.getRealPieData(e);if(!i||!i.length)return null;var o=e.props,a=o.cornerRadius,c=o.startAngle,u=o.endAngle,l=o.paddingAngle,s=o.dataKey,p=o.nameKey,d=o.valueKey,y=o.tooltipType,h=Math.abs(e.props.minAngle),v=n.parseCoordinateOfPie(e,r),m=n.parseDeltaAngle(c,u),b=Math.abs(m),g=s;f()(s)&&f()(d)?((0,A.Z)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g="value"):f()(s)&&((0,A.Z)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g=d);var O,x,w=i.filter((function(t){return 0!==(0,P.F$)(t,g,0)})).length,k=b-w*h-(b>=360?w:w-1)*l,E=i.reduce((function(t,e){var r=(0,P.F$)(e,g,0);return t+((0,S.hj)(r)?r:0)}),0);E>0&&(O=i.map((function(t,e){var r,n=(0,P.F$)(t,g,0),i=(0,P.F$)(t,p,e),o=((0,S.hj)(n)?n:0)/E,u=(r=e?x.endAngle+(0,S.uY)(m)*l*(0!==n?1:0):c)+(0,S.uY)(m)*((0!==n?h:0)+o*k),s=(r+u)/2,f=(v.innerRadius+v.outerRadius)/2,d=[{name:i,value:n,payload:t,dataKey:g,type:y}],b=(0,j.op)(v.cx,v.cy,f,s);return x=D(D(D({percent:o,cornerRadius:a,name:i,tooltipPayload:d,midAngle:s,middleRadius:f,tooltipPosition:b},t),v),{},{value:(0,P.F$)(t,g),startAngle:r,endAngle:u,payload:t,paddingAngle:(0,S.uY)(m)*l})})));return D(D({},v),{},{sectors:O,data:i})}))},10562:function(t,e,r){r.d(e,{I:function(){return k}});var n=r(89526),i=r(39277),o=r.n(i),a=r(90512),c=r(61452),u=r(96963),l=r(31234),s=r(49266),f=r(33790),p=r(9410),d=r(80072);function y(t){return y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(t)}function h(){return h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},h.apply(this,arguments)}function v(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function m(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?v(Object(r),!0).forEach((function(e){j(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function b(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,S(n.key),n)}}function g(t,e,r){return e=x(e),function(t,e){if(e&&("object"===y(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,O()?Reflect.construct(e,r||[],x(t).constructor):e.apply(t,r))}function O(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(O=function(){return!!t})()}function x(t){return x=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},x(t)}function w(t,e){return w=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},w(t,e)}function j(t,e,r){return(e=S(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function S(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:String(e)}var P=Math.PI/180,A=1e-5,k=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),g(this,e,arguments)}var r,i,y;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&w(t,e)}(e,t),r=e,y=[{key:"renderTickItem",value:function(t,e,r){return n.isValidElement(t)?n.cloneElement(t,e):o()(t)?t(e):n.createElement(s.x,h({},e,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],(i=[{key:"getTickLineCoord",value:function(t){var e=this.props,r=e.cx,n=e.cy,i=e.radius,o=e.orientation,a=e.tickSize||8,c=(0,d.op)(r,n,i,t.coordinate),u=(0,d.op)(r,n,i+("inner"===o?-1:1)*a,t.coordinate);return{x1:c.x,y1:c.y,x2:u.x,y2:u.y}}},{key:"getTickTextAnchor",value:function(t){var e=this.props.orientation,r=Math.cos(-t.coordinate*P);return r>A?"outer"===e?"start":"end":r<-A?"outer"===e?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,i=t.radius,o=t.axisLine,a=t.axisLineType,c=m(m({},(0,p.L6)(this.props,!1)),{},{fill:"none"},(0,p.L6)(o,!1));if("circle"===a)return n.createElement(u.o,h({className:"recharts-polar-angle-axis-line"},c,{cx:e,cy:r,r:i}));var s=this.props.ticks.map((function(t){return(0,d.op)(e,r,i,t.coordinate)}));return n.createElement(l.m,h({className:"recharts-polar-angle-axis-line"},c,{points:s}))}},{key:"renderTicks",value:function(){var t=this,r=this.props,i=r.ticks,o=r.tick,u=r.tickLine,l=r.tickFormatter,s=r.stroke,y=(0,p.L6)(this.props,!1),v=(0,p.L6)(o,!1),b=m(m({},y),{},{fill:"none"},(0,p.L6)(u,!1)),g=i.map((function(r,i){var p=t.getTickLineCoord(r),g=m(m(m({textAnchor:t.getTickTextAnchor(r)},y),{},{stroke:"none",fill:s},v),{},{index:i,payload:r,x:p.x2,y:p.y2});return n.createElement(c.m,h({className:(0,a.Z)("recharts-polar-angle-axis-tick",(0,d.$S)(o)),key:"tick-".concat(r.coordinate)},(0,f.bw)(t.props,r,i)),u&&n.createElement("line",h({className:"recharts-polar-angle-axis-tick-line"},b,p)),o&&e.renderTickItem(o,g,l?l(r.value,i):r.value))}));return n.createElement(c.m,{className:"recharts-polar-angle-axis-ticks"},g)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.radius,i=t.axisLine;return r<=0||!e||!e.length?null:n.createElement(c.m,{className:(0,a.Z)("recharts-polar-angle-axis",this.props.className)},i&&this.renderAxisLine(),this.renderTicks())}}])&&b(r.prototype,i),y&&b(r,y),Object.defineProperty(r,"prototype",{writable:!1}),e}(n.PureComponent);j(k,"displayName","PolarAngleAxis"),j(k,"axisType","angleAxis"),j(k,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0})},85322:function(t,e,r){r.d(e,{S:function(){return C}});var n=r(89526),i=r(58399),o=r.n(i),a=r(73),c=r.n(a),u=r(39277),l=r.n(u),s=r(90512),f=r(49266),p=r(43774),d=r(61452),y=r(80072),h=r(33790),v=r(9410),m=["cx","cy","angle","ticks","axisLine"],b=["ticks","tick","angle","tickFormatter","stroke"];function g(t){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},g(t)}function O(){return O=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},O.apply(this,arguments)}function x(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?x(Object(r),!0).forEach((function(e){T(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):x(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function j(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function S(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,I(n.key),n)}}function P(t,e,r){return e=k(e),function(t,e){if(e&&("object"===g(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,A()?Reflect.construct(e,r||[],k(t).constructor):e.apply(t,r))}function A(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(A=function(){return!!t})()}function k(t){return k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},k(t)}function E(t,e){return E=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},E(t,e)}function T(t,e,r){return(e=I(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function I(t){var e=function(t,e){if("object"!=g(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=g(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==g(e)?e:String(e)}var C=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),P(this,e,arguments)}var r,i,a;return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&E(t,e)}(e,t),r=e,a=[{key:"renderTickItem",value:function(t,e,r){return n.isValidElement(t)?n.cloneElement(t,e):l()(t)?t(e):n.createElement(f.x,O({},e,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],(i=[{key:"getTickValueCoord",value:function(t){var e=t.coordinate,r=this.props,n=r.angle,i=r.cx,o=r.cy;return(0,y.op)(i,o,e,n)}},{key:"getTickTextAnchor",value:function(){var t;switch(this.props.orientation){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t}},{key:"getViewBox",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,i=t.ticks,a=o()(i,(function(t){return t.coordinate||0}));return{cx:e,cy:r,startAngle:n,endAngle:n,innerRadius:c()(i,(function(t){return t.coordinate||0})).coordinate||0,outerRadius:a.coordinate||0}}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,i=t.angle,o=t.ticks,a=t.axisLine,c=j(t,m),u=o.reduce((function(t,e){return[Math.min(t[0],e.coordinate),Math.max(t[1],e.coordinate)]}),[1/0,-1/0]),l=(0,y.op)(e,r,u[0],i),s=(0,y.op)(e,r,u[1],i),f=w(w(w({},(0,v.L6)(c,!1)),{},{fill:"none"},(0,v.L6)(a,!1)),{},{x1:l.x,y1:l.y,x2:s.x,y2:s.y});return n.createElement("line",O({className:"recharts-polar-radius-axis-line"},f))}},{key:"renderTicks",value:function(){var t=this,r=this.props,i=r.ticks,o=r.tick,a=r.angle,c=r.tickFormatter,u=r.stroke,l=j(r,b),f=this.getTickTextAnchor(),p=(0,v.L6)(l,!1),m=(0,v.L6)(o,!1),g=i.map((function(r,i){var l=t.getTickValueCoord(r),v=w(w(w(w({textAnchor:f,transform:"rotate(".concat(90-a,", ").concat(l.x,", ").concat(l.y,")")},p),{},{stroke:"none",fill:u},m),{},{index:i},l),{},{payload:r});return n.createElement(d.m,O({className:(0,s.Z)("recharts-polar-radius-axis-tick",(0,y.$S)(o)),key:"tick-".concat(r.coordinate)},(0,h.bw)(t.props,r,i)),e.renderTickItem(o,v,c?c(r.value,i):r.value))}));return n.createElement(d.m,{className:"recharts-polar-radius-axis-ticks"},g)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.axisLine,i=t.tick;return e&&e.length?n.createElement(d.m,{className:(0,s.Z)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),i&&this.renderTicks(),p._.renderCallByParent(this.props,this.getViewBox())):null}}])&&S(r.prototype,i),a&&S(r,a),Object.defineProperty(r,"prototype",{writable:!1}),e}(n.PureComponent);T(C,"displayName","PolarRadiusAxis"),T(C,"axisType","radiusAxis"),T(C,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0})},74791:function(t,e,r){r.d(e,{X:function(){return y}});var n=r(89526),i=r(90512),o=r(16171),a=r(9410);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}var u=["x","y","top","left","width","height","className"];function l(){return l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},l.apply(this,arguments)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function f(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function p(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var d=function(t,e,r,n,i,o){return"M".concat(t,",").concat(i,"v").concat(n,"M").concat(o,",").concat(e,"h").concat(r)},y=function(t){var e=t.x,r=void 0===e?0:e,c=t.y,y=void 0===c?0:c,h=t.top,v=void 0===h?0:h,m=t.left,b=void 0===m?0:m,g=t.width,O=void 0===g?0:g,x=t.height,w=void 0===x?0:x,j=t.className,S=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach((function(e){f(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({x:r,y:y,top:v,left:b,width:O,height:w},p(t,u));return(0,o.hj)(r)&&(0,o.hj)(y)&&(0,o.hj)(O)&&(0,o.hj)(w)&&(0,o.hj)(v)&&(0,o.hj)(b)?n.createElement("path",l({},(0,a.L6)(S,!0),{className:(0,i.Z)("recharts-cross",j),d:d(r,y,O,w,v,b)})):null}},92147:function(t,e,r){r.d(e,{H:function(){return M}});var n=r(89526),i=r(5135),o=r(25908),a=r(60778),c=r(25846),u=r(79488),l=r(22081),s=r(19846),f=r(1971),p=r(15515),d=r(64706),y=r(82217),h=r(43483),v=r.n(h),m=r(39277),b=r.n(m),g=r(90512),O=r(33790),x=r(9410),w=r(16171);function j(t){return j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},j(t)}function S(){return S=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},S.apply(this,arguments)}function P(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function A(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?P(Object(r),!0).forEach((function(e){k(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function k(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=j(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=j(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==j(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var E={curveBasisClosed:i.Z,curveBasisOpen:o.Z,curveBasis:a.ZP,curveBumpX:c.sj,curveBumpY:c.BW,curveLinearClosed:u.Z,curveLinear:l.Z,curveMonotoneX:s.Z,curveMonotoneY:s.s,curveNatural:f.Z,curveStep:p.ZP,curveStepAfter:p.cD,curveStepBefore:p.RN},T=function(t){return t.x===+t.x&&t.y===+t.y},I=function(t){return t.x},C=function(t){return t.y},D=function(t){var e,r=t.type,n=void 0===r?"linear":r,i=t.points,o=void 0===i?[]:i,a=t.baseLine,c=t.layout,u=t.connectNulls,s=void 0!==u&&u,f=function(t,e){if(b()(t))return t;var r="curve".concat(v()(t));return"curveMonotone"!==r&&"curveBump"!==r||!e?E[r]||l.Z:E["".concat(r).concat("vertical"===e?"Y":"X")]}(n,c),p=s?o.filter((function(t){return T(t)})):o;if(Array.isArray(a)){var h=s?a.filter((function(t){return T(t)})):a,m=p.map((function(t,e){return A(A({},t),{},{base:h[e]})}));return(e="vertical"===c?(0,d.Z)().y(C).x1(I).x0((function(t){return t.base.x})):(0,d.Z)().x(I).y1(C).y0((function(t){return t.base.y}))).defined(T).curve(f),e(m)}return(e="vertical"===c&&(0,w.hj)(a)?(0,d.Z)().y(C).x1(I).x0(a):(0,w.hj)(a)?(0,d.Z)().x(I).y1(C).y0(a):(0,y.Z)().x(I).y(C)).defined(T).curve(f),e(p)},M=function(t){var e=t.className,r=t.points,i=t.path,o=t.pathRef;if((!r||!r.length)&&!i)return null;var a=r&&r.length?D(t):i;return n.createElement("path",S({},(0,x.L6)(t,!1),(0,O.Ym)(t),{className:(0,g.Z)("recharts-curve",e),d:a,ref:o}))}},96963:function(t,e,r){r.d(e,{o:function(){return u}});var n=r(89526),i=r(90512),o=r(33790),a=r(9410);function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(this,arguments)}var u=function(t){var e=t.cx,r=t.cy,u=t.r,l=t.className,s=(0,i.Z)("recharts-dot",l);return e===+e&&r===+r&&u===+u?n.createElement("circle",c({},(0,a.L6)(t,!1),(0,o.Ym)(t),{className:s,cx:e,cy:r,r:u})):null}},31234:function(t,e,r){r.d(e,{m:function(){return d}});var n=r(89526),i=r(90512),o=r(9410),a=["points","className","baseLinePoints","connectNulls"];function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(this,arguments)}function u(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function l(t){return function(t){if(Array.isArray(t))return s(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return s(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var f=function(t){return t&&t.x===+t.x&&t.y===+t.y},p=function(t,e){var r=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[[]];return t.forEach((function(t){f(t)?e[e.length-1].push(t):e[e.length-1].length>0&&e.push([])})),f(t[0])&&e[e.length-1].push(t[0]),e[e.length-1].length<=0&&(e=e.slice(0,-1)),e}(t);e&&(r=[r.reduce((function(t,e){return[].concat(l(t),l(e))}),[])]);var n=r.map((function(t){return t.reduce((function(t,e,r){return"".concat(t).concat(0===r?"M":"L").concat(e.x,",").concat(e.y)}),"")})).join("");return 1===r.length?"".concat(n,"Z"):n},d=function(t){var e=t.points,r=t.className,l=t.baseLinePoints,s=t.connectNulls,f=u(t,a);if(!e||!e.length)return null;var d=(0,i.Z)("recharts-polygon",r);if(l&&l.length){var y=f.stroke&&"none"!==f.stroke,h=function(t,e,r){var n=p(t,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(p(e.reverse(),r).slice(1))}(e,l,s);return n.createElement("g",{className:d},n.createElement("path",c({},(0,o.L6)(f,!0),{fill:"Z"===h.slice(-1)?f.fill:"none",stroke:"none",d:h})),y?n.createElement("path",c({},(0,o.L6)(f,!0),{fill:"none",d:p(e,s)})):null,y?n.createElement("path",c({},(0,o.L6)(f,!0),{fill:"none",d:p(l,s)})):null)}var v=p(e,s);return n.createElement("path",c({},(0,o.L6)(f,!0),{fill:"Z"===v.slice(-1)?f.fill:"none",className:d,d:v}))}},33951:function(t,e,r){r.d(e,{X:function(){return h},A:function(){return m}});var n=r(89526),i=r(90512),o=r(68059),a=r(9410);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(){return u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},u.apply(this,arguments)}function l(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return s(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function d(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var y=function(t,e,r,n,i){var o,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>a?a:i[f];o="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(o+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),o+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(o+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),o+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(o+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),o+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(o+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),o+="Z"}else if(a>0&&i===+i&&i>0){var p=Math.min(a,i);o="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else o="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return o},h=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,i=e.x,o=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(i,i+a),l=Math.max(i,i+a),s=Math.min(o,o+c),f=Math.max(o,o+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},v={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},m=function(t){var e=p(p({},v),t),r=(0,n.useRef)(),c=l((0,n.useState)(-1),2),s=c[0],f=c[1];(0,n.useEffect)((function(){if(r.current&&r.current.getTotalLength)try{var t=r.current.getTotalLength();t&&f(t)}catch(e){}}),[]);var d=e.x,h=e.y,m=e.width,b=e.height,g=e.radius,O=e.className,x=e.animationEasing,w=e.animationDuration,j=e.animationBegin,S=e.isAnimationActive,P=e.isUpdateAnimationActive;if(d!==+d||h!==+h||m!==+m||b!==+b||0===m||0===b)return null;var A=(0,i.Z)("recharts-rectangle",O);return P?n.createElement(o.ZP,{canBegin:s>0,from:{width:m,height:b,x:d,y:h},to:{width:m,height:b,x:d,y:h},duration:w,animationEasing:x,isActive:P},(function(t){var i=t.width,c=t.height,l=t.x,f=t.y;return n.createElement(o.ZP,{canBegin:s>0,from:"0px ".concat(-1===s?1:s,"px"),to:"".concat(s,"px 0px"),attributeName:"strokeDasharray",begin:j,duration:w,isActive:S,easing:x},n.createElement("path",u({},(0,a.L6)(e,!0),{className:A,d:y(l,f,i,c,g),ref:r})))})):n.createElement("path",u({},(0,a.L6)(e,!0),{className:A,d:y(d,h,m,b,g)}))}},61001:function(t,e,r){r.d(e,{L:function(){return v}});var n=r(89526),i=r(90512),o=r(9410),a=r(80072),c=r(16171);function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function l(){return l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},l.apply(this,arguments)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach((function(e){p(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function p(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var d=function(t){var e=t.cx,r=t.cy,n=t.radius,i=t.angle,o=t.sign,c=t.isExternal,u=t.cornerRadius,l=t.cornerIsExternal,s=u*(c?1:-1)+n,f=Math.asin(u/s)/a.Wk,p=l?i:i+o*f,d=l?i-o*f:i;return{center:(0,a.op)(e,r,s,p),circleTangency:(0,a.op)(e,r,n,p),lineTangency:(0,a.op)(e,r,s*Math.cos(f*a.Wk),d),theta:f}},y=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.startAngle,u=function(t,e){return(0,c.uY)(e-t)*Math.min(Math.abs(e-t),359.999)}(o,t.endAngle),l=o+u,s=(0,a.op)(e,r,i,o),f=(0,a.op)(e,r,i,l),p="M ".concat(s.x,",").concat(s.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(u)>180),",").concat(+(o>l),",\n    ").concat(f.x,",").concat(f.y,"\n  ");if(n>0){var d=(0,a.op)(e,r,n,o),y=(0,a.op)(e,r,n,l);p+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(u)>180),",").concat(+(o<=l),",\n            ").concat(d.x,",").concat(d.y," Z")}else p+="L ".concat(e,",").concat(r," Z");return p},h={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},v=function(t){var e=f(f({},h),t),r=e.cx,a=e.cy,u=e.innerRadius,s=e.outerRadius,p=e.cornerRadius,v=e.forceCornerRadius,m=e.cornerIsExternal,b=e.startAngle,g=e.endAngle,O=e.className;if(s<u||b===g)return null;var x,w=(0,i.Z)("recharts-sector",O),j=s-u,S=(0,c.h1)(p,j,0,!0);return x=S>0&&Math.abs(b-g)<360?function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.cornerRadius,a=t.forceCornerRadius,u=t.cornerIsExternal,l=t.startAngle,s=t.endAngle,f=(0,c.uY)(s-l),p=d({cx:e,cy:r,radius:i,angle:l,sign:f,cornerRadius:o,cornerIsExternal:u}),h=p.circleTangency,v=p.lineTangency,m=p.theta,b=d({cx:e,cy:r,radius:i,angle:s,sign:-f,cornerRadius:o,cornerIsExternal:u}),g=b.circleTangency,O=b.lineTangency,x=b.theta,w=u?Math.abs(l-s):Math.abs(l-s)-m-x;if(w<0)return a?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*o,",0\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*-o,",0\n      "):y({cx:e,cy:r,innerRadius:n,outerRadius:i,startAngle:l,endAngle:s});var j="M ".concat(v.x,",").concat(v.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(w>180),",").concat(+(f<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(O.x,",").concat(O.y,"\n  ");if(n>0){var S=d({cx:e,cy:r,radius:n,angle:l,sign:f,isExternal:!0,cornerRadius:o,cornerIsExternal:u}),P=S.circleTangency,A=S.lineTangency,k=S.theta,E=d({cx:e,cy:r,radius:n,angle:s,sign:-f,isExternal:!0,cornerRadius:o,cornerIsExternal:u}),T=E.circleTangency,I=E.lineTangency,C=E.theta,D=u?Math.abs(l-s):Math.abs(l-s)-k-C;if(D<0&&0===o)return"".concat(j,"L").concat(e,",").concat(r,"Z");j+="L".concat(I.x,",").concat(I.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(T.x,",").concat(T.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(D>180),",").concat(+(f>0),",").concat(P.x,",").concat(P.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(A.x,",").concat(A.y,"Z")}else j+="L".concat(e,",").concat(r,"Z");return j}({cx:r,cy:a,innerRadius:u,outerRadius:s,cornerRadius:Math.min(S,j/2),forceCornerRadius:v,cornerIsExternal:m,startAngle:b,endAngle:g}):y({cx:r,cy:a,innerRadius:u,outerRadius:s,startAngle:b,endAngle:g}),n.createElement("path",l({},(0,o.L6)(e,!0),{className:w,d:x,role:"img"}))}},71746:function(t,e,r){r.d(e,{v:function(){return P}});var n=r(89526),i=r(43483),o=r.n(i),a=r(83997),c=r(32346),u=r(12060),l=r(92138),s=r(38438),f=r(95029),p=r(7217),d=r(63446),y=r(90512),h=r(9410);function v(t){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(t)}var m=["type","size","sizeType"];function b(){return b=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},b.apply(this,arguments)}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function O(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach((function(e){x(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function x(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function w(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var j={symbolCircle:a.Z,symbolCross:c.Z,symbolDiamond:u.Z,symbolSquare:l.Z,symbolStar:s.Z,symbolTriangle:f.Z,symbolWye:p.Z},S=Math.PI/180,P=function(t){var e=t.type,r=void 0===e?"circle":e,i=t.size,c=void 0===i?64:i,u=t.sizeType,l=void 0===u?"area":u,s=O(O({},w(t,m)),{},{type:r,size:c,sizeType:l}),f=s.className,p=s.cx,v=s.cy,g=(0,h.L6)(s,!0);return p===+p&&v===+v&&c===+c?n.createElement("path",b({},g,{className:(0,y.Z)("recharts-symbols",f),transform:"translate(".concat(p,", ").concat(v,")"),d:function(){var t=function(t){var e="symbol".concat(o()(t));return j[e]||a.Z}(r),e=(0,d.ZP)().type(t).size(function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return.5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*S;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}}(c,l,r));return e()}()})):null};P.registerSymbol=function(t,e){j["symbol".concat(o()(t))]=e}},64397:function(t,e,r){r.d(e,{Z:function(){return v}});var n=r(89526),i=r(90512),o=r(68059),a=r(9410);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(){return u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},u.apply(this,arguments)}function l(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return s(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function d(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var y=function(t,e,r,n,i){var o,a=r-n;return o="M ".concat(t,",").concat(e),o+="L ".concat(t+r,",").concat(e),o+="L ".concat(t+r-a/2,",").concat(e+i),o+="L ".concat(t+r-a/2-n,",").concat(e+i),o+="L ".concat(t,",").concat(e," Z")},h={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},v=function(t){var e=p(p({},h),t),r=(0,n.useRef)(),c=l((0,n.useState)(-1),2),s=c[0],f=c[1];(0,n.useEffect)((function(){if(r.current&&r.current.getTotalLength)try{var t=r.current.getTotalLength();t&&f(t)}catch(e){}}),[]);var d=e.x,v=e.y,m=e.upperWidth,b=e.lowerWidth,g=e.height,O=e.className,x=e.animationEasing,w=e.animationDuration,j=e.animationBegin,S=e.isUpdateAnimationActive;if(d!==+d||v!==+v||m!==+m||b!==+b||g!==+g||0===m&&0===b||0===g)return null;var P=(0,i.Z)("recharts-trapezoid",O);return S?n.createElement(o.ZP,{canBegin:s>0,from:{upperWidth:0,lowerWidth:0,height:g,x:d,y:v},to:{upperWidth:m,lowerWidth:b,height:g,x:d,y:v},duration:w,animationEasing:x,isActive:S},(function(t){var i=t.upperWidth,c=t.lowerWidth,l=t.height,f=t.x,p=t.y;return n.createElement(o.ZP,{canBegin:s>0,from:"0px ".concat(-1===s?1:s,"px"),to:"".concat(s,"px 0px"),attributeName:"strokeDasharray",begin:j,duration:w,easing:x},n.createElement("path",u({},(0,a.L6)(e,!0),{className:P,d:y(f,p,i,c,l),ref:r})))})):n.createElement("g",null,n.createElement("path",u({},(0,a.L6)(e,!0),{className:P,d:y(d,v,m,b,g)})))}},69531:function(t,e,r){r.d(e,{Ob:function(){return P},bn:function(){return A},lT:function(){return k},V$:function(){return E},w7:function(){return T},a3:function(){return M}});var n=r(89526),i=r(39277),o=r.n(i),a=r(82678),c=r.n(a),u=r(23079),l=r.n(u),s=r(47184),f=r.n(s),p=r(33951),d=r(64397),y=r(61001),h=r(61452),v=r(71746),m=["option","shapeType","propTransformer","activeClassName","isActive"];function b(t){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},b(t)}function g(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function O(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?O(Object(r),!0).forEach((function(e){w(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function w(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=b(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==b(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function j(t,e){return x(x({},e),t)}function S(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return n.createElement(p.A,r);case"trapezoid":return n.createElement(d.Z,r);case"sector":return n.createElement(y.L,r);case"symbols":if(function(t){return"symbols"===t}(e))return n.createElement(v.v,r);break;default:return null}}function P(t){return(0,n.isValidElement)(t)?t.props:t}function A(t){var e,r=t.option,i=t.shapeType,a=t.propTransformer,u=void 0===a?j:a,s=t.activeClassName,f=void 0===s?"recharts-active-shape":s,p=t.isActive,d=g(t,m);if((0,n.isValidElement)(r))e=(0,n.cloneElement)(r,x(x({},d),P(r)));else if(o()(r))e=r(d);else if(c()(r)&&!l()(r)){var y=u(r,d);e=n.createElement(S,{shapeType:i,elementProps:y})}else{var v=d;e=n.createElement(S,{shapeType:i,elementProps:v})}return p?n.createElement(h.m,{className:f},e):e}function k(t,e){return null!=e&&"trapezoids"in t.props}function E(t,e){return null!=e&&"sectors"in t.props}function T(t,e){return null!=e&&"points"in t.props}function I(t,e){var r,n,i=t.x===(null===e||void 0===e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,o=t.y===(null===e||void 0===e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return i&&o}function C(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function D(t,e){var r=t.x===e.x,n=t.y===e.y,i=t.z===e.z;return r&&n&&i}function M(t){var e=t.activeTooltipItem,r=t.graphicalItem,n=t.itemData,i=function(t,e){var r;return k(t,e)?r="trapezoids":E(t,e)?r="sectors":T(t,e)&&(r="points"),r}(r,e),o=function(t,e){var r,n;return k(t,e)?null===(r=e.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:E(t,e)?null===(n=e.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:T(t,e)?e.payload:{}}(r,e),a=n.filter((function(t,n){var a=f()(o,t),c=r.props[i].filter((function(t){var n=function(t,e){var r;return k(t,e)?r=I:E(t,e)?r=C:T(t,e)&&(r=D),r}(r,e);return n(t,e)})),u=r.props[i].indexOf(c[c.length-1]);return a&&n===u}));return n.indexOf(a[a.length-1])}},87210:function(t,e,r){r.d(e,{t9:function(){return m},O1:function(){return b},_b:function(){return g},Ky:function(){return x},xE:function(){return w}});var n=r(40508),i=r.n(n),o=r(84168),a=r.n(o),c=r(36530),u=r(9410),l=r(16171),s=r(48218);function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,v(n.key),n)}}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function y(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach((function(e){h(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function h(t,e,r){return(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:String(e)}var m=function(t,e,r,n,i){var o=t.width,a=t.height,f=t.layout,p=t.children,d=Object.keys(e),v={left:r.left,leftMirror:r.left,right:o-r.right,rightMirror:o-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},m=!!(0,u.sP)(p,s.$);return d.reduce((function(o,a){var u,s,p,d,b,g=e[a],O=g.orientation,x=g.domain,w=g.padding,j=void 0===w?{}:w,S=g.mirror,P=g.reversed,A="".concat(O).concat(S?"Mirror":"");if("number"===g.type&&("gap"===g.padding||"no-gap"===g.padding)){var k=x[1]-x[0],E=1/0,T=g.categoricalDomain.sort();if(T.forEach((function(t,e){e>0&&(E=Math.min((t||0)-(T[e-1]||0),E))})),Number.isFinite(E)){var I=E/k,C="vertical"===g.layout?r.height:r.width;if("gap"===g.padding&&(u=I*C/2),"no-gap"===g.padding){var D=(0,l.h1)(t.barCategoryGap,I*C),M=I*C/2;u=M-D-(M-D)/C*D}}}s="xAxis"===n?[r.left+(j.left||0)+(u||0),r.left+r.width-(j.right||0)-(u||0)]:"yAxis"===n?"horizontal"===f?[r.top+r.height-(j.bottom||0),r.top+(j.top||0)]:[r.top+(j.top||0)+(u||0),r.top+r.height-(j.bottom||0)-(u||0)]:g.range,P&&(s=[s[1],s[0]]);var N=(0,c.Hq)(g,i,m),L=N.scale,B=N.realScaleType;L.domain(x).range(s),(0,c.zF)(L);var R=(0,c.g$)(L,y(y({},g),{},{realScaleType:B}));"xAxis"===n?(b="top"===O&&!S||"bottom"===O&&S,p=r.left,d=v[A]-b*g.height):"yAxis"===n&&(b="left"===O&&!S||"right"===O&&S,p=v[A]-b*g.width,d=r.top);var z=y(y(y({},g),R),{},{realScaleType:B,x:p,y:d,scale:L,width:"xAxis"===n?r.width:g.width,height:"yAxis"===n?r.height:g.height});return z.bandSize=(0,c.zT)(z,R),g.hide||"xAxis"!==n?g.hide||(v[A]+=(b?-1:1)*z.width):v[A]+=(b?-1:1)*z.height,y(y({},o),{},h({},a,z))}),{})},b=function(t,e){var r=t.x,n=t.y,i=e.x,o=e.y;return{x:Math.min(r,i),y:Math.min(n,o),width:Math.abs(i-r),height:Math.abs(o-n)}},g=function(t){var e=t.x1,r=t.y1,n=t.x2,i=t.y2;return b({x:e,y:r},{x:n,y:i})},O=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.scale=e}var e,r,n;return e=t,r=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+i;case"end":var o=this.bandwidth?this.bandwidth():0;return this.scale(t)+o}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],n=[{key:"create",value:function(e){return new t(e)}}],r&&p(e.prototype,r),n&&p(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();h(O,"EPS",1e-4);var x=function(t){var e=Object.keys(t).reduce((function(e,r){return y(y({},e),{},h({},r,O.create(t[r])))}),{});return y(y({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return i()(t,(function(t,r){return e[r].apply(t,{bandAware:n,position:o})}))},isInRange:function(t){return a()(t,(function(t,r){return e[r].isInRange(t)}))}})};var w=function(t){var e=t.width,r=t.height,n=function(t){return(t%180+180)%180}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),i=n*Math.PI/180,o=Math.atan(r/e),a=i>o&&i<Math.PI-o?r/Math.sin(i):e/Math.cos(i);return Math.abs(a)}},36530:function(t,e,r){r.d(e,{F$:function(){return V},gF:function(){return G},VO:function(){return Y},fk:function(){return U},pt:function(){return H},qz:function(){return $},By:function(){return q},ZI:function(){return J},s6:function(){return tt},NA:function(){return et},Rf:function(){return rt},uY:function(){return nt},DO:function(){return ot},Hq:function(){return at},zF:function(){return ut},Bu:function(){return lt},Vv:function(){return st},wh:function(){return dt},g$:function(){return yt},Hv:function(){return ht},Fy:function(){return vt},Yj:function(){return mt},O3:function(){return bt},EB:function(){return gt},LG:function(){return wt},zT:function(){return jt},ko:function(){return St},Qo:function(){return Pt}});var n=r(35406),i=r(8919),o=r(92768),a=r(30328),c=r(76019),u=r(43475),l=r(31843),s=r(36902),f=r(64016),p=r(14019),d=r.n(p),y=r(73398),h=r.n(y),v=r(51391),m=r.n(v),b=r(39277),g=r.n(b),O=r(72139),x=r.n(O),w=r(80089),j=r.n(w),S=r(22610),P=r.n(S),A=r(35813),k=r.n(A),E=r(43483),T=r.n(E),I=r(47184),C=r.n(I),D=r(65853),M=r.n(D),N=r(12360),L=r(65370),B=r(16171),R=r(9410),z=r(65436);function F(t){return F="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},F(t)}function _(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function W(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?_(Object(r),!0).forEach((function(e){Z(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Z(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=F(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=F(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==F(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function K(t){return function(t){if(Array.isArray(t))return X(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return X(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return X(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function X(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function V(t,e,r){return m()(t)||m()(e)?r:(0,B.P2)(e)?j()(t,e,r):g()(e)?e(t):r}function G(t,e,r,n){var i=P()(t,(function(t){return V(t,e)}));if("number"===r){var o=i.filter((function(t){return(0,B.hj)(t)||parseFloat(t)}));return o.length?[h()(o),d()(o)]:[1/0,-1/0]}return(n?i.filter((function(t){return!m()(t)})):i).map((function(t){return(0,B.P2)(t)||t instanceof Date?t:""}))}var Y=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o=-1,a=null!==(e=null===r||void 0===r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(i&&"angleAxis"===i.axisType&&Math.abs(Math.abs(i.range[1]-i.range[0])-360)<=1e-6)for(var c=i.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if((0,B.uY)(s-l)!==(0,B.uY)(f-s)){var d=[];if((0,B.uY)(f-s)===(0,B.uY)(c[1]-c[0])){p=f;var y=s+c[1]-c[0];d[0]=Math.min(y,(y+l)/2),d[1]=Math.max(y,(y+l)/2)}else{p=l;var h=f+c[1]-c[0];d[0]=Math.min(s,(h+s)/2),d[1]=Math.max(s,(h+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=d[0]&&t<=d[1]){o=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){o=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){o=r[g].index;break}return o},U=function(t){var e,r=t.type.displayName,n=t.props,i=n.stroke,o=n.fill;switch(r){case"Line":e=i;break;case"Area":case"Radar":e=i&&"none"!==i?i:o;break;default:e=o}return e},H=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,i=void 0===n?{}:n;if(!i)return{};for(var o={},a=Object.keys(i),c=0,u=a.length;c<u;c++)for(var l=i[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var d=l[s[f]],y=d.items,h=d.cateAxisId,v=y.filter((function(t){return(0,R.Gf)(t.type).indexOf("Bar")>=0}));if(v&&v.length){var b=v[0].props.barSize,g=v[0].props[h];o[g]||(o[g]=[]);var O=m()(b)?e:b;o[g].push({item:v[0],stackList:v.slice(1),barSize:m()(O)?void 0:(0,B.h1)(O,r,0)})}}return o},$=function(t){var e=t.barGap,r=t.barCategoryGap,n=t.bandSize,i=t.sizeList,o=void 0===i?[]:i,a=t.maxBarSize,c=o.length;if(c<1)return null;var u,l=(0,B.h1)(e,n,0,!0),s=[];if(o[0].barSize===+o[0].barSize){var f=!1,p=n/c,d=o.reduce((function(t,e){return t+e.barSize||0}),0);(d+=(c-1)*l)>=n&&(d-=(c-1)*l,l=0),d>=n&&p>0&&(f=!0,d=c*(p*=.9));var y={offset:((n-d)/2|0)-l,size:0};u=o.reduce((function(t,e){var r={item:e.item,position:{offset:y.offset+y.size+l,size:f?p:e.barSize}},n=[].concat(K(t),[r]);return y=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach((function(t){n.push({item:t,position:y})})),n}),s)}else{var h=(0,B.h1)(r,n,0,!0);n-2*h-(c-1)*l<=0&&(l=0);var v=(n-2*h-(c-1)*l)/c;v>1&&(v>>=0);var m=a===+a?Math.min(v,a):v;u=o.reduce((function(t,e,r){var n=[].concat(K(t),[{item:e.item,position:{offset:h+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach((function(t){n.push({item:t,position:n[n.length-1].position})})),n}),s)}return u},q=function(t,e,r,n){var i=r.children,o=r.width,a=r.margin,c=o-(a.left||0)-(a.right||0),u=(0,z.z)({children:i,legendWidth:c});if(u){var l=n||{},s=l.width,f=l.height,p=u.align,d=u.verticalAlign,y=u.layout;if(("vertical"===y||"horizontal"===y&&"middle"===d)&&"center"!==p&&(0,B.hj)(t[p]))return W(W({},t),{},Z({},p,t[p]+(s||0)));if(("horizontal"===y||"vertical"===y&&"center"===p)&&"middle"!==d&&(0,B.hj)(t[d]))return W(W({},t),{},Z({},d,t[d]+(f||0)))}return t},Q=function(t,e,r,n,i){var o=e.props.children,a=(0,R.NN)(o,L.W).filter((function(t){return function(t,e,r){return!!m()(e)||("horizontal"===t?"yAxis"===e:"vertical"===t||"x"===r?"xAxis"===e:"y"!==r||"yAxis"===e)}(n,i,t.props.direction)}));if(a&&a.length){var c=a.map((function(t){return t.props.dataKey}));return t.reduce((function(t,e){var n=V(e,r);if(m()(n))return t;var i=Array.isArray(n)?[h()(n),d()(n)]:[n,n],o=c.reduce((function(t,r){var n=V(e,r,0),o=i[0]-Math.abs(Array.isArray(n)?n[0]:n),a=i[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(o,t[0]),Math.max(a,t[1])]}),[1/0,-1/0]);return[Math.min(o[0],t[0]),Math.max(o[1],t[1])]}),[1/0,-1/0])}return null},J=function(t,e,r,n,i){var o=e.map((function(e){return Q(t,e,r,i,n)})).filter((function(t){return!m()(t)}));return o&&o.length?o.reduce((function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]}),[1/0,-1/0]):null},tt=function(t,e,r,n,i){var o=e.map((function(e){var o=e.props.dataKey;return"number"===r&&o&&Q(t,e,o,n)||G(t,o,r,i)}));if("number"===r)return o.reduce((function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]}),[1/0,-1/0]);var a={};return o.reduce((function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t}),[])},et=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},rt=function(t,e,r,n){if(n)return t.map((function(t){return t.coordinate}));var i,o,a=t.map((function(t){return t.coordinate===e&&(i=!0),t.coordinate===r&&(o=!0),t.coordinate}));return i||a.push(e),o||a.push(r),a},nt=function(t,e,r){if(!t)return null;var n=t.scale,i=t.duplicateDomain,o=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===o&&n.bandwidth?n.bandwidth()/c:0;return u="angleAxis"===t.axisType&&(null===a||void 0===a?void 0:a.length)>=2?2*(0,B.uY)(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks)?(t.ticks||t.niceTicks).map((function(t){var e=i?i.indexOf(t):t;return{coordinate:n(e)+u,value:t,offset:u}})).filter((function(t){return!k()(t.coordinate)})):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map((function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}})):n.ticks&&!r?n.ticks(t.tickCount).map((function(t){return{coordinate:n(t)+u,value:t,offset:u}})):n.domain().map((function(t,e){return{coordinate:n(t)+u,value:i?i[t]:t,index:e,offset:u}}))},it=new WeakMap,ot=function(t,e){if("function"!==typeof e)return t;it.has(t)||it.set(t,new WeakMap);var r=it.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},at=function(t,e,r){var a=t.scale,c=t.type,u=t.layout,l=t.axisType;if("auto"===a)return"radial"===u&&"radiusAxis"===l?{scale:n.Z(),realScaleType:"band"}:"radial"===u&&"angleAxis"===l?{scale:i.Z(),realScaleType:"linear"}:"category"===c&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:n.x(),realScaleType:"point"}:"category"===c?{scale:n.Z(),realScaleType:"band"}:{scale:i.Z(),realScaleType:"linear"};if(x()(a)){var s="scale".concat(T()(a));return{scale:(o[s]||n.x)(),realScaleType:o[s]?s:"point"}}return g()(a)?{scale:a}:{scale:n.x(),realScaleType:"point"}},ct=1e-4,ut=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),i=Math.min(n[0],n[1])-ct,o=Math.max(n[0],n[1])+ct,a=t(e[0]),c=t(e[r-1]);(a<i||a>o||c<i||c>o)&&t.domain([e[0],e[r-1]])}},lt=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},st=function(t,e){if(!e||2!==e.length||!(0,B.hj)(e[0])||!(0,B.hj)(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),i=[t[0],t[1]];return(!(0,B.hj)(t[0])||t[0]<r)&&(i[0]=r),(!(0,B.hj)(t[1])||t[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},ft={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0,a=0;a<e;++a){var c=k()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1]):(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1])}},expand:a.Z,none:c.Z,silhouette:u.Z,wiggle:l.Z,positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0;o<e;++o){var a=k()(t[o][r][1])?t[o][r][0]:t[o][r][1];a>=0?(t[o][r][0]=i,t[o][r][1]=i+a,i=t[o][r][1]):(t[o][r][0]=0,t[o][r][1]=0)}}},pt=function(t,e,r){var n=e.map((function(t){return t.props.dataKey})),i=ft[r];return(0,s.Z)().keys(n).value((function(t,e){return+V(t,e,0)})).order(f.Z).offset(i)(t)},dt=function(t,e,r,n,i,o){if(!t)return null;var a=(o?e.reverse():e).reduce((function(t,e){var i=e.props,o=i.stackId;if(i.hide)return t;var a=e.props[r],c=t[a]||{hasStack:!1,stackGroups:{}};if((0,B.P2)(o)){var u=c.stackGroups[o]||{numericAxisId:r,cateAxisId:n,items:[]};u.items.push(e),c.hasStack=!0,c.stackGroups[o]=u}else c.stackGroups[(0,B.EL)("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return W(W({},t),{},Z({},a,c))}),{});return Object.keys(a).reduce((function(e,o){var c=a[o];if(c.hasStack){c.stackGroups=Object.keys(c.stackGroups).reduce((function(e,o){var a=c.stackGroups[o];return W(W({},e),{},Z({},o,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:pt(t,a.items,i)}))}),{})}return W(W({},e),{},Z({},o,c))}),{})},yt=function(t,e){var r=e.realScaleType,n=e.type,i=e.tickCount,o=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(i&&"number"===n&&o&&("auto"===o[0]||"auto"===o[1])){var u=t.domain();if(!u.length)return null;var l=(0,N.Zj)(u,i,a);return t.domain([h()(l),d()(l)]),{niceTicks:l}}if(i&&"number"===n){var s=t.domain();return{niceTicks:(0,N.wZ)(s,i,a)}}return null};function ht(t){var e=t.axis,r=t.ticks,n=t.bandSize,i=t.entry,o=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!m()(i[e.dataKey])){var c=(0,B.Ap)(r,"value",i[e.dataKey]);if(c)return c.coordinate+n/2}return r[o]?r[o].coordinate+n/2:null}var u=V(i,m()(a)?e.dataKey:a);return m()(u)?null:e.scale(u)}var vt=function(t){var e=t.axis,r=t.ticks,n=t.offset,i=t.bandSize,o=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=V(o,e.dataKey,e.domain[a]);return m()(c)?null:e.scale(c)-i/2+n},mt=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},bt=function(t,e){var r=t.props.stackId;if((0,B.P2)(r)){var n=e[r];if(n){var i=n.items.indexOf(t);return i>=0?n.stackedData[i]:null}}return null},gt=function(t,e,r){return Object.keys(t).reduce((function(n,i){var o=t[i].stackedData.reduce((function(t,n){var i=n.slice(e,r+1).reduce((function(t,e){return[h()(e.concat([t[0]]).filter(B.hj)),d()(e.concat([t[1]]).filter(B.hj))]}),[1/0,-1/0]);return[Math.min(t[0],i[0]),Math.max(t[1],i[1])]}),[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]}),[1/0,-1/0]).map((function(t){return t===1/0||t===-1/0?0:t}))},Ot=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,xt=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,wt=function(t,e,r){if(g()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if((0,B.hj)(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(Ot.test(t[0])){var i=+Ot.exec(t[0])[1];n[0]=e[0]-i}else g()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if((0,B.hj)(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(xt.test(t[1])){var o=+xt.exec(t[1])[1];n[1]=e[1]+o}else g()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},jt=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var i=M()(e,(function(t){return t.coordinate})),o=1/0,a=1,c=i.length;a<c;a++){var u=i[a],l=i[a-1];o=Math.min((u.coordinate||0)-(l.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0},St=function(t,e,r){return t&&t.length?C()(t,j()(r,"type.defaultProps.domain"))?e:t:e},Pt=function(t,e){var r=t.props,n=r.dataKey,i=r.name,o=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return W(W({},(0,R.L6)(t,!1)),{},{dataKey:n,unit:o,formatter:a,name:i||n,color:U(t),value:V(e,n),type:c,payload:e,chartType:u,hide:l})}},99875:function(t,e,r){r.d(e,{xE:function(){return f},os:function(){return p}});var n=r(59509);function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach((function(e){c(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function c(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==i(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var u={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},s="recharts_measurement_span";var f=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0===t||null===t||n.x.isSsr)return{width:0,height:0};var r=function(t){var e=a({},t);return Object.keys(e).forEach((function(t){e[t]||delete e[t]})),e}(e),i=JSON.stringify({text:t,copyStyle:r});if(u.widthCache[i])return u.widthCache[i];try{var o=document.getElementById(s);o||((o=document.createElement("span")).setAttribute("id",s),o.setAttribute("aria-hidden","true"),document.body.appendChild(o));var c=a(a({},l),r);Object.assign(o.style,c),o.textContent="".concat(t);var f=o.getBoundingClientRect(),p={width:f.width,height:f.height};return u.widthCache[i]=p,++u.cacheCount>2e3&&(u.cacheCount=0,u.widthCache={}),p}catch(d){return{width:0,height:0}}},p=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}}},16171:function(t,e,r){r.d(e,{uY:function(){return f},hU:function(){return p},hj:function(){return d},P2:function(){return y},EL:function(){return v},h1:function(){return m},Kt:function(){return b},bv:function(){return g},k4:function(){return O},Ap:function(){return x},wr:function(){return w}});var n=r(72139),i=r.n(n),o=r(35813),a=r.n(o),c=r(80089),u=r.n(c),l=r(47315),s=r.n(l),f=function(t){return 0===t?0:t>0?1:-1},p=function(t){return i()(t)&&t.indexOf("%")===t.length-1},d=function(t){return s()(t)&&!a()(t)},y=function(t){return d(t)||i()(t)},h=0,v=function(t){var e=++h;return"".concat(t||"").concat(e)},m=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!d(t)&&!i()(t))return n;if(p(t)){var c=t.indexOf("%");r=e*parseFloat(t.slice(0,c))/100}else r=+t;return a()(r)&&(r=n),o&&r>e&&(r=e),r},b=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},g=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1},O=function(t,e){return d(t)&&d(e)?function(r){return t+r*(e-t)}:function(){return e}};function x(t,e,r){return t&&t.length?t.find((function(t){return t&&("function"===typeof e?e(t):u()(t,e))===r})):null}var w=function(t){if(!t||!t.length)return null;for(var e=t.length,r=0,n=0,i=0,o=0,a=1/0,c=-1/0,u=0,l=0,s=0;s<e;s++)r+=u=t[s].cx||0,n+=l=t[s].cy||0,i+=u*l,o+=u*u,a=Math.min(a,u),c=Math.max(c,u);var f=e*o!==r*r?(e*i-r*n)/(e*o-r*r):0;return{xmin:a,xmax:c,a:f,b:(n-f*r)/e}}},59509:function(t,e,r){r.d(e,{x:function(){return n}});var n={isSsr:!("undefined"!==typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(t){return n[t]},set:function(t,e){if("string"===typeof t)n[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach((function(e){n[e]=t[e]}))}}}},94694:function(t,e,r){r.d(e,{B:function(){return n}});var n=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e}},78706:function(t,e,r){r.d(e,{Z:function(){return n}});var n=function(t,e){for(var r=arguments.length,n=new Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]}},80072:function(t,e,r){r.d(e,{Wk:function(){return v},op:function(){return b},$4:function(){return g},t9:function(){return O},z3:function(){return j},$S:function(){return S}});var n=r(51391),i=r.n(n),o=r(89526),a=r(39277),c=r.n(a),u=r(16171),l=r(36530);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function d(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return h(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var v=Math.PI/180,m=function(t){return 180*t/Math.PI},b=function(t,e,r,n){return{x:t+Math.cos(-v*n)*r,y:e+Math.sin(-v*n)*r}},g=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},O=function(t,e,r,n,o){var a=t.width,c=t.height,s=t.startAngle,f=t.endAngle,h=(0,u.h1)(t.cx,a,a/2),v=(0,u.h1)(t.cy,c,c/2),m=g(a,c,r),b=(0,u.h1)(t.innerRadius,m,0),O=(0,u.h1)(t.outerRadius,m,.8*m);return Object.keys(e).reduce((function(t,r){var a,c=e[r],u=c.domain,m=c.reversed;if(i()(c.range))"angleAxis"===n?a=[s,f]:"radiusAxis"===n&&(a=[b,O]),m&&(a=[a[1],a[0]]);else{var g=y(a=c.range,2);s=g[0],f=g[1]}var x=(0,l.Hq)(c,o),w=x.realScaleType,j=x.scale;j.domain(u).range(a),(0,l.zF)(j);var S=(0,l.g$)(j,p(p({},c),{},{realScaleType:w})),P=p(p(p({},c),S),{},{range:a,radius:O,realScaleType:w,scale:j,cx:h,cy:v,innerRadius:b,outerRadius:O,startAngle:s,endAngle:f});return p(p({},t),{},d({},r,P))}),{})},x=function(t,e){var r=t.x,n=t.y,i=e.cx,o=e.cy,a=function(t,e){var r=t.x,n=t.y,i=e.x,o=e.y;return Math.sqrt(Math.pow(r-i,2)+Math.pow(n-o,2))}({x:r,y:n},{x:i,y:o});if(a<=0)return{radius:a};var c=(r-i)/a,u=Math.acos(c);return n>o&&(u=2*Math.PI-u),{radius:a,angle:m(u),angleInRadian:u}},w=function(t,e){var r=e.startAngle,n=e.endAngle,i=Math.floor(r/360),o=Math.floor(n/360);return t+360*Math.min(i,o)},j=function(t,e){var r=t.x,n=t.y,i=x({x:r,y:n},e),o=i.radius,a=i.angle,c=e.innerRadius,u=e.outerRadius;if(o<c||o>u)return!1;if(0===o)return!0;var l,s=function(t){var e=t.startAngle,r=t.endAngle,n=Math.floor(e/360),i=Math.floor(r/360),o=Math.min(n,i);return{startAngle:e-360*o,endAngle:r-360*o}}(e),f=s.startAngle,d=s.endAngle,y=a;if(f<=d){for(;y>d;)y-=360;for(;y<f;)y+=360;l=y>=f&&y<=d}else{for(;y>f;)y-=360;for(;y<d;)y+=360;l=y>=d&&y<=f}return l?p(p({},e),{},{radius:o,angle:w(y,e)}):null},S=function(t){return(0,o.isValidElement)(t)||c()(t)||"boolean"===typeof t?"":t.className}},9410:function(t,e,r){r.d(e,{Gf:function(){return j},NN:function(){return k},sP:function(){return E},TT:function(){return T},$k:function(){return D},hQ:function(){return M},L6:function(){return N},rL:function(){return L},eu:function(){return R},Bh:function(){return z},$R:function(){return F}});var n=r(80089),i=r.n(n),o=r(51391),a=r.n(o),c=r(72139),u=r.n(c),l=r(39277),s=r.n(l),f=r(23619),p=r.n(f),d=r(89526),y=r(338),h=r(16171),v=r(68201),m=r(33790),b=["children"],g=["children"];function O(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function x(t){return x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},x(t)}var w={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart"},j=function(t){return"string"===typeof t?t:t?t.displayName||t.name||"Component":""},S=null,P=null,A=function t(e){if(e===S&&Array.isArray(P))return P;var r=[];return d.Children.forEach(e,(function(e){a()(e)||((0,y.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))})),P=r,S=e,r};function k(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map((function(t){return j(t)})):[j(e)],A(t).forEach((function(t){var e=i()(t,"type.displayName")||i()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)})),r}function E(t,e){var r=k(t,e);return r&&r[0]}var T=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!(!(0,h.hj)(r)||r<=0||!(0,h.hj)(n)||n<=0)},I=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],C=function(t){return t&&t.type&&u()(t.type)&&I.indexOf(t.type)>=0},D=function(t){return t&&"object"===x(t)&&"cx"in t&&"cy"in t&&"r"in t},M=function(t){var e=[];return A(t).forEach((function(t){C(t)&&e.push(t)})),e},N=function(t,e,r){if(!t||"function"===typeof t||"boolean"===typeof t)return null;var n=t;if((0,d.isValidElement)(t)&&(n=t.props),!p()(n))return null;var i={};return Object.keys(n).forEach((function(t){var o;(function(t,e,r,n){var i,o=null!==(i=null===m.ry||void 0===m.ry?void 0:m.ry[n])&&void 0!==i?i:[];return!s()(t)&&(n&&o.includes(e)||m.Yh.includes(e))||r&&m.nv.includes(e)})(null===(o=n)||void 0===o?void 0:o[t],t,e,r)&&(i[t]=n[t])})),i},L=function t(e,r){if(e===r)return!0;var n=d.Children.count(e);if(n!==d.Children.count(r))return!1;if(0===n)return!0;if(1===n)return B(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var o=e[i],a=r[i];if(Array.isArray(o)||Array.isArray(a)){if(!t(o,a))return!1}else if(!B(o,a))return!1}return!0},B=function(t,e){if(a()(t)&&a()(e))return!0;if(!a()(t)&&!a()(e)){var r=t.props||{},n=r.children,i=O(r,b),o=e.props||{},c=o.children,u=O(o,g);return n&&c?(0,v.w)(i,u)&&L(n,c):!n&&!c&&(0,v.w)(i,u)}return!1},R=function(t,e){var r=[],n={};return A(t).forEach((function(t,i){if(C(t))r.push(t);else if(t){var o=j(t.type),a=e[o]||{},c=a.handler,u=a.once;if(c&&(!u||!n[o])){var l=c(t,o,i);r.push(l),n[o]=!0}}})),r},z=function(t){var e=t&&t.type;return e&&w[e]?w[e]:null},F=function(t,e){return A(e).indexOf(t)}},68201:function(t,e,r){function n(t,e){for(var r in t)if({}.hasOwnProperty.call(t,r)&&(!{}.hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if({}.hasOwnProperty.call(e,n)&&!{}.hasOwnProperty.call(t,n))return!1;return!0}r.d(e,{w:function(){return n}})},65436:function(t,e,r){r.d(e,{z:function(){return s}});var n=r(71015),i=r(36530),o=r(9410);function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function u(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach((function(e){l(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function l(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var s=function(t){var e,r=t.children,a=t.formattedGraphicalItems,c=t.legendWidth,l=t.legendContent,s=(0,o.sP)(r,n.D);return s?(e=s.props&&s.props.payload?s.props&&s.props.payload:"children"===l?(a||[]).reduce((function(t,e){var r=e.item,n=e.props,i=n.sectors||n.data||[];return t.concat(i.map((function(t){return{type:s.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}})))}),[]):(a||[]).map((function(t){var e=t.item,r=e.props,n=r.dataKey,o=r.name,a=r.legendType;return{inactive:r.hide,dataKey:n,type:s.props.iconType||a||"square",color:(0,i.fk)(e),value:o||n,payload:e.props}})),u(u(u({},s.props),n.D.getWithHeight(s,c)),{},{payload:e,item:s})):null}},56062:function(t,e,r){r.d(e,{z:function(){return c}});var n=r(80971),i=r.n(n),o=r(39277),a=r.n(o);function c(t,e,r){return!0===e?i()(t,r):a()(e)?i()(t,e):t}},33790:function(t,e,r){r.d(e,{Yh:function(){return c},ry:function(){return l},nv:function(){return s},Ym:function(){return f},bw:function(){return p}});var n=r(89526),i=r(23619),o=r.n(i);function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}var c=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],u=["points","pathLength"],l={svg:["viewBox","children"],polygon:u,polyline:u},s=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],f=function(t,e){if(!t||"function"===typeof t||"boolean"===typeof t)return null;var r=t;if((0,n.isValidElement)(t)&&(r=t.props),!o()(r))return null;var i={};return Object.keys(r).forEach((function(t){s.includes(t)&&(i[t]=e||function(e){return r[t](r,e)})})),i},p=function(t,e,r){if(!o()(t)||"object"!==a(t))return null;var n=null;return Object.keys(t).forEach((function(i){var o=t[i];s.includes(i)&&"function"===typeof o&&(n||(n={}),n[i]=function(t,e,r){return function(n){return t(e,r,n),null}}(o,e,r))})),n}}}]);
//# sourceMappingURL=recharts.07caf894bf74339a2efc373bce03e1c5.js.map