"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["rtcpeerconnection-shim"],{32134:function(e,t,r){var n=r(67985);function a(e,t,r,a,i){var s=n.writeRtpDescription(e.kind,t);if(s+=n.writeIceParameters(e.iceGatherer.getLocalParameters()),s+=n.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===r?"actpass":i||"active"),s+="a=mid:"+e.mid+"\r\n",e.rtpSender&&e.rtpReceiver?s+="a=sendrecv\r\n":e.rtpSender?s+="a=sendonly\r\n":e.rtpReceiver?s+="a=recvonly\r\n":s+="a=inactive\r\n",e.rtpSender){var o=e.rtpSender._initialTrackId||e.rtpSender.track.id;e.rtpSender._initialTrackId=o;var c="msid:"+(a?a.id:"-")+" "+o+"\r\n";s+="a="+c,s+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+c,e.sendEncodingParameters[0].rtx&&(s+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+c,s+="a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n")}return s+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+n.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(s+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+n.localCName+"\r\n"),s}function i(e,t){var r={codecs:[],headerExtensions:[],fecMechanisms:[]},n=function(e,t){e=parseInt(e,10);for(var r=0;r<t.length;r++)if(t[r].payloadType===e||t[r].preferredPayloadType===e)return t[r]},a=function(e,t,r,a){var i=n(e.parameters.apt,r),s=n(t.parameters.apt,a);return i&&s&&i.name.toLowerCase()===s.name.toLowerCase()};return e.codecs.forEach((function(n){for(var i=0;i<t.codecs.length;i++){var s=t.codecs[i];if(n.name.toLowerCase()===s.name.toLowerCase()&&n.clockRate===s.clockRate){if("rtx"===n.name.toLowerCase()&&n.parameters&&s.parameters.apt&&!a(n,s,e.codecs,t.codecs))continue;(s=JSON.parse(JSON.stringify(s))).numChannels=Math.min(n.numChannels,s.numChannels),r.codecs.push(s),s.rtcpFeedback=s.rtcpFeedback.filter((function(e){for(var t=0;t<n.rtcpFeedback.length;t++)if(n.rtcpFeedback[t].type===e.type&&n.rtcpFeedback[t].parameter===e.parameter)return!0;return!1}));break}}})),e.headerExtensions.forEach((function(e){for(var n=0;n<t.headerExtensions.length;n++){var a=t.headerExtensions[n];if(e.uri===a.uri){r.headerExtensions.push(a);break}}})),r}function s(e,t,r){return-1!=={offer:{setLocalDescription:["stable","have-local-offer"],setRemoteDescription:["stable","have-remote-offer"]},answer:{setLocalDescription:["have-remote-offer","have-local-pranswer"],setRemoteDescription:["have-local-offer","have-remote-pranswer"]}}[t][e].indexOf(r)}function o(e,t){var r=e.getRemoteCandidates().find((function(e){return t.foundation===e.foundation&&t.ip===e.ip&&t.port===e.port&&t.priority===e.priority&&t.protocol===e.protocol&&t.type===e.type}));return r||e.addRemoteCandidate(t),!r}function c(e,t){var r=new Error(t);return r.name=e,r}e.exports=function(e,t){function r(t,r){r.addTrack(t),r.dispatchEvent(new e.MediaStreamTrackEvent("addtrack",{track:t}))}function d(t,r,n,a){var i=new Event("track");i.track=r,i.receiver=n,i.transceiver={receiver:n},i.streams=a,e.setTimeout((function(){t._dispatchEvent("track",i)}))}var p=function(r){var a=this,i=document.createDocumentFragment();if(["addEventListener","removeEventListener","dispatchEvent"].forEach((function(e){a[e]=i[e].bind(i)})),this.canTrickleIceCandidates=null,this.needNegotiation=!1,this.localStreams=[],this.remoteStreams=[],this.localDescription=null,this.remoteDescription=null,this.signalingState="stable",this.iceConnectionState="new",this.iceGatheringState="new",r=JSON.parse(JSON.stringify(r||{})),this.usingBundle="max-bundle"===r.bundlePolicy,"negotiate"===r.rtcpMuxPolicy)throw c("NotSupportedError","rtcpMuxPolicy 'negotiate' is not supported");switch(r.rtcpMuxPolicy||(r.rtcpMuxPolicy="require"),r.iceTransportPolicy){case"all":case"relay":break;default:r.iceTransportPolicy="all"}switch(r.bundlePolicy){case"balanced":case"max-compat":case"max-bundle":break;default:r.bundlePolicy="balanced"}if(r.iceServers=function(e,t){var r=!1;return(e=JSON.parse(JSON.stringify(e))).filter((function(e){if(e&&(e.urls||e.url)){var n=e.urls||e.url;e.url&&!e.urls&&console.warn("RTCIceServer.url is deprecated! Use urls instead.");var a="string"===typeof n;return a&&(n=[n]),n=n.filter((function(e){return 0!==e.indexOf("turn:")||-1===e.indexOf("transport=udp")||-1!==e.indexOf("turn:[")||r?0===e.indexOf("stun:")&&t>=14393&&-1===e.indexOf("?transport=udp"):(r=!0,!0)})),delete e.url,e.urls=a?n[0]:n,!!n.length}}))}(r.iceServers||[],t),this._iceGatherers=[],r.iceCandidatePoolSize)for(var s=r.iceCandidatePoolSize;s>0;s--)this._iceGatherers.push(new e.RTCIceGatherer({iceServers:r.iceServers,gatherPolicy:r.iceTransportPolicy}));else r.iceCandidatePoolSize=0;this._config=r,this.transceivers=[],this._sdpSessionId=n.generateSessionId(),this._sdpSessionVersion=0,this._dtlsRole=void 0,this._isClosed=!1};p.prototype.onicecandidate=null,p.prototype.onaddstream=null,p.prototype.ontrack=null,p.prototype.onremovestream=null,p.prototype.onsignalingstatechange=null,p.prototype.oniceconnectionstatechange=null,p.prototype.onicegatheringstatechange=null,p.prototype.onnegotiationneeded=null,p.prototype.ondatachannel=null,p.prototype._dispatchEvent=function(e,t){this._isClosed||(this.dispatchEvent(t),"function"===typeof this["on"+e]&&this["on"+e](t))},p.prototype._emitGatheringStateChange=function(){var e=new Event("icegatheringstatechange");this._dispatchEvent("icegatheringstatechange",e)},p.prototype.getConfiguration=function(){return this._config},p.prototype.getLocalStreams=function(){return this.localStreams},p.prototype.getRemoteStreams=function(){return this.remoteStreams},p.prototype._createTransceiver=function(e){var t=this.transceivers.length>0,r={track:null,iceGatherer:null,iceTransport:null,dtlsTransport:null,localCapabilities:null,remoteCapabilities:null,rtpSender:null,rtpReceiver:null,kind:e,mid:null,sendEncodingParameters:null,recvEncodingParameters:null,stream:null,associatedRemoteMediaStreams:[],wantReceive:!0};if(this.usingBundle&&t)r.iceTransport=this.transceivers[0].iceTransport,r.dtlsTransport=this.transceivers[0].dtlsTransport;else{var n=this._createIceAndDtlsTransports();r.iceTransport=n.iceTransport,r.dtlsTransport=n.dtlsTransport}return this.transceivers.push(r),r},p.prototype.addTrack=function(t,r){if(this._isClosed)throw c("InvalidStateError","Attempted to call addTrack on a closed peerconnection.");var n;if(this.transceivers.find((function(e){return e.track===t})))throw c("InvalidAccessError","Track already exists.");for(var a=0;a<this.transceivers.length;a++)this.transceivers[a].track||this.transceivers[a].kind!==t.kind||(n=this.transceivers[a]);return n||(n=this._createTransceiver(t.kind)),this._maybeFireNegotiationNeeded(),-1===this.localStreams.indexOf(r)&&this.localStreams.push(r),n.track=t,n.stream=r,n.rtpSender=new e.RTCRtpSender(t,n.dtlsTransport),n.rtpSender},p.prototype.addStream=function(e){var r=this;if(t>=15025)e.getTracks().forEach((function(t){r.addTrack(t,e)}));else{var n=e.clone();e.getTracks().forEach((function(e,t){var r=n.getTracks()[t];e.addEventListener("enabled",(function(e){r.enabled=e.enabled}))})),n.getTracks().forEach((function(e){r.addTrack(e,n)}))}},p.prototype.removeTrack=function(t){if(this._isClosed)throw c("InvalidStateError","Attempted to call removeTrack on a closed peerconnection.");if(!(t instanceof e.RTCRtpSender))throw new TypeError("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.");var r=this.transceivers.find((function(e){return e.rtpSender===t}));if(!r)throw c("InvalidAccessError","Sender was not created by this connection.");var n=r.stream;r.rtpSender.stop(),r.rtpSender=null,r.track=null,r.stream=null,-1===this.transceivers.map((function(e){return e.stream})).indexOf(n)&&this.localStreams.indexOf(n)>-1&&this.localStreams.splice(this.localStreams.indexOf(n),1),this._maybeFireNegotiationNeeded()},p.prototype.removeStream=function(e){var t=this;e.getTracks().forEach((function(e){var r=t.getSenders().find((function(t){return t.track===e}));r&&t.removeTrack(r)}))},p.prototype.getSenders=function(){return this.transceivers.filter((function(e){return!!e.rtpSender})).map((function(e){return e.rtpSender}))},p.prototype.getReceivers=function(){return this.transceivers.filter((function(e){return!!e.rtpReceiver})).map((function(e){return e.rtpReceiver}))},p.prototype._createIceGatherer=function(t,r){var n=this;if(r&&t>0)return this.transceivers[0].iceGatherer;if(this._iceGatherers.length)return this._iceGatherers.shift();var a=new e.RTCIceGatherer({iceServers:this._config.iceServers,gatherPolicy:this._config.iceTransportPolicy});return Object.defineProperty(a,"state",{value:"new",writable:!0}),this.transceivers[t].bufferedCandidateEvents=[],this.transceivers[t].bufferCandidates=function(e){var r=!e.candidate||0===Object.keys(e.candidate).length;a.state=r?"completed":"gathering",null!==n.transceivers[t].bufferedCandidateEvents&&n.transceivers[t].bufferedCandidateEvents.push(e)},a.addEventListener("localcandidate",this.transceivers[t].bufferCandidates),a},p.prototype._gather=function(t,r){var a=this,i=this.transceivers[r].iceGatherer;if(!i.onlocalcandidate){var s=this.transceivers[r].bufferedCandidateEvents;this.transceivers[r].bufferedCandidateEvents=null,i.removeEventListener("localcandidate",this.transceivers[r].bufferCandidates),i.onlocalcandidate=function(e){if(!(a.usingBundle&&r>0)){var s=new Event("icecandidate");s.candidate={sdpMid:t,sdpMLineIndex:r};var o=e.candidate,c=!o||0===Object.keys(o).length;if(c)"new"!==i.state&&"gathering"!==i.state||(i.state="completed");else{"new"===i.state&&(i.state="gathering"),o.component=1;var d=n.writeCandidate(o);s.candidate=Object.assign(s.candidate,n.parseCandidate(d)),s.candidate.candidate=d}var p=n.getMediaSections(a.localDescription.sdp);p[s.candidate.sdpMLineIndex]+=c?"a=end-of-candidates\r\n":"a="+s.candidate.candidate+"\r\n",a.localDescription.sdp=n.getDescription(a.localDescription.sdp)+p.join("");var l=a.transceivers.every((function(e){return e.iceGatherer&&"completed"===e.iceGatherer.state}));"gathering"!==a.iceGatheringState&&(a.iceGatheringState="gathering",a._emitGatheringStateChange()),c||a._dispatchEvent("icecandidate",s),l&&(a._dispatchEvent("icecandidate",new Event("icecandidate")),a.iceGatheringState="complete",a._emitGatheringStateChange())}},e.setTimeout((function(){s.forEach((function(e){i.onlocalcandidate(e)}))}),0)}},p.prototype._createIceAndDtlsTransports=function(){var t=this,r=new e.RTCIceTransport(null);r.onicestatechange=function(){t._updateConnectionState()};var n=new e.RTCDtlsTransport(r);return n.ondtlsstatechange=function(){t._updateConnectionState()},n.onerror=function(){Object.defineProperty(n,"state",{value:"failed",writable:!0}),t._updateConnectionState()},{iceTransport:r,dtlsTransport:n}},p.prototype._disposeIceAndDtlsTransports=function(e){var t=this.transceivers[e].iceGatherer;t&&(delete t.onlocalcandidate,delete this.transceivers[e].iceGatherer);var r=this.transceivers[e].iceTransport;r&&(delete r.onicestatechange,delete this.transceivers[e].iceTransport);var n=this.transceivers[e].dtlsTransport;n&&(delete n.ondtlsstatechange,delete n.onerror,delete this.transceivers[e].dtlsTransport)},p.prototype._transceive=function(e,r,a){var s=i(e.localCapabilities,e.remoteCapabilities);r&&e.rtpSender&&(s.encodings=e.sendEncodingParameters,s.rtcp={cname:n.localCName,compound:e.rtcpParameters.compound},e.recvEncodingParameters.length&&(s.rtcp.ssrc=e.recvEncodingParameters[0].ssrc),e.rtpSender.send(s)),a&&e.rtpReceiver&&s.codecs.length>0&&("video"===e.kind&&e.recvEncodingParameters&&t<15019&&e.recvEncodingParameters.forEach((function(e){delete e.rtx})),e.recvEncodingParameters.length?s.encodings=e.recvEncodingParameters:s.encodings=[{}],s.rtcp={compound:e.rtcpParameters.compound},e.rtcpParameters.cname&&(s.rtcp.cname=e.rtcpParameters.cname),e.sendEncodingParameters.length&&(s.rtcp.ssrc=e.sendEncodingParameters[0].ssrc),e.rtpReceiver.receive(s))},p.prototype.setLocalDescription=function(e){var t,r,a=this;if(-1===["offer","answer"].indexOf(e.type))return Promise.reject(c("TypeError",'Unsupported type "'+e.type+'"'));if(!s("setLocalDescription",e.type,a.signalingState)||a._isClosed)return Promise.reject(c("InvalidStateError","Can not set local "+e.type+" in state "+a.signalingState));if("offer"===e.type)t=n.splitSections(e.sdp),r=t.shift(),t.forEach((function(e,t){var r=n.parseRtpParameters(e);a.transceivers[t].localCapabilities=r})),a.transceivers.forEach((function(e,t){a._gather(e.mid,t)}));else if("answer"===e.type){t=n.splitSections(a.remoteDescription.sdp),r=t.shift();var o=n.matchPrefix(r,"a=ice-lite").length>0;t.forEach((function(e,t){var s=a.transceivers[t],c=s.iceGatherer,d=s.iceTransport,p=s.dtlsTransport,l=s.localCapabilities,f=s.remoteCapabilities;if(!(n.isRejected(e)&&0===n.matchPrefix(e,"a=bundle-only").length)&&!s.isDatachannel){var u=n.getIceParameters(e,r),v=n.getDtlsParameters(e,r);o&&(v.role="server"),a.usingBundle&&0!==t||(a._gather(s.mid,t),"new"===d.state&&d.start(c,u,o?"controlling":"controlled"),"new"===p.state&&p.start(v));var h=i(l,f);a._transceive(s,h.codecs.length>0,!1)}}))}return a.localDescription={type:e.type,sdp:e.sdp},"offer"===e.type?a._updateSignalingState("have-local-offer"):a._updateSignalingState("stable"),Promise.resolve()},p.prototype.setRemoteDescription=function(a){var i=this;if(-1===["offer","answer"].indexOf(a.type))return Promise.reject(c("TypeError",'Unsupported type "'+a.type+'"'));if(!s("setRemoteDescription",a.type,i.signalingState)||i._isClosed)return Promise.reject(c("InvalidStateError","Can not set remote "+a.type+" in state "+i.signalingState));var p={};i.remoteStreams.forEach((function(e){p[e.id]=e}));var l=[],f=n.splitSections(a.sdp),u=f.shift(),v=n.matchPrefix(u,"a=ice-lite").length>0,h=n.matchPrefix(u,"a=group:BUNDLE ").length>0;i.usingBundle=h;var m=n.matchPrefix(u,"a=ice-options:")[0];return i.canTrickleIceCandidates=!!m&&m.substr(14).split(" ").indexOf("trickle")>=0,f.forEach((function(s,c){var d=n.splitLines(s),f=n.getKind(s),m=n.isRejected(s)&&0===n.matchPrefix(s,"a=bundle-only").length,g=d[0].substr(2).split(" ")[2],y=n.getDirection(s,u),S=n.parseMsid(s),E=n.getMid(s)||n.generateIdentifier();if("application"!==f||"DTLS/SCTP"!==g){var T,C,P,w,R,k,b,_,x,D,I,G=n.parseRtpParameters(s);m||(D=n.getIceParameters(s,u),(I=n.getDtlsParameters(s,u)).role="client"),b=n.parseRtpEncodingParameters(s);var O=n.parseRtcpParameters(s),L=n.matchPrefix(s,"a=end-of-candidates",u).length>0,M=n.matchPrefix(s,"a=candidate:").map((function(e){return n.parseCandidate(e)})).filter((function(e){return 1===e.component}));if(("offer"===a.type||"answer"===a.type)&&!m&&h&&c>0&&i.transceivers[c]&&(i._disposeIceAndDtlsTransports(c),i.transceivers[c].iceGatherer=i.transceivers[0].iceGatherer,i.transceivers[c].iceTransport=i.transceivers[0].iceTransport,i.transceivers[c].dtlsTransport=i.transceivers[0].dtlsTransport,i.transceivers[c].rtpSender&&i.transceivers[c].rtpSender.setTransport(i.transceivers[0].dtlsTransport),i.transceivers[c].rtpReceiver&&i.transceivers[c].rtpReceiver.setTransport(i.transceivers[0].dtlsTransport)),"offer"!==a.type||m)"answer"!==a.type||m||(C=(T=i.transceivers[c]).iceGatherer,P=T.iceTransport,w=T.dtlsTransport,R=T.rtpReceiver,k=T.sendEncodingParameters,_=T.localCapabilities,i.transceivers[c].recvEncodingParameters=b,i.transceivers[c].remoteCapabilities=G,i.transceivers[c].rtcpParameters=O,M.length&&"new"===P.state&&(!v&&!L||h&&0!==c?M.forEach((function(e){o(T.iceTransport,e)})):P.setRemoteCandidates(M)),h&&0!==c||("new"===P.state&&P.start(C,D,"controlling"),"new"===w.state&&w.start(I)),i._transceive(T,"sendrecv"===y||"recvonly"===y,"sendrecv"===y||"sendonly"===y),!R||"sendrecv"!==y&&"sendonly"!==y?delete T.rtpReceiver:(x=R.track,S?(p[S.stream]||(p[S.stream]=new e.MediaStream),r(x,p[S.stream]),l.push([x,R,p[S.stream]])):(p.default||(p.default=new e.MediaStream),r(x,p.default),l.push([x,R,p.default]))));else{(T=i.transceivers[c]||i._createTransceiver(f)).mid=E,T.iceGatherer||(T.iceGatherer=i._createIceGatherer(c,h)),M.length&&"new"===T.iceTransport.state&&(!L||h&&0!==c?M.forEach((function(e){o(T.iceTransport,e)})):T.iceTransport.setRemoteCandidates(M)),_=e.RTCRtpReceiver.getCapabilities(f),t<15019&&(_.codecs=_.codecs.filter((function(e){return"rtx"!==e.name}))),k=T.sendEncodingParameters||[{ssrc:1001*(2*c+2)}];var N,j=!1;if("sendrecv"===y||"sendonly"===y){if(j=!T.rtpReceiver,R=T.rtpReceiver||new e.RTCRtpReceiver(T.dtlsTransport,f),j)x=R.track,S&&"-"===S.stream||(S?(p[S.stream]||(p[S.stream]=new e.MediaStream,Object.defineProperty(p[S.stream],"id",{get:function(){return S.stream}})),Object.defineProperty(x,"id",{get:function(){return S.track}}),N=p[S.stream]):(p.default||(p.default=new e.MediaStream),N=p.default)),N&&(r(x,N),T.associatedRemoteMediaStreams.push(N)),l.push([x,R,N])}else T.rtpReceiver&&T.rtpReceiver.track&&(T.associatedRemoteMediaStreams.forEach((function(t){var r=t.getTracks().find((function(e){return e.id===T.rtpReceiver.track.id}));r&&function(t,r){r.removeTrack(t),r.dispatchEvent(new e.MediaStreamTrackEvent("removetrack",{track:t}))}(r,t)})),T.associatedRemoteMediaStreams=[]);T.localCapabilities=_,T.remoteCapabilities=G,T.rtpReceiver=R,T.rtcpParameters=O,T.sendEncodingParameters=k,T.recvEncodingParameters=b,i._transceive(i.transceivers[c],!1,j)}}else i.transceivers[c]={mid:E,isDatachannel:!0}})),void 0===i._dtlsRole&&(i._dtlsRole="offer"===a.type?"active":"passive"),i.remoteDescription={type:a.type,sdp:a.sdp},"offer"===a.type?i._updateSignalingState("have-remote-offer"):i._updateSignalingState("stable"),Object.keys(p).forEach((function(t){var r=p[t];if(r.getTracks().length){if(-1===i.remoteStreams.indexOf(r)){i.remoteStreams.push(r);var n=new Event("addstream");n.stream=r,e.setTimeout((function(){i._dispatchEvent("addstream",n)}))}l.forEach((function(e){var t=e[0],n=e[1];r.id===e[2].id&&d(i,t,n,[r])}))}})),l.forEach((function(e){e[2]||d(i,e[0],e[1],[])})),e.setTimeout((function(){i&&i.transceivers&&i.transceivers.forEach((function(e){e.iceTransport&&"new"===e.iceTransport.state&&e.iceTransport.getRemoteCandidates().length>0&&(console.warn("Timeout for addRemoteCandidate. Consider sending an end-of-candidates notification"),e.iceTransport.addRemoteCandidate({}))}))}),4e3),Promise.resolve()},p.prototype.close=function(){this.transceivers.forEach((function(e){e.iceTransport&&e.iceTransport.stop(),e.dtlsTransport&&e.dtlsTransport.stop(),e.rtpSender&&e.rtpSender.stop(),e.rtpReceiver&&e.rtpReceiver.stop()})),this._isClosed=!0,this._updateSignalingState("closed")},p.prototype._updateSignalingState=function(e){this.signalingState=e;var t=new Event("signalingstatechange");this._dispatchEvent("signalingstatechange",t)},p.prototype._maybeFireNegotiationNeeded=function(){var t=this;"stable"===this.signalingState&&!0!==this.needNegotiation&&(this.needNegotiation=!0,e.setTimeout((function(){if(t.needNegotiation){t.needNegotiation=!1;var e=new Event("negotiationneeded");t._dispatchEvent("negotiationneeded",e)}}),0))},p.prototype._updateConnectionState=function(){var e,t={new:0,closed:0,connecting:0,checking:0,connected:0,completed:0,disconnected:0,failed:0};if(this.transceivers.forEach((function(e){t[e.iceTransport.state]++,t[e.dtlsTransport.state]++})),t.connected+=t.completed,e="new",t.failed>0?e="failed":t.connecting>0||t.checking>0?e="connecting":t.disconnected>0?e="disconnected":t.new>0?e="new":(t.connected>0||t.completed>0)&&(e="connected"),e!==this.iceConnectionState){this.iceConnectionState=e;var r=new Event("iceconnectionstatechange");this._dispatchEvent("iceconnectionstatechange",r)}},p.prototype.createOffer=function(){var r=this;if(r._isClosed)return Promise.reject(c("InvalidStateError","Can not call createOffer after close"));var i=r.transceivers.filter((function(e){return"audio"===e.kind})).length,s=r.transceivers.filter((function(e){return"video"===e.kind})).length,o=arguments[0];if(o){if(o.mandatory||o.optional)throw new TypeError("Legacy mandatory/optional constraints not supported.");void 0!==o.offerToReceiveAudio&&(i=!0===o.offerToReceiveAudio?1:!1===o.offerToReceiveAudio?0:o.offerToReceiveAudio),void 0!==o.offerToReceiveVideo&&(s=!0===o.offerToReceiveVideo?1:!1===o.offerToReceiveVideo?0:o.offerToReceiveVideo)}for(r.transceivers.forEach((function(e){"audio"===e.kind?--i<0&&(e.wantReceive=!1):"video"===e.kind&&--s<0&&(e.wantReceive=!1)}));i>0||s>0;)i>0&&(r._createTransceiver("audio"),i--),s>0&&(r._createTransceiver("video"),s--);var d=n.writeSessionBoilerplate(r._sdpSessionId,r._sdpSessionVersion++);r.transceivers.forEach((function(a,i){var s=a.track,o=a.kind,c=a.mid||n.generateIdentifier();a.mid=c,a.iceGatherer||(a.iceGatherer=r._createIceGatherer(i,r.usingBundle));var d=e.RTCRtpSender.getCapabilities(o);t<15019&&(d.codecs=d.codecs.filter((function(e){return"rtx"!==e.name}))),d.codecs.forEach((function(e){"H264"===e.name&&void 0===e.parameters["level-asymmetry-allowed"]&&(e.parameters["level-asymmetry-allowed"]="1"),a.remoteCapabilities&&a.remoteCapabilities.codecs&&a.remoteCapabilities.codecs.forEach((function(t){e.name.toLowerCase()===t.name.toLowerCase()&&e.clockRate===t.clockRate&&(e.preferredPayloadType=t.payloadType)}))})),d.headerExtensions.forEach((function(e){(a.remoteCapabilities&&a.remoteCapabilities.headerExtensions||[]).forEach((function(t){e.uri===t.uri&&(e.id=t.id)}))}));var p=a.sendEncodingParameters||[{ssrc:1001*(2*i+1)}];s&&t>=15019&&"video"===o&&!p[0].rtx&&(p[0].rtx={ssrc:p[0].ssrc+1}),a.wantReceive&&(a.rtpReceiver=new e.RTCRtpReceiver(a.dtlsTransport,o)),a.localCapabilities=d,a.sendEncodingParameters=p})),"max-compat"!==r._config.bundlePolicy&&(d+="a=group:BUNDLE "+r.transceivers.map((function(e){return e.mid})).join(" ")+"\r\n"),d+="a=ice-options:trickle\r\n",r.transceivers.forEach((function(e,t){d+=a(e,e.localCapabilities,"offer",e.stream,r._dtlsRole),d+="a=rtcp-rsize\r\n",!e.iceGatherer||"new"===r.iceGatheringState||0!==t&&r.usingBundle||(e.iceGatherer.getLocalCandidates().forEach((function(e){e.component=1,d+="a="+n.writeCandidate(e)+"\r\n"})),"completed"===e.iceGatherer.state&&(d+="a=end-of-candidates\r\n"))}));var p=new e.RTCSessionDescription({type:"offer",sdp:d});return Promise.resolve(p)},p.prototype.createAnswer=function(){var r=this;if(r._isClosed)return Promise.reject(c("InvalidStateError","Can not call createAnswer after close"));var s=n.writeSessionBoilerplate(r._sdpSessionId,r._sdpSessionVersion++);r.usingBundle&&(s+="a=group:BUNDLE "+r.transceivers.map((function(e){return e.mid})).join(" ")+"\r\n");var o=n.getMediaSections(r.remoteDescription.sdp).length;r.transceivers.forEach((function(e,n){if(!(n+1>o))if(e.isDatachannel)s+="m=application 0 DTLS/SCTP 5000\r\nc=IN IP4 0.0.0.0\r\na=mid:"+e.mid+"\r\n";else{var c;if(e.stream)"audio"===e.kind?c=e.stream.getAudioTracks()[0]:"video"===e.kind&&(c=e.stream.getVideoTracks()[0]),c&&t>=15019&&"video"===e.kind&&!e.sendEncodingParameters[0].rtx&&(e.sendEncodingParameters[0].rtx={ssrc:e.sendEncodingParameters[0].ssrc+1});var d=i(e.localCapabilities,e.remoteCapabilities);!d.codecs.filter((function(e){return"rtx"===e.name.toLowerCase()})).length&&e.sendEncodingParameters[0].rtx&&delete e.sendEncodingParameters[0].rtx,s+=a(e,d,"answer",e.stream,r._dtlsRole),e.rtcpParameters&&e.rtcpParameters.reducedSize&&(s+="a=rtcp-rsize\r\n")}}));var d=new e.RTCSessionDescription({type:"answer",sdp:s});return Promise.resolve(d)},p.prototype.addIceCandidate=function(e){var t,r=this;return e&&void 0===e.sdpMLineIndex&&!e.sdpMid?Promise.reject(new TypeError("sdpMLineIndex or sdpMid required")):new Promise((function(a,i){if(!r.remoteDescription)return i(c("InvalidStateError","Can not add ICE candidate without a remote description"));if(e&&""!==e.candidate){var s=e.sdpMLineIndex;if(e.sdpMid)for(var d=0;d<r.transceivers.length;d++)if(r.transceivers[d].mid===e.sdpMid){s=d;break}var p=r.transceivers[s];if(!p)return i(c("OperationError","Can not add ICE candidate"));if(p.isDatachannel)return a();var l=Object.keys(e.candidate).length>0?n.parseCandidate(e.candidate):{};if("tcp"===l.protocol&&(0===l.port||9===l.port))return a();if(l.component&&1!==l.component)return a();if((0===s||s>0&&p.iceTransport!==r.transceivers[0].iceTransport)&&!o(p.iceTransport,l))return i(c("OperationError","Can not add ICE candidate"));var f=e.candidate.trim();0===f.indexOf("a=")&&(f=f.substr(2)),(t=n.getMediaSections(r.remoteDescription.sdp))[s]+="a="+(l.type?f:"end-of-candidates")+"\r\n",r.remoteDescription.sdp=t.join("")}else for(var u=0;u<r.transceivers.length&&(r.transceivers[u].isDatachannel||(r.transceivers[u].iceTransport.addRemoteCandidate({}),(t=n.getMediaSections(r.remoteDescription.sdp))[u]+="a=end-of-candidates\r\n",r.remoteDescription.sdp=n.getDescription(r.remoteDescription.sdp)+t.join(""),!r.usingBundle));u++);a()}))},p.prototype.getStats=function(){var e=[];this.transceivers.forEach((function(t){["rtpSender","rtpReceiver","iceGatherer","iceTransport","dtlsTransport"].forEach((function(r){t[r]&&e.push(t[r].getStats())}))}));return new Promise((function(t){var r=new Map;Promise.all(e).then((function(e){e.forEach((function(e){Object.keys(e).forEach((function(t){var n;e[t].type={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[(n=e[t]).type]||n.type,r.set(t,e[t])}))})),t(r)}))}))};var l=["createOffer","createAnswer"];return l.forEach((function(e){var t=p.prototype[e];p.prototype[e]=function(){var e=arguments;return"function"===typeof e[0]||"function"===typeof e[1]?t.apply(this,[arguments[2]]).then((function(t){"function"===typeof e[0]&&e[0].apply(null,[t])}),(function(t){"function"===typeof e[1]&&e[1].apply(null,[t])})):t.apply(this,arguments)}})),(l=["setLocalDescription","setRemoteDescription","addIceCandidate"]).forEach((function(e){var t=p.prototype[e];p.prototype[e]=function(){var e=arguments;return"function"===typeof e[1]||"function"===typeof e[2]?t.apply(this,arguments).then((function(){"function"===typeof e[1]&&e[1].apply(null)}),(function(t){"function"===typeof e[2]&&e[2].apply(null,[t])})):t.apply(this,arguments)}})),["getStats"].forEach((function(e){var t=p.prototype[e];p.prototype[e]=function(){var e=arguments;return"function"===typeof e[1]?t.apply(this,arguments).then((function(){"function"===typeof e[1]&&e[1].apply(null)})):t.apply(this,arguments)}})),p}}}]);
//# sourceMappingURL=rtcpeerconnection-shim.afacee4fbf38f71f3ffd0a9993f73214.js.map