"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["react-smooth"],{68059:function(t,e,n){n.d(e,{ZP:function(){return At}});var r=n(89526),o=n(2652),i=n.n(o),u=n(32690);function a(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=-1;requestAnimationFrame((function r(o){n<0&&(n=o),o-n>e?(t(o),n=-1):function(t){"undefined"!==typeof requestAnimationFrame&&requestAnimationFrame(t)}(r)}))}function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function f(t){return function(t){if(Array.isArray(t))return t}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return l(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(t,e)}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function s(){var t=function(){return null},e=!1,n=function n(r){if(!e){if(Array.isArray(r)){if(!r.length)return;var o=f(r),i=o[0],u=o.slice(1);return"number"===typeof i?void a(n.bind(null,u),i):(n(i),void a(n.bind(null,u)))}"object"===c(r)&&t(r),"function"===typeof r&&r()}};return{stop:function(){e=!0},start:function(t){e=!1,n(t)},subscribe:function(e){return t=e,function(){t=function(){return null}}}}}function p(t){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}function y(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function b(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?y(Object(n),!0).forEach((function(e){m(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function m(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==p(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==p(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===p(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var v=function(t){return t},d=function(t,e){return Object.keys(e).reduce((function(n,r){return b(b({},n),{},m({},r,t(r,e[r])))}),{})},h=function(t,e,n){return t.map((function(t){return"".concat((r=t,r.replace(/([A-Z])/g,(function(t){return"-".concat(t.toLowerCase())})))," ").concat(e,"ms ").concat(n);var r})).join(",")};function O(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,u,a=[],c=!0,f=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(a.push(r.value),a.length!==e);c=!0);}catch(t){f=!0,o=t}finally{try{if(!c&&null!=n.return&&(u=n.return(),Object(u)!==u))return}finally{if(f)throw o}}return a}}(t,e)||j(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(t){return function(t){if(Array.isArray(t))return S(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||j(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(t,e){if(t){if("string"===typeof t)return S(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?S(t,e):void 0}}function S(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var A=1e-4,w=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},P=function(t,e){return t.map((function(t,n){return t*Math.pow(e,n)})).reduce((function(t,e){return t+e}))},E=function(t,e){return function(n){var r=w(t,e);return P(r,n)}},k=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=e[0],o=e[1],i=e[2],u=e[3];if(1===e.length)switch(e[0]){case"linear":r=0,o=0,i=1,u=1;break;case"ease":r=.25,o=.1,i=.25,u=1;break;case"ease-in":r=.42,o=0,i=1,u=1;break;case"ease-out":r=.42,o=0,i=.58,u=1;break;case"ease-in-out":r=0,o=0,i=.58,u=1;break;default:var a=e[0].split("(");if("cubic-bezier"===a[0]&&4===a[1].split(")")[0].split(",").length){var c=O(a[1].split(")")[0].split(",").map((function(t){return parseFloat(t)})),4);r=c[0],o=c[1],i=c[2],u=c[3]}}[r,i,o,u].every((function(t){return"number"===typeof t&&t>=0&&t<=1}));var f,l,s=E(r,i),p=E(o,u),y=(f=r,l=i,function(t){var e=w(f,l),n=[].concat(g(e.map((function(t,e){return t*e})).slice(1)),[0]);return P(n,t)}),b=function(t){for(var e,n=t>1?1:t,r=n,o=0;o<8;++o){var i=s(r)-n,u=y(r);if(Math.abs(i-n)<A||u<A)return p(r);r=(e=r-i/u)>1?1:e<0?0:e}return p(r)};return b.isStepper=!1,b},T=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=e[0];if("string"===typeof r)switch(r){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return k(r);case"spring":return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,n=void 0===e?100:e,r=t.damping,o=void 0===r?8:r,i=t.dt,u=void 0===i?17:i,a=function(t,e,r){var i=r+(-(t-e)*n-r*o)*u/1e3,a=r*u/1e3+t;return Math.abs(a-e)<A&&Math.abs(i)<A?[e,0]:[a,i]};return a.isStepper=!0,a.dt=u,a}();default:if("cubic-bezier"===r.split("(")[0])return k(r)}return"function"===typeof r?r:null};function R(t){return R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},R(t)}function C(t){return function(t){if(Array.isArray(t))return B(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||x(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function _(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?D(Object(n),!0).forEach((function(e){I(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):D(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function I(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==R(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==R(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===R(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function N(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,u,a=[],c=!0,f=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(a.push(r.value),a.length!==e);c=!0);}catch(t){f=!0,o=t}finally{try{if(!c&&null!=n.return&&(u=n.return(),Object(u)!==u))return}finally{if(f)throw o}}return a}}(t,e)||x(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function x(t,e){if(t){if("string"===typeof t)return B(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?B(t,e):void 0}}function B(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var F=function(t,e,n){return t+(e-t)*n},M=function(t){return t.from!==t.to},q=function t(e,n,r){var o=d((function(t,n){if(M(n)){var r=N(e(n.from,n.to,n.velocity),2),o=r[0],i=r[1];return _(_({},n),{},{from:o,velocity:i})}return n}),n);return r<1?d((function(t,e){return M(e)?_(_({},e),{},{velocity:F(e.velocity,o[t].velocity,r),from:F(e.from,o[t].from,r)}):e}),n):t(e,o,r-1)},J=function(t,e,n,r,o){var i,u,a,c,f=(i=t,u=e,[Object.keys(i),Object.keys(u)].reduce((function(t,e){return t.filter((function(t){return e.includes(t)}))}))),l=f.reduce((function(n,r){return _(_({},n),{},I({},r,[t[r],e[r]]))}),{}),s=f.reduce((function(n,r){return _(_({},n),{},I({},r,{from:t[r],velocity:0,to:e[r]}))}),{}),p=-1,y=function(){return null};return y=n.isStepper?function(r){a||(a=r);var i=(r-a)/n.dt;s=q(n,s,i),o(_(_(_({},t),e),d((function(t,e){return e.from}),s))),a=r,Object.values(s).filter(M).length&&(p=requestAnimationFrame(y))}:function(i){c||(c=i);var u=(i-c)/r,a=d((function(t,e){return F.apply(void 0,C(e).concat([n(u)]))}),l);if(o(_(_(_({},t),e),a)),u<1)p=requestAnimationFrame(y);else{var f=d((function(t,e){return F.apply(void 0,C(e).concat([n(1)]))}),l);o(_(_(_({},t),e),f))}},function(){return requestAnimationFrame(y),function(){cancelAnimationFrame(p)}}};function U(t){return U="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},U(t)}var Z=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function $(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function z(t){return function(t){if(Array.isArray(t))return L(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return L(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return L(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function L(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function W(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function G(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?W(Object(n),!0).forEach((function(e){H(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):W(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function H(t,e,n){return(e=Q(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function K(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Q(r.key),r)}}function Q(t){var e=function(t,e){if("object"!==U(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==U(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===U(e)?e:String(e)}function V(t,e){return V=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},V(t,e)}function X(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=et(t);if(e){var o=et(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Y(this,n)}}function Y(t,e){if(e&&("object"===U(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return tt(t)}function tt(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function et(t){return et=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},et(t)}var nt=function(t){!function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&V(t,e)}(a,t);var e,n,o,i=X(a);function a(t,e){var n;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,a);var r=(n=i.call(this,t,e)).props,o=r.isActive,u=r.attributeName,c=r.from,f=r.to,l=r.steps,s=r.children,p=r.duration;if(n.handleStyleChange=n.handleStyleChange.bind(tt(n)),n.changeStyle=n.changeStyle.bind(tt(n)),!o||p<=0)return n.state={style:{}},"function"===typeof s&&(n.state={style:f}),Y(n);if(l&&l.length)n.state={style:l[0].style};else if(c){if("function"===typeof s)return n.state={style:c},Y(n);n.state={style:u?H({},u,c):c}}else n.state={style:{}};return n}return e=a,(n=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,n=t.canBegin;this.mounted=!0,e&&n&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,n=e.isActive,r=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,c=e.from,f=this.state.style;if(r)if(n){if(!((0,u.vZ)(t.to,a)&&t.canBegin&&t.isActive)){var l=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=l||i?c:t.to;if(this.state&&f){var p={style:o?H({},o,s):s};(o&&f[o]!==s||!o&&f!==s)&&this.setState(p)}this.runAnimation(G(G({},this.props),{},{from:s,begin:0}))}}else{var y={style:o?H({},o,a):a};this.state&&f&&(o&&f[o]!==a||!o&&f!==a)&&this.setState(y)}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,n=t.from,r=t.to,o=t.duration,i=t.easing,u=t.begin,a=t.onAnimationEnd,c=t.onAnimationStart,f=J(n,r,T(i),o,this.changeStyle);this.manager.start([c,u,function(){e.stopJSAnimation=f()},o,a])}},{key:"runStepAnimation",value:function(t){var e=this,n=t.steps,r=t.begin,o=t.onAnimationStart,i=n[0],u=i.style,a=i.duration,c=void 0===a?0:a;return this.manager.start([o].concat(z(n.reduce((function(t,r,o){if(0===o)return t;var i=r.duration,u=r.easing,a=void 0===u?"ease":u,c=r.style,f=r.properties,l=r.onAnimationEnd,s=o>0?n[o-1]:r,p=f||Object.keys(c);if("function"===typeof a||"spring"===a)return[].concat(z(t),[e.runJSAnimation.bind(e,{from:s.style,to:c,duration:i,easing:a}),i]);var y=h(p,i,a),b=G(G(G({},s.style),c),{},{transition:y});return[].concat(z(t),[b,i,l]).filter(v)}),[u,Math.max(c,r)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=s());var e=t.begin,n=t.duration,r=t.attributeName,o=t.to,i=t.easing,u=t.onAnimationStart,a=t.onAnimationEnd,c=t.steps,f=t.children,l=this.manager;if(this.unSubscribe=l.subscribe(this.handleStyleChange),"function"!==typeof i&&"function"!==typeof f&&"spring"!==i)if(c.length>1)this.runStepAnimation(t);else{var p=r?H({},r,o):o,y=h(Object.keys(p),n,i);l.start([u,e,G(G({},p),{},{transition:y}),n,a])}else this.runJSAnimation(t)}},{key:"render",value:function(){var t=this.props,e=t.children,n=(t.begin,t.duration),o=(t.attributeName,t.easing,t.isActive),i=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,$(t,Z)),u=r.Children.count(e),a=this.state.style;if("function"===typeof e)return e(a);if(!o||0===u||n<=0)return e;var c=function(t){var e=t.props,n=e.style,o=void 0===n?{}:n,u=e.className;return(0,r.cloneElement)(t,G(G({},i),{},{style:G(G({},o),a),className:u}))};return 1===u?c(r.Children.only(e)):r.createElement("div",null,r.Children.map(e,(function(t){return c(t)})))}}])&&K(e.prototype,n),o&&K(e,o),Object.defineProperty(e,"prototype",{writable:!1}),a}(r.PureComponent);nt.displayName="Animate",nt.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},nt.propTypes={from:i().oneOfType([i().object,i().string]),to:i().oneOfType([i().object,i().string]),attributeName:i().string,duration:i().number,begin:i().number,easing:i().oneOfType([i().string,i().func]),steps:i().arrayOf(i().shape({duration:i().number.isRequired,style:i().object.isRequired,easing:i().oneOfType([i().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),i().func]),properties:i().arrayOf("string"),onAnimationEnd:i().func})),children:i().oneOfType([i().node,i().func]),isActive:i().bool,canBegin:i().bool,onAnimationEnd:i().func,shouldReAnimate:i().bool,onAnimationStart:i().func,onAnimationReStart:i().func};var rt=nt,ot=n(32873),it=n(67958),ut=["children","appearOptions","enterOptions","leaveOptions"];function at(t){return at="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},at(t)}function ct(){return ct=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},ct.apply(this,arguments)}function ft(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function lt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function st(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?lt(Object(n),!0).forEach((function(e){dt(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):lt(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function pt(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,ht(r.key),r)}}function yt(t,e){return yt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},yt(t,e)}function bt(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=vt(t);if(e){var o=vt(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return function(t,e){if(e&&("object"===at(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return mt(t)}(this,n)}}function mt(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function vt(t){return vt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},vt(t)}function dt(t,e,n){return(e=ht(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function ht(t){var e=function(t,e){if("object"!==at(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==at(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===at(e)?e:String(e)}var Ot=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.steps,n=t.duration;return e&&e.length?e.reduce((function(t,e){return t+(Number.isFinite(e.duration)&&e.duration>0?e.duration:0)}),0):Number.isFinite(n)?n:0},gt=function(t){!function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&yt(t,e)}(u,t);var e,n,o,i=bt(u);function u(){var t;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),dt(mt(t=i.call(this)),"handleEnter",(function(e,n){var r=t.props,o=r.appearOptions,i=r.enterOptions;t.handleStyleActive(n?o:i)})),dt(mt(t),"handleExit",(function(){var e=t.props.leaveOptions;t.handleStyleActive(e)})),t.state={isActive:!1},t}return e=u,(n=[{key:"handleStyleActive",value:function(t){if(t){var e=t.onAnimationEnd?function(){t.onAnimationEnd()}:null;this.setState(st(st({},t),{},{onAnimationEnd:e,isActive:!0}))}}},{key:"parseTimeout",value:function(){var t=this.props,e=t.appearOptions,n=t.enterOptions,r=t.leaveOptions;return Ot(e)+Ot(n)+Ot(r)}},{key:"render",value:function(){var t=this,e=this.props,n=e.children,o=(e.appearOptions,e.enterOptions,e.leaveOptions,ft(e,ut));return r.createElement(it.ZP,ct({},o,{onEnter:this.handleEnter,onExit:this.handleExit,timeout:this.parseTimeout()}),(function(){return r.createElement(rt,t.state,r.Children.only(n))}))}}])&&pt(e.prototype,n),o&&pt(e,o),Object.defineProperty(e,"prototype",{writable:!1}),u}(r.Component);gt.propTypes={appearOptions:i().object,enterOptions:i().object,leaveOptions:i().object,children:i().element};var jt=gt;function St(t){var e=t.component,n=t.children,o=t.appear,i=t.enter,u=t.leave;return r.createElement(ot.Z,{component:e},r.Children.map(n,(function(t,e){return r.createElement(jt,{appearOptions:o,enterOptions:i,leaveOptions:u,key:"child-".concat(e)},t)})))}St.propTypes={appear:i().object,enter:i().object,leave:i().object,children:i().oneOfType([i().array,i().element]),component:i().any},St.defaultProps={component:"span"};var At=rt}}]);
//# sourceMappingURL=react-smooth.1574f32f78fc65f02c621bbb1f3d1ef0.js.map