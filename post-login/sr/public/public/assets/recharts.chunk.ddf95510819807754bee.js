/*! For license information please see recharts.chunk.ddf95510819807754bee.js.LICENSE.txt */
"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["recharts"],{48218:function(t,e,r){r.d(e,{$:function(){return V}});var n=r(89526),o=r(90512),i=r(80397),a=r(47184),c=r.n(a),u=r(51391),l=r.n(u),s=r(61452),f=r(65370),p=r(32214),d=r(34324),y=r(16171),h=r(9410),v=r(59509),m=r(36530),b=r(33790),g=r(78109),O=r(69531),x=["x","y"];function w(t){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},w(t)}function j(){return j=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},j.apply(this,arguments)}function P(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function S(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?P(Object(r),!0).forEach((function(e){A(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function A(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=w(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=w(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==w(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function E(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function k(t,e){var r=t.x,n=t.y,o=E(t,x),i="".concat(r),a=parseInt(i,10),c="".concat(n),u=parseInt(c,10),l="".concat(e.height||o.height),s=parseInt(l,10),f="".concat(e.width||o.width),p=parseInt(f,10);return S(S(S(S(S({},e),o),a?{x:a}:{}),u?{y:u}:{}),{},{height:s,width:p,name:e.name,radius:e.radius})}function T(t){return n.createElement(O.bn,j({shapeType:"rectangle",propTransformer:k,activeClassName:"recharts-active-bar"},t))}var C,I=["value","background"];function D(t){return D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},D(t)}function M(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function N(){return N=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},N.apply(this,arguments)}function B(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function L(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?B(Object(r),!0).forEach((function(e){K(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function R(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function z(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,X(n.key),n)}}function _(t,e,r){return e=W(e),function(t,e){if(e&&("object"===D(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,F()?Reflect.construct(e,r||[],W(t).constructor):e.apply(t,r))}function F(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(F=function(){return!!t})()}function W(t){return W=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},W(t)}function Z(t,e){return Z=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Z(t,e)}function K(t,e,r){return(e=X(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function X(t){var e=function(t,e){if("object"!=D(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=D(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==D(e)?e:e+""}var V=function(t){function e(){var t;R(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return K(t=_(this,e,[].concat(n)),"state",{isAnimationFinished:!1}),K(t,"id",(0,y.EL)("recharts-bar-")),K(t,"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()})),K(t,"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()})),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Z(t,e)}(e,t),r=e,u=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],(a=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,o=r.shape,i=r.dataKey,a=r.activeIndex,c=r.activeBar,u=(0,h.L6)(this.props,!1);return t&&t.map((function(t,r){var l=r===a,f=l?c:o,p=L(L(L({},u),t),{},{isActive:l,option:f,index:r,dataKey:i,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return n.createElement(s.m,N({className:"recharts-bar-rectangle"},(0,b.bw)(e.props,t,r),{key:"rectangle-".concat(null===t||void 0===t?void 0:t.x,"-").concat(null===t||void 0===t?void 0:t.y,"-").concat(null===t||void 0===t?void 0:t.value,"-").concat(r)}),n.createElement(T,p))}))}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,o=e.layout,a=e.isAnimationActive,c=e.animationBegin,u=e.animationDuration,l=e.animationEasing,f=e.animationId,p=this.state.prevData;return n.createElement(i.ZP,{begin:c,duration:u,isActive:a,easing:l,from:{t:0},to:{t:1},key:"bar-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(e){var i=e.t,a=r.map((function(t,e){var r=p&&p[e];if(r){var n=(0,y.k4)(r.x,t.x),a=(0,y.k4)(r.y,t.y),c=(0,y.k4)(r.width,t.width),u=(0,y.k4)(r.height,t.height);return L(L({},t),{},{x:n(i),y:a(i),width:c(i),height:u(i)})}if("horizontal"===o){var l=(0,y.k4)(0,t.height)(i);return L(L({},t),{},{y:t.y+t.height-l,height:l})}var s=(0,y.k4)(0,t.width)(i);return L(L({},t),{},{width:s})}));return n.createElement(s.m,null,t.renderRectanglesStatically(a))}))}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return!(r&&e&&e.length)||n&&c()(n,e)?this.renderRectanglesStatically(e):this.renderRectanglesWithAnimation()}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,o=e.dataKey,i=e.activeIndex,a=(0,h.L6)(this.props.background,!1);return r.map((function(e,r){e.value;var c=e.background,u=M(e,I);if(!c)return null;var l=L(L(L(L(L({},u),{},{fill:"#eee"},c),a),(0,b.bw)(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:o,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(T,N({key:"background-bar-".concat(r),option:t.props.background,isActive:r===i},l))}))}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,o=r.data,i=r.xAxis,a=r.yAxis,c=r.layout,u=r.children,l=(0,h.NN)(u,f.W);if(!l)return null;var p="vertical"===c?o[0].height/2:o[0].width/2,d=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:(0,m.F$)(t,e)}},y={clipPath:t?"url(#clipPath-".concat(e,")"):null};return n.createElement(s.m,y,l.map((function(t){return n.cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:o,xAxis:i,yAxis:a,layout:c,offset:p,dataPointFormatter:d})})))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,i=t.className,a=t.xAxis,c=t.yAxis,u=t.left,f=t.top,p=t.width,y=t.height,h=t.isAnimationActive,v=t.background,m=t.id;if(e||!r||!r.length)return null;var b=this.state.isAnimationFinished,g=(0,o.Z)("recharts-bar",i),O=a&&a.allowDataOverflow,x=c&&c.allowDataOverflow,w=O||x,j=l()(m)?this.id:m;return n.createElement(s.m,{className:g},O||x?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(j)},n.createElement("rect",{x:O?u:u-p/2,y:x?f:f-y/2,width:O?p:2*p,height:x?y:2*y}))):null,n.createElement(s.m,{className:"recharts-bar-rectangles",clipPath:w?"url(#clipPath-".concat(j,")"):null},v?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(w,j),(!h||b)&&d.e.renderCallByParent(this.props,r))}}])&&z(r.prototype,a),u&&z(r,u),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,a,u}(n.PureComponent);C=V,K(V,"displayName","Bar"),K(V,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!v.x.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),K(V,"getComposedData",(function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,d=t.offset,v=(0,m.Bu)(n,r);if(!v)return null;var b=e.layout,O=r.type.defaultProps,x=void 0!==O?L(L({},O),r.props):r.props,w=x.dataKey,j=x.children,P=x.minPointSize,S="horizontal"===b?a:i,A=l?S.scale.domain():null,E=(0,m.Yj)({numericAxis:S}),k=(0,h.NN)(j,p.b),T=f.map((function(t,e){var n,f,p,d,h,O;l?n=(0,m.Vv)(l[s+e],A):(n=(0,m.F$)(t,w),Array.isArray(n)||(n=[E,n]));var x=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"===typeof t)return t;var o=(0,y.hj)(r)||(0,y.Rw)(r);return o?t(r,n):(o||(0,g.Z)(!1),e)}}(P,C.defaultProps.minPointSize)(n[1],e);if("horizontal"===b){var j,S=[a.scale(n[0]),a.scale(n[1])],T=S[0],I=S[1];f=(0,m.Fy)({axis:i,ticks:c,bandSize:o,offset:v.offset,entry:t,index:e}),p=null!==(j=null!==I&&void 0!==I?I:T)&&void 0!==j?j:void 0,d=v.size;var D=T-I;if(h=Number.isNaN(D)?0:D,O={x:f,y:a.y,width:d,height:a.height},Math.abs(x)>0&&Math.abs(h)<Math.abs(x)){var M=(0,y.uY)(h||x)*(Math.abs(x)-Math.abs(h));p-=M,h+=M}}else{var N=[i.scale(n[0]),i.scale(n[1])],B=N[0],R=N[1];if(f=B,p=(0,m.Fy)({axis:a,ticks:u,bandSize:o,offset:v.offset,entry:t,index:e}),d=R-B,h=v.size,O={x:i.x,y:p,width:i.width,height:h},Math.abs(x)>0&&Math.abs(d)<Math.abs(x))d+=(0,y.uY)(d||x)*(Math.abs(x)-Math.abs(d))}return L(L(L({},t),{},{x:f,y:p,width:d,height:h,value:l?n:n[1],payload:t,background:O},k&&k[e]&&k[e].props),{},{tooltipPayload:[(0,m.Qo)(r,t)],tooltipPosition:{x:f+d/2,y:p+h/2}})}));return L({data:T,layout:b},d)}))},88974:function(t,e,r){r.d(e,{B:function(){return D}});var n=r(89526),o=r(90512),i=r(35406),a=r(39277),c=r.n(a),u=r(58120),l=r.n(u),s=r(61452),f=r(49266),p=r(36530),d=r(16171);function y(t){return y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(t)}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function v(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach((function(e){m(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function m(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var b=["Webkit","Moz","O","ms"],g=r(9410);function O(t){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(t)}function x(){return x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},x.apply(this,arguments)}function w(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?w(Object(r),!0).forEach((function(e){T(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function P(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,C(n.key),n)}}function S(t,e,r){return e=E(e),function(t,e){if(e&&("object"===O(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,A()?Reflect.construct(e,r||[],E(t).constructor):e.apply(t,r))}function A(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(A=function(){return!!t})()}function E(t){return E=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},E(t)}function k(t,e){return k=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},k(t,e)}function T(t,e,r){return(e=C(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function C(t){var e=function(t,e){if("object"!=O(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=O(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==O(e)?e:e+""}var I=function(t){return t.changedTouches&&!!t.changedTouches.length},D=function(t){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),T(r=S(this,e,[t]),"handleDrag",(function(t){r.leaveTimer&&(clearTimeout(r.leaveTimer),r.leaveTimer=null),r.state.isTravellerMoving?r.handleTravellerMove(t):r.state.isSlideMoving&&r.handleSlideDrag(t)})),T(r,"handleTouchMove",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&r.handleDrag(t.changedTouches[0])})),T(r,"handleDragEnd",(function(){r.setState({isTravellerMoving:!1,isSlideMoving:!1},(function(){var t=r.props,e=t.endIndex,n=t.onDragEnd,o=t.startIndex;null===n||void 0===n||n({endIndex:e,startIndex:o})})),r.detachDragEndListener()})),T(r,"handleLeaveWrapper",(function(){(r.state.isTravellerMoving||r.state.isSlideMoving)&&(r.leaveTimer=window.setTimeout(r.handleDragEnd,r.props.leaveTimeOut))})),T(r,"handleEnterSlideOrTraveller",(function(){r.setState({isTextActive:!0})})),T(r,"handleLeaveSlideOrTraveller",(function(){r.setState({isTextActive:!1})})),T(r,"handleSlideDragStart",(function(t){var e=I(t)?t.changedTouches[0]:t;r.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:e.pageX}),r.attachDragEndListener()})),r.travellerDragStartHandlers={startX:r.handleTravellerDragStart.bind(r,"startX"),endX:r.handleTravellerDragStart.bind(r,"endX")},r.state={},r}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&k(t,e)}(e,t),r=e,u=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,o=t.width,i=t.height,a=t.stroke,c=Math.floor(r+i/2)-1;return n.createElement(n.Fragment,null,n.createElement("rect",{x:e,y:r,width:o,height:i,fill:a,stroke:"none"}),n.createElement("line",{x1:e+1,y1:c,x2:e+o-1,y2:c,fill:"none",stroke:"#fff"}),n.createElement("line",{x1:e+1,y1:c+2,x2:e+o-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,r){return n.isValidElement(t)?n.cloneElement(t,r):c()(t)?t(r):e.renderDefaultTraveller(r)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,a=t.travellerWidth,c=t.updateId,u=t.startIndex,s=t.endIndex;if(r!==e.prevData||c!==e.prevUpdateId)return j({prevData:r,prevTravellerWidth:a,prevUpdateId:c,prevX:o,prevWidth:n},r&&r.length?function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,a=t.width,c=t.travellerWidth;if(!e||!e.length)return{};var u=e.length,s=(0,i.x)().domain(l()(0,u)).range([o,o+a-c]),f=s.domain().map((function(t){return s(t)}));return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(r),endX:s(n),scale:s,scaleValues:f}}({data:r,width:n,x:o,travellerWidth:a,startIndex:u,endIndex:s}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||a!==e.prevTravellerWidth)){e.scale.range([o,o+n-a]);var f=e.scale.domain().map((function(t){return e.scale(t)}));return{prevData:r,prevTravellerWidth:a,prevUpdateId:c,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:f}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=0,n=t.length-1;n-r>1;){var o=Math.floor((r+n)/2);t[o]>e?n=o:r=o}return e>=t[n]?n:r}}],(a=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var r=t.startX,n=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=Math.min(r,n),l=Math.max(r,n),s=e.getIndexInRange(o,u),f=e.getIndexInRange(o,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=(0,p.F$)(r[t],o,t);return c()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,c=i.width,u=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-o,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-o));var d=this.getIndex({startX:n+p,endX:o+p});d.startIndex===l&&d.endIndex===s||!f||f(d),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=I(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,d=c.data,y={startX:this.state.startX,endX:this.state.endX},h=t.pageX-r;h>0?h=Math.min(h,u+l-s-a):h<0&&(h=Math.max(h,u-a)),y[n]=a+h;var v=this.getIndex(y),m=v.startIndex,b=v.endIndex;this.setState(T(T({},n,a+h),"brushMoveStartX",t.pageX),(function(){f&&function(){var t=d.length-1;return"startX"===n&&(o>i?m%p===0:b%p===0)||o<i&&b===t||"endX"===n&&(o>i?b%p===0:m%p===0)||o>i&&b===t}()&&f(v)}))}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[e],u=o.indexOf(c);if(-1!==u){var l=u+t;if(!(-1===l||l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState(T({},e,s),(function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))}))}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.fill,c=t.stroke;return n.createElement("rect",{stroke:c,fill:a,x:e,y:r,width:o,height:i})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.data,c=t.children,u=t.padding,l=n.Children.only(c);return l?n.cloneElement(l,{x:e,y:r,width:o,height:i,margin:u,compact:!0,data:a}):null}},{key:"renderTravellerLayer",value:function(t,r){var o,i,a=this,c=this.props,u=c.y,l=c.travellerWidth,f=c.height,p=c.traveller,d=c.ariaLabel,y=c.data,h=c.startIndex,v=c.endIndex,m=Math.max(t,this.props.x),b=j(j({},(0,g.L6)(this.props,!1)),{},{x:m,y:u,width:l,height:f}),O=d||"Min value: ".concat(null===(o=y[h])||void 0===o?void 0:o.name,", Max value: ").concat(null===(i=y[v])||void 0===i?void 0:i.name);return n.createElement(s.m,{tabIndex:0,role:"slider","aria-label":O,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[r],onTouchStart:this.travellerDragStartHandlers[r],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,r))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},e.renderTraveller(p,b))}},{key:"renderSlide",value:function(t,e){var r=this.props,o=r.y,i=r.height,a=r.stroke,c=r.travellerWidth,u=Math.min(t,e)+c,l=Math.max(Math.abs(e-t)-c,0);return n.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:u,y:o,width:l,height:i})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,o=t.y,i=t.height,a=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,p=u.endX,d={pointerEvents:"none",fill:c};return n.createElement(s.m,{className:"recharts-brush-texts"},n.createElement(f.x,x({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,p)-5,y:o+i/2},d),this.getTextOfTick(e)),n.createElement(f.x,x({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,p)+a+5,y:o+i/2},d),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,i=t.children,a=t.x,c=t.y,u=t.width,l=t.height,f=t.alwaysShowText,p=this.state,y=p.startX,h=p.endX,g=p.isTextActive,O=p.isSlideMoving,x=p.isTravellerMoving,w=p.isTravellerFocused;if(!e||!e.length||!(0,d.hj)(a)||!(0,d.hj)(c)||!(0,d.hj)(u)||!(0,d.hj)(l)||u<=0||l<=0)return null;var j=(0,o.Z)("recharts-brush",r),P=1===n.Children.count(i),S=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,(function(t){return t.toUpperCase()})),n=b.reduce((function(t,n){return v(v({},t),{},m({},n+r,e))}),{});return n[t]=e,n}("userSelect","none");return n.createElement(s.m,{className:j,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:S},this.renderBackground(),P&&this.renderPanorama(),this.renderSlide(y,h),this.renderTravellerLayer(y,"startX"),this.renderTravellerLayer(h,"endX"),(g||O||x||w||f)&&this.renderText())}}])&&P(r.prototype,a),u&&P(r,u),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,a,u}(n.PureComponent);T(D,"displayName","Brush"),T(D,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1})},27871:function(t,e,r){r.d(e,{O:function(){return D}});var n=r(89526),o=r(39277),i=r.n(o),a=r(80089),c=r.n(a),u=r(90512),l=r(68201),s=r(61452),f=r(49266),p=r(43774),d=r(16171),y=r(33790),h=r(9410),v=r(37561),m=["viewBox"],b=["viewBox"],g=["ticks"];function O(t){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(t)}function x(){return x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},x.apply(this,arguments)}function w(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?w(Object(r),!0).forEach((function(e){C(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function P(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function S(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,I(n.key),n)}}function A(t,e,r){return e=k(e),function(t,e){if(e&&("object"===O(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,E()?Reflect.construct(e,r||[],k(t).constructor):e.apply(t,r))}function E(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(E=function(){return!!t})()}function k(t){return k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},k(t)}function T(t,e){return T=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},T(t,e)}function C(t,e,r){return(e=I(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function I(t){var e=function(t,e){if("object"!=O(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=O(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==O(e)?e:e+""}var D=function(t){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(r=A(this,e,[t])).state={fontSize:"",letterSpacing:""},r}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&T(t,e)}(e,t),r=e,a=[{key:"renderTickItem",value:function(t,e,r){var o=(0,u.Z)(e.className,"recharts-cartesian-axis-tick-value");return n.isValidElement(t)?n.cloneElement(t,j(j({},e),{},{className:o})):i()(t)?t(j(j({},e),{},{className:o})):n.createElement(f.x,x({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],(o=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=P(t,m),o=this.props,i=o.viewBox,a=P(o,b);return!(0,l.w)(r,i)||!(0,l.w)(n,a)||!(0,l.w)(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,y=c.tickSize,h=c.mirror,v=c.tickMargin,m=h?-1:1,b=t.tickSize||y,g=(0,d.hj)(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+ +!h*f)-m*b)-m*v,i=g;break;case"left":n=o=t.coordinate,i=(e=(r=u+ +!h*s)-m*b)-m*v,a=g;break;case"right":n=o=t.coordinate,i=(e=(r=u+ +h*s)+m*b)+m*v,a=g;break;default:e=r=t.coordinate,a=(n=(o=l+ +h*f)+m*b)+m*v,i=g}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.orientation,l=t.mirror,s=t.axisLine,f=j(j(j({},(0,h.L6)(this.props,!1)),(0,h.L6)(s,!1)),{},{fill:"none"});if("top"===a||"bottom"===a){var p=+("top"===a&&!l||"bottom"===a&&l);f=j(j({},f),{},{x1:e,y1:r+p*i,x2:e+o,y2:r+p*i})}else{var d=+("left"===a&&!l||"right"===a&&l);f=j(j({},f),{},{x1:e+d*o,y1:r,x2:e+d*o,y2:r+i})}return n.createElement("line",x({},f,{className:(0,u.Z)("recharts-cartesian-axis-line",c()(s,"className"))}))}},{key:"renderTicks",value:function(t,r,o){var a=this,l=this.props,f=l.tickLine,p=l.stroke,d=l.tick,m=l.tickFormatter,b=l.unit,g=(0,v.f)(j(j({},this.props),{},{ticks:t}),r,o),O=this.getTickTextAnchor(),w=this.getTickVerticalAnchor(),P=(0,h.L6)(this.props,!1),S=(0,h.L6)(d,!1),A=j(j({},P),{},{fill:"none"},(0,h.L6)(f,!1)),E=g.map((function(t,r){var o=a.getTickLineCoord(t),l=o.line,h=o.tick,v=j(j(j(j({textAnchor:O,verticalAnchor:w},P),{},{stroke:"none",fill:p},S),h),{},{index:r,payload:t,visibleTicksCount:g.length,tickFormatter:m});return n.createElement(s.m,x({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},(0,y.bw)(a.props,t,r)),f&&n.createElement("line",x({},A,l,{className:(0,u.Z)("recharts-cartesian-axis-tick-line",c()(f,"className"))})),d&&e.renderTickItem(d,v,"".concat(i()(m)?m(t.value,r):t.value).concat(b||"")))}));return n.createElement("g",{className:"recharts-cartesian-axis-ticks"},E)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,o=e.width,a=e.height,c=e.ticksGenerator,l=e.className;if(e.hide)return null;var f=this.props,d=f.ticks,y=P(f,g),h=d;return i()(c)&&(h=d&&d.length>0?c(this.props):c(y)),o<=0||a<=0||!h||!h.length?null:n.createElement(s.m,{className:(0,u.Z)("recharts-cartesian-axis",l),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(h,this.state.fontSize,this.state.letterSpacing),p._.renderCallByParent(this.props))}}])&&S(r.prototype,o),a&&S(r,a),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,a}(n.Component);C(D,"displayName","CartesianAxis"),C(D,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},42333:function(t,e,r){r.d(e,{q:function(){return C}});var n=r(89526),o=r(39277),i=r.n(o),a=r(78706),c=r(16171),u=r(9410),l=r(36530),s=r(37561),f=r(27871),p=r(86545),d=["x1","y1","x2","y2","key"],y=["offset"];function h(t){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},h(t)}function v(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function m(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?v(Object(r),!0).forEach((function(e){b(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function b(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=h(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=h(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==h(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function g(){return g=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},g.apply(this,arguments)}function O(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var x=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,o=t.x,i=t.y,a=t.width,c=t.height,u=t.ry;return n.createElement("rect",{x:o,y:i,ry:u,width:a,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function w(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if(i()(t))r=t(e);else{var o=e.x1,a=e.y1,c=e.x2,l=e.y2,s=e.key,f=O(e,d),p=(0,u.L6)(f,!1),h=(p.offset,O(p,y));r=n.createElement("line",g({},h,{x1:o,y1:a,x2:c,y2:l,fill:"none",key:s}))}return r}function j(t){var e=t.x,r=t.width,o=t.horizontal,i=void 0===o||o,a=t.horizontalPoints;if(!i||!a||!a.length)return null;var c=a.map((function(n,o){var a=m(m({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(o),index:o});return w(i,a)}));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function P(t){var e=t.y,r=t.height,o=t.vertical,i=void 0===o||o,a=t.verticalPoints;if(!i||!a||!a.length)return null;var c=a.map((function(n,o){var a=m(m({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(o),index:o});return w(i,a)}));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function S(t){var e=t.horizontalFill,r=t.fillOpacity,o=t.x,i=t.y,a=t.width,c=t.height,u=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=u.map((function(t){return Math.round(t+i-i)})).sort((function(t,e){return t-e}));i!==s[0]&&s.unshift(0);var f=s.map((function(t,u){var l=!s[u+1]?i+c-t:s[u+1]-t;if(l<=0)return null;var f=u%e.length;return n.createElement("rect",{key:"react-".concat(u),y:t,x:o,height:l,width:a,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})}));return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function A(t){var e=t.vertical,r=void 0===e||e,o=t.verticalFill,i=t.fillOpacity,a=t.x,c=t.y,u=t.width,l=t.height,s=t.verticalPoints;if(!r||!o||!o.length)return null;var f=s.map((function(t){return Math.round(t+a-a)})).sort((function(t,e){return t-e}));a!==f[0]&&f.unshift(0);var p=f.map((function(t,e){var r=!f[e+1]?a+u-t:f[e+1]-t;if(r<=0)return null;var s=e%o.length;return n.createElement("rect",{key:"react-".concat(e),x:t,y:c,width:r,height:l,stroke:"none",fill:o[s],fillOpacity:i,className:"recharts-cartesian-grid-bg"})}));return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},p)}var E=function(t,e){var r=t.xAxis,n=t.width,o=t.height,i=t.offset;return(0,l.Rf)((0,s.f)(m(m(m({},f.O.defaultProps),r),{},{ticks:(0,l.uY)(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,e)},k=function(t,e){var r=t.yAxis,n=t.width,o=t.height,i=t.offset;return(0,l.Rf)((0,s.f)(m(m(m({},f.O.defaultProps),r),{},{ticks:(0,l.uY)(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,e)},T={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function C(t){var e,r,o,u,l,s,f=(0,p.zn)(),d=(0,p.Mw)(),y=(0,p.qD)(),v=m(m({},t),{},{stroke:null!==(e=t.stroke)&&void 0!==e?e:T.stroke,fill:null!==(r=t.fill)&&void 0!==r?r:T.fill,horizontal:null!==(o=t.horizontal)&&void 0!==o?o:T.horizontal,horizontalFill:null!==(u=t.horizontalFill)&&void 0!==u?u:T.horizontalFill,vertical:null!==(l=t.vertical)&&void 0!==l?l:T.vertical,verticalFill:null!==(s=t.verticalFill)&&void 0!==s?s:T.verticalFill,x:(0,c.hj)(t.x)?t.x:y.left,y:(0,c.hj)(t.y)?t.y:y.top,width:(0,c.hj)(t.width)?t.width:y.width,height:(0,c.hj)(t.height)?t.height:y.height}),b=v.x,O=v.y,w=v.width,C=v.height,I=v.syncWithTicks,D=v.horizontalValues,M=v.verticalValues,N=(0,p.CW)(),B=(0,p.Nf)();if(!(0,c.hj)(w)||w<=0||!(0,c.hj)(C)||C<=0||!(0,c.hj)(b)||b!==+b||!(0,c.hj)(O)||O!==+O)return null;var L=v.verticalCoordinatesGenerator||E,R=v.horizontalCoordinatesGenerator||k,z=v.horizontalPoints,_=v.verticalPoints;if((!z||!z.length)&&i()(R)){var F=D&&D.length,W=R({yAxis:B?m(m({},B),{},{ticks:F?D:B.ticks}):void 0,width:f,height:d,offset:y},!!F||I);(0,a.Z)(Array.isArray(W),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(h(W),"]")),Array.isArray(W)&&(z=W)}if((!_||!_.length)&&i()(L)){var Z=M&&M.length,K=L({xAxis:N?m(m({},N),{},{ticks:Z?M:N.ticks}):void 0,width:f,height:d,offset:y},!!Z||I);(0,a.Z)(Array.isArray(K),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(h(K),"]")),Array.isArray(K)&&(_=K)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(x,{fill:v.fill,fillOpacity:v.fillOpacity,x:v.x,y:v.y,width:v.width,height:v.height,ry:v.ry}),n.createElement(j,g({},v,{offset:y,horizontalPoints:z,xAxis:N,yAxis:B})),n.createElement(P,g({},v,{offset:y,verticalPoints:_,xAxis:N,yAxis:B})),n.createElement(S,g({},v,{horizontalPoints:z})),n.createElement(A,g({},v,{verticalPoints:_})))}C.displayName="CartesianGrid"},65370:function(t,e,r){r.d(e,{W:function(){return x}});var n=r(89526),o=r(78109),i=r(61452),a=r(9410),c=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function l(){return l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},l.apply(this,arguments)}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return f(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return f(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function p(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function d(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function y(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,O(n.key),n)}}function h(t,e,r){return e=m(e),function(t,e){if(e&&("object"===u(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,v()?Reflect.construct(e,r||[],m(t).constructor):e.apply(t,r))}function v(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(v=function(){return!!t})()}function m(t){return m=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},m(t)}function b(t,e){return b=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},b(t,e)}function g(t,e,r){return(e=O(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function O(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}var x=function(t){function e(){return d(this,e),h(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&b(t,e)}(e,t),r=e,(u=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,u=t.width,f=t.dataKey,d=t.data,y=t.dataPointFormatter,h=t.xAxis,v=t.yAxis,m=p(t,c),b=(0,a.L6)(m,!1);"x"===this.props.direction&&"number"!==h.type&&(0,o.Z)(!1);var g=d.map((function(t){var o=y(t,f),a=o.x,c=o.y,p=o.value,d=o.errorVal;if(!d)return null;var m,g,O=[];if(Array.isArray(d)){var x=s(d,2);m=x[0],g=x[1]}else m=g=d;if("vertical"===r){var w=h.scale,j=c+e,P=j+u,S=j-u,A=w(p-m),E=w(p+g);O.push({x1:E,y1:P,x2:E,y2:S}),O.push({x1:A,y1:j,x2:E,y2:j}),O.push({x1:A,y1:P,x2:A,y2:S})}else if("horizontal"===r){var k=v.scale,T=a+e,C=T-u,I=T+u,D=k(p-m),M=k(p+g);O.push({x1:C,y1:M,x2:I,y2:M}),O.push({x1:T,y1:D,x2:T,y2:M}),O.push({x1:C,y1:D,x2:I,y2:D})}return n.createElement(i.m,l({className:"recharts-errorBar",key:"bar-".concat(O.map((function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)})))},b),O.map((function(t){return n.createElement("line",l({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))})))}));return n.createElement(i.m,{className:"recharts-errorBars"},g)}}])&&y(r.prototype,u),f&&y(r,f),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,u,f}(n.Component);g(x,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),g(x,"displayName","ErrorBar")},93264:function(t,e,r){r.d(e,{z:function(){return E}});var n=r(89526),o=r(39277),i=r.n(o),a=r(90512),c=r(61452),u=r(43774),l=r(87210),s=r(94694),f=r(16171),p=r(78706),d=r(33951),y=r(9410);function h(){return h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},h.apply(this,arguments)}function v(t){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(t)}function m(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function b(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?m(Object(r),!0).forEach((function(e){S(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function g(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function O(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,A(n.key),n)}}function x(t,e,r){return e=j(e),function(t,e){if(e&&("object"===v(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,w()?Reflect.construct(e,r||[],j(t).constructor):e.apply(t,r))}function w(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(w=function(){return!!t})()}function j(t){return j=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},j(t)}function P(t,e){return P=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},P(t,e)}function S(t,e,r){return(e=A(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function A(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}var E=function(t){function e(){return g(this,e),x(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&P(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){var t=this.props,r=t.x1,o=t.x2,i=t.y1,d=t.y2,h=t.className,v=t.alwaysShow,m=t.clipPathId;(0,p.Z)(void 0===v,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var g=(0,f.P2)(r),O=(0,f.P2)(o),x=(0,f.P2)(i),w=(0,f.P2)(d),j=this.props.shape;if(!g&&!O&&!x&&!w&&!j)return null;var P=function(t,e,r,n,o){var i=o.x1,a=o.x2,c=o.y1,u=o.y2,f=o.xAxis,p=o.yAxis;if(!f||!p)return null;var d=(0,l.Ky)({x:f.scale,y:p.scale}),y={x:t?d.x.apply(i,{position:"start"}):d.x.rangeMin,y:r?d.y.apply(c,{position:"start"}):d.y.rangeMin},h={x:e?d.x.apply(a,{position:"end"}):d.x.rangeMax,y:n?d.y.apply(u,{position:"end"}):d.y.rangeMax};return!(0,s.B)(o,"discard")||d.isInRange(y)&&d.isInRange(h)?(0,l.O1)(y,h):null}(g,O,x,w,this.props);if(!P&&!j)return null;var S=(0,s.B)(this.props,"hidden")?"url(#".concat(m,")"):void 0;return n.createElement(c.m,{className:(0,a.Z)("recharts-reference-area",h)},e.renderRect(j,b(b({clipPath:S},(0,y.L6)(this.props,!0)),P)),u._.renderCallByParent(this.props,P))}}])&&O(r.prototype,o),i&&O(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.Component);S(E,"displayName","ReferenceArea"),S(E,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),S(E,"renderRect",(function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):i()(t)?t(e):n.createElement(d.A,h({},e,{className:"recharts-reference-area-rect"}))}))},7629:function(t,e,r){r.d(e,{q:function(){return E}});var n=r(89526),o=r(39277),i=r.n(o),a=r(90512),c=r(61452),u=r(96963),l=r(43774),s=r(16171),f=r(94694),p=r(87210),d=r(78706),y=r(9410);function h(){return h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},h.apply(this,arguments)}function v(t){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(t)}function m(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function b(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?m(Object(r),!0).forEach((function(e){S(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function g(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function O(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,A(n.key),n)}}function x(t,e,r){return e=j(e),function(t,e){if(e&&("object"===v(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,w()?Reflect.construct(e,r||[],j(t).constructor):e.apply(t,r))}function w(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(w=function(){return!!t})()}function j(t){return j=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},j(t)}function P(t,e){return P=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},P(t,e)}function S(t,e,r){return(e=A(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function A(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}var E=function(t){function e(){return g(this,e),x(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&P(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){var t=this.props,r=t.x,o=t.y,i=t.r,u=t.alwaysShow,h=t.clipPathId,v=(0,s.P2)(r),m=(0,s.P2)(o);if((0,d.Z)(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!v||!m)return null;var g=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=(0,p.Ky)({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return(0,f.B)(t,"discard")&&!i.isInRange(a)?null:a}(this.props);if(!g)return null;var O=g.x,x=g.y,w=this.props,j=w.shape,P=w.className,S=b(b({clipPath:(0,f.B)(this.props,"hidden")?"url(#".concat(h,")"):void 0},(0,y.L6)(this.props,!0)),{},{cx:O,cy:x});return n.createElement(c.m,{className:(0,a.Z)("recharts-reference-dot",P)},e.renderDot(j,S),l._.renderCallByParent(this.props,{x:O-i,y:x-i,width:2*i,height:2*i}))}}])&&O(r.prototype,o),i&&O(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.Component);S(E,"displayName","ReferenceDot"),S(E,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),S(E,"renderDot",(function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):i()(t)?t(e):n.createElement(u.o,h({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))}))},48586:function(t,e,r){r.d(e,{d:function(){return D}});var n=r(89526),o=r(39277),i=r.n(o),a=r(60479),c=r.n(a),u=r(90512),l=r(61452),s=r(43774),f=r(94694),p=r(16171),d=r(87210),y=r(78706),h=r(9410),v=r(86545);function m(t){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},m(t)}function b(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function g(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,E(n.key),n)}}function O(t,e,r){return e=w(e),function(t,e){if(e&&("object"===m(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,x()?Reflect.construct(e,r||[],w(t).constructor):e.apply(t,r))}function x(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(x=function(){return!!t})()}function w(t){return w=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},w(t)}function j(t,e){return j=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},j(t,e)}function P(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function S(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?P(Object(r),!0).forEach((function(e){A(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function A(t,e,r){return(e=E(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function E(t){var e=function(t,e){if("object"!=m(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=m(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==m(e)?e:e+""}function k(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return T(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return T(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function T(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function C(){return C=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},C.apply(this,arguments)}function I(t){var e=t.x,r=t.y,o=t.segment,a=t.xAxisId,m=t.yAxisId,b=t.shape,g=t.className,O=t.alwaysShow,x=(0,v.sp)(),w=(0,v.bH)(a),j=(0,v.Ud)(m),P=(0,v.d2)();if(!x||!P)return null;(0,y.Z)(void 0===O,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var A=function(t,e,r,n,o,i,a,u,l){var s=o.x,p=o.y,d=o.width,y=o.height;if(r){var h=l.y,v=t.y.apply(h,{position:i});if((0,f.B)(l,"discard")&&!t.y.isInRange(v))return null;var m=[{x:s+d,y:v},{x:s,y:v}];return"left"===u?m.reverse():m}if(e){var b=l.x,g=t.x.apply(b,{position:i});if((0,f.B)(l,"discard")&&!t.x.isInRange(g))return null;var O=[{x:g,y:p+y},{x:g,y:p}];return"top"===a?O.reverse():O}if(n){var x=l.segment.map((function(e){return t.apply(e,{position:i})}));return(0,f.B)(l,"discard")&&c()(x,(function(e){return!t.isInRange(e)}))?null:x}return null}((0,d.Ky)({x:w.scale,y:j.scale}),(0,p.P2)(e),(0,p.P2)(r),o&&2===o.length,P,t.position,w.orientation,j.orientation,t);if(!A)return null;var E=k(A,2),T=E[0],I=T.x,D=T.y,M=E[1],N=M.x,B=M.y,L=S(S({clipPath:(0,f.B)(t,"hidden")?"url(#".concat(x,")"):void 0},(0,h.L6)(t,!0)),{},{x1:I,y1:D,x2:N,y2:B});return n.createElement(l.m,{className:(0,u.Z)("recharts-reference-line",g)},function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):i()(t)?t(e):n.createElement("line",C({},e,{className:"recharts-reference-line-line"}))}(b,L),s._.renderCallByParent(t,(0,d._b)({x1:I,y1:D,x2:N,y2:B})))}var D=function(t){function e(){return b(this,e),O(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&j(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){return n.createElement(I,this.props)}}])&&g(r.prototype,o),i&&g(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.Component);A(D,"displayName","ReferenceLine"),A(D,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"})},23007:function(t,e,r){r.d(e,{K:function(){return g}});var n=r(89526),o=r(90512),i=r(86545),a=r(27871),c=r(36530);function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function l(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function s(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,v(n.key),n)}}function f(t,e,r){return e=d(e),function(t,e){if(e&&("object"===u(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,p()?Reflect.construct(e,r||[],d(t).constructor):e.apply(t,r))}function p(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(p=function(){return!!t})()}function d(t){return d=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},d(t)}function y(t,e){return y=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},y(t,e)}function h(t,e,r){return(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}function m(){return m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},m.apply(this,arguments)}function b(t){var e=t.xAxisId,r=(0,i.zn)(),u=(0,i.Mw)(),l=(0,i.bH)(e);return null==l?null:n.createElement(a.O,m({},l,{className:(0,o.Z)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:r,height:u},ticksGenerator:function(t){return(0,c.uY)(t,!0)}}))}var g=function(t){function e(){return l(this,e),f(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&y(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){return n.createElement(b,this.props)}}])&&s(r.prototype,o),i&&s(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.Component);h(g,"displayName","XAxis"),h(g,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0})},58104:function(t,e,r){r.d(e,{B:function(){return g}});var n=r(89526),o=r(90512),i=r(86545),a=r(27871),c=r(36530);function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function l(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function s(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,v(n.key),n)}}function f(t,e,r){return e=d(e),function(t,e){if(e&&("object"===u(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,p()?Reflect.construct(e,r||[],d(t).constructor):e.apply(t,r))}function p(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(p=function(){return!!t})()}function d(t){return d=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},d(t)}function y(t,e){return y=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},y(t,e)}function h(t,e,r){return(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}function m(){return m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},m.apply(this,arguments)}var b=function(t){var e=t.yAxisId,r=(0,i.zn)(),u=(0,i.Mw)(),l=(0,i.Ud)(e);return null==l?null:n.createElement(a.O,m({},l,{className:(0,o.Z)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:r,height:u},ticksGenerator:function(t){return(0,c.uY)(t,!0)}}))},g=function(t){function e(){return l(this,e),f(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&y(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){return n.createElement(b,this.props)}}])&&s(r.prototype,o),i&&s(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.Component);h(g,"displayName","YAxis"),h(g,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1})},37561:function(t,e,r){r.d(e,{f:function(){return h}});var n=r(39277),o=r.n(n),i=r(16171),a=r(99875),c=r(59509),u=r(87210);function l(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e){if(void 0!==r&&!0!==r(t[o]))return;n.push(t[o])}return n}function s(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach((function(e){y(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function y(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h(t,e,r){var n=t.tick,f=t.ticks,p=t.viewBox,y=t.minTickGap,h=t.orientation,v=t.interval,m=t.tickFormatter,b=t.unit,g=t.angle;if(!f||!f.length||!n)return[];if((0,i.hj)(v)||c.x.isSsr)return function(t,e){return l(t,e+1)}(f,"number"===typeof v&&(0,i.hj)(v)?v:0);var O=[],x="top"===h||"bottom"===h?"width":"height",w=b&&"width"===x?(0,a.xE)(b,{fontSize:e,letterSpacing:r}):{width:0,height:0},j=function(t,n){var i=o()(m)?m(t.value,n):t.value;return"width"===x?function(t,e,r){var n={width:t.width+e.width,height:t.height+e.height};return(0,u.xE)(n,r)}((0,a.xE)(i,{fontSize:e,letterSpacing:r}),w,g):(0,a.xE)(i,{fontSize:e,letterSpacing:r})[x]},P=f.length>=2?(0,i.uY)(f[1].coordinate-f[0].coordinate):1,S=function(t,e,r){var n="width"===r,o=t.x,i=t.y,a=t.width,c=t.height;return 1===e?{start:n?o:i,end:n?o+a:i+c}:{start:n?o+a:i+c,end:n?o:i}}(p,P,x);return"equidistantPreserveStart"===v?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),c=e.start,u=e.end,f=0,p=1,d=c,y=function(){var e=null===n||void 0===n?void 0:n[f];if(void 0===e)return{v:l(n,p)};var i,a=f,y=function(){return void 0===i&&(i=r(e,a)),i},h=e.coordinate,v=0===f||s(t,h,y,d,u);v||(f=0,d=c,p+=1),v&&(d=h+t*(y()/2+o),f+=p)};p<=a.length;)if(i=y())return i.v;return[]}(P,S,j,f,y):(O="preserveStart"===v||"preserveStartEnd"===v?function(t,e,r,n,o,i){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(i){var f=n[c-1],p=r(f,c-1),y=t*(f.coordinate+t*p/2-l);a[c-1]=f=d(d({},f),{},{tickCoord:y>0?f.coordinate-y*t:f.coordinate}),s(t,f.tickCoord,(function(){return p}),u,l)&&(l=f.tickCoord-t*(p/2+o),a[c-1]=d(d({},f),{},{isShow:!0}))}for(var h=i?c-1:c,v=function(e){var n,i=a[e],c=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var f=t*(i.coordinate-t*c()/2-u);a[e]=i=d(d({},i),{},{tickCoord:f<0?i.coordinate-f*t:i.coordinate})}else a[e]=i=d(d({},i),{},{tickCoord:i.coordinate});s(t,i.tickCoord,c,u,l)&&(u=i.tickCoord+t*(c()/2+o),a[e]=d(d({},i),{},{isShow:!0}))},m=0;m<h;m++)v(m);return a}(P,S,j,f,y,"preserveStartEnd"===v):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=e.start,u=e.end,l=function(e){var n,l=i[e],f=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var p=t*(l.coordinate+t*f()/2-u);i[e]=l=d(d({},l),{},{tickCoord:p>0?l.coordinate-p*t:l.coordinate})}else i[e]=l=d(d({},l),{},{tickCoord:l.coordinate});s(t,l.tickCoord,f,c,u)&&(u=l.tickCoord-t*(f()/2+o),i[e]=d(d({},l),{},{isShow:!0}))},f=a-1;f>=0;f--)l(f);return i}(P,S,j,f,y),O.filter((function(t){return t.isShow})))}},79416:function(t,e,r){r.d(e,{v:function(){return u}});var n=r(88259),o=r(48218),i=r(23007),a=r(58104),c=r(87210),u=(0,n.z)({chartName:"BarChart",GraphicalChild:o.$,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:i.K},{axisType:"yAxis",AxisComp:a.B}],formatAxisMap:c.t9})},77434:function(t,e,r){r.d(e,{u:function(){return u}});var n=r(88259),o=r(10562),i=r(85322),a=r(80072),c=r(86246),u=(0,n.z)({chartName:"PieChart",GraphicalChild:c.b,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:o.I},{axisType:"radiusAxis",AxisComp:i.S}],formatAxisMap:a.t9,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}})},88259:function(t,e,r){r.d(e,{z:function(){return Rt}});var n=r(89526),o=r(51391),i=r.n(o),a=r(39277),c=r.n(a),u=r(58120),l=r.n(u),s=r(80089),f=r.n(s),p=r(65853),d=r.n(p),y=r(38172),h=r.n(y),v=r(90512),m=r(78109),b=r(93386),g=r(61452),O=r(18170),x=r(71015),w=r(96963),j=r(33951),P=r(9410),S=r(88974),A=r(99875),E=r(16171),k=r(36530),T=r(65436),C=r(7629),I=r(48586),D=r(93264),M=r(94694);function N(t){return function(t){if(Array.isArray(t))return B(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return B(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return B(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function B(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var L=function(t,e,r,n,o){var i=(0,P.NN)(t,I.d),a=(0,P.NN)(t,C.q),c=[].concat(N(i),N(a)),u=(0,P.NN)(t,D.z),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce((function(t,e){if(e.props[l]===r&&(0,M.B)(e.props,"extendDomain")&&(0,E.hj)(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t}),f)),u.length){var p="".concat(s,"1"),d="".concat(s,"2");f=u.reduce((function(t,e){if(e.props[l]===r&&(0,M.B)(e.props,"extendDomain")&&(0,E.hj)(e.props[p])&&(0,E.hj)(e.props[d])){var n=e.props[p],o=e.props[d];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t}),f)}return o&&o.length&&(f=o.reduce((function(t,e){return(0,E.hj)(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t}),f)),f},R=r(80072),z=r(68201),_=r(33034),F=new(r.n(_)()),W="recharts.syncMouseEvents",Z=r(33790);function K(t){return K="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},K(t)}function X(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,G(n.key),n)}}function V(t,e,r){return(e=G(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function G(t){var e=function(t,e){if("object"!=K(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=K(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==K(e)?e:e+""}var Y=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),V(this,"activeIndex",0),V(this,"coordinateList",[]),V(this,"layout","horizontal")},(e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!==n&&void 0!==n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!==i&&void 0!==i?i:this.container,this.layout=null!==c&&void 0!==c?c:this.layout,this.offset=null!==l&&void 0!==l?l:this.offset,this.mouseHandlerCallback=null!==f&&void 0!==f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){var t,e;if("horizontal"===this.layout&&0!==this.coordinateList.length){var r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,u=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=n+a+c,s=o+this.offset.top+i/2+u;this.mouseHandlerCallback({pageX:l,pageY:s})}}}])&&X(t.prototype,e),r&&X(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();var U=r(69531),H=r(92147),$=r(74791);function q(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[(0,R.op)(e,r,n,o),(0,R.op)(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}var Q=r(61001);function J(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return q(e);var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=(0,R.op)(c,u,l,f),d=(0,R.op)(c,u,s,f);n=p.x,o=p.y,i=d.x,a=d.y}return[{x:n,y:o},{x:i,y:a}]}function tt(t){return tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},tt(t)}function et(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function rt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?et(Object(r),!0).forEach((function(e){nt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):et(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function nt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=tt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tt(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ot(t){var e,r,o,i=t.element,a=t.tooltipEventType,c=t.isActive,u=t.activeCoordinate,l=t.activePayload,s=t.offset,f=t.activeTooltipIndex,p=t.tooltipAxisBandSize,d=t.layout,y=t.chartName,h=null!==(e=i.props.cursor)&&void 0!==e?e:null===(r=i.type.defaultProps)||void 0===r?void 0:r.cursor;if(!i||!h||!c||!u||"ScatterChart"!==y&&"axis"!==a)return null;var m=H.H;if("ScatterChart"===y)o=u,m=$.X;else if("BarChart"===y)o=function(t,e,r,n){var o=n/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===t?e.x-o:r.left+.5,y:"horizontal"===t?r.top+.5:e.y-o,width:"horizontal"===t?n:r.width-1,height:"horizontal"===t?r.height-1:n}}(d,u,s,p),m=j.A;else if("radial"===d){var b=q(u),g=b.cx,O=b.cy,x=b.radius;o={cx:g,cy:O,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:x,outerRadius:x},m=Q.L}else o={points:J(d,u,s)},m=H.H;var w=rt(rt(rt(rt({stroke:"#ccc",pointerEvents:"none"},s),o),(0,P.L6)(h,!1)),{},{payload:l,payloadIndex:f,className:(0,v.Z)("recharts-tooltip-cursor",h.className)});return(0,n.isValidElement)(h)?(0,n.cloneElement)(h,w):(0,n.createElement)(m,w)}var it=r(86545),at=["item"],ct=["children","className","width","height","style","compact","title","desc"];function ut(t){return ut="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ut(t)}function lt(){return lt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},lt.apply(this,arguments)}function st(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||bt(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ft(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function pt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,jt(n.key),n)}}function dt(t,e,r){return e=ht(e),function(t,e){if(e&&("object"===ut(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,yt()?Reflect.construct(e,r||[],ht(t).constructor):e.apply(t,r))}function yt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(yt=function(){return!!t})()}function ht(t){return ht=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ht(t)}function vt(t,e){return vt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},vt(t,e)}function mt(t){return function(t){if(Array.isArray(t))return gt(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||bt(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function bt(t,e){if(t){if("string"===typeof t)return gt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?gt(t,e):void 0}}function gt(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ot(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function xt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ot(Object(r),!0).forEach((function(e){wt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ot(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function wt(t,e,r){return(e=jt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function jt(t){var e=function(t,e){if("object"!=ut(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ut(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ut(e)?e:e+""}var Pt={xAxis:["bottom","top"],yAxis:["left","right"]},St={width:"100%",height:"100%"},At={x:0,y:0};function Et(t){return t}var kt=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!==r&&void 0!==r?r:[]).reduce((function(t,e){var r=e.props.data;return r&&r.length?[].concat(mt(t),mt(r)):t}),[]);return i.length>0?i:t&&t.length&&(0,E.hj)(n)&&(0,E.hj)(o)?t.slice(n,o+1):[]};function Tt(t){return"number"===t?[0,"auto"]:void 0}var Ct=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=kt(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce((function(o,c){var u,l,s=null!==(u=c.props.data)&&void 0!==u?u:e;if(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),i.dataKey&&!i.allowDuplicatedCategory){var f=void 0===s?a:s;l=(0,E.Ap)(f,i.dataKey,n)}else l=s&&s[r]||a[r];return l?[].concat(mt(o),[(0,k.Qo)(c,l)]):o}),[])},It=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i=function(t,e){return"horizontal"===e?t.x:"vertical"===e?t.y:"centric"===e?t.angle:t.radius}(o,r),a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=(0,k.VO)(i,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=Ct(t,e,l,s),p=function(t,e,r,n){var o=e.find((function(t){return t&&t.index===r}));if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return xt(xt(xt({},n),(0,R.op)(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,u=n.angle;return xt(xt(xt({},n),(0,R.op)(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return At}(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},Dt=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,a=e.axisIdKey,c=e.stackGroups,u=e.dataStartIndex,s=e.dataEndIndex,f=t.layout,p=t.children,d=t.stackOffset,y=(0,k.NA)(f,o);return r.reduce((function(e,r){var h,v=void 0!==r.type.defaultProps?xt(xt({},r.type.defaultProps),r.props):r.props,m=v.type,b=v.dataKey,g=v.allowDataOverflow,O=v.allowDuplicatedCategory,x=v.scale,w=v.ticks,j=v.includeHidden,P=v[a];if(e[P])return e;var S,A,T,C=kt(t.data,{graphicalItems:n.filter((function(t){var e;return(a in t.props?t.props[a]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[a])===P})),dataStartIndex:u,dataEndIndex:s}),I=C.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null===t||void 0===t?void 0:t[0],o=null===t||void 0===t?void 0:t[1];if(n&&o&&(0,E.hj)(n)&&(0,E.hj)(o))return!0}return!1})(v.domain,g,m)&&(S=(0,k.LG)(v.domain,null,g),!y||"number"!==m&&"auto"===x||(T=(0,k.gF)(C,b,"category")));var D=Tt(m);if(!S||0===S.length){var M,N=null!==(M=v.domain)&&void 0!==M?M:D;if(b){if(S=(0,k.gF)(C,b,m),"category"===m&&y){var B=(0,E.bv)(S);O&&B?(A=S,S=l()(0,I)):O||(S=(0,k.ko)(N,S,r).reduce((function(t,e){return t.indexOf(e)>=0?t:[].concat(mt(t),[e])}),[]))}else if("category"===m)S=O?S.filter((function(t){return""!==t&&!i()(t)})):(0,k.ko)(N,S,r).reduce((function(t,e){return t.indexOf(e)>=0||""===e||i()(e)?t:[].concat(mt(t),[e])}),[]);else if("number"===m){var R=(0,k.ZI)(C,n.filter((function(t){var e,r,n=a in t.props?t.props[a]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[a],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===P&&(j||!o)})),b,o,f);R&&(S=R)}!y||"number"!==m&&"auto"===x||(T=(0,k.gF)(C,b,"category"))}else S=y?l()(0,I):c&&c[P]&&c[P].hasStack&&"number"===m?"expand"===d?[0,1]:(0,k.EB)(c[P].stackGroups,u,s):(0,k.s6)(C,n.filter((function(t){var e=a in t.props?t.props[a]:t.type.defaultProps[a],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===P&&(j||!r)})),m,f,!0);if("number"===m)S=L(p,S,P,o,w),N&&(S=(0,k.LG)(N,S,g));else if("category"===m&&N){var z=N;S.every((function(t){return z.indexOf(t)>=0}))&&(S=z)}}return xt(xt({},e),{},wt({},P,xt(xt({},v),{},{axisType:o,domain:S,categoricalDomain:T,duplicateDomain:A,originalDomain:null!==(h=v.domain)&&void 0!==h?h:D,isCategorical:y,layout:f})))}),{})},Mt=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.children,p="".concat(n,"Id"),d=(0,P.NN)(s,o),y={};return d&&d.length?y=Dt(t,{axes:d,graphicalItems:i,axisType:n,axisIdKey:p,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):i&&i.length&&(y=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.layout,p=t.children,d=kt(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),y=d.length,h=(0,k.NA)(s,o),v=-1;return r.reduce((function(t,e){var m,b=(void 0!==e.type.defaultProps?xt(xt({},e.type.defaultProps),e.props):e.props)[i],g=Tt("number");return t[b]?t:(v++,h?m=l()(0,y):a&&a[b]&&a[b].hasStack?(m=(0,k.EB)(a[b].stackGroups,c,u),m=L(p,m,b,o)):(m=(0,k.LG)(g,(0,k.s6)(d,r.filter((function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===b&&!o})),"number",s),n.defaultProps.allowDataOverflow),m=L(p,m,b,o)),xt(xt({},t),{},wt({},b,xt(xt({axisType:o},n.defaultProps),{},{hide:!0,orientation:f()(Pt,"".concat(o,".").concat(v%2),null),domain:m,originalDomain:g,isCategorical:h,layout:s}))))}),{})}(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:p,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),y},Nt=function(t){var e=t.children,r=t.defaultShowTooltip,n=(0,P.sP)(e,S.B),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:Boolean(r)}},Bt=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},Lt=function(t,e){return"xAxis"===e?t[e].width:"yAxis"===e?t[e].height:void 0},Rt=function(t){var e=t.chartName,r=t.GraphicalChild,o=t.defaultTooltipEventType,a=void 0===o?"axis":o,u=t.validateTooltipEventTypes,l=void 0===u?["axis"]:u,s=t.axisComponents,p=t.legendContent,y=t.formatAxisMap,C=t.defaultProps,I=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,a=e.updateId,c=e.dataStartIndex,u=e.dataEndIndex,l=t.barSize,f=t.layout,p=t.barGap,d=t.barCategoryGap,y=t.maxBarSize,h=Bt(f),v=h.numericAxisName,b=h.cateAxisName,g=function(t){return!(!t||!t.length)&&t.some((function(t){var e=(0,P.Gf)(t&&t.type);return e&&e.indexOf("Bar")>=0}))}(r),O=[];return r.forEach((function(r,h){var x=kt(t.data,{graphicalItems:[r],dataStartIndex:c,dataEndIndex:u}),w=void 0!==r.type.defaultProps?xt(xt({},r.type.defaultProps),r.props):r.props,j=w.dataKey,S=w.maxBarSize,A=w["".concat(v,"Id")],E=w["".concat(b,"Id")],T=s.reduce((function(t,r){var n=e["".concat(r.axisType,"Map")],o=w["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||(0,m.Z)(!1);var i=n[o];return xt(xt({},t),{},wt(wt({},r.axisType,i),"".concat(r.axisType,"Ticks"),(0,k.uY)(i)))}),{}),C=T[b],I=T["".concat(b,"Ticks")],D=n&&n[A]&&n[A].hasStack&&(0,k.O3)(r,n[A].stackGroups),M=(0,P.Gf)(r.type).indexOf("Bar")>=0,N=(0,k.zT)(C,I),B=[],L=g&&(0,k.pt)({barSize:l,stackGroups:n,totalSize:Lt(T,b)});if(M){var R,z,_=i()(S)?y:S,F=null!==(R=null!==(z=(0,k.zT)(C,I,!0))&&void 0!==z?z:_)&&void 0!==R?R:0;B=(0,k.qz)({barGap:p,barCategoryGap:d,bandSize:F!==N?F:N,sizeList:L[E],maxBarSize:_}),F!==N&&(B=B.map((function(t){return xt(xt({},t),{},{position:xt(xt({},t.position),{},{offset:t.position.offset-F/2})})})))}var W=r&&r.type&&r.type.getComposedData;W&&O.push({props:xt(xt({},W(xt(xt({},T),{},{displayedData:x,props:t,dataKey:j,item:r,bandSize:N,barPosition:B,offset:o,stackedData:D,layout:f,dataStartIndex:c,dataEndIndex:u}))),{},wt(wt(wt({key:r.key||"item-".concat(h)},v,T[v]),b,T[b]),"animationId",a)),childIndex:(0,P.$R)(r,t.children),item:r})})),O},D=function(t,n){var o=t.props,i=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!(0,P.TT)({props:o}))return null;var u=o.children,l=o.layout,p=o.stackOffset,h=o.data,v=o.reverseStackOrder,m=Bt(l),b=m.numericAxisName,g=m.cateAxisName,O=(0,P.NN)(u,r),w=(0,k.wh)(h,O,"".concat(b,"Id"),"".concat(g,"Id"),p,v),j=s.reduce((function(t,e){var r="".concat(e.axisType,"Map");return xt(xt({},t),{},wt({},r,Mt(o,xt(xt({},e),{},{graphicalItems:O,stackGroups:e.axisType===b&&w,dataStartIndex:i,dataEndIndex:a}))))}),{}),A=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,p=r.margin||{},d=(0,P.sP)(s,S.B),y=(0,P.sP)(s,x.D),h=Object.keys(c).reduce((function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:xt(xt({},t),{},wt({},n,t[n]+r.width))}),{left:p.left||0,right:p.right||0}),v=Object.keys(i).reduce((function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:xt(xt({},t),{},wt({},n,f()(t,"".concat(n))+r.height))}),{top:p.top||0,bottom:p.bottom||0}),m=xt(xt({},v),h),b=m.bottom;d&&(m.bottom+=d.props.height||S.B.defaultProps.height),y&&e&&(m=(0,k.By)(m,n,r,e));var g=u-m.left-m.right,O=l-m.top-m.bottom;return xt(xt({brushBottom:b},m),{},{width:Math.max(g,0),height:Math.max(O,0)})}(xt(xt({},j),{},{props:o,graphicalItems:O}),null===n||void 0===n?void 0:n.legendBBox);Object.keys(j).forEach((function(t){j[t]=y(o,j[t],A,t.replace("Map",""),e)}));var T=function(t){var e=(0,E.Kt)(t),r=(0,k.uY)(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:d()(r,(function(t){return t.coordinate})),tooltipAxis:e,tooltipAxisBandSize:(0,k.zT)(e,r)}}(j["".concat(g,"Map")]),C=I(o,xt(xt({},j),{},{dataStartIndex:i,dataEndIndex:a,updateId:c,graphicalItems:O,stackGroups:w,offset:A}));return xt(xt({formattedGraphicalItems:C,graphicalItems:O,offset:A,stackGroups:w},T),j)},M=function(t){function r(t){var o,a,u;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,r),wt(u=dt(this,r,[t]),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),wt(u,"accessibilityManager",new Y),wt(u,"handleLegendBBoxUpdate",(function(t){if(t){var e=u.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;u.setState(xt({legendBBox:t},D({props:u.props,dataStartIndex:r,dataEndIndex:n,updateId:o},xt(xt({},u.state),{},{legendBBox:t}))))}})),wt(u,"handleReceiveSyncEvent",(function(t,e,r){if(u.props.syncId===t){if(r===u.eventEmitterSymbol&&"function"!==typeof u.props.syncMethod)return;u.applySyncEvent(e)}})),wt(u,"handleBrushChange",(function(t){var e=t.startIndex,r=t.endIndex;if(e!==u.state.dataStartIndex||r!==u.state.dataEndIndex){var n=u.state.updateId;u.setState((function(){return xt({dataStartIndex:e,dataEndIndex:r},D({props:u.props,dataStartIndex:e,dataEndIndex:r,updateId:n},u.state))})),u.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}})),wt(u,"handleMouseEnter",(function(t){var e=u.getMouseInfo(t);if(e){var r=xt(xt({},e),{},{isTooltipActive:!0});u.setState(r),u.triggerSyncEvent(r);var n=u.props.onMouseEnter;c()(n)&&n(r,t)}})),wt(u,"triggeredAfterMouseMove",(function(t){var e=u.getMouseInfo(t),r=e?xt(xt({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};u.setState(r),u.triggerSyncEvent(r);var n=u.props.onMouseMove;c()(n)&&n(r,t)})),wt(u,"handleItemMouseEnter",(function(t){u.setState((function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}}))})),wt(u,"handleItemMouseLeave",(function(){u.setState((function(){return{isTooltipActive:!1}}))})),wt(u,"handleMouseMove",(function(t){t.persist(),u.throttleTriggeredAfterMouseMove(t)})),wt(u,"handleMouseLeave",(function(t){u.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};u.setState(e),u.triggerSyncEvent(e);var r=u.props.onMouseLeave;c()(r)&&r(e,t)})),wt(u,"handleOuterEvent",(function(t){var e,r=(0,P.Bh)(t),n=f()(u.props,"".concat(r));r&&c()(n)&&n(null!==(e=/.*touch.*/i.test(r)?u.getMouseInfo(t.changedTouches[0]):u.getMouseInfo(t))&&void 0!==e?e:{},t)})),wt(u,"handleClick",(function(t){var e=u.getMouseInfo(t);if(e){var r=xt(xt({},e),{},{isTooltipActive:!0});u.setState(r),u.triggerSyncEvent(r);var n=u.props.onClick;c()(n)&&n(r,t)}})),wt(u,"handleMouseDown",(function(t){var e=u.props.onMouseDown;c()(e)&&e(u.getMouseInfo(t),t)})),wt(u,"handleMouseUp",(function(t){var e=u.props.onMouseUp;c()(e)&&e(u.getMouseInfo(t),t)})),wt(u,"handleTouchMove",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.throttleTriggeredAfterMouseMove(t.changedTouches[0])})),wt(u,"handleTouchStart",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.handleMouseDown(t.changedTouches[0])})),wt(u,"handleTouchEnd",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.handleMouseUp(t.changedTouches[0])})),wt(u,"handleDoubleClick",(function(t){var e=u.props.onDoubleClick;c()(e)&&e(u.getMouseInfo(t),t)})),wt(u,"handleContextMenu",(function(t){var e=u.props.onContextMenu;c()(e)&&e(u.getMouseInfo(t),t)})),wt(u,"triggerSyncEvent",(function(t){void 0!==u.props.syncId&&F.emit(W,u.props.syncId,t,u.eventEmitterSymbol)})),wt(u,"applySyncEvent",(function(t){var e=u.props,r=e.layout,n=e.syncMethod,o=u.state.updateId,i=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)u.setState(xt({dataStartIndex:i,dataEndIndex:a},D({props:u.props,dataStartIndex:i,dataEndIndex:a,updateId:o},u.state)));else if(void 0!==t.activeTooltipIndex){var c=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=u.state,p=f.offset,d=f.tooltipTicks;if(!p)return;if("function"===typeof n)s=n(d,t);else if("value"===n){s=-1;for(var y=0;y<d.length;y++)if(d[y].value===t.activeLabel){s=y;break}}var h=xt(xt({},p),{},{x:p.left,y:p.top}),v=Math.min(c,h.x+h.width),m=Math.min(l,h.y+h.height),b=d[s]&&d[s].value,g=Ct(u.state,u.props.data,s),O=d[s]?{x:"horizontal"===r?d[s].coordinate:v,y:"horizontal"===r?m:d[s].coordinate}:At;u.setState(xt(xt({},t),{},{activeLabel:b,activeCoordinate:O,activePayload:g,activeTooltipIndex:s}))}else u.setState(t)})),wt(u,"renderCursor",(function(t){var r,o=u.state,i=o.isTooltipActive,a=o.activeCoordinate,c=o.activePayload,l=o.offset,s=o.activeTooltipIndex,f=o.tooltipAxisBandSize,p=u.getTooltipEventType(),d=null!==(r=t.props.active)&&void 0!==r?r:i,y=u.props.layout,h=t.key||"_recharts-cursor";return n.createElement(ot,{key:h,activeCoordinate:a,activePayload:c,activeTooltipIndex:s,chartName:e,element:t,isActive:d,layout:y,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})})),wt(u,"renderPolarAxis",(function(t,e,r){var o=f()(t,"type.axisType"),i=f()(u.state,"".concat(o,"Map")),a=t.type.defaultProps,c=void 0!==a?xt(xt({},a),t.props):t.props,l=i&&i[c["".concat(o,"Id")]];return(0,n.cloneElement)(t,xt(xt({},l),{},{className:(0,v.Z)(o,l.className),key:t.key||"".concat(e,"-").concat(r),ticks:(0,k.uY)(l,!0)}))})),wt(u,"renderPolarGrid",(function(t){var e=t.props,r=e.radialLines,o=e.polarAngles,i=e.polarRadius,a=u.state,c=a.radiusAxisMap,l=a.angleAxisMap,s=(0,E.Kt)(c),f=(0,E.Kt)(l),p=f.cx,d=f.cy,y=f.innerRadius,h=f.outerRadius;return(0,n.cloneElement)(t,{polarAngles:Array.isArray(o)?o:(0,k.uY)(f,!0).map((function(t){return t.coordinate})),polarRadius:Array.isArray(i)?i:(0,k.uY)(s,!0).map((function(t){return t.coordinate})),cx:p,cy:d,innerRadius:y,outerRadius:h,key:t.key||"polar-grid",radialLines:r})})),wt(u,"renderLegend",(function(){var t=u.state.formattedGraphicalItems,e=u.props,r=e.children,o=e.width,i=e.height,a=u.props.margin||{},c=o-(a.left||0)-(a.right||0),l=(0,T.z)({children:r,formattedGraphicalItems:t,legendWidth:c,legendContent:p});if(!l)return null;var s=l.item,f=ft(l,at);return(0,n.cloneElement)(s,xt(xt({},f),{},{chartWidth:o,chartHeight:i,margin:a,onBBoxUpdate:u.handleLegendBBoxUpdate}))})),wt(u,"renderTooltip",(function(){var t,e=u.props,r=e.children,o=e.accessibilityLayer,i=(0,P.sP)(r,O.u);if(!i)return null;var a=u.state,c=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,d=null!==(t=i.props.active)&&void 0!==t?t:c;return(0,n.cloneElement)(i,{viewBox:xt(xt({},p),{},{x:p.left,y:p.top}),active:d,label:f,payload:d?s:[],coordinate:l,accessibilityLayer:o})})),wt(u,"renderBrush",(function(t){var e=u.props,r=e.margin,o=e.data,i=u.state,a=i.offset,c=i.dataStartIndex,l=i.dataEndIndex,s=i.updateId;return(0,n.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:(0,k.DO)(u.handleBrushChange,t.props.onChange),data:o,x:(0,E.hj)(t.props.x)?t.props.x:a.left,y:(0,E.hj)(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:(0,E.hj)(t.props.width)?t.props.width:a.width,startIndex:c,endIndex:l,updateId:"brush-".concat(s)})})),wt(u,"renderReferenceElement",(function(t,e,r){if(!t)return null;var o=u.clipPathId,i=u.state,a=i.xAxisMap,c=i.yAxisMap,l=i.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,d=void 0===p?s.xAxisId:p,y=f.yAxisId,h=void 0===y?s.yAxisId:y;return(0,n.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[d],yAxis:c[h],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:o})})),wt(u,"renderActivePoints",(function(t){var e=t.item,n=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,c=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?xt(xt({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=xt(xt({index:i,dataKey:l.dataKey,cx:n.x,cy:n.y,r:4,fill:(0,k.fk)(e.item),strokeWidth:2,stroke:"#fff",payload:n.payload,value:n.value},(0,P.L6)(s,!1)),(0,Z.Ym)(s));return c.push(r.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(i))),o?c.push(r.renderActiveDot(s,xt(xt({},f),{},{cx:o.x,cy:o.y}),"".concat(u,"-basePoint-").concat(i))):a&&c.push(null),c})),wt(u,"renderGraphicChild",(function(t,e,r){var o=u.filterFormatItem(t,e,r);if(!o)return null;var a=u.getTooltipEventType(),c=u.state,l=c.isTooltipActive,s=c.tooltipAxis,f=c.activeTooltipIndex,p=c.activeLabel,d=u.props.children,y=(0,P.sP)(d,O.u),h=o.props,v=h.points,m=h.isRange,b=h.baseLine,g=void 0!==o.item.type.defaultProps?xt(xt({},o.item.type.defaultProps),o.item.props):o.item.props,x=g.activeDot,w=g.hide,j=g.activeBar,S=g.activeShape,A=Boolean(!w&&l&&y&&(x||j||S)),T={};"axis"!==a&&y&&"click"===y.props.trigger?T={onClick:(0,k.DO)(u.handleItemMouseEnter,t.props.onClick)}:"axis"!==a&&(T={onMouseLeave:(0,k.DO)(u.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:(0,k.DO)(u.handleItemMouseEnter,t.props.onMouseEnter)});var C=(0,n.cloneElement)(t,xt(xt({},o.props),T));if(A){if(!(f>=0)){var I,D=(null!==(I=u.getItemByXY(u.state.activeCoordinate))&&void 0!==I?I:{graphicalItem:C}).graphicalItem,M=D.item,N=void 0===M?t:M,B=D.childIndex,L=xt(xt(xt({},o.props),T),{},{activeIndex:B});return[(0,n.cloneElement)(N,L),null,null]}var R,z;if(s.dataKey&&!s.allowDuplicatedCategory){var _="function"===typeof s.dataKey?function(t){return"function"===typeof s.dataKey?s.dataKey(t.payload):null}:"payload.".concat(s.dataKey.toString());R=(0,E.Ap)(v,_,p),z=m&&b&&(0,E.Ap)(b,_,p)}else R=null===v||void 0===v?void 0:v[f],z=m&&b&&b[f];if(S||j){var F=void 0!==t.props.activeIndex?t.props.activeIndex:f;return[(0,n.cloneElement)(t,xt(xt(xt({},o.props),T),{},{activeIndex:F})),null,null]}if(!i()(R))return[C].concat(mt(u.renderActivePoints({item:o,activePoint:R,basePoint:z,childIndex:f,isRange:m})))}return m?[C,null,null]:[C,null]})),wt(u,"renderCustomized",(function(t,e,r){return(0,n.cloneElement)(t,xt(xt({key:"recharts-customized-".concat(r)},u.props),u.state))})),wt(u,"renderMap",{CartesianGrid:{handler:Et,once:!0},ReferenceArea:{handler:u.renderReferenceElement},ReferenceLine:{handler:Et},ReferenceDot:{handler:u.renderReferenceElement},XAxis:{handler:Et},YAxis:{handler:Et},Brush:{handler:u.renderBrush,once:!0},Bar:{handler:u.renderGraphicChild},Line:{handler:u.renderGraphicChild},Area:{handler:u.renderGraphicChild},Radar:{handler:u.renderGraphicChild},RadialBar:{handler:u.renderGraphicChild},Scatter:{handler:u.renderGraphicChild},Pie:{handler:u.renderGraphicChild},Funnel:{handler:u.renderGraphicChild},Tooltip:{handler:u.renderCursor,once:!0},PolarGrid:{handler:u.renderPolarGrid,once:!0},PolarAngleAxis:{handler:u.renderPolarAxis},PolarRadiusAxis:{handler:u.renderPolarAxis},Customized:{handler:u.renderCustomized}}),u.clipPathId="".concat(null!==(o=t.id)&&void 0!==o?o:(0,E.EL)("recharts"),"-clip"),u.throttleTriggeredAfterMouseMove=h()(u.triggeredAfterMouseMove,null!==(a=t.throttleDelay)&&void 0!==a?a:1e3/60),u.state={},u}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&vt(t,e)}(r,t),o=r,u=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=(0,P.sP)(e,O.u);if(i){var a=i.props.defaultIndex;if(!("number"!==typeof a||a<0||a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=Ct(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find((function(t){return"Scatter"===t.item.type.name}));p&&(f=xt(xt({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var d={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(d),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){return this.props.accessibilityLayer?(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin&&this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}}),null):null;var r,n}},{key:"componentDidUpdate",value:function(t){(0,P.rL)([(0,P.sP)(t.children,O.u)],[(0,P.sP)(this.props.children,O.u)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=(0,P.sP)(this.props.children,O.u);if(t&&"boolean"===typeof t.props.shared){var e=t.props.shared?"axis":"item";return l.indexOf(e)>=0?e:a}return a}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n=(0,A.os)(r),o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap,s=this.getTooltipEventType(),f=It(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&u&&l){var p=(0,E.Kt)(u).scale,d=(0,E.Kt)(l).scale,y=p&&p.invert?p.invert(o.chartX):null,h=d&&d.invert?d.invert(o.chartY):null;return xt(xt({},o),{},{xValue:y,yValue:h},f)}return f?xt(xt({},o),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset,c=o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height;return c?{x:o,y:i}:null}var u=this.state,l=u.angleAxisMap,s=u.radiusAxisMap;if(l&&s){var f=(0,E.Kt)(l);return(0,R.z3)({x:o,y:i},f)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=(0,P.sP)(t,O.u),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),xt(xt({},(0,Z.Ym)(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){F.on(W,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){F.removeListener(W,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===(0,P.Gf)(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,o=e.top,i=e.height,a=e.width;return n.createElement("defs",null,n.createElement("clipPath",{id:t},n.createElement("rect",{x:r,y:o,height:i,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce((function(t,e){var r=st(e,2),n=r[0],o=r[1];return xt(xt({},t),{},wt({},n,o.scale))}),{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce((function(t,e){var r=st(e,2),n=r[0],o=r[1];return xt(xt({},t),{},wt({},n,o.scale))}),{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?xt(xt({},u.type.defaultProps),u.props):u.props,s=(0,P.Gf)(u.type);if("Bar"===s){var f=(c.data||[]).find((function(e){return(0,j.X)(t,e)}));if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find((function(e){return(0,R.z3)(t,e)}));if(p)return{graphicalItem:a,payload:p}}else if((0,U.lT)(a,n)||(0,U.V$)(a,n)||(0,U.w7)(a,n)){var d=(0,U.a3)({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),y=void 0===l.activeIndex?d:l.activeIndex;return{graphicalItem:xt(xt({},a),{},{childIndex:y}),payload:(0,U.w7)(a,n)?l.data[d]:a.props.data[d]}}}return null}},{key:"render",value:function(){var t=this;if(!(0,P.TT)(this))return null;var e,r,o=this.props,i=o.children,a=o.className,c=o.width,u=o.height,l=o.style,s=o.compact,f=o.title,p=o.desc,d=ft(o,ct),y=(0,P.L6)(d,!1);if(s)return n.createElement(it.br,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement(b.T,lt({},y,{width:c,height:u,title:f,desc:p}),this.renderClipPath(),(0,P.eu)(i,this.renderMap)));this.props.accessibilityLayer&&(y.tabIndex=null!==(e=this.props.tabIndex)&&void 0!==e?e:0,y.role=null!==(r=this.props.role)&&void 0!==r?r:"application",y.onKeyDown=function(e){t.accessibilityManager.keyboardEvent(e)},y.onFocus=function(){t.accessibilityManager.focus()});var h=this.parseEventsOfWrapper();return n.createElement(it.br,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement("div",lt({className:(0,v.Z)("recharts-wrapper",a),style:xt({position:"relative",cursor:"default",width:c,height:u},l)},h,{ref:function(e){t.container=e}}),n.createElement(b.T,lt({},y,{width:c,height:u,title:f,desc:p,style:St}),this.renderClipPath(),(0,P.eu)(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],u&&pt(o.prototype,u),s&&pt(o,s),Object.defineProperty(o,"prototype",{writable:!1}),o;var o,u,s}(n.Component);wt(M,"displayName",e),wt(M,"defaultProps",xt({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},C)),wt(M,"getDerivedStateFromProps",(function(t,e){var r=t.dataKey,n=t.data,o=t.children,a=t.width,c=t.height,u=t.layout,l=t.stackOffset,s=t.margin,f=e.dataStartIndex,p=e.dataEndIndex;if(void 0===e.updateId){var d=Nt(t);return xt(xt(xt({},d),{},{updateId:0},D(xt(xt({props:t},d),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:c,prevLayout:u,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||a!==e.prevWidth||c!==e.prevHeight||u!==e.prevLayout||l!==e.prevStackOffset||!(0,z.w)(s,e.prevMargin)){var y=Nt(t),h={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=xt(xt({},It(e,n,u)),{},{updateId:e.updateId+1}),m=xt(xt(xt({},y),h),v);return xt(xt(xt({},m),D(xt({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:c,prevLayout:u,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(!(0,P.rL)(o,e.prevChildren)){var b,g,O,x,w=(0,P.sP)(o,S.B),j=w&&null!==(b=null===(g=w.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:f,A=w&&null!==(O=null===(x=w.props)||void 0===x?void 0:x.endIndex)&&void 0!==O?O:p,E=j!==f||A!==p,k=!i()(n)&&!E?e.updateId:e.updateId+1;return xt(xt({updateId:k},D(xt(xt({props:t},e),{},{updateId:k,dataStartIndex:j,dataEndIndex:A}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:A})}return null})),wt(M,"renderActiveDot",(function(t,e,r){var o;return o=(0,n.isValidElement)(t)?(0,n.cloneElement)(t,e):c()(t)?t(e):n.createElement(w.o,e),n.createElement(g.m,{className:"recharts-active-dot",key:r},o)}));var N=(0,n.forwardRef)((function(t,e){return n.createElement(M,lt({},t,{ref:e}))}));return N.displayName=M.displayName,N}},32214:function(t,e,r){r.d(e,{b:function(){return n}});var n=function(t){return null};n.displayName="Cell"},58935:function(t,e,r){r.d(e,{g:function(){return j}});var n=r(89526),o=r(39277),i=r.n(o),a=r(90512),c=r(78706),u=r(93386),l=r(71746),s=r(33790);function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(){return p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},p.apply(this,arguments)}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function y(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function h(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,x(n.key),n)}}function v(t,e,r){return e=b(e),function(t,e){if(e&&("object"===f(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,m()?Reflect.construct(e,r||[],b(t).constructor):e.apply(t,r))}function m(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(m=function(){return!!t})()}function b(t){return b=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},b(t)}function g(t,e){return g=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},g(t,e)}function O(t,e,r){return(e=x(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function x(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var w=32,j=function(t){function e(){return y(this,e),v(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&g(t,e)}(e,t),r=e,o=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=16,o=w/6,i=w/3,a=t.inactive?e:t.color;if("plainline"===t.type)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:a,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:r,x2:w,y2:r,className:"recharts-legend-icon"});if("line"===t.type)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:a,d:"M0,".concat(r,"h").concat(i,"\n            A").concat(o,",").concat(o,",0,1,1,").concat(2*i,",").concat(r,"\n            H").concat(w,"M").concat(2*i,",").concat(r,"\n            A").concat(o,",").concat(o,",0,1,1,").concat(i,",").concat(r),className:"recharts-legend-icon"});if("rect"===t.type)return n.createElement("path",{stroke:"none",fill:a,d:"M0,".concat(4,"h").concat(w,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(t.legendIcon)){var c=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach((function(e){O(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},t);return delete c.legendIcon,n.cloneElement(t.legendIcon,c)}return n.createElement(l.v,{fill:a,cx:r,cy:r,size:w,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,o=e.iconSize,l=e.layout,f=e.formatter,d=e.inactiveColor,y={x:0,y:0,width:w,height:w},h={display:"horizontal"===l?"inline-block":"block",marginRight:10},v={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map((function(e,r){var l=e.formatter||f,m=(0,a.Z)(O(O({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var b=i()(e.value)?null:e.value;(0,c.Z)(!i()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var g=e.inactive?d:e.color;return n.createElement("li",p({className:m,style:h,key:"legend-item-".concat(r)},(0,s.bw)(t.props,e,r)),n.createElement(u.T,{width:o,height:o,viewBox:y,style:v},t.renderIcon(e)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:g}},l?l(b,e,r):b))}))}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,o=t.align;if(!e||!e.length)return null;var i={padding:0,margin:0,textAlign:"horizontal"===r?o:"left"};return n.createElement("ul",{className:"recharts-default-legend",style:i},this.renderItems())}}],o&&h(r.prototype,o),f&&h(r,f),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,f}(n.PureComponent);O(j,"displayName","Legend"),O(j,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"})},69100:function(t,e,r){r.d(e,{x:function(){return b}});var n=r(89526),o=r(65853),i=r.n(o),a=r(51391),c=r.n(a),u=r(90512),l=r(16171);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function f(){return f=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},f.apply(this,arguments)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return d(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function y(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?y(Object(r),!0).forEach((function(e){v(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function v(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function m(t){return Array.isArray(t)&&(0,l.P2)(t[0])&&(0,l.P2)(t[1])?t.join(" ~ "):t}var b=function(t){var e=t.separator,r=void 0===e?" : ":e,o=t.contentStyle,a=void 0===o?{}:o,s=t.itemStyle,d=void 0===s?{}:s,y=t.labelStyle,v=void 0===y?{}:y,b=t.payload,g=t.formatter,O=t.itemSorter,x=t.wrapperClassName,w=t.labelClassName,j=t.label,P=t.labelFormatter,S=t.accessibilityLayer,A=void 0!==S&&S,E=h({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),k=h({margin:0},v),T=!c()(j),C=T?j:"",I=(0,u.Z)("recharts-default-tooltip",x),D=(0,u.Z)("recharts-tooltip-label",w);T&&P&&void 0!==b&&null!==b&&(C=P(j,b));var M=A?{role:"status","aria-live":"assertive"}:{};return n.createElement("div",f({className:I,style:E},M),n.createElement("p",{className:D,style:k},n.isValidElement(C)?C:"".concat(C)),function(){if(b&&b.length){var t=(O?i()(b,O):b).map((function(t,e){if("none"===t.type)return null;var o=h({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},d),i=t.formatter||g||m,a=t.value,c=t.name,u=a,s=c;if(i&&null!=u&&null!=s){var f=i(a,c,t,e,b);if(Array.isArray(f)){var y=p(f,2);u=y[0],s=y[1]}else u=f}return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:o},(0,l.P2)(s)?n.createElement("span",{className:"recharts-tooltip-item-name"},s):null,(0,l.P2)(s)?n.createElement("span",{className:"recharts-tooltip-item-separator"},r):null,n.createElement("span",{className:"recharts-tooltip-item-value"},u),n.createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))}));return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())}},43774:function(t,e,r){r.d(e,{_:function(){return S}});var n=r(89526),o=r(51391),i=r.n(o),a=r(39277),c=r.n(a),u=r(23619),l=r.n(u),s=r(90512),f=r(49266),p=r(9410),d=r(16171),y=r(80072);function h(t){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},h(t)}var v=["offset"];function m(t){return function(t){if(Array.isArray(t))return b(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return b(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return b(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function O(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?O(Object(r),!0).forEach((function(e){w(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function w(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=h(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=h(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==h(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function j(){return j=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},j.apply(this,arguments)}var P=function(t,e,r){var o,a,c=t.position,u=t.viewBox,l=t.offset,f=t.className,p=u,h=p.cx,v=p.cy,m=p.innerRadius,b=p.outerRadius,g=p.startAngle,O=p.endAngle,x=p.clockWise,w=(m+b)/2,P=function(t,e){return(0,d.uY)(e-t)*Math.min(Math.abs(e-t),360)}(g,O),S=P>=0?1:-1;"insideStart"===c?(o=g+S*l,a=x):"insideEnd"===c?(o=O-S*l,a=!x):"end"===c&&(o=O+S*l,a=x),a=P<=0?a:!a;var A=(0,y.op)(h,v,w,o),E=(0,y.op)(h,v,w,o+359*(a?1:-1)),k="M".concat(A.x,",").concat(A.y,"\n    A").concat(w,",").concat(w,",0,1,").concat(a?0:1,",\n    ").concat(E.x,",").concat(E.y),T=i()(t.id)?(0,d.EL)("recharts-radial-line-"):t.id;return n.createElement("text",j({},r,{dominantBaseline:"central",className:(0,s.Z)("recharts-radial-bar-label",f)}),n.createElement("defs",null,n.createElement("path",{id:T,d:k})),n.createElement("textPath",{xlinkHref:"#".concat(T)},e))};function S(t){var e,r=t.offset,o=x({offset:void 0===r?5:r},g(t,v)),a=o.viewBox,u=o.position,h=o.value,m=o.children,b=o.content,O=o.className,w=void 0===O?"":O,S=o.textBreakAll;if(!a||i()(h)&&i()(m)&&!(0,n.isValidElement)(b)&&!c()(b))return null;if((0,n.isValidElement)(b))return(0,n.cloneElement)(b,o);if(c()(b)){if(e=(0,n.createElement)(b,o),(0,n.isValidElement)(e))return e}else e=function(t){var e=t.value,r=t.formatter,n=i()(t.children)?e:t.children;return c()(r)?r(n):n}(o);var A=function(t){return"cx"in t&&(0,d.hj)(t.cx)}(a),E=(0,p.L6)(o,!0);if(A&&("insideStart"===u||"insideEnd"===u||"end"===u))return P(o,e,E);var k=A?function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e,i=o.cx,a=o.cy,c=o.innerRadius,u=o.outerRadius,l=(o.startAngle+o.endAngle)/2;if("outside"===n){var s=(0,y.op)(i,a,u+r,l),f=s.x;return{x:f,y:s.y,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var p=(c+u)/2,d=(0,y.op)(i,a,p,l);return{x:d.x,y:d.y,textAnchor:"middle",verticalAnchor:"middle"}}(o):function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e,a=i.x,c=i.y,u=i.width,s=i.height,f=s>=0?1:-1,p=f*n,y=f>0?"end":"start",h=f>0?"start":"end",v=u>=0?1:-1,m=v*n,b=v>0?"end":"start",g=v>0?"start":"end";if("top"===o)return x(x({},{x:a+u/2,y:c-f*n,textAnchor:"middle",verticalAnchor:y}),r?{height:Math.max(c-r.y,0),width:u}:{});if("bottom"===o)return x(x({},{x:a+u/2,y:c+s+p,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(c+s),0),width:u}:{});if("left"===o){var O={x:a-m,y:c+s/2,textAnchor:b,verticalAnchor:"middle"};return x(x({},O),r?{width:Math.max(O.x-r.x,0),height:s}:{})}if("right"===o){var w={x:a+u+m,y:c+s/2,textAnchor:g,verticalAnchor:"middle"};return x(x({},w),r?{width:Math.max(r.x+r.width-w.x,0),height:s}:{})}var j=r?{width:u,height:s}:{};return"insideLeft"===o?x({x:a+m,y:c+s/2,textAnchor:g,verticalAnchor:"middle"},j):"insideRight"===o?x({x:a+u-m,y:c+s/2,textAnchor:b,verticalAnchor:"middle"},j):"insideTop"===o?x({x:a+u/2,y:c+p,textAnchor:"middle",verticalAnchor:h},j):"insideBottom"===o?x({x:a+u/2,y:c+s-p,textAnchor:"middle",verticalAnchor:y},j):"insideTopLeft"===o?x({x:a+m,y:c+p,textAnchor:g,verticalAnchor:h},j):"insideTopRight"===o?x({x:a+u-m,y:c+p,textAnchor:b,verticalAnchor:h},j):"insideBottomLeft"===o?x({x:a+m,y:c+s-p,textAnchor:g,verticalAnchor:y},j):"insideBottomRight"===o?x({x:a+u-m,y:c+s-p,textAnchor:b,verticalAnchor:y},j):l()(o)&&((0,d.hj)(o.x)||(0,d.hU)(o.x))&&((0,d.hj)(o.y)||(0,d.hU)(o.y))?x({x:a+(0,d.h1)(o.x,u),y:c+(0,d.h1)(o.y,s),textAnchor:"end",verticalAnchor:"end"},j):x({x:a+u/2,y:c+s/2,textAnchor:"middle",verticalAnchor:"middle"},j)}(o);return n.createElement(f.x,j({className:(0,s.Z)("recharts-label",w)},E,k,{breakAll:S}),e)}S.displayName="Label";var A=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,y=t.left,h=t.width,v=t.height,m=t.clockWise,b=t.labelViewBox;if(b)return b;if((0,d.hj)(h)&&(0,d.hj)(v)){if((0,d.hj)(s)&&(0,d.hj)(f))return{x:s,y:f,width:h,height:v};if((0,d.hj)(p)&&(0,d.hj)(y))return{x:p,y:y,width:h,height:v}}return(0,d.hj)(s)&&(0,d.hj)(f)?{x:s,y:f,width:0,height:0}:(0,d.hj)(e)&&(0,d.hj)(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:m}:t.viewBox?t.viewBox:{}},E=function(t,e){return t?!0===t?n.createElement(S,{key:"label-implicit",viewBox:e}):(0,d.P2)(t)?n.createElement(S,{key:"label-implicit",viewBox:e,value:t}):(0,n.isValidElement)(t)?t.type===S?(0,n.cloneElement)(t,{key:"label-implicit",viewBox:e}):n.createElement(S,{key:"label-implicit",content:t,viewBox:e}):c()(t)?n.createElement(S,{key:"label-implicit",content:t,viewBox:e}):l()(t)?n.createElement(S,j({viewBox:e},t,{key:"label-implicit"})):null:null};S.parseViewBox=A,S.renderCallByParent=function(t,e){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&r&&!t.label)return null;var o=t.children,i=A(t),a=(0,p.NN)(o,S).map((function(t,r){return(0,n.cloneElement)(t,{viewBox:e||i,key:"label-".concat(r)})}));if(!r)return a;var c=E(t.label,e||i);return[c].concat(m(a))}},34324:function(t,e,r){r.d(e,{e:function(){return E}});var n=r(89526),o=r(51391),i=r.n(o),a=r(23619),c=r.n(a),u=r(39277),l=r.n(u),s=r(80275),f=r.n(s),p=r(43774),d=r(61452),y=r(9410),h=r(36530);function v(t){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(t)}var m=["valueAccessor"],b=["data","dataKey","clockWise","id","textBreakAll"];function g(t){return function(t){if(Array.isArray(t))return O(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return O(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return O(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function x(){return x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},x.apply(this,arguments)}function w(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?w(Object(r),!0).forEach((function(e){P(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function P(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function S(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var A=function(t){return Array.isArray(t.value)?f()(t.value):t.value};function E(t){var e=t.valueAccessor,r=void 0===e?A:e,o=S(t,m),a=o.data,c=o.dataKey,u=o.clockWise,l=o.id,s=o.textBreakAll,f=S(o,b);return a&&a.length?n.createElement(d.m,{className:"recharts-label-list"},a.map((function(t,e){var o=i()(c)?r(t,e):(0,h.F$)(t&&t.payload,c),a=i()(l)?{}:{id:"".concat(l,"-").concat(e)};return n.createElement(p._,x({},(0,y.L6)(t,!0),f,a,{parentViewBox:t.parentViewBox,value:o,textBreakAll:s,viewBox:p._.parseViewBox(i()(u)?t:j(j({},t),{},{clockWise:u})),key:"label-".concat(e),index:e}))}))):null}function k(t,e){return t?!0===t?n.createElement(E,{key:"labelList-implicit",data:e}):n.isValidElement(t)||l()(t)?n.createElement(E,{key:"labelList-implicit",data:e,content:t}):c()(t)?n.createElement(E,x({data:e},t,{key:"labelList-implicit"})):null:null}E.displayName="LabelList",E.renderCallByParent=function(t,e){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&r&&!t.label)return null;var o=t.children,i=(0,y.NN)(o,E).map((function(t,r){return(0,n.cloneElement)(t,{data:e,key:"labelList-".concat(r)})}));if(!r)return i;var a=k(t.label,e);return[a].concat(g(i))}},71015:function(t,e,r){r.d(e,{D:function(){return x}});var n=r(89526),o=r(58935),i=r(16171),a=r(56062);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}var u=["ref"];function l(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function s(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l(Object(r),!0).forEach((function(e){m(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function f(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function p(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,b(n.key),n)}}function d(t,e,r){return e=h(e),function(t,e){if(e&&("object"===c(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,y()?Reflect.construct(e,r||[],h(t).constructor):e.apply(t,r))}function y(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(y=function(){return!!t})()}function h(t){return h=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},h(t)}function v(t,e){return v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},v(t,e)}function m(t,e,r){return(e=b(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function b(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}function g(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function O(t){return t.value}var x=function(t){function e(){var t;f(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return m(t=d(this,e,[].concat(n)),"lastBoundingBox",{width:-1,height:-1}),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&v(t,e)}(e,t),r=e,l=[{key:"getWithHeight",value:function(t,e){var r=s(s({},this.defaultProps),t.props).layout;return"vertical"===r&&(0,i.hj)(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],(c=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):-1===this.lastBoundingBox.width&&-1===this.lastBoundingBox.height||(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?s({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),s(s({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,i=e.width,c=e.height,l=e.wrapperStyle,f=e.payloadUniqBy,p=e.payload,d=s(s({position:"absolute",width:i||"auto",height:c||"auto"},this.getDefaultPosition(l)),l);return n.createElement("div",{className:"recharts-legend-wrapper",style:d,ref:function(e){t.wrapperNode=e}},function(t,e){if(n.isValidElement(t))return n.cloneElement(t,e);if("function"===typeof t)return n.createElement(t,e);e.ref;var r=g(e,u);return n.createElement(o.g,r)}(r,s(s({},this.props),{},{payload:(0,a.z)(p,f,O)})))}}])&&p(r.prototype,c),l&&p(r,l),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,c,l}(n.PureComponent);m(x,"displayName","Legend"),m(x,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"})},71824:function(t,e,r){r.d(e,{h:function(){return v}});var n=r(90512),o=r(89526),i=r(38172),a=r.n(i),c=r(16171),u=r(78706),l=r(9410);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function d(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return h(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var v=(0,o.forwardRef)((function(t,e){var r=t.aspect,i=t.initialDimension,s=void 0===i?{width:-1,height:-1}:i,f=t.width,d=void 0===f?"100%":f,h=t.height,v=void 0===h?"100%":h,m=t.minWidth,b=void 0===m?0:m,g=t.minHeight,O=t.maxHeight,x=t.children,w=t.debounce,j=void 0===w?0:w,P=t.id,S=t.className,A=t.onResize,E=t.style,k=void 0===E?{}:E,T=(0,o.useRef)(null),C=(0,o.useRef)();C.current=A,(0,o.useImperativeHandle)(e,(function(){return Object.defineProperty(T.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),T.current},configurable:!0})}));var I=y((0,o.useState)({containerWidth:s.width,containerHeight:s.height}),2),D=I[0],M=I[1],N=(0,o.useCallback)((function(t,e){M((function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}}))}),[]);(0,o.useEffect)((function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;N(n,o),null===(e=C.current)||void 0===e||e.call(C,n,o)};j>0&&(t=a()(t,j,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=T.current.getBoundingClientRect(),n=r.width,o=r.height;return N(n,o),e.observe(T.current),function(){e.disconnect()}}),[N,j]);var B=(0,o.useMemo)((function(){var t=D.containerWidth,e=D.containerHeight;if(t<0||e<0)return null;(0,u.Z)((0,c.hU)(d)||(0,c.hU)(v),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",d,v),(0,u.Z)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,c.hU)(d)?t:d,i=(0,c.hU)(v)?e:v;r&&r>0&&(n?i=n/r:i&&(n=i*r),O&&i>O&&(i=O)),(0,u.Z)(n>0||i>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,i,d,v,b,g,r);var a=!Array.isArray(x)&&(0,l.Gf)(x.type).endsWith("Chart");return o.Children.map(x,(function(t){return o.isValidElement(t)?(0,o.cloneElement)(t,p({width:n,height:i},a?{style:p({height:"100%",width:"100%",maxHeight:i,maxWidth:n},t.props.style)}:{})):t}))}),[r,x,v,O,g,b,D,d]);return o.createElement("div",{id:P?"".concat(P):void 0,className:(0,n.Z)("recharts-responsive-container",S),style:p(p({},k),{},{width:d,height:v,minWidth:b,minHeight:g,maxHeight:O}),ref:T},B)}))},49266:function(t,e,r){r.d(e,{x:function(){return _}});var n=r(89526),o=r(51391),i=r.n(o),a=r(90512),c=r(16171),u=r(59509),l=r(9410),s=r(99875);function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return d(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function y(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h(n.key),n)}}function h(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var v=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,m=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,b=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,g=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,O={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},x=Object.keys(O),w="NaN";var j=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.num=e,this.unit=r,this.num=e,this.unit=r,Number.isNaN(e)&&(this.unit=""),""===r||b.test(r)||(this.num=NaN,this.unit=""),x.includes(r)&&(this.num=function(t,e){return t*O[e]}(e,r),this.unit="px")}return e=t,n=[{key:"parse",value:function(e){var r,n=p(null!==(r=g.exec(e))&&void 0!==r?r:[],3),o=n[1],i=n[2];return new t(parseFloat(o),null!==i&&void 0!==i?i:"")}}],(r=[{key:"add",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}])&&y(e.prototype,r),n&&y(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();function P(t){if(t.includes(w))return w;for(var e=t;e.includes("*")||e.includes("/");){var r,n=p(null!==(r=v.exec(e))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],c=j.parse(null!==o&&void 0!==o?o:""),u=j.parse(null!==a&&void 0!==a?a:""),l="*"===i?c.multiply(u):c.divide(u);if(l.isNaN())return w;e=e.replace(v,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=p(null!==(s=m.exec(e))&&void 0!==s?s:[],4),d=f[1],y=f[2],h=f[3],b=j.parse(null!==d&&void 0!==d?d:""),g=j.parse(null!==h&&void 0!==h?h:""),O="+"===y?b.add(g):b.subtract(g);if(O.isNaN())return w;e=e.replace(m,O.toString())}return e}var S=/\(([^()]*)\)/;function A(t){var e=t.replace(/\s+/g,"");return e=function(t){for(var e=t;e.includes("(");){var r=p(S.exec(e),2)[1];e=e.replace(S,P(r))}return e}(e),e=P(e)}function E(t){var e=function(t){try{return A(t)}catch(e){return w}}(t.slice(5,-1));return e===w?"":e}var k=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],T=["dx","dy","angle","className","breakAll"];function C(){return C=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},C.apply(this,arguments)}function I(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function D(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return M(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return M(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function M(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var N=/[ \f\n\r\t\v\u2028\u2029]+/,B=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];return i()(e)||(o=r?e.toString().split(""):e.toString().split(N)),{wordsWithComputedWidth:o.map((function(t){return{word:t,width:(0,s.xE)(t,n).width}})),spaceWidth:r?0:(0,s.xE)("\xa0",n).width}}catch(a){return null}},L=function(t){return[{words:i()(t)?[]:t.toString().split(N)}]},R=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!u.x.isSsr){var l=B({breakAll:i,children:n,style:o});return l?function(t,e,r,n,o){var i=t.maxLines,a=t.children,u=t.style,l=t.breakAll,s=(0,c.hj)(i),f=a,p=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce((function(t,e){var i=e.word,a=e.width,c=t[t.length-1];if(c&&(null==n||o||c.width+a+r<Number(n)))c.words.push(i),c.width+=a+r;else{var u={words:[i],width:a};t.push(u)}return t}),[])},d=p(e);if(!s)return d;for(var y,h=function(t){var e=f.slice(0,t),r=B({breakAll:l,style:u,children:e+"\u2026"}).wordsWithComputedWidth,o=p(r),a=o.length>i||function(t){return t.reduce((function(t,e){return t.width>e.width?t:e}))}(o).width>Number(n);return[a,o]},v=0,m=f.length-1,b=0;v<=m&&b<=f.length-1;){var g=Math.floor((v+m)/2),O=D(h(g-1),2),x=O[0],w=O[1],j=D(h(g),1)[0];if(x||j||(v=g+1),x&&j&&(m=g-1),!x&&j){y=w;break}b++}return y||d}({breakAll:i,children:n,maxLines:a,style:o},l.wordsWithComputedWidth,l.spaceWidth,e,r):L(n)}return L(n)},z="#808080",_=function(t){var e=t.x,r=void 0===e?0:e,o=t.y,i=void 0===o?0:o,u=t.lineHeight,s=void 0===u?"1em":u,f=t.capHeight,p=void 0===f?"0.71em":f,d=t.scaleToFit,y=void 0!==d&&d,h=t.textAnchor,v=void 0===h?"start":h,m=t.verticalAnchor,b=void 0===m?"end":m,g=t.fill,O=void 0===g?z:g,x=I(t,k),w=(0,n.useMemo)((function(){return R({breakAll:x.breakAll,children:x.children,maxLines:x.maxLines,scaleToFit:y,style:x.style,width:x.width})}),[x.breakAll,x.children,x.maxLines,y,x.style,x.width]),j=x.dx,P=x.dy,S=x.angle,A=x.className,D=x.breakAll,M=I(x,T);if(!(0,c.P2)(r)||!(0,c.P2)(i))return null;var N,B=r+((0,c.hj)(j)?j:0),L=i+((0,c.hj)(P)?P:0);switch(b){case"start":N=E("calc(".concat(p,")"));break;case"middle":N=E("calc(".concat((w.length-1)/2," * -").concat(s," + (").concat(p," / 2))"));break;default:N=E("calc(".concat(w.length-1," * -").concat(s,")"))}var _=[];if(y){var F=w[0].width,W=x.width;_.push("scale(".concat(((0,c.hj)(W)?W/F:1)/F,")"))}return S&&_.push("rotate(".concat(S,", ").concat(B,", ").concat(L,")")),_.length&&(M.transform=_.join(" ")),n.createElement("text",C({},(0,l.L6)(M,!0),{x:B,y:L,className:(0,a.Z)("recharts-text",A),textAnchor:v,fill:O.includes("url")?z:O}),w.map((function(t,e){var r=t.words.join(D?"":" ");return n.createElement("tspan",{x:B,dy:0===e?N:s,key:"".concat(r,"-").concat(e)},r)})))}},18170:function(t,e,r){r.d(e,{u:function(){return _}});var n=r(89526),o=r(69100),i=r(90512),a=r(16171);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var l="recharts-tooltip-wrapper",s={visibility:"hidden"};function f(t){var e=t.coordinate,r=t.translateX,n=t.translateY;return(0,i.Z)(l,u(u(u(u({},"".concat(l,"-right"),(0,a.hj)(r)&&e&&(0,a.hj)(e.x)&&r>=e.x),"".concat(l,"-left"),(0,a.hj)(r)&&e&&(0,a.hj)(e.x)&&r<e.x),"".concat(l,"-bottom"),(0,a.hj)(n)&&e&&(0,a.hj)(e.y)&&n>=e.y),"".concat(l,"-top"),(0,a.hj)(n)&&e&&(0,a.hj)(e.y)&&n<e.y))}function p(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,c=t.reverseDirection,u=t.tooltipDimension,l=t.viewBox,s=t.viewBoxDimension;if(i&&(0,a.hj)(i[n]))return i[n];var f=r[n]-u-o,p=r[n]+o;return e[n]?c[n]?f:p:c[n]?f<l[n]?Math.max(p,l[n]):Math.max(f,l[n]):p+u>l[n]+s?Math.max(f,l[n]):Math.max(p,l[n])}function d(t){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},d(t)}function y(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?y(Object(r),!0).forEach((function(e){w(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function v(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function m(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,j(n.key),n)}}function b(t,e,r){return e=O(e),function(t,e){if(e&&("object"===d(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,g()?Reflect.construct(e,r||[],O(t).constructor):e.apply(t,r))}function g(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(g=function(){return!!t})()}function O(t){return O=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},O(t)}function x(t,e){return x=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},x(t,e)}function w(t,e,r){return(e=j(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function j(t){var e=function(t,e){if("object"!=d(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=d(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==d(e)?e:e+""}var P=function(t){function e(){var t;v(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return w(t=b(this,e,[].concat(n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),w(t,"handleKeyDown",(function(e){var r,n,o,i;"Escape"===e.key&&t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})})),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&x(t,e)}(e,t),r=e,(o=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else-1===this.state.lastBoundingBox.width&&-1===this.state.lastBoundingBox.height||this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)===this.state.dismissedAtCoordinate.x&&(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)===this.state.dismissedAtCoordinate.y||(this.state.dismissed=!1))}},{key:"render",value:function(){var t=this,e=this.props,r=e.active,o=e.allowEscapeViewBox,i=e.animationDuration,a=e.animationEasing,c=e.children,u=e.coordinate,l=e.hasPayload,d=e.isAnimationActive,y=e.offset,v=e.position,m=e.reverseDirection,b=e.useTranslate3d,g=e.viewBox,O=e.wrapperStyle,x=function(t){var e,r,n=t.allowEscapeViewBox,o=t.coordinate,i=t.offsetTopLeft,a=t.position,c=t.reverseDirection,u=t.tooltipBox,l=t.useTranslate3d,d=t.viewBox;return{cssProperties:u.height>0&&u.width>0&&o?function(t){var e=t.translateX,r=t.translateY;return{transform:t.useTranslate3d?"translate3d(".concat(e,"px, ").concat(r,"px, 0)"):"translate(".concat(e,"px, ").concat(r,"px)")}}({translateX:e=p({allowEscapeViewBox:n,coordinate:o,key:"x",offsetTopLeft:i,position:a,reverseDirection:c,tooltipDimension:u.width,viewBox:d,viewBoxDimension:d.width}),translateY:r=p({allowEscapeViewBox:n,coordinate:o,key:"y",offsetTopLeft:i,position:a,reverseDirection:c,tooltipDimension:u.height,viewBox:d,viewBoxDimension:d.height}),useTranslate3d:l}):s,cssClasses:f({translateX:e,translateY:r,coordinate:o})}}({allowEscapeViewBox:o,coordinate:u,offsetTopLeft:y,position:v,reverseDirection:m,tooltipBox:this.state.lastBoundingBox,useTranslate3d:b,viewBox:g}),w=x.cssClasses,j=x.cssProperties,P=h(h({transition:d&&r?"transform ".concat(i,"ms ").concat(a):void 0},j),{},{pointerEvents:"none",visibility:!this.state.dismissed&&r&&l?"visible":"hidden",position:"absolute",top:0,left:0},O);return n.createElement("div",{tabIndex:-1,className:w,style:P,ref:function(e){t.wrapperNode=e}},c)}}])&&m(r.prototype,o),i&&m(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.PureComponent),S=r(59509),A=r(56062);function E(t){return E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},E(t)}function k(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function T(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?k(Object(r),!0).forEach((function(e){L(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function C(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function I(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,R(n.key),n)}}function D(t,e,r){return e=N(e),function(t,e){if(e&&("object"===E(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,M()?Reflect.construct(e,r||[],N(t).constructor):e.apply(t,r))}function M(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(M=function(){return!!t})()}function N(t){return N=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},N(t)}function B(t,e){return B=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},B(t,e)}function L(t,e,r){return(e=R(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function R(t){var e=function(t,e){if("object"!=E(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=E(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==E(e)?e:e+""}function z(t){return t.dataKey}var _=function(t){function e(){return C(this,e),D(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&B(t,e)}(e,t),r=e,(i=[{key:"render",value:function(){var t=this,e=this.props,r=e.active,i=e.allowEscapeViewBox,a=e.animationDuration,c=e.animationEasing,u=e.content,l=e.coordinate,s=e.filterNull,f=e.isAnimationActive,p=e.offset,d=e.payload,y=e.payloadUniqBy,h=e.position,v=e.reverseDirection,m=e.useTranslate3d,b=e.viewBox,g=e.wrapperStyle,O=null!==d&&void 0!==d?d:[];s&&O.length&&(O=(0,A.z)(d.filter((function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)})),y,z));var x=O.length>0;return n.createElement(P,{allowEscapeViewBox:i,animationDuration:a,animationEasing:c,isAnimationActive:f,active:r,coordinate:l,hasPayload:x,offset:p,position:h,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):"function"===typeof t?n.createElement(t,e):n.createElement(o.x,e)}(u,T(T({},this.props),{},{payload:O})))}}])&&I(r.prototype,i),a&&I(r,a),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,i,a}(n.PureComponent);L(_,"displayName","Tooltip"),L(_,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!S.x.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}})},61452:function(t,e,r){r.d(e,{m:function(){return l}});var n=r(89526),o=r(90512),i=r(9410),a=["children","className"];function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(this,arguments)}function u(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var l=n.forwardRef((function(t,e){var r=t.children,l=t.className,s=u(t,a),f=(0,o.Z)("recharts-layer",l);return n.createElement("g",c({className:f},(0,i.L6)(s,!0),{ref:e}),r)}))},93386:function(t,e,r){r.d(e,{T:function(){return l}});var n=r(89526),o=r(90512),i=r(9410),a=["children","width","height","viewBox","className","style","title","desc"];function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(this,arguments)}function u(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function l(t){var e=t.children,r=t.width,l=t.height,s=t.viewBox,f=t.className,p=t.style,d=t.title,y=t.desc,h=u(t,a),v=s||{width:r,height:l,x:0,y:0},m=(0,o.Z)("recharts-surface",f);return n.createElement("svg",c({},(0,i.L6)(h,!0,"svg"),{className:m,width:r,height:l,style:p,viewBox:"".concat(v.x," ").concat(v.y," ").concat(v.width," ").concat(v.height)}),n.createElement("title",null,d),n.createElement("desc",null,y),e)}},86545:function(t,e,r){r.d(e,{br:function(){return g},CW:function(){return w},Mw:function(){return k},zn:function(){return E},sp:function(){return O},qD:function(){return A},d2:function(){return S},bH:function(){return x},Ud:function(){return P},Nf:function(){return j}});var n=r(89526),o=r(78109),i=r(92210),a=r.n(i),c=r(84168),u=r.n(c),l=r(54883),s=r.n(l)()((function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}}),(function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")})),f=r(16171);var p=(0,n.createContext)(void 0),d=(0,n.createContext)(void 0),y=(0,n.createContext)(void 0),h=(0,n.createContext)({}),v=(0,n.createContext)(void 0),m=(0,n.createContext)(0),b=(0,n.createContext)(0),g=function(t){var e=t.state,r=e.xAxisMap,o=e.yAxisMap,i=e.offset,a=t.clipPathId,c=t.children,u=t.width,l=t.height,f=s(i);return n.createElement(p.Provider,{value:r},n.createElement(d.Provider,{value:o},n.createElement(h.Provider,{value:i},n.createElement(y.Provider,{value:f},n.createElement(v.Provider,{value:a},n.createElement(m.Provider,{value:l},n.createElement(b.Provider,{value:u},c)))))))},O=function(){return(0,n.useContext)(v)};var x=function(t){var e=(0,n.useContext)(p);null==e&&(0,o.Z)(!1);var r=e[t];return null==r&&(0,o.Z)(!1),r},w=function(){var t=(0,n.useContext)(p);return(0,f.Kt)(t)},j=function(){var t=(0,n.useContext)(d);return a()(t,(function(t){return u()(t.domain,Number.isFinite)}))||(0,f.Kt)(t)},P=function(t){var e=(0,n.useContext)(d);null==e&&(0,o.Z)(!1);var r=e[t];return null==r&&(0,o.Z)(!1),r},S=function(){return(0,n.useContext)(y)},A=function(){return(0,n.useContext)(h)},E=function(){return(0,n.useContext)(b)},k=function(){return(0,n.useContext)(m)}},40076:function(t,e,r){r.r(e),r.d(e,{Area:function(){return ze},AreaChart:function(){return In},Bar:function(){return _e.$},BarChart:function(){return br.v},Brush:function(){return Yt.B},CartesianAxis:function(){return qt.O},CartesianGrid:function(){return Qt.q},Cell:function(){return s.b},ComposedChart:function(){return Mn},Cross:function(){return E.X},Curve:function(){return j.H},Customized:function(){return x},DefaultLegendContent:function(){return a.g},DefaultTooltipContent:function(){return u.x},Dot:function(){return A.o},ErrorBar:function(){return Jt.W},Funnel:function(){return vo},FunnelChart:function(){return mo},Global:function(){return ot.x},Label:function(){return p._},LabelList:function(){return d.e},Layer:function(){return o.m},Legend:function(){return i.D},Line:function(){return me},LineChart:function(){return mr},Pie:function(){return Y.b},PieChart:function(){return gr.u},PolarAngleAxis:function(){return G.I},PolarGrid:function(){return X},PolarRadiusAxis:function(){return V.S},Polygon:function(){return S.m},Radar:function(){return Ot},RadarChart:function(){return Tn},RadialBar:function(){return Gt},RadialBarChart:function(){return Dn},Rectangle:function(){return P.A},ReferenceArea:function(){return $t.z},ReferenceDot:function(){return Ht.q},ReferenceLine:function(){return Ut.d},ResponsiveContainer:function(){return l.h},Sankey:function(){return kn},Scatter:function(){return pr},ScatterChart:function(){return Cn},Sector:function(){return w.L},SunburstChart:function(){return Kn},Surface:function(){return n.T},Symbols:function(){return k.v},Text:function(){return f.x},Tooltip:function(){return c.u},Trapezoid:function(){return bo.Z},Treemap:function(){return Gr},XAxis:function(){return dr.K},YAxis:function(){return yr.B},ZAxis:function(){return He}});var n=r(93386),o=r(61452),i=r(71015),a=r(58935),c=r(18170),u=r(69100),l=r(71824),s=r(32214),f=r(49266),p=r(43774),d=r(34324),y=r(89526),h=r(39277),v=r.n(h),m=r(78706),b=["component"];function g(t){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},g(t)}function O(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function x(t){var e,r=t.component,n=O(t,b);return(0,y.isValidElement)(r)?e=(0,y.cloneElement)(r,n):v()(r)?e=(0,y.createElement)(r,n):(0,m.Z)(!1,"Customized's props `component` must be React.element or Function, but got %s.",g(r)),y.createElement(o.m,{className:"recharts-customized-wrapper"},e)}x.displayName="Customized";var w=r(61001),j=r(92147),P=r(33951),S=r(31234),A=r(96963),E=r(74791),k=r(71746),T=r(90512),C=r(80072),I=r(9410),D=["cx","cy","innerRadius","outerRadius","gridType","radialLines"];function M(t){return M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},M(t)}function N(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function B(){return B=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},B.apply(this,arguments)}function L(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function R(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?L(Object(r),!0).forEach((function(e){z(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):L(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function z(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=M(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=M(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==M(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var _=function(t,e,r,n){var o="";return n.forEach((function(n,i){var a=(0,C.op)(e,r,t,n);o+=i?"L ".concat(a.x,",").concat(a.y):"M ".concat(a.x,",").concat(a.y)})),o+="Z"},F=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.polarAngles,a=t.radialLines;if(!i||!i.length||!a)return null;var c=R({stroke:"#ccc"},(0,I.L6)(t,!1));return y.createElement("g",{className:"recharts-polar-grid-angle"},i.map((function(t){var i=(0,C.op)(e,r,n,t),a=(0,C.op)(e,r,o,t);return y.createElement("line",B({},c,{key:"line-".concat(t),x1:i.x,y1:i.y,x2:a.x,y2:a.y}))})))},W=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.index,i=R(R({stroke:"#ccc"},(0,I.L6)(t,!1)),{},{fill:"none"});return y.createElement("circle",B({},i,{className:(0,T.Z)("recharts-polar-grid-concentric-circle",t.className),key:"circle-".concat(o),cx:e,cy:r,r:n}))},Z=function(t){var e=t.radius,r=t.index,n=R(R({stroke:"#ccc"},(0,I.L6)(t,!1)),{},{fill:"none"});return y.createElement("path",B({},n,{className:(0,T.Z)("recharts-polar-grid-concentric-polygon",t.className),key:"path-".concat(r),d:_(e,t.cx,t.cy,t.polarAngles)}))},K=function(t){var e=t.polarRadius,r=t.gridType;return e&&e.length?y.createElement("g",{className:"recharts-polar-grid-concentric"},e.map((function(e,n){var o=n;return"circle"===r?y.createElement(W,B({key:o},t,{radius:e,index:n})):y.createElement(Z,B({key:o},t,{radius:e,index:n}))}))):null},X=function(t){var e=t.cx,r=void 0===e?0:e,n=t.cy,o=void 0===n?0:n,i=t.innerRadius,a=void 0===i?0:i,c=t.outerRadius,u=void 0===c?0:c,l=t.gridType,s=void 0===l?"polygon":l,f=t.radialLines,p=void 0===f||f,d=N(t,D);return u<=0?null:y.createElement("g",{className:"recharts-polar-grid"},y.createElement(F,B({cx:r,cy:o,innerRadius:a,outerRadius:u,gridType:s,radialLines:p},d)),y.createElement(K,B({cx:r,cy:o,innerRadius:a,outerRadius:u,gridType:s,radialLines:p},d)))};X.displayName="PolarGrid";var V=r(85322),G=r(10562),Y=r(86246),U=r(80397),H=r(51391),$=r.n(H),q=r(80275),Q=r.n(q),J=r(48e3),tt=r.n(J),et=r(47184),rt=r.n(et),nt=r(16171),ot=r(59509),it=r(36530),at=["key"];function ct(t){return ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ct(t)}function ut(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function lt(){return lt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},lt.apply(this,arguments)}function st(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ft(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?st(Object(r),!0).forEach((function(e){bt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):st(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function pt(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function dt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,gt(n.key),n)}}function yt(t,e,r){return e=vt(e),function(t,e){if(e&&("object"===ct(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,ht()?Reflect.construct(e,r||[],vt(t).constructor):e.apply(t,r))}function ht(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(ht=function(){return!!t})()}function vt(t){return vt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},vt(t)}function mt(t,e){return mt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},mt(t,e)}function bt(t,e,r){return(e=gt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function gt(t){var e=function(t,e){if("object"!=ct(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ct(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ct(e)?e:e+""}var Ot=function(t){function e(){var t;pt(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return bt(t=yt(this,e,[].concat(n)),"state",{isAnimationFinished:!1}),bt(t,"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),v()(e)&&e()})),bt(t,"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),v()(e)&&e()})),bt(t,"handleMouseEnter",(function(e){var r=t.props.onMouseEnter;r&&r(t.props,e)})),bt(t,"handleMouseLeave",(function(e){var r=t.props.onMouseLeave;r&&r(t.props,e)})),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&mt(t,e)}(e,t),r=e,i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"renderDotItem",value:function(t,e){var r;if(y.isValidElement(t))r=y.cloneElement(t,e);else if(v()(t))r=t(e);else{var n=e.key,o=ut(e,at);r=y.createElement(A.o,lt({},o,{key:n,className:(0,T.Z)("recharts-radar-dot","boolean"!==typeof t?t.className:"")}))}return r}}],(n=[{key:"renderDots",value:function(t){var r=this.props,n=r.dot,i=r.dataKey,a=(0,I.L6)(this.props,!1),c=(0,I.L6)(n,!0),u=t.map((function(t,r){var o=ft(ft(ft({key:"dot-".concat(r),r:3},a),c),{},{dataKey:i,cx:t.x,cy:t.y,index:r,payload:t});return e.renderDotItem(n,o)}));return y.createElement(o.m,{className:"recharts-radar-dots"},u)}},{key:"renderPolygonStatically",value:function(t){var e,r=this.props,n=r.shape,i=r.dot,a=r.isRange,c=r.baseLinePoints,u=r.connectNulls;return e=y.isValidElement(n)?y.cloneElement(n,ft(ft({},this.props),{},{points:t})):v()(n)?n(ft(ft({},this.props),{},{points:t})):y.createElement(S.m,lt({},(0,I.L6)(this.props,!0),{onMouseEnter:this.handleMouseEnter,onMouseLeave:this.handleMouseLeave,points:t,baseLinePoints:a?c:null,connectNulls:u})),y.createElement(o.m,{className:"recharts-radar-polygon"},e,i?this.renderDots(t):null)}},{key:"renderPolygonWithAnimation",value:function(){var t=this,e=this.props,r=e.points,n=e.isAnimationActive,o=e.animationBegin,i=e.animationDuration,a=e.animationEasing,c=e.animationId,u=this.state.prevPoints;return y.createElement(U.ZP,{begin:o,duration:i,isActive:n,easing:a,from:{t:0},to:{t:1},key:"radar-".concat(c),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(e){var n=e.t,o=u&&u.length/r.length,i=r.map((function(t,e){var r=u&&u[Math.floor(e*o)];if(r){var i=(0,nt.k4)(r.x,t.x),a=(0,nt.k4)(r.y,t.y);return ft(ft({},t),{},{x:i(n),y:a(n)})}var c=(0,nt.k4)(t.cx,t.x),l=(0,nt.k4)(t.cy,t.y);return ft(ft({},t),{},{x:c(n),y:l(n)})}));return t.renderPolygonStatically(i)}))}},{key:"renderPolygon",value:function(){var t=this.props,e=t.points,r=t.isAnimationActive,n=t.isRange,o=this.state.prevPoints;return!(r&&e&&e.length)||n||o&&rt()(o,e)?this.renderPolygonStatically(e):this.renderPolygonWithAnimation()}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.className,n=t.points,i=t.isAnimationActive;if(e||!n||!n.length)return null;var a=this.state.isAnimationFinished,c=(0,T.Z)("recharts-radar",r);return y.createElement(o.m,{className:c},this.renderPolygon(),(!i||a)&&d.e.renderCallByParent(this.props,n))}}])&&dt(r.prototype,n),i&&dt(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,i}(y.PureComponent);bt(Ot,"displayName","Radar"),bt(Ot,"defaultProps",{angleAxisId:0,radiusAxisId:0,hide:!1,activeDot:!0,dot:!1,legendType:"rect",isAnimationActive:!ot.x.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),bt(Ot,"getComposedData",(function(t){var e=t.radiusAxis,r=t.angleAxis,n=t.displayedData,o=t.dataKey,i=t.bandSize,a=r.cx,c=r.cy,u=!1,l=[],s="number"!==r.type&&null!==i&&void 0!==i?i:0;n.forEach((function(t,n){var i=(0,it.F$)(t,r.dataKey,n),f=(0,it.F$)(t,o),p=r.scale(i)+s,d=Array.isArray(f)?Q()(f):f,y=$()(d)?void 0:e.scale(d);Array.isArray(f)&&f.length>=2&&(u=!0),l.push(ft(ft({},(0,C.op)(a,c,y,p)),{},{name:i,value:f,cx:a,cy:c,radius:y,angle:p,payload:t}))}));var f=[];return u&&l.forEach((function(t){if(Array.isArray(t.value)){var r=tt()(t.value),n=$()(r)?void 0:e.scale(r);f.push(ft(ft({},t),{},{radius:n},(0,C.op)(a,c,n,t.angle)))}else f.push(t)})),{points:l,isRange:u,baseLinePoints:f}}));var xt=r(69531);function wt(t){return wt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wt(t)}function jt(){return jt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},jt.apply(this,arguments)}function Pt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function St(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Pt(Object(r),!0).forEach((function(e){At(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Pt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function At(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=wt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=wt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==wt(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Et(t){return"string"===typeof t?parseInt(t,10):t}function kt(t,e){var r="".concat(e.cx||t.cx),n=Number(r),o="".concat(e.cy||t.cy),i=Number(o);return St(St(St({},e),t),{},{cx:n,cy:i})}function Tt(t){return y.createElement(xt.bn,jt({shapeType:"sector",propTransformer:kt},t))}var Ct=r(33790),It=["shape","activeShape","activeIndex","cornerRadius"],Dt=["value","background"];function Mt(t){return Mt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mt(t)}function Nt(){return Nt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Nt.apply(this,arguments)}function Bt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Lt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Bt(Object(r),!0).forEach((function(e){Xt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Bt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Rt(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function zt(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Vt(n.key),n)}}function Ft(t,e,r){return e=Zt(e),function(t,e){if(e&&("object"===Mt(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Wt()?Reflect.construct(e,r||[],Zt(t).constructor):e.apply(t,r))}function Wt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Wt=function(){return!!t})()}function Zt(t){return Zt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Zt(t)}function Kt(t,e){return Kt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Kt(t,e)}function Xt(t,e,r){return(e=Vt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Vt(t){var e=function(t,e){if("object"!=Mt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Mt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Mt(e)?e:e+""}var Gt=function(t){function e(){var t;zt(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return Xt(t=Ft(this,e,[].concat(n)),"state",{isAnimationFinished:!1}),Xt(t,"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),v()(e)&&e()})),Xt(t,"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),v()(e)&&e()})),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Kt(t,e)}(e,t),r=e,i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],(n=[{key:"getDeltaAngle",value:function(){var t=this.props,e=t.startAngle,r=t.endAngle;return(0,nt.uY)(r-e)*Math.min(Math.abs(r-e),360)}},{key:"renderSectorsStatically",value:function(t){var e=this,r=this.props,n=r.shape,o=r.activeShape,i=r.activeIndex,a=r.cornerRadius,c=Rt(r,It),u=(0,I.L6)(c,!1);return t.map((function(t,r){var l=r===i,s=Lt(Lt(Lt(Lt({},u),{},{cornerRadius:Et(a)},t),(0,Ct.bw)(e.props,t,r)),{},{className:"recharts-radial-bar-sector ".concat(t.className),forceCornerRadius:c.forceCornerRadius,cornerIsExternal:c.cornerIsExternal,isActive:l,option:l?o:n});return y.createElement(Tt,Nt({},s,{key:"sector-".concat(r)}))}))}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.isAnimationActive,i=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,l=this.state.prevData;return y.createElement(U.ZP,{begin:i,duration:a,isActive:n,easing:c,from:{t:0},to:{t:1},key:"radialBar-".concat(u),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(e){var n=e.t,i=r.map((function(t,e){var r=l&&l[e];if(r){var o=(0,nt.k4)(r.startAngle,t.startAngle),i=(0,nt.k4)(r.endAngle,t.endAngle);return Lt(Lt({},t),{},{startAngle:o(n),endAngle:i(n)})}var a=t.endAngle,c=t.startAngle,u=(0,nt.k4)(c,a);return Lt(Lt({},t),{},{endAngle:u(n)})}));return y.createElement(o.m,null,t.renderSectorsStatically(i))}))}},{key:"renderSectors",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return!(r&&e&&e.length)||n&&rt()(n,e)?this.renderSectorsStatically(e):this.renderSectorsWithAnimation()}},{key:"renderBackground",value:function(t){var e=this,r=this.props.cornerRadius,n=(0,I.L6)(this.props.background,!1);return t.map((function(t,o){t.value;var i=t.background,a=Rt(t,Dt);if(!i)return null;var c=Lt(Lt(Lt(Lt(Lt({cornerRadius:Et(r)},a),{},{fill:"#eee"},i),n),(0,Ct.bw)(e.props,t,o)),{},{index:o,className:(0,T.Z)("recharts-radial-bar-background-sector",null===n||void 0===n?void 0:n.className),option:i,isActive:!1});return y.createElement(Tt,Nt({},c,{key:"sector-".concat(o)}))}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,i=t.background,a=t.isAnimationActive;if(e||!r||!r.length)return null;var c=this.state.isAnimationFinished,u=(0,T.Z)("recharts-area",n);return y.createElement(o.m,{className:u},i&&y.createElement(o.m,{className:"recharts-radial-bar-background"},this.renderBackground(r)),y.createElement(o.m,{className:"recharts-radial-bar-sectors"},this.renderSectors()),(!a||c)&&d.e.renderCallByParent(Lt({},this.props),r))}}])&&_t(r.prototype,n),i&&_t(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,i}(y.PureComponent);Xt(Gt,"displayName","RadialBar"),Xt(Gt,"defaultProps",{angleAxisId:0,radiusAxisId:0,minPointSize:0,hide:!1,legendType:"rect",data:[],isAnimationActive:!ot.x.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease",forceCornerRadius:!1,cornerIsExternal:!1}),Xt(Gt,"getComposedData",(function(t){var e=t.item,r=t.props,n=t.radiusAxis,o=t.radiusAxisTicks,i=t.angleAxis,a=t.angleAxisTicks,c=t.displayedData,u=t.dataKey,l=t.stackedData,f=t.barPosition,p=t.bandSize,d=t.dataStartIndex,y=(0,it.Bu)(f,e);if(!y)return null;var h=i.cx,v=i.cy,m=r.layout,b=e.props,g=b.children,O=b.minPointSize,x="radial"===m?i:n,w=l?x.scale.domain():null,j=(0,it.Yj)({numericAxis:x}),P=(0,I.NN)(g,s.b);return{data:c.map((function(t,c){var s,f,b,g,x,S;if(l?s=(0,it.Vv)(l[d+c],w):(s=(0,it.F$)(t,u),Array.isArray(s)||(s=[j,s])),"radial"===m){f=(0,it.Fy)({axis:n,ticks:o,bandSize:p,offset:y.offset,entry:t,index:c}),x=i.scale(s[1]),g=i.scale(s[0]),b=f+y.size;var A=x-g;if(Math.abs(O)>0&&Math.abs(A)<Math.abs(O))x+=(0,nt.uY)(A||O)*(Math.abs(O)-Math.abs(A));S={background:{cx:h,cy:v,innerRadius:f,outerRadius:b,startAngle:r.startAngle,endAngle:r.endAngle}}}else{f=n.scale(s[0]),b=n.scale(s[1]),x=(g=(0,it.Fy)({axis:i,ticks:a,bandSize:p,offset:y.offset,entry:t,index:c}))+y.size;var E=b-f;if(Math.abs(O)>0&&Math.abs(E)<Math.abs(O))b+=(0,nt.uY)(E||O)*(Math.abs(O)-Math.abs(E))}return Lt(Lt(Lt(Lt({},t),S),{},{payload:t,value:l?s:s[1],cx:h,cy:v,innerRadius:f,outerRadius:b,startAngle:g,endAngle:x},P&&P[c]&&P[c].props),{},{tooltipPayload:[(0,it.Qo)(e,t)],tooltipPosition:(0,C.op)(h,v,(f+b)/2,(g+x)/2)})})),layout:m}}));var Yt=r(88974),Ut=r(48586),Ht=r(7629),$t=r(93264),qt=r(27871),Qt=r(42333),Jt=r(65370),te=["type","layout","connectNulls","ref"],ee=["key"];function re(t){return re="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},re(t)}function ne(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function oe(){return oe=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},oe.apply(this,arguments)}function ie(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ae(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ie(Object(r),!0).forEach((function(e){he(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ie(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ce(t){return function(t){if(Array.isArray(t))return ue(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return ue(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ue(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ue(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function le(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function se(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ve(n.key),n)}}function fe(t,e,r){return e=de(e),function(t,e){if(e&&("object"===re(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,pe()?Reflect.construct(e,r||[],de(t).constructor):e.apply(t,r))}function pe(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(pe=function(){return!!t})()}function de(t){return de=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},de(t)}function ye(t,e){return ye=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ye(t,e)}function he(t,e,r){return(e=ve(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ve(t){var e=function(t,e){if("object"!=re(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=re(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==re(e)?e:e+""}var me=function(t){function e(){var t;le(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return he(t=fe(this,e,[].concat(n)),"state",{isAnimationFinished:!0,totalLength:0}),he(t,"generateSimpleStrokeDasharray",(function(t,e){return"".concat(e,"px ").concat(t-e,"px")})),he(t,"getStrokeDasharray",(function(r,n,o){var i=o.reduce((function(t,e){return t+e}));if(!i)return t.generateSimpleStrokeDasharray(n,r);for(var a=Math.floor(r/i),c=r%i,u=n-r,l=[],s=0,f=0;s<o.length;f+=o[s],++s)if(f+o[s]>c){l=[].concat(ce(o.slice(0,s)),[c-f]);break}var p=l.length%2===0?[0,u]:[u];return[].concat(ce(e.repeat(o,a)),ce(l),p).map((function(t){return"".concat(t,"px")})).join(", ")})),he(t,"id",(0,nt.EL)("recharts-line-")),he(t,"pathRef",(function(e){t.mainCurve=e})),he(t,"handleAnimationEnd",(function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()})),he(t,"handleAnimationStart",(function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()})),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ye(t,e)}(e,t),r=e,i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"repeat",value:function(t,e){for(var r=t.length%2!==0?[].concat(ce(t),[0]):t,n=[],o=0;o<e;++o)n=[].concat(ce(n),ce(r));return n}},{key:"renderDotItem",value:function(t,e){var r;if(y.isValidElement(t))r=y.cloneElement(t,e);else if(v()(t))r=t(e);else{var n=e.key,o=ne(e,ee),i=(0,T.Z)("recharts-line-dot","boolean"!==typeof t?t.className:"");r=y.createElement(A.o,oe({key:n},o,{className:i}))}return r}}],(n=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();this.setState({totalLength:t})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();t!==this.state.totalLength&&this.setState({totalLength:t})}}},{key:"getTotalLength",value:function(){var t=this.mainCurve;try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(e){return 0}}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.points,i=r.xAxis,a=r.yAxis,c=r.layout,u=r.children,l=(0,I.NN)(u,Jt.W);if(!l)return null;var s=function(t,e){return{x:t.x,y:t.y,value:t.value,errorVal:(0,it.F$)(t.payload,e)}},f={clipPath:t?"url(#clipPath-".concat(e,")"):null};return y.createElement(o.m,f,l.map((function(t){return y.cloneElement(t,{key:"bar-".concat(t.props.dataKey),data:n,xAxis:i,yAxis:a,layout:c,dataPointFormatter:s})})))}},{key:"renderDots",value:function(t,r,n){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var i=this.props,a=i.dot,c=i.points,u=i.dataKey,l=(0,I.L6)(this.props,!1),s=(0,I.L6)(a,!0),f=c.map((function(t,r){var n=ae(ae(ae({key:"dot-".concat(r),r:3},l),s),{},{index:r,cx:t.x,cy:t.y,value:t.value,dataKey:u,payload:t.payload,points:c});return e.renderDotItem(a,n)})),p={clipPath:t?"url(#clipPath-".concat(r?"":"dots-").concat(n,")"):null};return y.createElement(o.m,oe({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(t,e,r,n){var o=this.props,i=o.type,a=o.layout,c=o.connectNulls,u=(o.ref,ne(o,te)),l=ae(ae(ae({},(0,I.L6)(u,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:e?"url(#clipPath-".concat(r,")"):null,points:t},n),{},{type:i,layout:a,connectNulls:c});return y.createElement(j.H,oe({},l,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(t,e){var r=this,n=this.props,o=n.points,i=n.strokeDasharray,a=n.isAnimationActive,c=n.animationBegin,u=n.animationDuration,l=n.animationEasing,s=n.animationId,f=n.animateNewValues,p=n.width,d=n.height,h=this.state,v=h.prevPoints,m=h.totalLength;return y.createElement(U.ZP,{begin:c,duration:u,isActive:a,easing:l,from:{t:0},to:{t:1},key:"line-".concat(s),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(n){var a=n.t;if(v){var c=v.length/o.length,u=o.map((function(t,e){var r=Math.floor(e*c);if(v[r]){var n=v[r],o=(0,nt.k4)(n.x,t.x),i=(0,nt.k4)(n.y,t.y);return ae(ae({},t),{},{x:o(a),y:i(a)})}if(f){var u=(0,nt.k4)(2*p,t.x),l=(0,nt.k4)(d/2,t.y);return ae(ae({},t),{},{x:u(a),y:l(a)})}return ae(ae({},t),{},{x:t.x,y:t.y})}));return r.renderCurveStatically(u,t,e)}var l,s=(0,nt.k4)(0,m)(a);if(i){var y="".concat(i).split(/[,\s]+/gim).map((function(t){return parseFloat(t)}));l=r.getStrokeDasharray(s,m,y)}else l=r.generateSimpleStrokeDasharray(m,s);return r.renderCurveStatically(o,t,e,{strokeDasharray:l})}))}},{key:"renderCurve",value:function(t,e){var r=this.props,n=r.points,o=r.isAnimationActive,i=this.state,a=i.prevPoints,c=i.totalLength;return o&&n&&n.length&&(!a&&c>0||!rt()(a,n))?this.renderCurveWithAnimation(t,e):this.renderCurveStatically(n,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,n=e.dot,i=e.points,a=e.className,c=e.xAxis,u=e.yAxis,l=e.top,s=e.left,f=e.width,p=e.height,h=e.isAnimationActive,v=e.id;if(r||!i||!i.length)return null;var m=this.state.isAnimationFinished,b=1===i.length,g=(0,T.Z)("recharts-line",a),O=c&&c.allowDataOverflow,x=u&&u.allowDataOverflow,w=O||x,j=$()(v)?this.id:v,P=null!==(t=(0,I.L6)(n,!1))&&void 0!==t?t:{r:3,strokeWidth:2},S=P.r,A=void 0===S?3:S,E=P.strokeWidth,k=void 0===E?2:E,C=((0,I.jf)(n)?n:{}).clipDot,D=void 0===C||C,M=2*A+k;return y.createElement(o.m,{className:g},O||x?y.createElement("defs",null,y.createElement("clipPath",{id:"clipPath-".concat(j)},y.createElement("rect",{x:O?s:s-f/2,y:x?l:l-p/2,width:O?f:2*f,height:x?p:2*p})),!D&&y.createElement("clipPath",{id:"clipPath-dots-".concat(j)},y.createElement("rect",{x:s-M/2,y:l-M/2,width:f+M,height:p+M}))):null,!b&&this.renderCurve(w,j),this.renderErrorBar(w,j),(b||n)&&this.renderDots(w,D,j),(!h||m)&&d.e.renderCallByParent(this.props,i))}}])&&se(r.prototype,n),i&&se(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,i}(y.PureComponent);he(me,"displayName","Line"),he(me,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!ot.x.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),he(me,"getComposedData",(function(t){var e=t.props,r=t.xAxis,n=t.yAxis,o=t.xAxisTicks,i=t.yAxisTicks,a=t.dataKey,c=t.bandSize,u=t.displayedData,l=t.offset,s=e.layout;return ae({points:u.map((function(t,e){var u=(0,it.F$)(t,a);return"horizontal"===s?{x:(0,it.Hv)({axis:r,ticks:o,bandSize:c,entry:t,index:e}),y:$()(u)?null:n.scale(u),value:u,payload:t}:{x:$()(u)?null:r.scale(u),y:(0,it.Hv)({axis:n,ticks:i,bandSize:c,entry:t,index:e}),value:u,payload:t}})),layout:s},l)}));var be,ge=r(14019),Oe=r.n(ge),xe=r(35813),we=r.n(xe),je=["layout","type","stroke","connectNulls","isRange","ref"],Pe=["key"];function Se(t){return Se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Se(t)}function Ae(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function Ee(){return Ee=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ee.apply(this,arguments)}function ke(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Te(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ke(Object(r),!0).forEach((function(e){Le(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ke(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ce(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Ie(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Re(n.key),n)}}function De(t,e,r){return e=Ne(e),function(t,e){if(e&&("object"===Se(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Me()?Reflect.construct(e,r||[],Ne(t).constructor):e.apply(t,r))}function Me(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Me=function(){return!!t})()}function Ne(t){return Ne=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Ne(t)}function Be(t,e){return Be=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Be(t,e)}function Le(t,e,r){return(e=Re(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Re(t){var e=function(t,e){if("object"!=Se(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Se(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Se(e)?e:e+""}var ze=function(t){function e(){var t;Ce(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return Le(t=De(this,e,[].concat(n)),"state",{isAnimationFinished:!0}),Le(t,"id",(0,nt.EL)("recharts-area-")),Le(t,"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),v()(e)&&e()})),Le(t,"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),v()(e)&&e()})),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Be(t,e)}(e,t),r=e,i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,curBaseLine:t.baseLine,prevPoints:e.curPoints,prevBaseLine:e.curBaseLine}:t.points!==e.curPoints||t.baseLine!==e.curBaseLine?{curPoints:t.points,curBaseLine:t.baseLine}:null}}],(n=[{key:"renderDots",value:function(t,r,n){var i=this.props.isAnimationActive,a=this.state.isAnimationFinished;if(i&&!a)return null;var c=this.props,u=c.dot,l=c.points,s=c.dataKey,f=(0,I.L6)(this.props,!1),p=(0,I.L6)(u,!0),d=l.map((function(t,r){var n=Te(Te(Te({key:"dot-".concat(r),r:3},f),p),{},{index:r,cx:t.x,cy:t.y,dataKey:s,value:t.value,payload:t.payload,points:l});return e.renderDotItem(u,n)})),h={clipPath:t?"url(#clipPath-".concat(r?"":"dots-").concat(n,")"):null};return y.createElement(o.m,Ee({className:"recharts-area-dots"},h),d)}},{key:"renderHorizontalRect",value:function(t){var e=this.props,r=e.baseLine,n=e.points,o=e.strokeWidth,i=n[0].x,a=n[n.length-1].x,c=t*Math.abs(i-a),u=Oe()(n.map((function(t){return t.y||0})));return(0,nt.hj)(r)&&"number"===typeof r?u=Math.max(r,u):r&&Array.isArray(r)&&r.length&&(u=Math.max(Oe()(r.map((function(t){return t.y||0}))),u)),(0,nt.hj)(u)?y.createElement("rect",{x:i<a?i:i-c,y:0,width:c,height:Math.floor(u+(o?parseInt("".concat(o),10):1))}):null}},{key:"renderVerticalRect",value:function(t){var e=this.props,r=e.baseLine,n=e.points,o=e.strokeWidth,i=n[0].y,a=n[n.length-1].y,c=t*Math.abs(i-a),u=Oe()(n.map((function(t){return t.x||0})));return(0,nt.hj)(r)&&"number"===typeof r?u=Math.max(r,u):r&&Array.isArray(r)&&r.length&&(u=Math.max(Oe()(r.map((function(t){return t.x||0}))),u)),(0,nt.hj)(u)?y.createElement("rect",{x:0,y:i<a?i:i-c,width:u+(o?parseInt("".concat(o),10):1),height:Math.floor(c)}):null}},{key:"renderClipRect",value:function(t){return"vertical"===this.props.layout?this.renderVerticalRect(t):this.renderHorizontalRect(t)}},{key:"renderAreaStatically",value:function(t,e,r,n){var i=this.props,a=i.layout,c=i.type,u=i.stroke,l=i.connectNulls,s=i.isRange,f=(i.ref,Ae(i,je));return y.createElement(o.m,{clipPath:r?"url(#clipPath-".concat(n,")"):null},y.createElement(j.H,Ee({},(0,I.L6)(f,!0),{points:t,connectNulls:l,type:c,baseLine:e,layout:a,stroke:"none",className:"recharts-area-area"})),"none"!==u&&y.createElement(j.H,Ee({},(0,I.L6)(this.props,!1),{className:"recharts-area-curve",layout:a,type:c,connectNulls:l,fill:"none",points:t})),"none"!==u&&s&&y.createElement(j.H,Ee({},(0,I.L6)(this.props,!1),{className:"recharts-area-curve",layout:a,type:c,connectNulls:l,fill:"none",points:e})))}},{key:"renderAreaWithAnimation",value:function(t,e){var r=this,n=this.props,i=n.points,a=n.baseLine,c=n.isAnimationActive,u=n.animationBegin,l=n.animationDuration,s=n.animationEasing,f=n.animationId,p=this.state,d=p.prevPoints,h=p.prevBaseLine;return y.createElement(U.ZP,{begin:u,duration:l,isActive:c,easing:s,from:{t:0},to:{t:1},key:"area-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(n){var c=n.t;if(d){var u,l=d.length/i.length,s=i.map((function(t,e){var r=Math.floor(e*l);if(d[r]){var n=d[r],o=(0,nt.k4)(n.x,t.x),i=(0,nt.k4)(n.y,t.y);return Te(Te({},t),{},{x:o(c),y:i(c)})}return t}));return u=(0,nt.hj)(a)&&"number"===typeof a?(0,nt.k4)(h,a)(c):$()(a)||we()(a)?(0,nt.k4)(h,0)(c):a.map((function(t,e){var r=Math.floor(e*l);if(h[r]){var n=h[r],o=(0,nt.k4)(n.x,t.x),i=(0,nt.k4)(n.y,t.y);return Te(Te({},t),{},{x:o(c),y:i(c)})}return t})),r.renderAreaStatically(s,u,t,e)}return y.createElement(o.m,null,y.createElement("defs",null,y.createElement("clipPath",{id:"animationClipPath-".concat(e)},r.renderClipRect(c))),y.createElement(o.m,{clipPath:"url(#animationClipPath-".concat(e,")")},r.renderAreaStatically(i,a,t,e)))}))}},{key:"renderArea",value:function(t,e){var r=this.props,n=r.points,o=r.baseLine,i=r.isAnimationActive,a=this.state,c=a.prevPoints,u=a.prevBaseLine,l=a.totalLength;return i&&n&&n.length&&(!c&&l>0||!rt()(c,n)||!rt()(u,o))?this.renderAreaWithAnimation(t,e):this.renderAreaStatically(n,o,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,n=e.dot,i=e.points,a=e.className,c=e.top,u=e.left,l=e.xAxis,s=e.yAxis,f=e.width,p=e.height,h=e.isAnimationActive,v=e.id;if(r||!i||!i.length)return null;var m=this.state.isAnimationFinished,b=1===i.length,g=(0,T.Z)("recharts-area",a),O=l&&l.allowDataOverflow,x=s&&s.allowDataOverflow,w=O||x,j=$()(v)?this.id:v,P=null!==(t=(0,I.L6)(n,!1))&&void 0!==t?t:{r:3,strokeWidth:2},S=P.r,A=void 0===S?3:S,E=P.strokeWidth,k=void 0===E?2:E,C=((0,I.jf)(n)?n:{}).clipDot,D=void 0===C||C,M=2*A+k;return y.createElement(o.m,{className:g},O||x?y.createElement("defs",null,y.createElement("clipPath",{id:"clipPath-".concat(j)},y.createElement("rect",{x:O?u:u-f/2,y:x?c:c-p/2,width:O?f:2*f,height:x?p:2*p})),!D&&y.createElement("clipPath",{id:"clipPath-dots-".concat(j)},y.createElement("rect",{x:u-M/2,y:c-M/2,width:f+M,height:p+M}))):null,b?null:this.renderArea(w,j),(n||b)&&this.renderDots(w,D,j),(!h||m)&&d.e.renderCallByParent(this.props,i))}}])&&Ie(r.prototype,n),i&&Ie(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,i}(y.PureComponent);be=ze,Le(ze,"displayName","Area"),Le(ze,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!ot.x.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),Le(ze,"getBaseValue",(function(t,e,r,n){var o=t.layout,i=t.baseValue,a=e.props.baseValue,c=null!==a&&void 0!==a?a:i;if((0,nt.hj)(c)&&"number"===typeof c)return c;var u="horizontal"===o?n:r,l=u.scale.domain();if("number"===u.type){var s=Math.max(l[0],l[1]),f=Math.min(l[0],l[1]);return"dataMin"===c?f:"dataMax"===c||s<0?s:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===c?l[0]:"dataMax"===c?l[1]:l[0]})),Le(ze,"getComposedData",(function(t){var e,r=t.props,n=t.item,o=t.xAxis,i=t.yAxis,a=t.xAxisTicks,c=t.yAxisTicks,u=t.bandSize,l=t.dataKey,s=t.stackedData,f=t.dataStartIndex,p=t.displayedData,d=t.offset,y=r.layout,h=s&&s.length,v=be.getBaseValue(r,n,o,i),m="horizontal"===y,b=!1,g=p.map((function(t,e){var r;h?r=s[f+e]:(r=(0,it.F$)(t,l),Array.isArray(r)?b=!0:r=[v,r]);var n=null==r[1]||h&&null==(0,it.F$)(t,l);return m?{x:(0,it.Hv)({axis:o,ticks:a,bandSize:u,entry:t,index:e}),y:n?null:i.scale(r[1]),value:r,payload:t}:{x:n?null:o.scale(r[1]),y:(0,it.Hv)({axis:i,ticks:c,bandSize:u,entry:t,index:e}),value:r,payload:t}}));return e=h||b?g.map((function(t){var e=Array.isArray(t.value)?t.value[0]:null;return m?{x:t.x,y:null!=e&&null!=t.y?i.scale(e):null}:{x:null!=e?o.scale(e):null,y:t.y}})):m?i.scale(v):o.scale(v),Te({points:g,baseLine:e,layout:y,isRange:b},d)})),Le(ze,"renderDotItem",(function(t,e){var r;if(y.isValidElement(t))r=y.cloneElement(t,e);else if(v()(t))r=t(e);else{var n=(0,T.Z)("recharts-area-dot","boolean"!==typeof t?t.className:""),o=e.key,i=Ae(e,Pe);r=y.createElement(A.o,Ee({},i,{key:o,className:n}))}return r}));var _e=r(48218);function Fe(t){return Fe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fe(t)}function We(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Ze(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Ue(n.key),n)}}function Ke(t,e,r){return e=Ve(e),function(t,e){if(e&&("object"===Fe(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Xe()?Reflect.construct(e,r||[],Ve(t).constructor):e.apply(t,r))}function Xe(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Xe=function(){return!!t})()}function Ve(t){return Ve=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Ve(t)}function Ge(t,e){return Ge=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Ge(t,e)}function Ye(t,e,r){return(e=Ue(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ue(t){var e=function(t,e){if("object"!=Fe(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Fe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Fe(e)?e:e+""}var He=function(t){function e(){return We(this,e),Ke(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ge(t,e)}(e,t),r=e,(n=[{key:"render",value:function(){return null}}])&&Ze(r.prototype,n),o&&Ze(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}(y.Component);Ye(He,"displayName","ZAxis"),Ye(He,"defaultProps",{zAxisId:0,range:[64,64],scale:"auto",type:"number"});var $e=["option","isActive"];function qe(){return qe=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},qe.apply(this,arguments)}function Qe(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function Je(t){var e=t.option,r=t.isActive,n=Qe(t,$e);return"string"===typeof e?y.createElement(xt.bn,qe({option:y.createElement(k.v,qe({type:e},n)),isActive:r,shapeType:"symbols"},n)):y.createElement(xt.bn,qe({option:e,isActive:r,shapeType:"symbols"},n))}function tr(t){return tr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},tr(t)}function er(){return er=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},er.apply(this,arguments)}function rr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function nr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rr(Object(r),!0).forEach((function(e){sr(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rr(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function or(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ir(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fr(n.key),n)}}function ar(t,e,r){return e=ur(e),function(t,e){if(e&&("object"===tr(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,cr()?Reflect.construct(e,r||[],ur(t).constructor):e.apply(t,r))}function cr(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(cr=function(){return!!t})()}function ur(t){return ur=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ur(t)}function lr(t,e){return lr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},lr(t,e)}function sr(t,e,r){return(e=fr(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fr(t){var e=function(t,e){if("object"!=tr(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tr(e)?e:e+""}var pr=function(t){function e(){var t;or(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return sr(t=ar(this,e,[].concat(n)),"state",{isAnimationFinished:!1}),sr(t,"handleAnimationEnd",(function(){t.setState({isAnimationFinished:!0})})),sr(t,"handleAnimationStart",(function(){t.setState({isAnimationFinished:!1})})),sr(t,"id",(0,nt.EL)("recharts-scatter-")),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&lr(t,e)}(e,t),r=e,i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}}],(n=[{key:"renderSymbolsStatically",value:function(t){var e=this,r=this.props,n=r.shape,i=r.activeShape,a=r.activeIndex,c=(0,I.L6)(this.props,!1);return t.map((function(t,r){var u=a===r,l=u?i:n,s=nr(nr({},c),t);return y.createElement(o.m,er({className:"recharts-scatter-symbol",key:"symbol-".concat(null===t||void 0===t?void 0:t.cx,"-").concat(null===t||void 0===t?void 0:t.cy,"-").concat(null===t||void 0===t?void 0:t.size,"-").concat(r)},(0,Ct.bw)(e.props,t,r),{role:"img"}),y.createElement(Je,er({option:l,isActive:u,key:"symbol-".concat(r)},s)))}))}},{key:"renderSymbolsWithAnimation",value:function(){var t=this,e=this.props,r=e.points,n=e.isAnimationActive,i=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,l=this.state.prevPoints;return y.createElement(U.ZP,{begin:i,duration:a,isActive:n,easing:c,from:{t:0},to:{t:1},key:"pie-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(e){var n=e.t,i=r.map((function(t,e){var r=l&&l[e];if(r){var o=(0,nt.k4)(r.cx,t.cx),i=(0,nt.k4)(r.cy,t.cy),a=(0,nt.k4)(r.size,t.size);return nr(nr({},t),{},{cx:o(n),cy:i(n),size:a(n)})}var c=(0,nt.k4)(0,t.size);return nr(nr({},t),{},{size:c(n)})}));return y.createElement(o.m,null,t.renderSymbolsStatically(i))}))}},{key:"renderSymbols",value:function(){var t=this.props,e=t.points,r=t.isAnimationActive,n=this.state.prevPoints;return!(r&&e&&e.length)||n&&rt()(n,e)?this.renderSymbolsStatically(e):this.renderSymbolsWithAnimation()}},{key:"renderErrorBar",value:function(){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var t=this.props,e=t.points,r=t.xAxis,n=t.yAxis,o=t.children,i=(0,I.NN)(o,Jt.W);return i?i.map((function(t,o){var i=t.props,a=i.direction,c=i.dataKey;return y.cloneElement(t,{key:"".concat(a,"-").concat(c,"-").concat(e[o]),data:e,xAxis:r,yAxis:n,layout:"x"===a?"vertical":"horizontal",dataPointFormatter:function(t,e){return{x:t.cx,y:t.cy,value:"x"===a?+t.node.x:+t.node.y,errorVal:(0,it.F$)(t,e)}}})})):null}},{key:"renderLine",value:function(){var t,e,r=this.props,n=r.points,i=r.line,a=r.lineType,c=r.lineJointType,u=(0,I.L6)(this.props,!1),l=(0,I.L6)(i,!1);if("joint"===a)t=n.map((function(t){return{x:t.cx,y:t.cy}}));else if("fitting"===a){var s=(0,nt.wr)(n),f=s.xmin,p=s.xmax,d=s.a,h=s.b,m=function(t){return d*t+h};t=[{x:f,y:m(f)},{x:p,y:m(p)}]}var b=nr(nr(nr({},u),{},{fill:"none",stroke:u&&u.fill},l),{},{points:t});return e=y.isValidElement(i)?y.cloneElement(i,b):v()(i)?i(b):y.createElement(j.H,er({},b,{type:c})),y.createElement(o.m,{className:"recharts-scatter-line",key:"recharts-scatter-line"},e)}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.points,n=t.line,i=t.className,a=t.xAxis,c=t.yAxis,u=t.left,l=t.top,s=t.width,f=t.height,p=t.id,h=t.isAnimationActive;if(e||!r||!r.length)return null;var v=this.state.isAnimationFinished,m=(0,T.Z)("recharts-scatter",i),b=a&&a.allowDataOverflow,g=c&&c.allowDataOverflow,O=b||g,x=$()(p)?this.id:p;return y.createElement(o.m,{className:m,clipPath:O?"url(#clipPath-".concat(x,")"):null},b||g?y.createElement("defs",null,y.createElement("clipPath",{id:"clipPath-".concat(x)},y.createElement("rect",{x:b?u:u-s/2,y:g?l:l-f/2,width:b?s:2*s,height:g?f:2*f}))):null,n&&this.renderLine(),this.renderErrorBar(),y.createElement(o.m,{key:"recharts-scatter-symbols"},this.renderSymbols()),(!h||v)&&d.e.renderCallByParent(this.props,r))}}])&&ir(r.prototype,n),i&&ir(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,i}(y.PureComponent);sr(pr,"displayName","Scatter"),sr(pr,"defaultProps",{xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!ot.x.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"}),sr(pr,"getComposedData",(function(t){var e=t.xAxis,r=t.yAxis,n=t.zAxis,o=t.item,i=t.displayedData,a=t.xAxisTicks,c=t.yAxisTicks,u=t.offset,l=o.props.tooltipType,f=(0,I.NN)(o.props.children,s.b),p=$()(e.dataKey)?o.props.dataKey:e.dataKey,d=$()(r.dataKey)?o.props.dataKey:r.dataKey,y=n&&n.dataKey,h=n?n.range:He.defaultProps.range,v=h&&h[0],m=e.scale.bandwidth?e.scale.bandwidth():0,b=r.scale.bandwidth?r.scale.bandwidth():0,g=i.map((function(t,i){var u=(0,it.F$)(t,p),s=(0,it.F$)(t,d),h=!$()(y)&&(0,it.F$)(t,y)||"-",g=[{name:$()(e.dataKey)?o.props.name:e.name||e.dataKey,unit:e.unit||"",value:u,payload:t,dataKey:p,type:l},{name:$()(r.dataKey)?o.props.name:r.name||r.dataKey,unit:r.unit||"",value:s,payload:t,dataKey:d,type:l}];"-"!==h&&g.push({name:n.name||n.dataKey,unit:n.unit||"",value:h,payload:t,dataKey:y,type:l});var O=(0,it.Hv)({axis:e,ticks:a,bandSize:m,entry:t,index:i,dataKey:p}),x=(0,it.Hv)({axis:r,ticks:c,bandSize:b,entry:t,index:i,dataKey:d}),w="-"!==h?n.scale(h):v,j=Math.sqrt(Math.max(w,0)/Math.PI);return nr(nr({},t),{},{cx:O,cy:x,x:O-j,y:x-j,xAxis:e,yAxis:r,zAxis:n,width:2*j,height:2*j,size:w,node:{x:u,y:s,z:h},tooltipPayload:g,tooltipPosition:{x:O,y:x},payload:t},f&&f[i]&&f[i].props)}));return nr({points:g},u)}));var dr=r(23007),yr=r(58104),hr=r(88259),vr=r(87210),mr=(0,hr.z)({chartName:"LineChart",GraphicalChild:me,axisComponents:[{axisType:"xAxis",AxisComp:dr.K},{axisType:"yAxis",AxisComp:yr.B}],formatAxisMap:vr.t9}),br=r(79416),gr=r(77434),Or=r(38863),xr=r.n(Or),wr=r(80089),jr=r.n(wr),Pr=["#1890FF","#66B5FF","#41D9C7","#2FC25B","#6EDB8F","#9AE65C","#FACC14","#E6965C","#57AD71","#223273","#738AE6","#7564CC","#8543E0","#A877ED","#5C8EE6","#13C2C2","#70E0E0","#5CA3E6","#3436C7","#8082FF","#DD81E6","#F04864","#FA7D92","#D598D9"],Sr=r(99875),Ar=["width","height","className","style","children","type"];function Er(t){return Er="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Er(t)}function kr(){return kr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},kr.apply(this,arguments)}function Tr(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function Cr(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Ir(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,_r(n.key),n)}}function Dr(t,e,r){return e=Nr(e),function(t,e){if(e&&("object"===Er(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Mr()?Reflect.construct(e,r||[],Nr(t).constructor):e.apply(t,r))}function Mr(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Mr=function(){return!!t})()}function Nr(t){return Nr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Nr(t)}function Br(t,e){return Br=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Br(t,e)}function Lr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Rr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Lr(Object(r),!0).forEach((function(e){zr(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Lr(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function zr(t,e,r){return(e=_r(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _r(t){var e=function(t,e){if("object"!=Er(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Er(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Er(e)?e:e+""}var Fr="value",Wr=function t(e){var r,n=e.depth,o=e.node,i=e.index,a=e.valueKey,c=o.children,u=n+1,l=c&&c.length?c.map((function(e,r){return t({depth:u,node:e,index:r,valueKey:a})})):null;return r=c&&c.length?l.reduce((function(t,e){return t+e.value}),0):we()(o[a])||o[a]<=0?0:o[a],Rr(Rr({},o),{},zr(zr(zr({children:l},Fr,r),"depth",n),"index",i))},Zr=function(t,e,r){var n=e*e,o=t.area*t.area,i=t.reduce((function(t,e){return{min:Math.min(t.min,e.area),max:Math.max(t.max,e.area)}}),{min:1/0,max:0}),a=i.min,c=i.max;return o?Math.max(n*c*r/o,o/(n*a*r)):1/0},Kr=function(t,e,r,n){return e===r.width?function(t,e,r,n){var o=e?Math.round(t.area/e):0;(n||o>r.height)&&(o=r.height);for(var i,a=r.x,c=0,u=t.length;c<u;c++)(i=t[c]).x=a,i.y=r.y,i.height=o,i.width=Math.min(o?Math.round(i.area/o):0,r.x+r.width-a),a+=i.width;return i.width+=r.x+r.width-a,Rr(Rr({},r),{},{y:r.y+o,height:r.height-o})}(t,e,r,n):function(t,e,r,n){var o=e?Math.round(t.area/e):0;(n||o>r.width)&&(o=r.width);for(var i,a=r.y,c=0,u=t.length;c<u;c++)(i=t[c]).x=r.x,i.y=a,i.width=o,i.height=Math.min(o?Math.round(i.area/o):0,r.y+r.height-a),a+=i.height;return i&&(i.height+=r.y+r.height-a),Rr(Rr({},r),{},{x:r.x+o,width:r.width-o})}(t,e,r,n)},Xr=function t(e,r){var n=e.children;if(n&&n.length){var o,i,a=function(t){return{x:t.x,y:t.y,width:t.width,height:t.height}}(e),c=[],u=1/0,l=Math.min(a.width,a.height),s=function(t,e){var r=e<0?0:e;return t.map((function(t){var e=t.value*r;return Rr(Rr({},t),{},{area:we()(e)||e<=0?0:e})}))}(n,a.width*a.height/e.value),f=s.slice();for(c.area=0;f.length>0;)c.push(o=f[0]),c.area+=o.area,(i=Zr(c,l,r))<=u?(f.shift(),u=i):(c.area-=c.pop().area,a=Kr(c,l,a,!1),l=Math.min(a.width,a.height),c.length=c.area=0,u=1/0);return c.length&&(a=Kr(c,l,a,!0),c.length=c.area=0),Rr(Rr({},e),{},{children:s.map((function(e){return t(e,r)}))})}return e},Vr={isTooltipActive:!1,isAnimationFinished:!1,activeNode:null,formatRoot:null,currentRoot:null,nestIndex:[]},Gr=function(t){function e(){var t;Cr(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return zr(t=Dr(this,e,[].concat(n)),"state",Rr({},Vr)),zr(t,"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),v()(e)&&e()})),zr(t,"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),v()(e)&&e()})),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Br(t,e)}(e,t),r=e,a=[{key:"getDerivedStateFromProps",value:function(t,e){if(t.data!==e.prevData||t.type!==e.prevType||t.width!==e.prevWidth||t.height!==e.prevHeight||t.dataKey!==e.prevDataKey||t.aspectRatio!==e.prevAspectRatio){var r=Wr({depth:0,node:{children:t.data,x:0,y:0,width:t.width,height:t.height},index:0,valueKey:t.dataKey}),n=Xr(r,t.aspectRatio);return Rr(Rr({},e),{},{formatRoot:n,currentRoot:r,nestIndex:[r],prevAspectRatio:t.aspectRatio,prevData:t.data,prevWidth:t.width,prevHeight:t.height,prevDataKey:t.dataKey,prevType:t.type})}return null}},{key:"renderContentItem",value:function(t,e,r,n){if(y.isValidElement(t))return y.cloneElement(t,e);if(v()(t))return t(e);var o=e.x,i=e.y,a=e.width,c=e.height,u=e.index,l=null;a>10&&c>10&&e.children&&"nest"===r&&(l=y.createElement(S.m,{points:[{x:o+2,y:i+c/2},{x:o+6,y:i+c/2+3},{x:o+2,y:i+c/2+6}]}));var s=null,f=(0,Sr.xE)(e.name);a>20&&c>20&&f.width<a&&f.height<c&&(s=y.createElement("text",{x:o+8,y:i+c/2+7,fontSize:14},e.name));var p=n||Pr;return y.createElement("g",null,y.createElement(P.A,kr({fill:e.depth<2?p[u%p.length]:"rgba(255,255,255,0)",stroke:"#fff"},xr()(e,"children"),{role:"img"})),l,s)}}],(i=[{key:"handleMouseEnter",value:function(t,e){e.persist();var r=this.props,n=r.onMouseEnter,o=r.children;(0,I.sP)(o,c.u)?this.setState({isTooltipActive:!0,activeNode:t},(function(){n&&n(t,e)})):n&&n(t,e)}},{key:"handleMouseLeave",value:function(t,e){e.persist();var r=this.props,n=r.onMouseLeave,o=r.children;(0,I.sP)(o,c.u)?this.setState({isTooltipActive:!1,activeNode:null},(function(){n&&n(t,e)})):n&&n(t,e)}},{key:"handleClick",value:function(t){var e=this.props,r=e.onClick;if("nest"===e.type&&t.children){var n=this.props,o=n.width,i=n.height,a=n.dataKey,c=n.aspectRatio,u=Wr({depth:0,node:Rr(Rr({},t),{},{x:0,y:0,width:o,height:i}),index:0,valueKey:a}),l=Xr(u,c),s=this.state.nestIndex;s.push(t),this.setState({formatRoot:l,currentRoot:u,nestIndex:s})}r&&r(t)}},{key:"handleNestIndex",value:function(t,e){var r=this.state.nestIndex,n=this.props,o=n.width,i=n.height,a=n.dataKey,c=n.aspectRatio,u=Wr({depth:0,node:Rr(Rr({},t),{},{x:0,y:0,width:o,height:i}),index:0,valueKey:a}),l=Xr(u,c);r=r.slice(0,e+1),this.setState({formatRoot:l,currentRoot:t,nestIndex:r})}},{key:"renderItem",value:function(t,e,r){var n=this,i=this.props,a=i.isAnimationActive,c=i.animationBegin,u=i.animationDuration,l=i.animationEasing,s=i.isUpdateAnimationActive,f=i.type,p=i.animationId,d=i.colorPanel,h=this.state.isAnimationFinished,v=e.width,m=e.height,b=e.x,g=e.y,O=e.depth,x=parseInt("".concat((2*Math.random()-1)*v),10),w={};return(r||"nest"===f)&&(w={onMouseEnter:this.handleMouseEnter.bind(this,e),onMouseLeave:this.handleMouseLeave.bind(this,e),onClick:this.handleClick.bind(this,e)}),a?y.createElement(U.ZP,{begin:c,duration:u,isActive:a,easing:l,key:"treemap-".concat(p),from:{x:b,y:g,width:v,height:m},to:{x:b,y:g,width:v,height:m},onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(r){var i=r.x,p=r.y,v=r.width,m=r.height;return y.createElement(U.ZP,{from:"translate(".concat(x,"px, ").concat(x,"px)"),to:"translate(0, 0)",attributeName:"transform",begin:c,easing:l,isActive:a,duration:u},y.createElement(o.m,w,O>2&&!h?null:n.constructor.renderContentItem(t,Rr(Rr({},e),{},{isAnimationActive:a,isUpdateAnimationActive:!s,width:v,height:m,x:i,y:p}),f,d)))})):y.createElement(o.m,w,this.constructor.renderContentItem(t,Rr(Rr({},e),{},{isAnimationActive:!1,isUpdateAnimationActive:!1,width:v,height:m,x:b,y:g}),f,d))}},{key:"renderNode",value:function(t,e){var r=this,n=this.props,i=n.content,a=n.type,c=Rr(Rr(Rr({},(0,I.L6)(this.props,!1)),e),{},{root:t}),u=!e.children||!e.children.length;return!(this.state.currentRoot.children||[]).filter((function(t){return t.depth===e.depth&&t.name===e.name})).length&&t.depth&&"nest"===a?null:y.createElement(o.m,{key:"recharts-treemap-node-".concat(c.x,"-").concat(c.y,"-").concat(c.name),className:"recharts-treemap-depth-".concat(e.depth)},this.renderItem(i,c,u),e.children&&e.children.length?e.children.map((function(t){return r.renderNode(e,t)})):null)}},{key:"renderAllNodes",value:function(){var t=this.state.formatRoot;return t?this.renderNode(t,t):null}},{key:"renderTooltip",value:function(){var t=this.props,e=t.children,r=t.nameKey,n=(0,I.sP)(e,c.u);if(!n)return null;var o=this.props,i=o.width,a=o.height,u=this.state,l=u.isTooltipActive,s=u.activeNode,f={x:0,y:0,width:i,height:a},p=s?{x:s.x+s.width/2,y:s.y+s.height/2}:null,d=l&&s?[{payload:s,name:(0,it.F$)(s,r,""),value:(0,it.F$)(s,Fr)}]:[];return y.cloneElement(n,{viewBox:f,active:l,coordinate:p,label:"",payload:d})}},{key:"renderNestIndex",value:function(){var t=this,e=this.props,r=e.nameKey,n=e.nestIndexContent,o=this.state.nestIndex;return y.createElement("div",{className:"recharts-treemap-nest-index-wrapper",style:{marginTop:"8px",textAlign:"center"}},o.map((function(e,o){var i=jr()(e,r,"root"),a=null;return y.isValidElement(n)&&(a=y.cloneElement(n,e,o)),a=v()(n)?n(e,o):i,y.createElement("div",{onClick:t.handleNestIndex.bind(t,e,o),key:"nest-index-".concat((0,nt.EL)()),className:"recharts-treemap-nest-index-box",style:{cursor:"pointer",display:"inline-block",padding:"0 7px",background:"#000",color:"#fff",marginRight:"3px"}},a)})))}},{key:"render",value:function(){if(!(0,I.TT)(this))return null;var t=this.props,e=t.width,r=t.height,o=t.className,i=t.style,a=t.children,c=t.type,u=Tr(t,Ar),l=(0,I.L6)(u,!1);return y.createElement("div",{className:(0,T.Z)("recharts-wrapper",o),style:Rr(Rr({},i),{},{position:"relative",cursor:"default",width:e,height:r}),role:"region"},y.createElement(n.T,kr({},l,{width:e,height:"nest"===c?r-30:r}),this.renderAllNodes(),(0,I.hQ)(a)),this.renderTooltip(),"nest"===c&&this.renderNestIndex())}}])&&Ir(r.prototype,i),a&&Ir(r,a),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,i,a}(y.PureComponent);zr(Gr,"displayName","Treemap"),zr(Gr,"defaultProps",{aspectRatio:.5*(1+Math.sqrt(5)),dataKey:"value",type:"flat",isAnimationActive:!ot.x.isSsr,isUpdateAnimationActive:!ot.x.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"linear"});var Yr=r(58399),Ur=r.n(Yr),Hr=r(73398),$r=r.n(Hr),qr=r(61224),Qr=r.n(qr),Jr=r(68201),tn=["width","height","className","style","children"],en=["sourceX","sourceY","sourceControlX","targetX","targetY","targetControlX","linkWidth"];function rn(t){return rn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},rn(t)}function nn(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function on(){return on=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},on.apply(this,arguments)}function an(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function cn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hn(n.key),n)}}function un(t,e,r){return e=sn(e),function(t,e){if(e&&("object"===rn(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,ln()?Reflect.construct(e,r||[],sn(t).constructor):e.apply(t,r))}function ln(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(ln=function(){return!!t})()}function sn(t){return sn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},sn(t)}function fn(t,e){return fn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},fn(t,e)}function pn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function dn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pn(Object(r),!0).forEach((function(e){yn(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pn(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function yn(t,e,r){return(e=hn(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hn(t){var e=function(t,e){if("object"!=rn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=rn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==rn(e)?e:e+""}var vn={x:0,y:0},mn=function(t){return t.y+t.dy/2},bn=function(t){return t&&t.value||0},gn=function(t,e){return e.reduce((function(e,r){return e+bn(t[r])}),0)},On=function(t,e,r){return r.reduce((function(r,n){var o=e[n],i=t[o.source];return r+mn(i)*bn(e[n])}),0)},xn=function(t,e,r){return r.reduce((function(r,n){var o=e[n],i=t[o.target];return r+mn(i)*bn(e[n])}),0)},wn=function(t,e){return t.y-e.y},jn=function t(e,r){for(var n=r.targetNodes,o=0,i=n.length;o<i;o++){var a=e[n[o]];a&&(a.depth=Math.max(r.depth+1,a.depth),t(e,a))}},Pn=function(t,e,r){for(var n=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],o=0,i=t.length;o<i;o++){var a=t[o],c=a.length;n&&a.sort(wn);for(var u=0,l=0;l<c;l++){var s=a[l],f=u-s.y;f>0&&(s.y+=f),u=s.y+s.dy+r}u=e+r;for(var p=c-1;p>=0;p--){var d=a[p],y=d.y+d.dy+r-u;if(!(y>0))break;d.y-=y,u=d.y}}},Sn=function(t,e,r,n){for(var o=0,i=e.length;o<i;o++)for(var a=e[o],c=0,u=a.length;c<u;c++){var l=a[c];if(l.sourceLinks.length){var s=gn(r,l.sourceLinks),f=On(t,r,l.sourceLinks)/s;l.y+=(f-mn(l))*n}}},An=function(t,e,r,n){for(var o=e.length-1;o>=0;o--)for(var i=e[o],a=0,c=i.length;a<c;a++){var u=i[a];if(u.targetLinks.length){var l=gn(r,u.targetLinks),s=xn(t,r,u.targetLinks)/l;u.y+=(s-mn(u))*n}}},En=function(t){var e=t.data,r=t.width,n=t.height,o=t.iterations,i=t.nodeWidth,a=t.nodePadding,c=t.sort,u=e.links,l=function(t,e,r){for(var n=t.nodes,o=t.links,i=n.map((function(t,e){var r=function(t,e){for(var r=[],n=[],o=[],i=[],a=0,c=t.length;a<c;a++){var u=t[a];u.source===e&&(o.push(u.target),i.push(a)),u.target===e&&(r.push(u.source),n.push(a))}return{sourceNodes:r,sourceLinks:n,targetLinks:i,targetNodes:o}}(o,e);return dn(dn(dn({},t),r),{},{value:Math.max(gn(o,r.sourceLinks),gn(o,r.targetLinks)),depth:0})})),a=0,c=i.length;a<c;a++){var u=i[a];u.sourceNodes.length||jn(i,u)}var l=Ur()(i,(function(t){return t.depth})).depth;if(l>=1)for(var s=(e-r)/l,f=0,p=i.length;f<p;f++){var d=i[f];d.targetNodes.length||(d.depth=l),d.x=d.depth*s,d.dx=r}return{tree:i,maxDepth:l}}(e,r,i),s=l.tree,f=function(t){for(var e=[],r=0,n=t.length;r<n;r++){var o=t[r];e[o.depth]||(e[o.depth]=[]),e[o.depth].push(o)}return e}(s),p=function(t,e,r,n){for(var o=$r()(t.map((function(t){return(e-(t.length-1)*r)/Qr()(t,bn)}))),i=0,a=t.length;i<a;i++)for(var c=0,u=t[i].length;c<u;c++){var l=t[i][c];l.y=c,l.dy=l.value*o}return n.map((function(t){return dn(dn({},t),{},{dy:bn(t)*o})}))}(f,n,a,u);Pn(f,n,a,c);for(var d=1,y=1;y<=o;y++)An(s,f,p,d*=.99),Pn(f,n,a,c),Sn(s,f,p,d),Pn(f,n,a,c);return function(t,e){for(var r=0,n=t.length;r<n;r++){var o=t[r],i=0,a=0;o.targetLinks.sort((function(r,n){return t[e[r].target].y-t[e[n].target].y})),o.sourceLinks.sort((function(r,n){return t[e[r].source].y-t[e[n].source].y}));for(var c=0,u=o.targetLinks.length;c<u;c++){var l=e[o.targetLinks[c]];l&&(l.sy=i,i+=l.dy)}for(var s=0,f=o.sourceLinks.length;s<f;s++){var p=e[o.sourceLinks[s]];p&&(p.ty=a,a+=p.dy)}}}(s,p),{nodes:s,links:p}},kn=function(t){function e(){var t;an(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return yn(t=un(this,e,[].concat(n)),"state",{activeElement:null,activeElementType:null,isTooltipActive:!1,nodes:[],links:[]}),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fn(t,e)}(e,t),r=e,a=[{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.height,i=t.margin,a=t.iterations,c=t.nodeWidth,u=t.nodePadding,l=t.sort;if(r!==e.prevData||n!==e.prevWidth||o!==e.prevHeight||!(0,Jr.w)(i,e.prevMargin)||a!==e.prevIterations||c!==e.prevNodeWidth||u!==e.prevNodePadding||l!==e.sort){var s=n-(i&&i.left||0)-(i&&i.right||0),f=o-(i&&i.top||0)-(i&&i.bottom||0),p=En({data:r,width:s,height:f,iterations:a,nodeWidth:c,nodePadding:u,sort:l}),d=p.links,y=p.nodes;return dn(dn({},e),{},{nodes:y,links:d,prevData:r,prevWidth:a,prevHeight:o,prevMargin:i,prevNodePadding:u,prevNodeWidth:c,prevIterations:a,prevSort:l})}return null}},{key:"renderLinkItem",value:function(t,e){if(y.isValidElement(t))return y.cloneElement(t,e);if(v()(t))return t(e);var r=e.sourceX,n=e.sourceY,o=e.sourceControlX,i=e.targetX,a=e.targetY,c=e.targetControlX,u=e.linkWidth,l=nn(e,en);return y.createElement("path",on({className:"recharts-sankey-link",d:"\n          M".concat(r,",").concat(n,"\n          C").concat(o,",").concat(n," ").concat(c,",").concat(a," ").concat(i,",").concat(a,"\n        "),fill:"none",stroke:"#333",strokeWidth:u,strokeOpacity:"0.2"},(0,I.L6)(l,!1)))}},{key:"renderNodeItem",value:function(t,e){return y.isValidElement(t)?y.cloneElement(t,e):v()(t)?t(e):y.createElement(P.A,on({className:"recharts-sankey-node",fill:"#0088fe",fillOpacity:"0.8"},(0,I.L6)(e,!1),{role:"img"}))}}],(i=[{key:"handleMouseEnter",value:function(t,e,r){var n=this.props,o=n.onMouseEnter,i=n.children,a=(0,I.sP)(i,c.u);a?this.setState((function(r){return"hover"===a.props.trigger?dn(dn({},r),{},{activeElement:t,activeElementType:e,isTooltipActive:!0}):r}),(function(){o&&o(t,e,r)})):o&&o(t,e,r)}},{key:"handleMouseLeave",value:function(t,e,r){var n=this.props,o=n.onMouseLeave,i=n.children,a=(0,I.sP)(i,c.u);a?this.setState((function(t){return"hover"===a.props.trigger?dn(dn({},t),{},{activeElement:void 0,activeElementType:void 0,isTooltipActive:!1}):t}),(function(){o&&o(t,e,r)})):o&&o(t,e,r)}},{key:"handleClick",value:function(t,e,r){var n=this.props,o=n.onClick,i=n.children,a=(0,I.sP)(i,c.u);a&&"click"===a.props.trigger&&(this.state.isTooltipActive?this.setState((function(t){return dn(dn({},t),{},{activeElement:void 0,activeElementType:void 0,isTooltipActive:!1})})):this.setState((function(r){return dn(dn({},r),{},{activeElement:t,activeElementType:e,isTooltipActive:!0})}))),o&&o(t,e,r)}},{key:"renderLinks",value:function(t,e){var r=this,n=this.props,i=n.linkCurvature,a=n.link,c=n.margin,u=jr()(c,"top")||0,l=jr()(c,"left")||0;return y.createElement(o.m,{className:"recharts-sankey-links",key:"recharts-sankey-links"},t.map((function(t,n){var c=t.sy,s=t.ty,f=t.dy,p=e[t.source],d=e[t.target],h=p.x+p.dx+l,v=d.x+l,m=function(t,e){var r=+t,n=e-r;return function(t){return r+n*t}}(h,v),b=m(i),g=m(1-i),O=dn({sourceX:h,targetX:v,sourceY:p.y+c+f/2+u,targetY:d.y+s+f/2+u,sourceControlX:b,targetControlX:g,sourceRelativeY:c,targetRelativeY:s,linkWidth:f,index:n,payload:dn(dn({},t),{},{source:p,target:d})},(0,I.L6)(a,!1)),x={onMouseEnter:r.handleMouseEnter.bind(r,O,"link"),onMouseLeave:r.handleMouseLeave.bind(r,O,"link"),onClick:r.handleClick.bind(r,O,"link")};return y.createElement(o.m,on({key:"link-".concat(t.source,"-").concat(t.target,"-").concat(t.value)},x),r.constructor.renderLinkItem(a,O))})))}},{key:"renderNodes",value:function(t){var e=this,r=this.props,n=r.node,i=r.margin,a=jr()(i,"top")||0,c=jr()(i,"left")||0;return y.createElement(o.m,{className:"recharts-sankey-nodes",key:"recharts-sankey-nodes"},t.map((function(t,r){var i=t.x,u=t.y,l=t.dx,s=t.dy,f=dn(dn({},(0,I.L6)(n,!1)),{},{x:i+c,y:u+a,width:l,height:s,index:r,payload:t}),p={onMouseEnter:e.handleMouseEnter.bind(e,f,"node"),onMouseLeave:e.handleMouseLeave.bind(e,f,"node"),onClick:e.handleClick.bind(e,f,"node")};return y.createElement(o.m,on({key:"node-".concat(t.x,"-").concat(t.y,"-").concat(t.value)},p),e.constructor.renderNodeItem(n,f))})))}},{key:"renderTooltip",value:function(){var t=this.props,e=t.children,r=t.width,n=t.height,o=t.nameKey,i=(0,I.sP)(e,c.u);if(!i)return null;var a,u=this.state,l=u.isTooltipActive,s=u.activeElement,f=u.activeElementType,p={x:0,y:0,width:r,height:n},d=s?(a=s,"node"===f?{x:a.x+a.width/2,y:a.y+a.height/2}:{x:(a.sourceX+a.targetX)/2,y:(a.sourceY+a.targetY)/2}):vn,h=s?function(t,e,r){var n=t.payload;if("node"===e)return[{payload:t,name:(0,it.F$)(n,r,""),value:(0,it.F$)(n,"value")}];if(n.source&&n.target){var o=(0,it.F$)(n.source,r,""),i=(0,it.F$)(n.target,r,"");return[{payload:t,name:"".concat(o," - ").concat(i),value:(0,it.F$)(n,"value")}]}return[]}(s,f,o):[];return y.cloneElement(i,{viewBox:p,active:l,coordinate:d,label:"",payload:h})}},{key:"render",value:function(){if(!(0,I.TT)(this))return null;var t=this.props,e=t.width,r=t.height,o=t.className,i=t.style,a=t.children,c=nn(t,tn),u=this.state,l=u.links,s=u.nodes,f=(0,I.L6)(c,!1);return y.createElement("div",{className:(0,T.Z)("recharts-wrapper",o),style:dn(dn({},i),{},{position:"relative",cursor:"default",width:e,height:r}),role:"region"},y.createElement(n.T,on({},f,{width:e,height:r}),(0,I.hQ)(a),this.renderLinks(l,s),this.renderNodes(s)),this.renderTooltip())}}])&&cn(r.prototype,i),a&&cn(r,a),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,i,a}(y.PureComponent);yn(kn,"displayName","Sankey"),yn(kn,"defaultProps",{nameKey:"name",dataKey:"value",nodePadding:10,nodeWidth:10,linkCurvature:.5,iterations:32,margin:{top:5,right:5,bottom:5,left:5},sort:!0});var Tn=(0,hr.z)({chartName:"RadarChart",GraphicalChild:Ot,axisComponents:[{axisType:"angleAxis",AxisComp:G.I},{axisType:"radiusAxis",AxisComp:V.S}],formatAxisMap:C.t9,defaultProps:{layout:"centric",startAngle:90,endAngle:-270,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),Cn=(0,hr.z)({chartName:"ScatterChart",GraphicalChild:pr,defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],axisComponents:[{axisType:"xAxis",AxisComp:dr.K},{axisType:"yAxis",AxisComp:yr.B},{axisType:"zAxis",AxisComp:He}],formatAxisMap:vr.t9}),In=(0,hr.z)({chartName:"AreaChart",GraphicalChild:ze,axisComponents:[{axisType:"xAxis",AxisComp:dr.K},{axisType:"yAxis",AxisComp:yr.B}],formatAxisMap:vr.t9}),Dn=(0,hr.z)({chartName:"RadialBarChart",GraphicalChild:Gt,legendContent:"children",defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"angleAxis",AxisComp:G.I},{axisType:"radiusAxis",AxisComp:V.S}],formatAxisMap:C.t9,defaultProps:{layout:"radial",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),Mn=(0,hr.z)({chartName:"ComposedChart",GraphicalChild:[me,ze,_e.$,pr],axisComponents:[{axisType:"xAxis",AxisComp:dr.K},{axisType:"yAxis",AxisComp:yr.B},{axisType:"zAxis",AxisComp:He}],formatAxisMap:vr.t9}),Nn=r(8919);function Bn(){return Bn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Bn.apply(this,arguments)}function Ln(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||zn(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Rn(t){return function(t){if(Array.isArray(t))return _n(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||zn(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zn(t,e){if(t){if("string"===typeof t)return _n(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_n(t,e):void 0}}function _n(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Fn={fontWeight:"bold",paintOrder:"stroke fill",fontSize:".75rem",stroke:"#FFF",fill:"black",pointerEvents:"none"};function Wn(t){if(!t.children||0===t.children.length)return 1;var e=t.children.map((function(t){return Wn(t)}));return 1+Math.max.apply(Math,Rn(e))}var Zn,Kn=function(t){var e=t.className,r=t.data,i=t.children,a=t.width,u=t.height,l=t.padding,s=void 0===l?2:l,p=t.dataKey,d=void 0===p?"value":p,h=t.ringPadding,v=void 0===h?2:h,m=t.innerRadius,b=void 0===m?50:m,g=t.fill,O=void 0===g?"#333":g,x=t.stroke,j=void 0===x?"#FFF":x,P=t.textOptions,S=void 0===P?Fn:P,A=t.outerRadius,E=void 0===A?Math.min(a,u)/2:A,k=t.cx,D=void 0===k?a/2:k,M=t.cy,N=void 0===M?u/2:M,B=t.startAngle,L=void 0===B?0:B,R=t.endAngle,z=void 0===R?360:R,_=t.onClick,F=t.onMouseEnter,W=t.onMouseLeave,Z=Ln((0,y.useState)(!1),2),K=Z[0],X=Z[1],V=Ln((0,y.useState)(null),2),G=V[0],Y=V[1],U=(0,Nn.Z)([0,r[d]],[0,z]),H=(E-b)/Wn(r),$=[],q=new Map([]);function Q(t,e){F&&F(t,e),Y(t),X(!0)}function J(t,e){W&&W(t,e),Y(null),X(!1)}function tt(t){_&&_(t)}!function t(e,r){var n=r.radius,o=r.innerR,i=r.initialAngle,a=r.childColor,c=i;e&&e.forEach((function(e){var r,i,u=U(e[d]),l=c,p=null!==(r=null!==(i=null===e||void 0===e?void 0:e.fill)&&void 0!==i?i:a)&&void 0!==r?r:O,h=(0,C.op)(0,0,o+n/2,-(l+u-u/2)),m=h.x,b=h.y;c+=u,$.push(y.createElement("g",{"aria-label":e.name,tabIndex:0},y.createElement(w.L,{onClick:function(){return tt(e)},onMouseEnter:function(t){return Q(e,t)},onMouseLeave:function(t){return J(e,t)},fill:p,stroke:j,strokeWidth:s,startAngle:l,endAngle:l+u,innerRadius:o,outerRadius:o+n,cx:D,cy:N}),y.createElement(f.x,Bn({},S,{alignmentBaseline:"middle",textAnchor:"middle",x:m+D,y:N-b}),e[d])));var g=(0,C.op)(D,N,o+n/2,l),x=g.x,P=g.y;return q.set(e.name,{x:x,y:P}),t(e.children,{radius:n,innerR:o+n+v,initialAngle:l,childColor:p})}))}(r.children,{radius:H,innerR:b,initialAngle:L});var et=(0,T.Z)("recharts-sunburst",e);return y.createElement("div",{className:(0,T.Z)("recharts-wrapper",e),style:{position:"relative",width:a,height:u},role:"region"},y.createElement(n.T,{width:a,height:u},i,y.createElement(o.m,{className:et},$)),function(){var t=(0,I.sP)([i],c.u);if(!t||!G)return null;var e={x:0,y:0,width:a,height:u};return y.cloneElement(t,{viewBox:e,coordinate:q.get(G.name),payload:[G],active:K})}())},Xn=r(47315),Vn=r.n(Xn),Gn=r(72139),Yn=r.n(Gn);function Un(t){return Un="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Un(t)}function Hn(){return Hn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Hn.apply(this,arguments)}function $n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function qn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?$n(Object(r),!0).forEach((function(e){Qn(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):$n(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Qn(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=Un(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Un(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Un(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Jn(t,e){var r="".concat(e.x||t.x),n=parseInt(r,10),o="".concat(e.y||t.y),i=parseInt(o,10),a="".concat((null===e||void 0===e?void 0:e.height)||(null===t||void 0===t?void 0:t.height)),c=parseInt(a,10);return qn(qn(qn({},e),(0,xt.Ob)(t)),{},{height:c,x:n,y:i})}function to(t){return y.createElement(xt.bn,Hn({shapeType:"trapezoid",propTransformer:Jn},t))}function eo(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return ro(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ro(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ro(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function no(t){return no="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},no(t)}function oo(){return oo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},oo.apply(this,arguments)}function io(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ao(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?io(Object(r),!0).forEach((function(e){yo(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):io(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function co(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function uo(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ho(n.key),n)}}function lo(t,e,r){return e=fo(e),function(t,e){if(e&&("object"===no(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,so()?Reflect.construct(e,r||[],fo(t).constructor):e.apply(t,r))}function so(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(so=function(){return!!t})()}function fo(t){return fo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},fo(t)}function po(t,e){return po=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},po(t,e)}function yo(t,e,r){return(e=ho(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ho(t){var e=function(t,e){if("object"!=no(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=no(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==no(e)?e:e+""}var vo=function(t){function e(){var t;co(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return yo(t=lo(this,e,[].concat(n)),"state",{isAnimationFinished:!1}),yo(t,"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),v()(e)&&e()})),yo(t,"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),v()(e)&&e()})),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&po(t,e)}(e,t),r=e,i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curTrapezoids:t.trapezoids,prevTrapezoids:e.curTrapezoids}:t.trapezoids!==e.curTrapezoids?{curTrapezoids:t.trapezoids}:null}}],(n=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"renderTrapezoidsStatically",value:function(t){var e=this,r=this.props,n=r.shape,i=r.activeShape;return t.map((function(t,r){var a=e.isActiveIndex(r)?i:n,c=ao(ao({},t),{},{isActive:e.isActiveIndex(r),stroke:t.stroke});return y.createElement(o.m,oo({className:"recharts-funnel-trapezoid"},(0,Ct.bw)(e.props,t,r),{key:"trapezoid-".concat(null===t||void 0===t?void 0:t.x,"-").concat(null===t||void 0===t?void 0:t.y,"-").concat(null===t||void 0===t?void 0:t.name,"-").concat(null===t||void 0===t?void 0:t.value),role:"img"}),y.createElement(to,oo({option:a},c)))}))}},{key:"renderTrapezoidsWithAnimation",value:function(){var t=this,e=this.props,r=e.trapezoids,n=e.isAnimationActive,i=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,l=this.state.prevTrapezoids;return y.createElement(U.ZP,{begin:i,duration:a,isActive:n,easing:c,from:{t:0},to:{t:1},key:"funnel-".concat(u),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(e){var n=e.t,i=r.map((function(t,e){var r=l&&l[e];if(r){var o=(0,nt.k4)(r.x,t.x),i=(0,nt.k4)(r.y,t.y),a=(0,nt.k4)(r.upperWidth,t.upperWidth),c=(0,nt.k4)(r.lowerWidth,t.lowerWidth),u=(0,nt.k4)(r.height,t.height);return ao(ao({},t),{},{x:o(n),y:i(n),upperWidth:a(n),lowerWidth:c(n),height:u(n)})}var s=(0,nt.k4)(t.x+t.upperWidth/2,t.x),f=(0,nt.k4)(t.y+t.height/2,t.y),p=(0,nt.k4)(0,t.upperWidth),d=(0,nt.k4)(0,t.lowerWidth),y=(0,nt.k4)(0,t.height);return ao(ao({},t),{},{x:s(n),y:f(n),upperWidth:p(n),lowerWidth:d(n),height:y(n)})}));return y.createElement(o.m,null,t.renderTrapezoidsStatically(i))}))}},{key:"renderTrapezoids",value:function(){var t=this.props,e=t.trapezoids,r=t.isAnimationActive,n=this.state.prevTrapezoids;return!(r&&e&&e.length)||n&&rt()(n,e)?this.renderTrapezoidsStatically(e):this.renderTrapezoidsWithAnimation()}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.trapezoids,n=t.className,i=t.isAnimationActive,a=this.state.isAnimationFinished;if(e||!r||!r.length)return null;var c=(0,T.Z)("recharts-trapezoids",n);return y.createElement(o.m,{className:c},this.renderTrapezoids(),(!i||a)&&d.e.renderCallByParent(this.props,r))}}])&&uo(r.prototype,n),i&&uo(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,i}(y.PureComponent);Zn=vo,yo(vo,"displayName","Funnel"),yo(vo,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",labelLine:!0,hide:!1,isAnimationActive:!ot.x.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",lastShapeType:"triangle"}),yo(vo,"getRealFunnelData",(function(t){var e=t.props,r=e.data,n=e.children,o=(0,I.L6)(t.props,!1),i=(0,I.NN)(n,s.b);return r&&r.length?r.map((function(t,e){return ao(ao(ao({payload:t},o),t),i&&i[e]&&i[e].props)})):i&&i.length?i.map((function(t){return ao(ao({},o),t.props)})):[]})),yo(vo,"getRealWidthHeight",(function(t,e){var r=t.props.width,n=e.width,o=e.height,i=e.left,a=e.right,c=e.top,u=e.bottom,l=o,s=n;return Vn()(r)?s=r:Yn()(r)&&(s=s*parseFloat(r)/100),{realWidth:s-i-a-50,realHeight:l-u-c,offsetX:(n-s)/2,offsetY:(o-l)/2}})),yo(vo,"getComposedData",(function(t){var e=t.item,r=t.offset,n=Zn.getRealFunnelData(e),o=e.props,i=o.dataKey,a=o.nameKey,c=o.tooltipType,u=o.lastShapeType,l=o.reversed,s=r.left,f=r.top,p=Zn.getRealWidthHeight(e,r),d=p.realHeight,y=p.realWidth,h=p.offsetX,v=p.offsetY,m=Math.max.apply(null,n.map((function(t){return(0,it.F$)(t,i,0)}))),b=n.length,g=d/b,O={x:r.left,y:r.top,width:r.width,height:r.height},x=n.map((function(t,e){var r,o=(0,it.F$)(t,i,0),l=(0,it.F$)(t,a,e),p=o;if(e!==b-1)(r=(0,it.F$)(n[e+1],i,0))instanceof Array&&(r=eo(r,1)[0]);else if(o instanceof Array&&2===o.length){var d=eo(o,2);p=d[0],r=d[1]}else r="rectangle"===u?p:0;var x=(m-p)*y/(2*m)+f+25+h,w=g*e+s+v,j=p/m*y,P=r/m*y,S=[{name:l,value:p,payload:t,dataKey:i,type:c}],A={x:x+j/2,y:w+g/2};return ao(ao({x:x,y:w,width:Math.max(j,P),upperWidth:j,lowerWidth:P,height:g,name:l,val:p,tooltipPayload:S,tooltipPosition:A},xr()(t,"width")),{},{payload:t,parentViewBox:O,labelViewBox:{x:x+(j-P)/4,y:w,width:Math.abs(j-P)/2+Math.min(j,P),height:g}})}));return l&&(x=x.map((function(t,e){var r=t.y-e*g+(b-1-e)*g;return ao(ao({},t),{},{upperWidth:t.lowerWidth,lowerWidth:t.upperWidth,x:t.x-(t.lowerWidth-t.upperWidth)/2,y:t.y-e*g+(b-1-e)*g,tooltipPosition:ao(ao({},t.tooltipPosition),{},{y:r+g/2}),labelViewBox:ao(ao({},t.labelViewBox),{},{y:r})})}))),{trapezoids:x,data:n}}));var mo=(0,hr.z)({chartName:"FunnelChart",GraphicalChild:vo,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",axisComponents:[],defaultProps:{layout:"centric"}}),bo=r(64397)},86246:function(t,e,r){r.d(e,{b:function(){return F}});var n,o=r(89526),i=r(80397),a=r(80089),c=r.n(a),u=r(47184),l=r.n(u),s=r(51391),f=r.n(s),p=r(39277),d=r.n(p),y=r(90512),h=r(61452),v=r(92147),m=r(49266),b=r(43774),g=r(34324),O=r(32214),x=r(9410),w=r(59509),j=r(80072),P=r(16171),S=r(36530),A=r(78706),E=r(33790),k=r(69531);function T(t){return T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},T(t)}function C(){return C=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},C.apply(this,arguments)}function I(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function D(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?I(Object(r),!0).forEach((function(e){z(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function M(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,_(n.key),n)}}function N(t,e,r){return e=L(e),function(t,e){if(e&&("object"===T(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,B()?Reflect.construct(e,r||[],L(t).constructor):e.apply(t,r))}function B(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(B=function(){return!!t})()}function L(t){return L=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},L(t)}function R(t,e){return R=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},R(t,e)}function z(t,e,r){return(e=_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _(t){var e=function(t,e){if("object"!=T(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=T(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==T(e)?e:e+""}var F=function(t){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),z(r=N(this,e,[t]),"pieRef",null),z(r,"sectorRefs",[]),z(r,"id",(0,P.EL)("recharts-pie-")),z(r,"handleAnimationEnd",(function(){var t=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),d()(t)&&t()})),z(r,"handleAnimationStart",(function(){var t=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),d()(t)&&t()})),r.state={isAnimationFinished:!t.isAnimationActive,prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,sectorToFocus:0},r}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&R(t,e)}(e,t),r=e,a=[{key:"getDerivedStateFromProps",value:function(t,e){return e.prevIsAnimationActive!==t.isAnimationActive?{prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:[],isAnimationFinished:!0}:t.isAnimationActive&&t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:e.curSectors,isAnimationFinished:!0}:t.sectors!==e.curSectors?{curSectors:t.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(t,e){return t>e?"start":t<e?"end":"middle"}},{key:"renderLabelLineItem",value:function(t,e,r){if(o.isValidElement(t))return o.cloneElement(t,e);if(d()(t))return t(e);var n=(0,y.Z)("recharts-pie-label-line","boolean"!==typeof t?t.className:"");return o.createElement(v.H,C({},e,{key:r,type:"linear",className:n}))}},{key:"renderLabelItem",value:function(t,e,r){if(o.isValidElement(t))return o.cloneElement(t,e);var n=r;if(d()(t)&&(n=t(e),o.isValidElement(n)))return n;var i=(0,y.Z)("recharts-pie-label-text","boolean"===typeof t||d()(t)?"":t.className);return o.createElement(m.x,C({},e,{alignmentBaseline:"middle",className:i}),n)}}],(n=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"hasActiveIndex",value:function(){var t=this.props.activeIndex;return Array.isArray(t)?0!==t.length:t||0===t}},{key:"renderLabels",value:function(t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.label,i=r.labelLine,a=r.dataKey,c=r.valueKey,u=(0,x.L6)(this.props,!1),l=(0,x.L6)(n,!1),s=(0,x.L6)(i,!1),p=n&&n.offsetRadius||20,d=t.map((function(t,r){var d=(t.startAngle+t.endAngle)/2,y=(0,j.op)(t.cx,t.cy,t.outerRadius+p,d),v=D(D(D(D({},u),t),{},{stroke:"none"},l),{},{index:r,textAnchor:e.getTextAnchor(y.x,t.cx)},y),m=D(D(D(D({},u),t),{},{fill:"none",stroke:t.fill},s),{},{index:r,points:[(0,j.op)(t.cx,t.cy,t.outerRadius,d),y]}),b=a;return f()(a)&&f()(c)?b="value":f()(a)&&(b=c),o.createElement(h.m,{key:"label-".concat(t.startAngle,"-").concat(t.endAngle,"-").concat(t.midAngle,"-").concat(r)},i&&e.renderLabelLineItem(i,m,"line"),e.renderLabelItem(n,v,(0,S.F$)(t,b)))}));return o.createElement(h.m,{className:"recharts-pie-labels"},d)}},{key:"renderSectorsStatically",value:function(t){var e=this,r=this.props,n=r.activeShape,i=r.blendStroke,a=r.inactiveShape;return t.map((function(r,c){if(0===(null===r||void 0===r?void 0:r.startAngle)&&0===(null===r||void 0===r?void 0:r.endAngle)&&1!==t.length)return null;var u=e.isActiveIndex(c),l=a&&e.hasActiveIndex()?a:null,s=u?n:l,f=D(D({},r),{},{stroke:i?r.fill:r.stroke,tabIndex:-1});return o.createElement(h.m,C({ref:function(t){t&&!e.sectorRefs.includes(t)&&e.sectorRefs.push(t)},tabIndex:-1,className:"recharts-pie-sector"},(0,E.bw)(e.props,r,c),{key:"sector-".concat(null===r||void 0===r?void 0:r.startAngle,"-").concat(null===r||void 0===r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(c)}),o.createElement(k.bn,C({option:s,isActive:u,shapeType:"sector"},f)))}))}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,r=e.sectors,n=e.isAnimationActive,a=e.animationBegin,u=e.animationDuration,l=e.animationEasing,s=e.animationId,f=this.state,p=f.prevSectors,d=f.prevIsAnimationActive;return o.createElement(i.ZP,{begin:a,duration:u,isActive:n,easing:l,from:{t:0},to:{t:1},key:"pie-".concat(s,"-").concat(d),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(e){var n=e.t,i=[],a=(r&&r[0]).startAngle;return r.forEach((function(t,e){var r=p&&p[e],o=e>0?c()(t,"paddingAngle",0):0;if(r){var u=(0,P.k4)(r.endAngle-r.startAngle,t.endAngle-t.startAngle),l=D(D({},t),{},{startAngle:a+o,endAngle:a+u(n)+o});i.push(l),a=l.endAngle}else{var s=t.endAngle,f=t.startAngle,d=(0,P.k4)(0,s-f)(n),y=D(D({},t),{},{startAngle:a+o,endAngle:a+d+o});i.push(y),a=y.endAngle}})),o.createElement(h.m,null,t.renderSectorsStatically(i))}))}},{key:"attachKeyboardHandlers",value:function(t){var e=this;t.onkeydown=function(t){if(!t.altKey)switch(t.key){case"ArrowLeft":var r=++e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[r].focus(),e.setState({sectorToFocus:r});break;case"ArrowRight":var n=--e.state.sectorToFocus<0?e.sectorRefs.length-1:e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[n].focus(),e.setState({sectorToFocus:n});break;case"Escape":e.sectorRefs[e.state.sectorToFocus].blur(),e.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var t=this.props,e=t.sectors,r=t.isAnimationActive,n=this.state.prevSectors;return!(r&&e&&e.length)||n&&l()(n,e)?this.renderSectorsStatically(e):this.renderSectorsWithAnimation()}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var t=this,e=this.props,r=e.hide,n=e.sectors,i=e.className,a=e.label,c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.isAnimationActive,p=this.state.isAnimationFinished;if(r||!n||!n.length||!(0,P.hj)(c)||!(0,P.hj)(u)||!(0,P.hj)(l)||!(0,P.hj)(s))return null;var d=(0,y.Z)("recharts-pie",i);return o.createElement(h.m,{tabIndex:this.props.rootTabIndex,className:d,ref:function(e){t.pieRef=e}},this.renderSectors(),a&&this.renderLabels(n),b._.renderCallByParent(this.props,null,!1),(!f||p)&&g.e.renderCallByParent(this.props,n,!1))}}])&&M(r.prototype,n),a&&M(r,a),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,a}(o.PureComponent);n=F,z(F,"displayName","Pie"),z(F,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!w.x.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),z(F,"parseDeltaAngle",(function(t,e){return(0,P.uY)(e-t)*Math.min(Math.abs(e-t),360)})),z(F,"getRealPieData",(function(t){var e=t.data,r=t.children,n=(0,x.L6)(t,!1),o=(0,x.NN)(r,O.b);return e&&e.length?e.map((function(t,e){return D(D(D({payload:t},n),t),o&&o[e]&&o[e].props)})):o&&o.length?o.map((function(t){return D(D({},n),t.props)})):[]})),z(F,"parseCoordinateOfPie",(function(t,e){var r=e.top,n=e.left,o=e.width,i=e.height,a=(0,j.$4)(o,i);return{cx:n+(0,P.h1)(t.cx,o,o/2),cy:r+(0,P.h1)(t.cy,i,i/2),innerRadius:(0,P.h1)(t.innerRadius,a,0),outerRadius:(0,P.h1)(t.outerRadius,a,.8*a),maxRadius:t.maxRadius||Math.sqrt(o*o+i*i)/2}})),z(F,"getComposedData",(function(t){var e=t.item,r=t.offset,o=void 0!==e.type.defaultProps?D(D({},e.type.defaultProps),e.props):e.props,i=n.getRealPieData(o);if(!i||!i.length)return null;var a=o.cornerRadius,c=o.startAngle,u=o.endAngle,l=o.paddingAngle,s=o.dataKey,p=o.nameKey,d=o.valueKey,y=o.tooltipType,h=Math.abs(o.minAngle),v=n.parseCoordinateOfPie(o,r),m=n.parseDeltaAngle(c,u),b=Math.abs(m),g=s;f()(s)&&f()(d)?((0,A.Z)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g="value"):f()(s)&&((0,A.Z)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g=d);var O,x,w=i.filter((function(t){return 0!==(0,S.F$)(t,g,0)})).length,E=b-w*h-(b>=360?w:w-1)*l,k=i.reduce((function(t,e){var r=(0,S.F$)(e,g,0);return t+((0,P.hj)(r)?r:0)}),0);k>0&&(O=i.map((function(t,e){var r,n=(0,S.F$)(t,g,0),o=(0,S.F$)(t,p,e),i=((0,P.hj)(n)?n:0)/k,u=(r=e?x.endAngle+(0,P.uY)(m)*l*(0!==n?1:0):c)+(0,P.uY)(m)*((0!==n?h:0)+i*E),s=(r+u)/2,f=(v.innerRadius+v.outerRadius)/2,d=[{name:o,value:n,payload:t,dataKey:g,type:y}],b=(0,j.op)(v.cx,v.cy,f,s);return x=D(D(D({percent:i,cornerRadius:a,name:o,tooltipPayload:d,midAngle:s,middleRadius:f,tooltipPosition:b},t),v),{},{value:(0,S.F$)(t,g),startAngle:r,endAngle:u,payload:t,paddingAngle:(0,P.uY)(m)*l})})));return D(D({},v),{},{sectors:O,data:i})}))},10562:function(t,e,r){r.d(e,{I:function(){return k}});var n=r(89526),o=r(39277),i=r.n(o),a=r(90512),c=r(61452),u=r(96963),l=r(31234),s=r(49266),f=r(33790),p=r(9410),d=r(80072);function y(t){return y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(t)}function h(){return h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},h.apply(this,arguments)}function v(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function m(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?v(Object(r),!0).forEach((function(e){P(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function b(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function g(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,S(n.key),n)}}function O(t,e,r){return e=w(e),function(t,e){if(e&&("object"===y(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,x()?Reflect.construct(e,r||[],w(t).constructor):e.apply(t,r))}function x(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(x=function(){return!!t})()}function w(t){return w=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},w(t)}function j(t,e){return j=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},j(t,e)}function P(t,e,r){return(e=S(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function S(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:e+""}var A=Math.PI/180,E=1e-5,k=function(t){function e(){return b(this,e),O(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&j(t,e)}(e,t),r=e,y=[{key:"renderTickItem",value:function(t,e,r){return n.isValidElement(t)?n.cloneElement(t,e):i()(t)?t(e):n.createElement(s.x,h({},e,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],(o=[{key:"getTickLineCoord",value:function(t){var e=this.props,r=e.cx,n=e.cy,o=e.radius,i=e.orientation,a=e.tickSize||8,c=(0,d.op)(r,n,o,t.coordinate),u=(0,d.op)(r,n,o+("inner"===i?-1:1)*a,t.coordinate);return{x1:c.x,y1:c.y,x2:u.x,y2:u.y}}},{key:"getTickTextAnchor",value:function(t){var e=this.props.orientation,r=Math.cos(-t.coordinate*A);return r>E?"outer"===e?"start":"end":r<-E?"outer"===e?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,o=t.radius,i=t.axisLine,a=t.axisLineType,c=m(m({},(0,p.L6)(this.props,!1)),{},{fill:"none"},(0,p.L6)(i,!1));if("circle"===a)return n.createElement(u.o,h({className:"recharts-polar-angle-axis-line"},c,{cx:e,cy:r,r:o}));var s=this.props.ticks.map((function(t){return(0,d.op)(e,r,o,t.coordinate)}));return n.createElement(l.m,h({className:"recharts-polar-angle-axis-line"},c,{points:s}))}},{key:"renderTicks",value:function(){var t=this,r=this.props,o=r.ticks,i=r.tick,u=r.tickLine,l=r.tickFormatter,s=r.stroke,y=(0,p.L6)(this.props,!1),v=(0,p.L6)(i,!1),b=m(m({},y),{},{fill:"none"},(0,p.L6)(u,!1)),g=o.map((function(r,o){var p=t.getTickLineCoord(r),g=m(m(m({textAnchor:t.getTickTextAnchor(r)},y),{},{stroke:"none",fill:s},v),{},{index:o,payload:r,x:p.x2,y:p.y2});return n.createElement(c.m,h({className:(0,a.Z)("recharts-polar-angle-axis-tick",(0,d.$S)(i)),key:"tick-".concat(r.coordinate)},(0,f.bw)(t.props,r,o)),u&&n.createElement("line",h({className:"recharts-polar-angle-axis-tick-line"},b,p)),i&&e.renderTickItem(i,g,l?l(r.value,o):r.value))}));return n.createElement(c.m,{className:"recharts-polar-angle-axis-ticks"},g)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.radius,o=t.axisLine;return r<=0||!e||!e.length?null:n.createElement(c.m,{className:(0,a.Z)("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks())}}])&&g(r.prototype,o),y&&g(r,y),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,y}(n.PureComponent);P(k,"displayName","PolarAngleAxis"),P(k,"axisType","angleAxis"),P(k,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0})},85322:function(t,e,r){r.d(e,{S:function(){return D}});var n=r(89526),o=r(58399),i=r.n(o),a=r(73),c=r.n(a),u=r(39277),l=r.n(u),s=r(90512),f=r(49266),p=r(43774),d=r(61452),y=r(80072),h=r(33790),v=r(9410),m=["cx","cy","angle","ticks","axisLine"],b=["ticks","tick","angle","tickFormatter","stroke"];function g(t){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},g(t)}function O(){return O=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},O.apply(this,arguments)}function x(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?x(Object(r),!0).forEach((function(e){C(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):x(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function j(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function P(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function S(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,I(n.key),n)}}function A(t,e,r){return e=k(e),function(t,e){if(e&&("object"===g(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,E()?Reflect.construct(e,r||[],k(t).constructor):e.apply(t,r))}function E(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(E=function(){return!!t})()}function k(t){return k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},k(t)}function T(t,e){return T=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},T(t,e)}function C(t,e,r){return(e=I(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function I(t){var e=function(t,e){if("object"!=g(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=g(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==g(e)?e:e+""}var D=function(t){function e(){return P(this,e),A(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&T(t,e)}(e,t),r=e,a=[{key:"renderTickItem",value:function(t,e,r){return n.isValidElement(t)?n.cloneElement(t,e):l()(t)?t(e):n.createElement(f.x,O({},e,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],(o=[{key:"getTickValueCoord",value:function(t){var e=t.coordinate,r=this.props,n=r.angle,o=r.cx,i=r.cy;return(0,y.op)(o,i,e,n)}},{key:"getTickTextAnchor",value:function(){var t;switch(this.props.orientation){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t}},{key:"getViewBox",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,o=t.ticks,a=i()(o,(function(t){return t.coordinate||0}));return{cx:e,cy:r,startAngle:n,endAngle:n,innerRadius:c()(o,(function(t){return t.coordinate||0})).coordinate||0,outerRadius:a.coordinate||0}}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,o=t.angle,i=t.ticks,a=t.axisLine,c=j(t,m),u=i.reduce((function(t,e){return[Math.min(t[0],e.coordinate),Math.max(t[1],e.coordinate)]}),[1/0,-1/0]),l=(0,y.op)(e,r,u[0],o),s=(0,y.op)(e,r,u[1],o),f=w(w(w({},(0,v.L6)(c,!1)),{},{fill:"none"},(0,v.L6)(a,!1)),{},{x1:l.x,y1:l.y,x2:s.x,y2:s.y});return n.createElement("line",O({className:"recharts-polar-radius-axis-line"},f))}},{key:"renderTicks",value:function(){var t=this,r=this.props,o=r.ticks,i=r.tick,a=r.angle,c=r.tickFormatter,u=r.stroke,l=j(r,b),f=this.getTickTextAnchor(),p=(0,v.L6)(l,!1),m=(0,v.L6)(i,!1),g=o.map((function(r,o){var l=t.getTickValueCoord(r),v=w(w(w(w({textAnchor:f,transform:"rotate(".concat(90-a,", ").concat(l.x,", ").concat(l.y,")")},p),{},{stroke:"none",fill:u},m),{},{index:o},l),{},{payload:r});return n.createElement(d.m,O({className:(0,s.Z)("recharts-polar-radius-axis-tick",(0,y.$S)(i)),key:"tick-".concat(r.coordinate)},(0,h.bw)(t.props,r,o)),e.renderTickItem(i,v,c?c(r.value,o):r.value))}));return n.createElement(d.m,{className:"recharts-polar-radius-axis-ticks"},g)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.axisLine,o=t.tick;return e&&e.length?n.createElement(d.m,{className:(0,s.Z)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),o&&this.renderTicks(),p._.renderCallByParent(this.props,this.getViewBox())):null}}])&&S(r.prototype,o),a&&S(r,a),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,a}(n.PureComponent);C(D,"displayName","PolarRadiusAxis"),C(D,"axisType","radiusAxis"),C(D,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0})},74791:function(t,e,r){r.d(e,{X:function(){return y}});var n=r(89526),o=r(90512),i=r(16171),a=r(9410);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}var u=["x","y","top","left","width","height","className"];function l(){return l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},l.apply(this,arguments)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function f(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function p(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var d=function(t,e,r,n,o,i){return"M".concat(t,",").concat(o,"v").concat(n,"M").concat(i,",").concat(e,"h").concat(r)},y=function(t){var e=t.x,r=void 0===e?0:e,c=t.y,y=void 0===c?0:c,h=t.top,v=void 0===h?0:h,m=t.left,b=void 0===m?0:m,g=t.width,O=void 0===g?0:g,x=t.height,w=void 0===x?0:x,j=t.className,P=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach((function(e){f(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({x:r,y:y,top:v,left:b,width:O,height:w},p(t,u));return(0,i.hj)(r)&&(0,i.hj)(y)&&(0,i.hj)(O)&&(0,i.hj)(w)&&(0,i.hj)(v)&&(0,i.hj)(b)?n.createElement("path",l({},(0,a.L6)(P,!0),{className:(0,o.Z)("recharts-cross",j),d:d(r,y,O,w,v,b)})):null}},92147:function(t,e,r){r.d(e,{H:function(){return M}});var n=r(89526),o=r(5135),i=r(25908),a=r(60778),c=r(25846),u=r(79488),l=r(22081),s=r(19846),f=r(1971),p=r(15515),d=r(64706),y=r(82217),h=r(43483),v=r.n(h),m=r(39277),b=r.n(m),g=r(90512),O=r(33790),x=r(9410),w=r(16171);function j(t){return j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},j(t)}function P(){return P=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},P.apply(this,arguments)}function S(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function A(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?S(Object(r),!0).forEach((function(e){E(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function E(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=j(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=j(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==j(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var k={curveBasisClosed:o.Z,curveBasisOpen:i.Z,curveBasis:a.ZP,curveBumpX:c.sj,curveBumpY:c.BW,curveLinearClosed:u.Z,curveLinear:l.Z,curveMonotoneX:s.Z,curveMonotoneY:s.s,curveNatural:f.Z,curveStep:p.ZP,curveStepAfter:p.cD,curveStepBefore:p.RN},T=function(t){return t.x===+t.x&&t.y===+t.y},C=function(t){return t.x},I=function(t){return t.y},D=function(t){var e,r=t.type,n=void 0===r?"linear":r,o=t.points,i=void 0===o?[]:o,a=t.baseLine,c=t.layout,u=t.connectNulls,s=void 0!==u&&u,f=function(t,e){if(b()(t))return t;var r="curve".concat(v()(t));return"curveMonotone"!==r&&"curveBump"!==r||!e?k[r]||l.Z:k["".concat(r).concat("vertical"===e?"Y":"X")]}(n,c),p=s?i.filter((function(t){return T(t)})):i;if(Array.isArray(a)){var h=s?a.filter((function(t){return T(t)})):a,m=p.map((function(t,e){return A(A({},t),{},{base:h[e]})}));return(e="vertical"===c?(0,d.Z)().y(I).x1(C).x0((function(t){return t.base.x})):(0,d.Z)().x(C).y1(I).y0((function(t){return t.base.y}))).defined(T).curve(f),e(m)}return(e="vertical"===c&&(0,w.hj)(a)?(0,d.Z)().y(I).x1(C).x0(a):(0,w.hj)(a)?(0,d.Z)().x(C).y1(I).y0(a):(0,y.Z)().x(C).y(I)).defined(T).curve(f),e(p)},M=function(t){var e=t.className,r=t.points,o=t.path,i=t.pathRef;if((!r||!r.length)&&!o)return null;var a=r&&r.length?D(t):o;return n.createElement("path",P({},(0,x.L6)(t,!1),(0,O.Ym)(t),{className:(0,g.Z)("recharts-curve",e),d:a,ref:i}))}},96963:function(t,e,r){r.d(e,{o:function(){return u}});var n=r(89526),o=r(90512),i=r(33790),a=r(9410);function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(this,arguments)}var u=function(t){var e=t.cx,r=t.cy,u=t.r,l=t.className,s=(0,o.Z)("recharts-dot",l);return e===+e&&r===+r&&u===+u?n.createElement("circle",c({},(0,a.L6)(t,!1),(0,i.Ym)(t),{className:s,cx:e,cy:r,r:u})):null}},31234:function(t,e,r){r.d(e,{m:function(){return d}});var n=r(89526),o=r(90512),i=r(9410),a=["points","className","baseLinePoints","connectNulls"];function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(this,arguments)}function u(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function l(t){return function(t){if(Array.isArray(t))return s(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return s(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var f=function(t){return t&&t.x===+t.x&&t.y===+t.y},p=function(t,e){var r=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[[]];return t.forEach((function(t){f(t)?e[e.length-1].push(t):e[e.length-1].length>0&&e.push([])})),f(t[0])&&e[e.length-1].push(t[0]),e[e.length-1].length<=0&&(e=e.slice(0,-1)),e}(t);e&&(r=[r.reduce((function(t,e){return[].concat(l(t),l(e))}),[])]);var n=r.map((function(t){return t.reduce((function(t,e,r){return"".concat(t).concat(0===r?"M":"L").concat(e.x,",").concat(e.y)}),"")})).join("");return 1===r.length?"".concat(n,"Z"):n},d=function(t){var e=t.points,r=t.className,l=t.baseLinePoints,s=t.connectNulls,f=u(t,a);if(!e||!e.length)return null;var d=(0,o.Z)("recharts-polygon",r);if(l&&l.length){var y=f.stroke&&"none"!==f.stroke,h=function(t,e,r){var n=p(t,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(p(e.reverse(),r).slice(1))}(e,l,s);return n.createElement("g",{className:d},n.createElement("path",c({},(0,i.L6)(f,!0),{fill:"Z"===h.slice(-1)?f.fill:"none",stroke:"none",d:h})),y?n.createElement("path",c({},(0,i.L6)(f,!0),{fill:"none",d:p(e,s)})):null,y?n.createElement("path",c({},(0,i.L6)(f,!0),{fill:"none",d:p(l,s)})):null)}var v=p(e,s);return n.createElement("path",c({},(0,i.L6)(f,!0),{fill:"Z"===v.slice(-1)?f.fill:"none",className:d,d:v}))}},33951:function(t,e,r){r.d(e,{X:function(){return h},A:function(){return m}});var n=r(89526),o=r(90512),i=r(80397),a=r(9410);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(){return u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},u.apply(this,arguments)}function l(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return s(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function d(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var y=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),i+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),i+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),i+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},h=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+c),f=Math.max(i,i+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},v={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},m=function(t){var e=p(p({},v),t),r=(0,n.useRef)(),c=l((0,n.useState)(-1),2),s=c[0],f=c[1];(0,n.useEffect)((function(){if(r.current&&r.current.getTotalLength)try{var t=r.current.getTotalLength();t&&f(t)}catch(e){}}),[]);var d=e.x,h=e.y,m=e.width,b=e.height,g=e.radius,O=e.className,x=e.animationEasing,w=e.animationDuration,j=e.animationBegin,P=e.isAnimationActive,S=e.isUpdateAnimationActive;if(d!==+d||h!==+h||m!==+m||b!==+b||0===m||0===b)return null;var A=(0,o.Z)("recharts-rectangle",O);return S?n.createElement(i.ZP,{canBegin:s>0,from:{width:m,height:b,x:d,y:h},to:{width:m,height:b,x:d,y:h},duration:w,animationEasing:x,isActive:S},(function(t){var o=t.width,c=t.height,l=t.x,f=t.y;return n.createElement(i.ZP,{canBegin:s>0,from:"0px ".concat(-1===s?1:s,"px"),to:"".concat(s,"px 0px"),attributeName:"strokeDasharray",begin:j,duration:w,isActive:P,easing:x},n.createElement("path",u({},(0,a.L6)(e,!0),{className:A,d:y(l,f,o,c,g),ref:r})))})):n.createElement("path",u({},(0,a.L6)(e,!0),{className:A,d:y(d,h,m,b,g)}))}},61001:function(t,e,r){r.d(e,{L:function(){return v}});var n=r(89526),o=r(90512),i=r(9410),a=r(80072),c=r(16171);function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function l(){return l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},l.apply(this,arguments)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach((function(e){p(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function p(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var d=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,c=t.isExternal,u=t.cornerRadius,l=t.cornerIsExternal,s=u*(c?1:-1)+n,f=Math.asin(u/s)/a.Wk,p=l?o:o+i*f,d=l?o-i*f:o;return{center:(0,a.op)(e,r,s,p),circleTangency:(0,a.op)(e,r,n,p),lineTangency:(0,a.op)(e,r,s*Math.cos(f*a.Wk),d),theta:f}},y=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.startAngle,u=function(t,e){return(0,c.uY)(e-t)*Math.min(Math.abs(e-t),359.999)}(i,t.endAngle),l=i+u,s=(0,a.op)(e,r,o,i),f=(0,a.op)(e,r,o,l),p="M ".concat(s.x,",").concat(s.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(u)>180),",").concat(+(i>l),",\n    ").concat(f.x,",").concat(f.y,"\n  ");if(n>0){var d=(0,a.op)(e,r,n,i),y=(0,a.op)(e,r,n,l);p+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(u)>180),",").concat(+(i<=l),",\n            ").concat(d.x,",").concat(d.y," Z")}else p+="L ".concat(e,",").concat(r," Z");return p},h={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},v=function(t){var e=f(f({},h),t),r=e.cx,a=e.cy,u=e.innerRadius,s=e.outerRadius,p=e.cornerRadius,v=e.forceCornerRadius,m=e.cornerIsExternal,b=e.startAngle,g=e.endAngle,O=e.className;if(s<u||b===g)return null;var x,w=(0,o.Z)("recharts-sector",O),j=s-u,P=(0,c.h1)(p,j,0,!0);return x=P>0&&Math.abs(b-g)<360?function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,u=t.cornerIsExternal,l=t.startAngle,s=t.endAngle,f=(0,c.uY)(s-l),p=d({cx:e,cy:r,radius:o,angle:l,sign:f,cornerRadius:i,cornerIsExternal:u}),h=p.circleTangency,v=p.lineTangency,m=p.theta,b=d({cx:e,cy:r,radius:o,angle:s,sign:-f,cornerRadius:i,cornerIsExternal:u}),g=b.circleTangency,O=b.lineTangency,x=b.theta,w=u?Math.abs(l-s):Math.abs(l-s)-m-x;if(w<0)return a?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*-i,",0\n      "):y({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:l,endAngle:s});var j="M ".concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(h.x,",").concat(h.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(w>180),",").concat(+(f<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(O.x,",").concat(O.y,"\n  ");if(n>0){var P=d({cx:e,cy:r,radius:n,angle:l,sign:f,isExternal:!0,cornerRadius:i,cornerIsExternal:u}),S=P.circleTangency,A=P.lineTangency,E=P.theta,k=d({cx:e,cy:r,radius:n,angle:s,sign:-f,isExternal:!0,cornerRadius:i,cornerIsExternal:u}),T=k.circleTangency,C=k.lineTangency,I=k.theta,D=u?Math.abs(l-s):Math.abs(l-s)-E-I;if(D<0&&0===i)return"".concat(j,"L").concat(e,",").concat(r,"Z");j+="L".concat(C.x,",").concat(C.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(T.x,",").concat(T.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(D>180),",").concat(+(f>0),",").concat(S.x,",").concat(S.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(A.x,",").concat(A.y,"Z")}else j+="L".concat(e,",").concat(r,"Z");return j}({cx:r,cy:a,innerRadius:u,outerRadius:s,cornerRadius:Math.min(P,j/2),forceCornerRadius:v,cornerIsExternal:m,startAngle:b,endAngle:g}):y({cx:r,cy:a,innerRadius:u,outerRadius:s,startAngle:b,endAngle:g}),n.createElement("path",l({},(0,i.L6)(e,!0),{className:w,d:x,role:"img"}))}},71746:function(t,e,r){r.d(e,{v:function(){return S}});var n=r(89526),o=r(43483),i=r.n(o),a=r(83997),c=r(32346),u=r(12060),l=r(92138),s=r(38438),f=r(95029),p=r(7217),d=r(63446),y=r(90512),h=r(9410);function v(t){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(t)}var m=["type","size","sizeType"];function b(){return b=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},b.apply(this,arguments)}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function O(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach((function(e){x(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function x(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function w(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var j={symbolCircle:a.Z,symbolCross:c.Z,symbolDiamond:u.Z,symbolSquare:l.Z,symbolStar:s.Z,symbolTriangle:f.Z,symbolWye:p.Z},P=Math.PI/180,S=function(t){var e=t.type,r=void 0===e?"circle":e,o=t.size,c=void 0===o?64:o,u=t.sizeType,l=void 0===u?"area":u,s=O(O({},w(t,m)),{},{type:r,size:c,sizeType:l}),f=s.className,p=s.cx,v=s.cy,g=(0,h.L6)(s,!0);return p===+p&&v===+v&&c===+c?n.createElement("path",b({},g,{className:(0,y.Z)("recharts-symbols",f),transform:"translate(".concat(p,", ").concat(v,")"),d:function(){var t=function(t){var e="symbol".concat(i()(t));return j[e]||a.Z}(r),e=(0,d.ZP)().type(t).size(function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return.5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*P;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}}(c,l,r));return e()}()})):null};S.registerSymbol=function(t,e){j["symbol".concat(i()(t))]=e}},64397:function(t,e,r){r.d(e,{Z:function(){return v}});var n=r(89526),o=r(90512),i=r(80397),a=r(9410);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(){return u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},u.apply(this,arguments)}function l(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return s(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function d(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var y=function(t,e,r,n,o){var i,a=r-n;return i="M ".concat(t,",").concat(e),i+="L ".concat(t+r,",").concat(e),i+="L ".concat(t+r-a/2,",").concat(e+o),i+="L ".concat(t+r-a/2-n,",").concat(e+o),i+="L ".concat(t,",").concat(e," Z")},h={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},v=function(t){var e=p(p({},h),t),r=(0,n.useRef)(),c=l((0,n.useState)(-1),2),s=c[0],f=c[1];(0,n.useEffect)((function(){if(r.current&&r.current.getTotalLength)try{var t=r.current.getTotalLength();t&&f(t)}catch(e){}}),[]);var d=e.x,v=e.y,m=e.upperWidth,b=e.lowerWidth,g=e.height,O=e.className,x=e.animationEasing,w=e.animationDuration,j=e.animationBegin,P=e.isUpdateAnimationActive;if(d!==+d||v!==+v||m!==+m||b!==+b||g!==+g||0===m&&0===b||0===g)return null;var S=(0,o.Z)("recharts-trapezoid",O);return P?n.createElement(i.ZP,{canBegin:s>0,from:{upperWidth:0,lowerWidth:0,height:g,x:d,y:v},to:{upperWidth:m,lowerWidth:b,height:g,x:d,y:v},duration:w,animationEasing:x,isActive:P},(function(t){var o=t.upperWidth,c=t.lowerWidth,l=t.height,f=t.x,p=t.y;return n.createElement(i.ZP,{canBegin:s>0,from:"0px ".concat(-1===s?1:s,"px"),to:"".concat(s,"px 0px"),attributeName:"strokeDasharray",begin:j,duration:w,easing:x},n.createElement("path",u({},(0,a.L6)(e,!0),{className:S,d:y(f,p,o,c,l),ref:r})))})):n.createElement("g",null,n.createElement("path",u({},(0,a.L6)(e,!0),{className:S,d:y(d,v,m,b,g)})))}},69531:function(t,e,r){r.d(e,{Ob:function(){return S},bn:function(){return A},lT:function(){return E},V$:function(){return k},w7:function(){return T},a3:function(){return M}});var n=r(89526),o=r(39277),i=r.n(o),a=r(82678),c=r.n(a),u=r(23079),l=r.n(u),s=r(47184),f=r.n(s),p=r(33951),d=r(64397),y=r(61001),h=r(61452),v=r(71746),m=["option","shapeType","propTransformer","activeClassName","isActive"];function b(t){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},b(t)}function g(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function O(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?O(Object(r),!0).forEach((function(e){w(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function w(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=b(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==b(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function j(t,e){return x(x({},e),t)}function P(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return n.createElement(p.A,r);case"trapezoid":return n.createElement(d.Z,r);case"sector":return n.createElement(y.L,r);case"symbols":if(function(t,e){return"symbols"===t}(e))return n.createElement(v.v,r);break;default:return null}}function S(t){return(0,n.isValidElement)(t)?t.props:t}function A(t){var e,r=t.option,o=t.shapeType,a=t.propTransformer,u=void 0===a?j:a,s=t.activeClassName,f=void 0===s?"recharts-active-shape":s,p=t.isActive,d=g(t,m);if((0,n.isValidElement)(r))e=(0,n.cloneElement)(r,x(x({},d),S(r)));else if(i()(r))e=r(d);else if(c()(r)&&!l()(r)){var y=u(r,d);e=n.createElement(P,{shapeType:o,elementProps:y})}else{var v=d;e=n.createElement(P,{shapeType:o,elementProps:v})}return p?n.createElement(h.m,{className:f},e):e}function E(t,e){return null!=e&&"trapezoids"in t.props}function k(t,e){return null!=e&&"sectors"in t.props}function T(t,e){return null!=e&&"points"in t.props}function C(t,e){var r,n,o=t.x===(null===e||void 0===e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,i=t.y===(null===e||void 0===e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return o&&i}function I(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function D(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}function M(t){var e=t.activeTooltipItem,r=t.graphicalItem,n=t.itemData,o=function(t,e){var r;return E(t,e)?r="trapezoids":k(t,e)?r="sectors":T(t,e)&&(r="points"),r}(r,e),i=function(t,e){var r,n;return E(t,e)?null===(r=e.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:k(t,e)?null===(n=e.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:T(t,e)?e.payload:{}}(r,e),a=n.filter((function(t,n){var a=f()(i,t),c=r.props[o].filter((function(t){var n=function(t,e){var r;return E(t,e)?r=C:k(t,e)?r=I:T(t,e)&&(r=D),r}(r,e);return n(t,e)})),u=r.props[o].indexOf(c[c.length-1]);return a&&n===u}));return n.indexOf(a[a.length-1])}},87210:function(t,e,r){r.d(e,{t9:function(){return m},O1:function(){return b},_b:function(){return g},Ky:function(){return x},xE:function(){return j}});var n=r(40508),o=r.n(n),i=r(84168),a=r.n(i),c=r(36530),u=r(9410),l=r(16171),s=r(48218);function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,v(n.key),n)}}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function y(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach((function(e){h(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function h(t,e,r){return(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var m=function(t,e,r,n,o){var i=t.width,a=t.height,f=t.layout,p=t.children,d=Object.keys(e),v={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},m=!!(0,u.sP)(p,s.$);return d.reduce((function(i,a){var u,s,p,d,b,g=e[a],O=g.orientation,x=g.domain,w=g.padding,j=void 0===w?{}:w,P=g.mirror,S=g.reversed,A="".concat(O).concat(P?"Mirror":"");if("number"===g.type&&("gap"===g.padding||"no-gap"===g.padding)){var E=x[1]-x[0],k=1/0,T=g.categoricalDomain.sort(l.fC);if(T.forEach((function(t,e){e>0&&(k=Math.min((t||0)-(T[e-1]||0),k))})),Number.isFinite(k)){var C=k/E,I="vertical"===g.layout?r.height:r.width;if("gap"===g.padding&&(u=C*I/2),"no-gap"===g.padding){var D=(0,l.h1)(t.barCategoryGap,C*I),M=C*I/2;u=M-D-(M-D)/I*D}}}s="xAxis"===n?[r.left+(j.left||0)+(u||0),r.left+r.width-(j.right||0)-(u||0)]:"yAxis"===n?"horizontal"===f?[r.top+r.height-(j.bottom||0),r.top+(j.top||0)]:[r.top+(j.top||0)+(u||0),r.top+r.height-(j.bottom||0)-(u||0)]:g.range,S&&(s=[s[1],s[0]]);var N=(0,c.Hq)(g,o,m),B=N.scale,L=N.realScaleType;B.domain(x).range(s),(0,c.zF)(B);var R=(0,c.g$)(B,y(y({},g),{},{realScaleType:L}));"xAxis"===n?(b="top"===O&&!P||"bottom"===O&&P,p=r.left,d=v[A]-b*g.height):"yAxis"===n&&(b="left"===O&&!P||"right"===O&&P,p=v[A]-b*g.width,d=r.top);var z=y(y(y({},g),R),{},{realScaleType:L,x:p,y:d,scale:B,width:"xAxis"===n?r.width:g.width,height:"yAxis"===n?r.height:g.height});return z.bandSize=(0,c.zT)(z,R),g.hide||"xAxis"!==n?g.hide||(v[A]+=(b?-1:1)*z.width):v[A]+=(b?-1:1)*z.height,y(y({},i),{},h({},a,z))}),{})},b=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},g=function(t){var e=t.x1,r=t.y1,n=t.x2,o=t.y2;return b({x:e,y:r},{x:n,y:o})},O=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.scale=e}return e=t,r=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],n=[{key:"create",value:function(e){return new t(e)}}],r&&p(e.prototype,r),n&&p(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();h(O,"EPS",1e-4);var x=function(t){var e=Object.keys(t).reduce((function(e,r){return y(y({},e),{},h({},r,O.create(t[r])))}),{});return y(y({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,i=r.position;return o()(t,(function(t,r){return e[r].apply(t,{bandAware:n,position:i})}))},isInRange:function(t){return a()(t,(function(t,r){return e[r].isInRange(t)}))}})};function w(t){return(t%180+180)%180}var j=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=w(n),i=o*Math.PI/180,a=Math.atan(r/e),c=i>a&&i<Math.PI-a?r/Math.sin(i):e/Math.cos(i);return Math.abs(c)}},36530:function(t,e,r){r.d(e,{F$:function(){return V},gF:function(){return G},VO:function(){return Y},fk:function(){return U},pt:function(){return H},qz:function(){return $},By:function(){return q},ZI:function(){return J},s6:function(){return tt},NA:function(){return et},Rf:function(){return rt},uY:function(){return nt},DO:function(){return it},Hq:function(){return at},zF:function(){return ut},Bu:function(){return lt},Vv:function(){return st},wh:function(){return dt},g$:function(){return yt},Hv:function(){return ht},Fy:function(){return vt},Yj:function(){return mt},O3:function(){return bt},EB:function(){return gt},LG:function(){return wt},zT:function(){return jt},ko:function(){return Pt},Qo:function(){return St}});var n=r(35406),o=r(8919),i=r(92768),a=r(30328),c=r(76019),u=r(43475),l=r(31843),s=r(36902),f=r(64016),p=r(14019),d=r.n(p),y=r(73398),h=r.n(y),v=r(51391),m=r.n(v),b=r(39277),g=r.n(b),O=r(72139),x=r.n(O),w=r(80089),j=r.n(w),P=r(22610),S=r.n(P),A=r(35813),E=r.n(A),k=r(43483),T=r.n(k),C=r(47184),I=r.n(C),D=r(65853),M=r.n(D),N=r(12360),B=r(65370),L=r(16171),R=r(9410),z=r(65436);function _(t){return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_(t)}function F(t){return function(t){if(Array.isArray(t))return W(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return W(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return W(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function W(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Z(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function K(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Z(Object(r),!0).forEach((function(e){X(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Z(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function X(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=_(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=_(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==_(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function V(t,e,r){return m()(t)||m()(e)?r:(0,L.P2)(e)?j()(t,e,r):g()(e)?e(t):r}function G(t,e,r,n){var o=S()(t,(function(t){return V(t,e)}));if("number"===r){var i=o.filter((function(t){return(0,L.hj)(t)||parseFloat(t)}));return i.length?[h()(i),d()(i)]:[1/0,-1/0]}return(n?o.filter((function(t){return!m()(t)})):o).map((function(t){return(0,L.P2)(t)||t instanceof Date?t:""}))}var Y=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null===r||void 0===r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&Math.abs(Math.abs(o.range[1]-o.range[0])-360)<=1e-6)for(var c=o.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if((0,L.uY)(s-l)!==(0,L.uY)(f-s)){var d=[];if((0,L.uY)(f-s)===(0,L.uY)(c[1]-c[0])){p=f;var y=s+c[1]-c[0];d[0]=Math.min(y,(y+l)/2),d[1]=Math.max(y,(y+l)/2)}else{p=l;var h=f+c[1]-c[0];d[0]=Math.min(s,(h+s)/2),d[1]=Math.max(s,(h+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=d[0]&&t<=d[1]){i=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},U=function(t){var e,r,n=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?K(K({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},H=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,u=a.length;c<u;c++)for(var l=o[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var d=l[s[f]],y=d.items,h=d.cateAxisId,v=y.filter((function(t){return(0,R.Gf)(t.type).indexOf("Bar")>=0}));if(v&&v.length){var b=v[0].type.defaultProps,g=void 0!==b?K(K({},b),v[0].props):v[0].props,O=g.barSize,x=g[h];i[x]||(i[x]=[]);var w=m()(O)?e:O;i[x].push({item:v[0],stackList:v.slice(1),barSize:m()(w)?void 0:(0,L.h1)(w,r,0)})}}return i},$=function(t){var e=t.barGap,r=t.barCategoryGap,n=t.bandSize,o=t.sizeList,i=void 0===o?[]:o,a=t.maxBarSize,c=i.length;if(c<1)return null;var u,l=(0,L.h1)(e,n,0,!0),s=[];if(i[0].barSize===+i[0].barSize){var f=!1,p=n/c,d=i.reduce((function(t,e){return t+e.barSize||0}),0);(d+=(c-1)*l)>=n&&(d-=(c-1)*l,l=0),d>=n&&p>0&&(f=!0,d=c*(p*=.9));var y={offset:((n-d)/2>>0)-l,size:0};u=i.reduce((function(t,e){var r={item:e.item,position:{offset:y.offset+y.size+l,size:f?p:e.barSize}},n=[].concat(F(t),[r]);return y=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach((function(t){n.push({item:t,position:y})})),n}),s)}else{var h=(0,L.h1)(r,n,0,!0);n-2*h-(c-1)*l<=0&&(l=0);var v=(n-2*h-(c-1)*l)/c;v>1&&(v>>=0);var m=a===+a?Math.min(v,a):v;u=i.reduce((function(t,e,r){var n=[].concat(F(t),[{item:e.item,position:{offset:h+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach((function(t){n.push({item:t,position:n[n.length-1].position})})),n}),s)}return u},q=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,c=i-(a.left||0)-(a.right||0),u=(0,z.z)({children:o,legendWidth:c});if(u){var l=n||{},s=l.width,f=l.height,p=u.align,d=u.verticalAlign,y=u.layout;if(("vertical"===y||"horizontal"===y&&"middle"===d)&&"center"!==p&&(0,L.hj)(t[p]))return K(K({},t),{},X({},p,t[p]+(s||0)));if(("horizontal"===y||"vertical"===y&&"center"===p)&&"middle"!==d&&(0,L.hj)(t[d]))return K(K({},t),{},X({},d,t[d]+(f||0)))}return t},Q=function(t,e,r,n,o){var i=e.props.children,a=(0,R.NN)(i,B.W).filter((function(t){return function(t,e,r){return!!m()(e)||("horizontal"===t?"yAxis"===e:"vertical"===t||"x"===r?"xAxis"===e:"y"!==r||"yAxis"===e)}(n,o,t.props.direction)}));if(a&&a.length){var c=a.map((function(t){return t.props.dataKey}));return t.reduce((function(t,e){var n=V(e,r);if(m()(n))return t;var o=Array.isArray(n)?[h()(n),d()(n)]:[n,n],i=c.reduce((function(t,r){var n=V(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]}),[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]}),[1/0,-1/0])}return null},J=function(t,e,r,n,o){var i=e.map((function(e){return Q(t,e,r,o,n)})).filter((function(t){return!m()(t)}));return i&&i.length?i.reduce((function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]}),[1/0,-1/0]):null},tt=function(t,e,r,n,o){var i=e.map((function(e){var i=e.props.dataKey;return"number"===r&&i&&Q(t,e,i,n)||G(t,i,r,o)}));if("number"===r)return i.reduce((function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]}),[1/0,-1/0]);var a={};return i.reduce((function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t}),[])},et=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},rt=function(t,e,r,n){if(n)return t.map((function(t){return t.coordinate}));var o,i,a=t.map((function(t){return t.coordinate===e&&(o=!0),t.coordinate===r&&(i=!0),t.coordinate}));return o||a.push(e),i||a.push(r),a},nt=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return u="angleAxis"===t.axisType&&(null===a||void 0===a?void 0:a.length)>=2?2*(0,L.uY)(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks)?(t.ticks||t.niceTicks).map((function(t){var e=o?o.indexOf(t):t;return{coordinate:n(e)+u,value:t,offset:u}})).filter((function(t){return!E()(t.coordinate)})):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map((function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}})):n.ticks&&!r?n.ticks(t.tickCount).map((function(t){return{coordinate:n(t)+u,value:t,offset:u}})):n.domain().map((function(t,e){return{coordinate:n(t)+u,value:o?o[t]:t,index:e,offset:u}}))},ot=new WeakMap,it=function(t,e){if("function"!==typeof e)return t;ot.has(t)||ot.set(t,new WeakMap);var r=ot.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},at=function(t,e,r){var a=t.scale,c=t.type,u=t.layout,l=t.axisType;if("auto"===a)return"radial"===u&&"radiusAxis"===l?{scale:n.Z(),realScaleType:"band"}:"radial"===u&&"angleAxis"===l?{scale:o.Z(),realScaleType:"linear"}:"category"===c&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:n.x(),realScaleType:"point"}:"category"===c?{scale:n.Z(),realScaleType:"band"}:{scale:o.Z(),realScaleType:"linear"};if(x()(a)){var s="scale".concat(T()(a));return{scale:(i[s]||n.x)(),realScaleType:i[s]?s:"point"}}return g()(a)?{scale:a}:{scale:n.x(),realScaleType:"point"}},ct=1e-4,ut=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-ct,i=Math.max(n[0],n[1])+ct,a=t(e[0]),c=t(e[r-1]);(a<o||a>i||c<o||c>i)&&t.domain([e[0],e[r-1]])}},lt=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},st=function(t,e){if(!e||2!==e.length||!(0,L.hj)(e[0])||!(0,L.hj)(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!(0,L.hj)(t[0])||t[0]<r)&&(o[0]=r),(!(0,L.hj)(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},ft={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var c=E()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1])}},expand:a.Z,none:c.Z,silhouette:u.Z,wiggle:l.Z,positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=E()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},pt=function(t,e,r){var n=e.map((function(t){return t.props.dataKey})),o=ft[r];return(0,s.Z)().keys(n).value((function(t,e){return+V(t,e,0)})).order(f.Z).offset(o)(t)},dt=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce((function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?K(K({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var c=i[r],u=t[c]||{hasStack:!1,stackGroups:{}};if((0,L.P2)(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[(0,L.EL)("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return K(K({},t),{},X({},c,u))}),{});return Object.keys(a).reduce((function(e,i){var c=a[i];if(c.hasStack){c.stackGroups=Object.keys(c.stackGroups).reduce((function(e,i){var a=c.stackGroups[i];return K(K({},e),{},X({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:pt(t,a.items,o)}))}),{})}return K(K({},e),{},X({},i,c))}),{})},yt=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var u=t.domain();if(!u.length)return null;var l=(0,N.Zj)(u,o,a);return t.domain([h()(l),d()(l)]),{niceTicks:l}}if(o&&"number"===n){var s=t.domain();return{niceTicks:(0,N.wZ)(s,o,a)}}return null};function ht(t){var e=t.axis,r=t.ticks,n=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!m()(o[e.dataKey])){var c=(0,L.Ap)(r,"value",o[e.dataKey]);if(c)return c.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var u=V(o,m()(a)?e.dataKey:a);return m()(u)?null:e.scale(u)}var vt=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=V(i,e.dataKey,e.domain[a]);return m()(c)?null:e.scale(c)-o/2+n},mt=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},bt=function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?K(K({},t.type.defaultProps),t.props):t.props).stackId;if((0,L.P2)(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},gt=function(t,e,r){return Object.keys(t).reduce((function(n,o){var i=t[o].stackedData.reduce((function(t,n){var o=n.slice(e,r+1).reduce((function(t,e){return[h()(e.concat([t[0]]).filter(L.hj)),d()(e.concat([t[1]]).filter(L.hj))]}),[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]}),[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]}),[1/0,-1/0]).map((function(t){return t===1/0||t===-1/0?0:t}))},Ot=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,xt=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,wt=function(t,e,r){if(g()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if((0,L.hj)(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(Ot.test(t[0])){var o=+Ot.exec(t[0])[1];n[0]=e[0]-o}else g()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if((0,L.hj)(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(xt.test(t[1])){var i=+xt.exec(t[1])[1];n[1]=e[1]+i}else g()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},jt=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=M()(e,(function(t){return t.coordinate})),i=1/0,a=1,c=o.length;a<c;a++){var u=o[a],l=o[a-1];i=Math.min((u.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},Pt=function(t,e,r){return t&&t.length?I()(t,j()(r,"type.defaultProps.domain"))?e:t:e},St=function(t,e){var r=t.type.defaultProps?K(K({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return K(K({},(0,R.L6)(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:U(t),value:V(e,n),type:c,payload:e,chartType:u,hide:l})}},99875:function(t,e,r){r.d(e,{xE:function(){return p},os:function(){return d}});var n=r(59509);function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach((function(e){c(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function c(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=o(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==o(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var u={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},s="recharts_measurement_span";function f(t){var e=a({},t);return Object.keys(e).forEach((function(t){e[t]||delete e[t]})),e}var p=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0===t||null===t||n.x.isSsr)return{width:0,height:0};var r=f(e),o=JSON.stringify({text:t,copyStyle:r});if(u.widthCache[o])return u.widthCache[o];try{var i=document.getElementById(s);i||((i=document.createElement("span")).setAttribute("id",s),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var c=a(a({},l),r);Object.assign(i.style,c),i.textContent="".concat(t);var p=i.getBoundingClientRect(),d={width:p.width,height:p.height};return u.widthCache[o]=d,++u.cacheCount>2e3&&(u.cacheCount=0,u.widthCache={}),d}catch(y){return{width:0,height:0}}},d=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}}},16171:function(t,e,r){r.d(e,{uY:function(){return d},hU:function(){return y},hj:function(){return h},Rw:function(){return v},P2:function(){return m},EL:function(){return g},h1:function(){return O},Kt:function(){return x},bv:function(){return w},k4:function(){return j},Ap:function(){return P},wr:function(){return S},fC:function(){return A}});var n=r(72139),o=r.n(n),i=r(35813),a=r.n(i),c=r(80089),u=r.n(c),l=r(47315),s=r.n(l),f=r(51391),p=r.n(f),d=function(t){return 0===t?0:t>0?1:-1},y=function(t){return o()(t)&&t.indexOf("%")===t.length-1},h=function(t){return s()(t)&&!a()(t)},v=function(t){return p()(t)},m=function(t){return h(t)||o()(t)},b=0,g=function(t){var e=++b;return"".concat(t||"").concat(e)},O=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!h(t)&&!o()(t))return n;if(y(t)){var c=t.indexOf("%");r=e*parseFloat(t.slice(0,c))/100}else r=+t;return a()(r)&&(r=n),i&&r>e&&(r=e),r},x=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},w=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1},j=function(t,e){return h(t)&&h(e)?function(r){return t+r*(e-t)}:function(){return e}};function P(t,e,r){return t&&t.length?t.find((function(t){return t&&("function"===typeof e?e(t):u()(t,e))===r})):null}var S=function(t){if(!t||!t.length)return null;for(var e=t.length,r=0,n=0,o=0,i=0,a=1/0,c=-1/0,u=0,l=0,s=0;s<e;s++)r+=u=t[s].cx||0,n+=l=t[s].cy||0,o+=u*l,i+=u*u,a=Math.min(a,u),c=Math.max(c,u);var f=e*i!==r*r?(e*o-r*n)/(e*i-r*r):0;return{xmin:a,xmax:c,a:f,b:(n-f*r)/e}},A=function(t,e){return h(t)&&h(e)?t-e:o()(t)&&o()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))}},59509:function(t,e,r){r.d(e,{x:function(){return n}});var n={isSsr:!("undefined"!==typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(t){return n[t]},set:function(t,e){if("string"===typeof t)n[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach((function(e){n[e]=t[e]}))}}}},94694:function(t,e,r){r.d(e,{B:function(){return n}});var n=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e}},78706:function(t,e,r){r.d(e,{Z:function(){return n}});var n=function(t,e){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]}},80072:function(t,e,r){r.d(e,{Wk:function(){return v},op:function(){return b},$4:function(){return g},t9:function(){return O},z3:function(){return j},$S:function(){return P}});var n=r(51391),o=r.n(n),i=r(89526),a=r(39277),c=r.n(a),u=r(16171),l=r(36530);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function d(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return h(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var v=Math.PI/180,m=function(t){return 180*t/Math.PI},b=function(t,e,r,n){return{x:t+Math.cos(-v*n)*r,y:e+Math.sin(-v*n)*r}},g=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},O=function(t,e,r,n,i){var a=t.width,c=t.height,s=t.startAngle,f=t.endAngle,h=(0,u.h1)(t.cx,a,a/2),v=(0,u.h1)(t.cy,c,c/2),m=g(a,c,r),b=(0,u.h1)(t.innerRadius,m,0),O=(0,u.h1)(t.outerRadius,m,.8*m);return Object.keys(e).reduce((function(t,r){var a,c=e[r],u=c.domain,m=c.reversed;if(o()(c.range))"angleAxis"===n?a=[s,f]:"radiusAxis"===n&&(a=[b,O]),m&&(a=[a[1],a[0]]);else{var g=y(a=c.range,2);s=g[0],f=g[1]}var x=(0,l.Hq)(c,i),w=x.realScaleType,j=x.scale;j.domain(u).range(a),(0,l.zF)(j);var P=(0,l.g$)(j,p(p({},c),{},{realScaleType:w})),S=p(p(p({},c),P),{},{range:a,radius:O,realScaleType:w,scale:j,cx:h,cy:v,innerRadius:b,outerRadius:O,startAngle:s,endAngle:f});return p(p({},t),{},d({},r,S))}),{})},x=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return Math.sqrt(Math.pow(r-o,2)+Math.pow(n-i,2))}({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=(r-o)/a,u=Math.acos(c);return n>i&&(u=2*Math.PI-u),{radius:a,angle:m(u),angleInRadian:u}},w=function(t,e){var r=e.startAngle,n=e.endAngle,o=Math.floor(r/360),i=Math.floor(n/360);return t+360*Math.min(o,i)},j=function(t,e){var r=t.x,n=t.y,o=x({x:r,y:n},e),i=o.radius,a=o.angle,c=e.innerRadius,u=e.outerRadius;if(i<c||i>u)return!1;if(0===i)return!0;var l,s=function(t){var e=t.startAngle,r=t.endAngle,n=Math.floor(e/360),o=Math.floor(r/360),i=Math.min(n,o);return{startAngle:e-360*i,endAngle:r-360*i}}(e),f=s.startAngle,d=s.endAngle,y=a;if(f<=d){for(;y>d;)y-=360;for(;y<f;)y+=360;l=y>=f&&y<=d}else{for(;y>f;)y-=360;for(;y<d;)y+=360;l=y>=d&&y<=f}return l?p(p({},e),{},{radius:i,angle:w(y,e)}):null},P=function(t){return(0,i.isValidElement)(t)||c()(t)||"boolean"===typeof t?"":t.className}},9410:function(t,e,r){r.d(e,{Gf:function(){return j},NN:function(){return E},sP:function(){return k},TT:function(){return T},jf:function(){return D},hQ:function(){return M},L6:function(){return N},rL:function(){return B},eu:function(){return R},Bh:function(){return z},$R:function(){return _}});var n=r(80089),o=r.n(n),i=r(51391),a=r.n(i),c=r(72139),u=r.n(c),l=r(39277),s=r.n(l),f=r(23619),p=r.n(f),d=r(89526),y=r(32822),h=r(16171),v=r(68201),m=r(33790),b=["children"],g=["children"];function O(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function x(t){return x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},x(t)}var w={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},j=function(t){return"string"===typeof t?t:t?t.displayName||t.name||"Component":""},P=null,S=null,A=function t(e){if(e===P&&Array.isArray(S))return S;var r=[];return d.Children.forEach(e,(function(e){a()(e)||((0,y.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))})),S=r,P=e,r};function E(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map((function(t){return j(t)})):[j(e)],A(t).forEach((function(t){var e=o()(t,"type.displayName")||o()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)})),r}function k(t,e){var r=E(t,e);return r&&r[0]}var T=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!(!(0,h.hj)(r)||r<=0||!(0,h.hj)(n)||n<=0)},C=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],I=function(t){return t&&t.type&&u()(t.type)&&C.indexOf(t.type)>=0},D=function(t){return t&&"object"===x(t)&&"clipDot"in t},M=function(t){var e=[];return A(t).forEach((function(t){I(t)&&e.push(t)})),e},N=function(t,e,r){if(!t||"function"===typeof t||"boolean"===typeof t)return null;var n=t;if((0,d.isValidElement)(t)&&(n=t.props),!p()(n))return null;var o={};return Object.keys(n).forEach((function(t){var i;(function(t,e,r,n){var o,i=null!==(o=null===m.ry||void 0===m.ry?void 0:m.ry[n])&&void 0!==o?o:[];return e.startsWith("data-")||!s()(t)&&(n&&i.includes(e)||m.Yh.includes(e))||r&&m.nv.includes(e)})(null===(i=n)||void 0===i?void 0:i[t],t,e,r)&&(o[t]=n[t])})),o},B=function t(e,r){if(e===r)return!0;var n=d.Children.count(e);if(n!==d.Children.count(r))return!1;if(0===n)return!0;if(1===n)return L(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var i=e[o],a=r[o];if(Array.isArray(i)||Array.isArray(a)){if(!t(i,a))return!1}else if(!L(i,a))return!1}return!0},L=function(t,e){if(a()(t)&&a()(e))return!0;if(!a()(t)&&!a()(e)){var r=t.props||{},n=r.children,o=O(r,b),i=e.props||{},c=i.children,u=O(i,g);return n&&c?(0,v.w)(o,u)&&B(n,c):!n&&!c&&(0,v.w)(o,u)}return!1},R=function(t,e){var r=[],n={};return A(t).forEach((function(t,o){if(I(t))r.push(t);else if(t){var i=j(t.type),a=e[i]||{},c=a.handler,u=a.once;if(c&&(!u||!n[i])){var l=c(t,i,o);r.push(l),n[i]=!0}}})),r},z=function(t){var e=t&&t.type;return e&&w[e]?w[e]:null},_=function(t,e){return A(e).indexOf(t)}},68201:function(t,e,r){function n(t,e){for(var r in t)if({}.hasOwnProperty.call(t,r)&&(!{}.hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if({}.hasOwnProperty.call(e,n)&&!{}.hasOwnProperty.call(t,n))return!1;return!0}r.d(e,{w:function(){return n}})},65436:function(t,e,r){r.d(e,{z:function(){return s}});var n=r(71015),o=r(36530),i=r(9410);function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function u(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach((function(e){l(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function l(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var s=function(t){var e=t.children,r=t.formattedGraphicalItems,a=t.legendWidth,c=t.legendContent,l=(0,i.sP)(e,n.D);if(!l)return null;var s,f=n.D.defaultProps,p=void 0!==f?u(u({},f),l.props):{};return s=l.props&&l.props.payload?l.props&&l.props.payload:"children"===c?(r||[]).reduce((function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map((function(t){return{type:l.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}})))}),[]):(r||[]).map((function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?u(u({},r),e.props):{},i=n.dataKey,a=n.name,c=n.legendType;return{inactive:n.hide,dataKey:i,type:p.iconType||c||"square",color:(0,o.fk)(e),value:a||i,payload:n}})),u(u(u({},p),n.D.getWithHeight(l,a)),{},{payload:s,item:l})}},56062:function(t,e,r){r.d(e,{z:function(){return c}});var n=r(80971),o=r.n(n),i=r(39277),a=r.n(i);function c(t,e,r){return!0===e?o()(t,r):a()(e)?o()(t,e):t}},33790:function(t,e,r){r.d(e,{Yh:function(){return c},ry:function(){return l},nv:function(){return s},Ym:function(){return f},bw:function(){return p}});var n=r(89526),o=r(23619),i=r.n(o);function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}var c=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],u=["points","pathLength"],l={svg:["viewBox","children"],polygon:u,polyline:u},s=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],f=function(t,e){if(!t||"function"===typeof t||"boolean"===typeof t)return null;var r=t;if((0,n.isValidElement)(t)&&(r=t.props),!i()(r))return null;var o={};return Object.keys(r).forEach((function(t){s.includes(t)&&(o[t]=e||function(e){return r[t](r,e)})})),o},p=function(t,e,r){if(!i()(t)||"object"!==a(t))return null;var n=null;return Object.keys(t).forEach((function(o){var i=t[o];s.includes(o)&&"function"===typeof i&&(n||(n={}),n[o]=function(t,e,r){return function(n){return t(e,r,n),null}}(i,e,r))})),n}},32018:function(t,e){var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),y=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),v=Symbol.for("react.offscreen");function m(t){if("object"===typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case i:case c:case a:case p:case d:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case h:case y:case u:return t;default:return e}}case o:return e}}}r=Symbol.for("react.module.reference"),e.isFragment=function(t){return m(t)===i}},32822:function(t,e,r){t.exports=r(32018)}}]);
//# sourceMappingURL=recharts.35747d298b1c0b1418864f95211cca7c.js.map