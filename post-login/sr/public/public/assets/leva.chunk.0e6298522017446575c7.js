"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["leva"],{1981:function(e,t,n){n.d(t,{Zf:function(){return po},LI:function(){return Ar},M4:function(){return ho}});var r=n(73961),o=n(89526),i=n(42651),a=n(64525);function l(e,t){if(Object.is(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r=0;r<n.length;r++)if(!Object.prototype.hasOwnProperty.call(t,n[r])||!Object.is(e[n[r]],t[n[r]]))return!1;return!0}var s=n(34353),c=n(86744),u=n(86320),d=n(49090);function f(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}let p;!function(e){e[e.UNSUPPORTED_INPUT=0]="UNSUPPORTED_INPUT",e[e.NO_COMPONENT_FOR_TYPE=1]="NO_COMPONENT_FOR_TYPE",e[e.UNKNOWN_INPUT=2]="UNKNOWN_INPUT",e[e.DUPLICATE_KEYS=3]="DUPLICATE_KEYS",e[e.ALREADY_REGISTERED_TYPE=4]="ALREADY_REGISTERED_TYPE",e[e.CLIPBOARD_ERROR=5]="CLIPBOARD_ERROR",e[e.THEME_ERROR=6]="THEME_ERROR",e[e.PATH_DOESNT_EXIST=7]="PATH_DOESNT_EXIST",e[e.INPUT_TYPE_OVERRIDE=8]="INPUT_TYPE_OVERRIDE",e[e.EMPTY_KEY=9]="EMPTY_KEY"}(p||(p={}));const g={[p.UNSUPPORTED_INPUT]:(e,t)=>[`An input with type \`${e}\` input was found at path \`${t}\` but it's not supported yet.`],[p.NO_COMPONENT_FOR_TYPE]:(e,t)=>[`Type \`${e}\` found at path \`${t}\` can't be displayed in panel because no component supports it yet.`],[p.UNKNOWN_INPUT]:(e,t)=>[`input at path \`${e}\` is not recognized.`,t],[p.DUPLICATE_KEYS]:(e,t,n)=>[`Key \`${e}\` of path \`${t}\` already exists at path \`${n}\`. Even nested keys need to be unique. Rename one of the keys.`],[p.ALREADY_REGISTERED_TYPE]:e=>[`Type ${e} has already been registered. You can't register a component with the same type.`],[p.CLIPBOARD_ERROR]:e=>["Error copying the value",e],[p.THEME_ERROR]:(e,t)=>[`Error accessing the theme \`${e}.${t}\` value.`],[p.PATH_DOESNT_EXIST]:e=>[`Error getting the value at path \`${e}\`. There is probably an error in your \`render\` function.`],[p.PATH_DOESNT_EXIST]:e=>[`Error accessing the value at path \`${e}\``],[p.INPUT_TYPE_OVERRIDE]:(e,t,n)=>[`Input at path \`${e}\` already exists with type: \`${t}\`. Its type cannot be overridden with type \`${n}\`.`],[p.EMPTY_KEY]:()=>["Keys can not be empty, if you want to hide a label use whitespace."]};function h(e,t,...n){const[r,...o]=g[t](...n);console[e]("LEVA: "+r,...o)}const m=h.bind(null,"warn"),v=h.bind(null,"log"),b=["value"],y=["schema"],E=["value"],w=[],$={};function O(e){let{value:t}=e,n=f(e,b);for(let r of w){const e=r(t,n);if(e)return e}}function C(e,t){let{schema:n}=t,r=f(t,y);e in $?m(p.ALREADY_REGISTERED_TYPE,e):(w.push(((t,r)=>n(t,r)&&e)),$[e]=r)}function x(e,t,n,r){const{normalize:o}=$[e];if(o)return o(t,n,r);if("object"!==typeof t||!("value"in t))return{value:t};const{value:i}=t;return{value:i,settings:f(t,E)}}function R(e,t,n){const{format:r}=$[e];return r?r(t,n):t}function S(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function k(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function T(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?k(Object(n),!0).forEach((function(t){S(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const D=(e,t,n)=>e>n?n:e<t?t:e,j=e=>{if(""===e||"number"===typeof e)return e;try{const t=V(e);if(!isNaN(t))return t}catch(t){}return parseFloat(e)},P=Math.log(10);function _(e){let t=Math.abs(+String(e).replace(".",""));if(0===t)return.01;for(;0!==t&&t%10===0;)t/=10;const n=Math.floor(Math.log(t)/P)+1,r=Math.floor(Math.log10(Math.abs(e))),o=Math.pow(10,r-n);return Math.max(o,.001)}const A=(e,t,n)=>{if(n===t)return 0;return(D(e,t,n)-t)/(n-t)},I=(e,t,n)=>e*(n-t)+t,F=/\(([0-9+\-*/^ .]+)\)/,L=/(\d+(?:\.\d+)?) ?\^ ?(\d+(?:\.\d+)?)/,z=/(\d+(?:\.\d+)?) ?\* ?(\d+(?:\.\d+)?)/,N=/(\d+(?:\.\d+)?) ?\/ ?(\d+(?:\.\d+)?)/,M=/(\d+(?:\.\d+)?) ?\+ ?(\d+(?:\.\d+)?)/,U=/(\d+(?:\.\d+)?) ?- ?(\d+(?:\.\d+)?)/;function V(e){if(isNaN(Number(e))){if(F.test(e)){const t=e.replace(F,((e,t)=>String(V(t))));return V(t)}if(L.test(e)){return V(e.replace(L,((e,t,n)=>String(Math.pow(Number(t),Number(n))))))}if(z.test(e)){return V(e.replace(z,((e,t,n)=>String(Number(t)*Number(n)))))}if(N.test(e)){return V(e.replace(N,((e,t,n)=>{if(0!=n)return String(Number(t)/Number(n));throw new Error("Division by zero")})))}if(M.test(e)){return V(e.replace(M,((e,t,n)=>String(Number(t)+Number(n)))))}if(U.test(e)){return V(e.replace(U,((e,t,n)=>String(Number(t)-Number(n)))))}return Number(e)}return Number(e)}function H(e){return"[object Object]"===Object.prototype.toString.call(e)}const B=e=>H(e)&&0===Object.keys(e).length;let W,G;!function(e){e.BUTTON="BUTTON",e.BUTTON_GROUP="BUTTON_GROUP",e.MONITOR="MONITOR",e.FOLDER="FOLDER"}(W||(W={})),function(e){e.SELECT="SELECT",e.IMAGE="IMAGE",e.NUMBER="NUMBER",e.COLOR="COLOR",e.STRING="STRING",e.BOOLEAN="BOOLEAN",e.INTERVAL="INTERVAL",e.VECTOR3D="VECTOR3D",e.VECTOR2D="VECTOR2D"}(G||(G={}));const K=["type","__customInput"],Y=["render","label","optional","order","disabled","hint","onChange","onEditStart","onEditEnd","transient"],Z=["type"];function J(e,t,n={},r){var o,i;if("object"!==typeof e||Array.isArray(e))return{type:r,input:e,options:T({key:t,label:t,optional:!1,disabled:!1,order:0},n)};if("__customInput"in e){const{type:n,__customInput:r}=e;return J(r,t,f(e,K),n)}const{render:a,label:l,optional:s,order:c=0,disabled:u,hint:d,onChange:p,onEditStart:g,onEditEnd:h,transient:m}=e,v=f(e,Y),b=T({render:a,key:t,label:null!==l&&void 0!==l?l:t,hint:d,transient:null!==m&&void 0!==m?m:!!p,onEditStart:g,onEditEnd:h,disabled:u,optional:s,order:c},n);let y,{type:E}=v,w=f(v,Z);return E=null!==r&&void 0!==r?r:E,E in W?{type:E,input:w,options:b}:(y=r&&H(w)&&"value"in w?w.value:B(w)?void 0:w,{type:E,input:y,options:T(T({},b),{},{onChange:p,optional:null!==(o=b.optional)&&void 0!==o&&o,disabled:null!==(i=b.disabled)&&void 0!==i&&i})})}function X(e,t,n,r){const o=J(e,t),{type:i,input:a,options:l}=o;if(i)return i in W?o:{type:i,input:x(i,a,n,r),options:l};let s=O(a);return s?{type:s,input:x(s,a,n,r),options:l}:(s=O({value:a}),!!s&&{type:s,input:x(s,{value:a},n,r),options:l})}function q(e,t,n,r,o){const{value:i,type:a,settings:l}=e;e.value=ee({type:a,value:i,settings:l},t,n,r),e.fromPanel=o}const Q=function(e,t,n){this.type="LEVA_ERROR",this.message="LEVA: "+e,this.previousValue=t,this.error=n};function ee({type:e,value:t,settings:n},r,o,i){const l="SELECT"!==e&&"function"===typeof r?r(t):r;let s;try{s=function(e,t,n,r,o,i){const{sanitize:a}=$[e];return a?a(t,n,r,o,i):t}(e,l,n,t,o,i)}catch(c){throw new Q(`The value \`${r}\` did not result in a correct value.`,t,c)}return(0,a.J)(s,t)?t:s}const te=(e,t,n=!1)=>{let r=0;return function(){const o=arguments,i=n&&!r,a=()=>e.apply(this,o);window.clearTimeout(r),r=window.setTimeout(a,t),i&&a()}},ne=e=>e.shiftKey?5:e.altKey?.2:1;const re=["value"],oe=["min","max"],ie=(e,{min:t=-1/0,max:n=1/0,suffix:r})=>{const o=parseFloat(e);if(""===e||isNaN(o))throw Error("Invalid number");const i=D(o,t,n);return r?i+r:i},ae=e=>{let{value:t}=e,n=f(e,re);const{min:r=-1/0,max:o=1/0}=n,i=f(n,oe);let a=parseFloat(t);const l="string"===typeof t?t.substring((""+a).length):void 0;a=D(a,r,o);let s=n.step;s||(Number.isFinite(r)?s=Number.isFinite(o)?+(Math.abs(o-r)/100).toPrecision(1):+(Math.abs(a-r)/100).toPrecision(1):Number.isFinite(o)&&(s=+(Math.abs(o-a)/100).toPrecision(1)));const c=s?10*_(s):_(a);s=s||c/10;return{value:l?a+l:a,settings:T({initialValue:a,step:s,pad:Math.round(D(Math.log10(1/c),0,2)),min:r,max:o,suffix:l},i)}},le=(e,{step:t,initialValue:n})=>n+Math.round((e-n)/t)*t;var se=Object.freeze({__proto__:null,schema:e=>{if("number"===typeof e)return!0;if("string"===typeof e){const t=parseFloat(e);if(isNaN(t))return!1;return e.substring((""+t).length).trim().length<4}return!1},sanitize:ie,format:(e,{pad:t=0,suffix:n})=>{const r=parseFloat(e).toFixed(t);return n?r+n:r},normalize:ae,sanitizeStep:le});function ce(){return ce=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ce.apply(this,arguments)}const ue=(0,o.createContext)({});function de(){return(0,o.useContext)(ue)}const fe=(0,o.createContext)(null),pe=(0,o.createContext)(null),ge=(0,o.createContext)(null);function he(){return(0,o.useContext)(pe)}const me=()=>({colors:{elevation1:"#292d39",elevation2:"#181c20",elevation3:"#373c4b",accent1:"#0066dc",accent2:"#007bff",accent3:"#3c93ff",highlight1:"#535760",highlight2:"#8c92a4",highlight3:"#fefefe",vivid1:"#ffcc00",folderWidgetColor:"$highlight2",folderTextColor:"$highlight3",toolTipBackground:"$highlight3",toolTipText:"$elevation2"},radii:{xs:"2px",sm:"3px",lg:"10px"},space:{xs:"3px",sm:"6px",md:"10px",rowGap:"7px",colGap:"7px"},fonts:{mono:"ui-monospace, SFMono-Regular, Menlo, 'Roboto Mono', monospace",sans:"system-ui, sans-serif"},fontSizes:{root:"11px",toolTip:"$root"},sizes:{rootWidth:"280px",controlWidth:"160px",numberInputMinWidth:"38px",scrubberWidth:"8px",scrubberHeight:"16px",rowHeight:"24px",folderTitleHeight:"20px",checkboxSize:"16px",joystickWidth:"100px",joystickHeight:"100px",colorPickerWidth:"$controlWidth",colorPickerHeight:"100px",imagePreviewWidth:"$controlWidth",imagePreviewHeight:"100px",monitorHeight:"60px",titleBarHeight:"39px"},shadows:{level1:"0 0 9px 0 #00000088",level2:"0 4px 14px #00000033"},borderWidths:{root:"0px",input:"1px",focus:"1px",hover:"1px",active:"1px",folder:"1px"},fontWeights:{label:"normal",folder:"normal",button:"normal"}});function ve(e,t){const[n,r]=e.split(" "),o={};return"none"!==n&&(o.boxShadow=`${t.inset?"inset ":""}0 0 0 $borderWidths${[t.key]} $colors${"default"!==n&&n||t.borderColor}`),r&&(o.backgroundColor=r),o}const be={$inputStyle:()=>e=>ve(e,{key:"$input",borderColor:"$highlight1",inset:!0}),$focusStyle:()=>e=>ve(e,{key:"$focus",borderColor:"$accent2"}),$hoverStyle:()=>e=>ve(e,{key:"$hover",borderColor:"$accent1",inset:!0}),$activeStyle:()=>e=>ve(e,{key:"$active",borderColor:"$accent1",inset:!0})},{styled:ye,css:Ee,createTheme:we,globalCss:$e,keyframes:Oe}=(0,c.Th)({prefix:"leva",theme:me(),utils:T(T({},be),{},{$flex:()=>({display:"flex",alignItems:"center"}),$flexCenter:()=>({display:"flex",alignItems:"center",justifyContent:"center"}),$reset:()=>({outline:"none",fontSize:"inherit",fontWeight:"inherit",color:"inherit",fontFamily:"inherit",border:"none",backgroundColor:"transparent",appearance:"none"}),$draggable:()=>({touchAction:"none",WebkitUserDrag:"none",userSelect:"none"}),$focus:e=>({"&:focus":be.$focusStyle()(e)}),$focusWithin:e=>({"&:focus-within":be.$focusStyle()(e)}),$hover:e=>({"&:hover":be.$hoverStyle()(e)}),$active:e=>({"&:active":be.$activeStyle()(e)})})}),Ce=$e({".leva__panel__dragged":{WebkitUserDrag:"none",userSelect:"none",input:{userSelect:"none"},"*":{cursor:"ew-resize !important"}}});function xe(e,t){const{theme:n}=(0,o.useContext)(fe);if(!(e in n)||!(t in n[e]))return m(p.THEME_ERROR,e,t),"";let r=t;for(;;){let t=n[e][r];if("string"!==typeof t||"$"!==t.charAt(0))return t;r=t.substr(1)}}const Re=ye("input",{$reset:"",padding:"0 $sm",width:0,minWidth:0,flex:1,height:"100%",variants:{levaType:{number:{textAlign:"right"}},as:{textarea:{padding:"$sm"}}}}),Se=ye("div",{$draggable:"",height:"100%",$flexCenter:"",position:"relative",padding:"0 $xs",fontSize:"0.8em",opacity:.8,cursor:"default",touchAction:"none",[`& + ${Re}`]:{paddingLeft:0}}),ke=ye(Se,{cursor:"ew-resize",marginRight:"-$xs",textTransform:"uppercase",opacity:.3,"&:hover":{opacity:1},variants:{dragging:{true:{backgroundColor:"$accent2",opacity:1}}}}),Te=ye("div",{$flex:"",position:"relative",borderRadius:"$sm",overflow:"hidden",color:"inherit",height:"$rowHeight",backgroundColor:"$elevation3",$inputStyle:"$elevation1",$hover:"",$focusWithin:"",variants:{textArea:{true:{height:"auto"}}}}),De=["innerLabel","value","onUpdate","onChange","onKeyDown","type","id","inputType","rows"],je=["onUpdate"];function Pe(e){let{innerLabel:t,value:n,onUpdate:r,onChange:i,onKeyDown:a,type:l,id:s,inputType:c="text",rows:u=0}=e,d=f(e,De);const{id:p,emitOnEditStart:g,emitOnEditEnd:h,disabled:m}=de(),v=s||p,b=(0,o.useRef)(null),y=u>0,E=y?"textarea":"input",w=(0,o.useCallback)((e=>t=>{const n=t.currentTarget.value;e(n)}),[]);o.useEffect((()=>{const e=b.current,t=w((e=>{r(e),h()}));return null===e||void 0===e||e.addEventListener("blur",t),()=>null===e||void 0===e?void 0:e.removeEventListener("blur",t)}),[w,r,h]);const $=(0,o.useCallback)((e=>{"Enter"===e.key&&w(r)(e)}),[w,r]),O=Object.assign({as:E},y?{rows:u}:{},d);return o.createElement(Te,{textArea:y},t&&"string"===typeof t?o.createElement(Se,null,t):t,o.createElement(Re,ce({levaType:l,ref:b,id:v,type:c,autoComplete:"off",spellCheck:"false",value:n,onChange:w(i),onFocus:()=>g(),onKeyPress:$,onKeyDown:a,disabled:m},O)))}function _e(e){let{onUpdate:t}=e,n=f(e,je);const r=(0,o.useCallback)((e=>t(j(e))),[t]),i=(0,o.useCallback)((e=>{const n="ArrowUp"===e.key?1:"ArrowDown"===e.key?-1:0;if(n){e.preventDefault();const r=e.altKey?.1:e.shiftKey?10:1;t((e=>parseFloat(e)+n*r))}}),[t]);return o.createElement(Pe,ce({},n,{onUpdate:r,onKeyDown:i,type:"number"}))}const Ae=ye("div",{}),Ie=ye("div",{position:"relative",background:"$elevation2",transition:"height 300ms ease",variants:{fill:{true:{},false:{}},flat:{false:{},true:{}},isRoot:{true:{},false:{paddingLeft:"$md","&::after":{content:'""',position:"absolute",left:0,top:0,width:"$borderWidths$folder",height:"100%",backgroundColor:"$folderWidgetColor",opacity:.4,transform:"translateX(-50%)"}}}},compoundVariants:[{isRoot:!0,fill:!1,css:{overflowY:"auto",maxHeight:"calc(100vh - 20px - $$titleBarHeight)"}},{isRoot:!0,flat:!1,css:{borderRadius:"$lg"}}]}),Fe=ye("div",{$flex:"",color:"$folderTextColor",userSelect:"none",cursor:"pointer",height:"$folderTitleHeight",fontWeight:"$folder","> svg":{marginLeft:-4,marginRight:4,cursor:"pointer",fill:"$folderWidgetColor",opacity:.6},"&:hover > svg":{fill:"$folderWidgetColor"},[`&:hover + ${Ie}::after`]:{opacity:.6},[`${Ae}:hover > & + ${Ie}::after`]:{opacity:.6},[`${Ae}:hover > & > svg`]:{opacity:1}}),Le=ye("div",{position:"relative",display:"grid",gridTemplateColumns:"100%",rowGap:"$rowGap",transition:"opacity 250ms ease",variants:{toggled:{true:{opacity:1,transitionDelay:"250ms"},false:{opacity:0,transitionDelay:"0ms",pointerEvents:"none"}},isRoot:{true:{"& > div":{paddingLeft:"$md",paddingRight:"$md"},"& > div:first-of-type":{paddingTop:"$sm"},"& > div:last-of-type":{paddingBottom:"$sm"},[`> ${Ae}:not(:first-of-type)`]:{paddingTop:"$sm",marginTop:"$md",borderTop:"$borderWidths$folder solid $colors$elevation1"}}}}}),ze=ye("div",{position:"relative",zIndex:100,display:"grid",rowGap:"$rowGap",gridTemplateRows:"minmax($sizes$rowHeight, max-content)",alignItems:"center",color:"$highlight2",[`${Le} > &`]:{"&:first-of-type":{marginTop:"$rowGap"},"&:last-of-type":{marginBottom:"$rowGap"}},variants:{disabled:{true:{pointerEvents:"none"},false:{"&:hover,&:focus-within":{color:"$highlight3"}}}}}),Ne=ye(ze,{gridTemplateColumns:"auto $sizes$controlWidth",columnGap:"$colGap"}),Me=ye("div",{$flex:"",height:"100%",position:"relative",overflow:"hidden","& > div":{marginLeft:"$colGap",padding:"0 $xs",opacity:.4},"& > div:hover":{opacity:.8},"& > div > svg":{display:"none",cursor:"pointer",width:13,minWidth:13,height:13,backgroundColor:"$elevation2"},"&:hover > div > svg":{display:"block"},variants:{align:{top:{height:"100%",alignItems:"flex-start",paddingTop:"$sm"}}}}),Ue=ye("input",{$reset:"",height:0,width:0,opacity:0,margin:0,"& + label":{position:"relative",$flexCenter:"",height:"100%",userSelect:"none",cursor:"pointer",paddingLeft:2,paddingRight:"$sm",pointerEvents:"auto"},"& + label:after":{content:'""',width:6,height:6,backgroundColor:"$elevation3",borderRadius:"50%",$activeStyle:""},"&:focus + label:after":{$focusStyle:""},"& + label:active:after":{backgroundColor:"$accent1",$focusStyle:""},"&:checked + label:after":{backgroundColor:"$accent1"}}),Ve=ye("label",{fontWeight:"$label",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap","& > svg":{display:"block"}}),He=ye("div",{opacity:1,variants:{disabled:{true:{opacity:.6,pointerEvents:"none",[`& ${Ve}`]:{pointerEvents:"auto"}}}}}),Be=ye("div",{position:"fixed",top:0,bottom:0,right:0,left:0,zIndex:1e3,userSelect:"none"}),We=ye("div",{background:"$toolTipBackground",fontFamily:"$sans",fontSize:"$toolTip",padding:"$xs $sm",color:"$toolTipText",borderRadius:"$xs",boxShadow:"$level2",maxWidth:260}),Ge=ye(d.Eh,{fill:"$toolTipBackground"});function Ke({children:e}){const{className:t}=(0,o.useContext)(fe);return o.createElement(i.f,{className:t},e)}const Ye=["align"];function Ze(){const{id:e,disable:t,disabled:n}=de();return o.createElement(o.Fragment,null,o.createElement(Ue,{id:e+"__disable",type:"checkbox",checked:!n,onChange:()=>t(!n)}),o.createElement("label",{htmlFor:e+"__disable"}))}function Je(e){const{id:t,optional:n,hint:r}=de(),i=e.htmlFor||(t?{htmlFor:t}:null),a=r||"string"!==typeof e.children?null:{title:e.children};return o.createElement(o.Fragment,null,n&&o.createElement(Ze,null),void 0!==r?o.createElement(d.fC,null,o.createElement(d.xz,{asChild:!0},o.createElement(Ve,ce({},i,e))),o.createElement(d.VY,{side:"top",sideOffset:2},o.createElement(We,null,r,o.createElement(Ge,null)))):o.createElement(Ve,ce({},i,a,e)))}function Xe(e){let{align:t}=e,n=f(e,Ye);const{value:r,label:i,key:a,disabled:l}=de(),{hideCopyButton:s}=(0,o.useContext)(ge),c=!s&&void 0!==a,[u,d]=(0,o.useState)(!1);return o.createElement(Me,{align:t,onPointerLeave:()=>d(!1)},o.createElement(Je,n),c&&!l&&o.createElement("div",{title:`Click to copy ${"string"===typeof i?i:a} value`},u?o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor"},o.createElement("path",{d:"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"}),o.createElement("path",{fillRule:"evenodd",d:"M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm9.707 5.707a1 1 0 00-1.414-1.414L9 12.586l-1.293-1.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})):o.createElement("svg",{onClick:async()=>{try{await navigator.clipboard.writeText(JSON.stringify({[a]:null!==r&&void 0!==r?r:""})),d(!0)}catch(e){m(p.CLIPBOARD_ERROR,{[a]:r})}},xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor"},o.createElement("path",{d:"M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"}),o.createElement("path",{d:"M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"}))))}const qe=["toggled"],Qe=ye("svg",{fill:"currentColor",transition:"transform 350ms ease, fill 250ms ease"});function et(e){let{toggled:t}=e,n=f(e,qe);return o.createElement(Qe,ce({width:"9",height:"5",viewBox:"0 0 9 5",xmlns:"http://www.w3.org/2000/svg",style:{transform:`rotate(${t?0:-90}deg)`}},n),o.createElement("path",{d:"M3.8 4.4c.4.3 1 .3 1.4 0L8 1.7A1 1 0 007.4 0H1.6a1 1 0 00-.7 1.7l3 2.7z"}))}const tt=["input"];function nt(e){let{input:t}=e,n=f(e,tt);return t?o.createElement(Ne,n):o.createElement(ze,n)}function rt({value:e,type:t,settings:n,setValue:r}){const[i,l]=(0,o.useState)(R(t,e,n)),s=(0,o.useRef)(e),c=(0,o.useRef)(n);c.current=n;const u=(0,o.useCallback)((e=>l(R(t,e,c.current))),[t]),d=(0,o.useCallback)((e=>{try{r(e)}catch(t){const{type:e,previousValue:n}=t;if("LEVA_ERROR"!==e)throw t;u(n)}}),[u,r]);return(0,o.useEffect)((()=>{(0,a.J)(e,s.current)||u(e),s.current=e}),[e,u]),{displayValue:i,onChange:l,onUpdate:d}}function ot(e,t){const{emitOnEditStart:n,emitOnEditEnd:r}=de();return(0,u.c0)((t=>{t.first&&(document.body.classList.add("leva__panel__dragged"),null===n||void 0===n||n());const o=e(t);return t.last&&(document.body.classList.remove("leva__panel__dragged"),null===r||void 0===r||r()),o}),t)}function it(){const e=(0,o.useRef)(null),t=(0,o.useRef)({x:0,y:0}),n=(0,o.useCallback)((n=>{Object.assign(t.current,n),e.current&&(e.current.style.transform=`translate3d(${t.current.x}px, ${t.current.y}px, 0)`)}),[]);return[e,n]}const at=["__refCount"],lt=(e,t)=>{if(!e[t])return null;return f(e[t],at)};const st=ye("div",{variants:{hasRange:{true:{position:"relative",display:"grid",gridTemplateColumns:"auto $sizes$numberInputMinWidth",columnGap:"$colGap",alignItems:"center"}}}}),ct=ye("div",{position:"relative",width:"100%",height:2,borderRadius:"$xs",backgroundColor:"$elevation1"}),ut=ye("div",{position:"absolute",width:"$scrubberWidth",height:"$scrubberHeight",borderRadius:"$xs",boxShadow:"0 0 0 2px $colors$elevation2",backgroundColor:"$accent2",cursor:"pointer",$active:"none $accent1",$hover:"none $accent3",variants:{position:{left:{borderTopRightRadius:0,borderBottomRightRadius:0,transform:"translateX(calc(-0.5 * ($sizes$scrubberWidth + 4px)))"},right:{borderTopLeftRadius:0,borderBottomLeftRadius:0,transform:"translateX(calc(0.5 * ($sizes$scrubberWidth + 4px)))"}}}}),dt=ye("div",{position:"relative",$flex:"",height:"100%",cursor:"pointer",touchAction:"none"}),ft=ye("div",{position:"absolute",height:"100%",backgroundColor:"$accent2"});function pt({value:e,min:t,max:n,onDrag:r,step:i,initialValue:a}){const l=(0,o.useRef)(null),s=(0,o.useRef)(null),c=(0,o.useRef)(0),u=xe("sizes","scrubberWidth"),d=ot((({event:o,first:d,xy:[f],movement:[p],memo:g})=>{if(d){const{width:r,left:i}=l.current.getBoundingClientRect();c.current=r-parseFloat(u);g=(null===o||void 0===o?void 0:o.target)===s.current?e:I((f-i)/r,t,n)}const h=g+I(p/c.current,0,n-t);return r(le(h,{step:i,initialValue:a})),g})),f=A(e,t,n);return o.createElement(dt,ce({ref:l},d()),o.createElement(ct,null,o.createElement(ft,{style:{left:0,right:100*(1-f)+"%"}})),o.createElement(ut,{ref:s,style:{left:`calc(${f} * (100% - ${u}))`}}))}const gt=o.memo((({label:e,onUpdate:t,step:n,innerLabelTrim:r})=>{const[i,a]=(0,o.useState)(!1),l=ot((({active:e,delta:[r],event:o,memo:i=0})=>(a(e),i+=r/2,Math.abs(i)>=1&&(t((e=>parseFloat(e)+Math.floor(i)*n*ne(o))),i=0),i)));return o.createElement(ke,ce({dragging:i,title:e.length>1?e:""},l()),e.slice(0,r))}));function ht({label:e,id:t,displayValue:n,onUpdate:r,onChange:i,settings:a,innerLabelTrim:l=1}){const s=l>0&&o.createElement(gt,{label:e,step:a.step,onUpdate:r,innerLabelTrim:l});return o.createElement(_e,{id:t,value:String(n),onUpdate:r,onChange:i,innerLabel:s})}const{sanitizeStep:mt}=se;var vt=T({component:function(){const e=de(),{label:t,value:n,onUpdate:r,settings:i,id:a}=e,{min:l,max:s}=i,c=s!==1/0&&l!==-1/0;return o.createElement(nt,{input:!0},o.createElement(Xe,null,t),o.createElement(st,{hasRange:c},c&&o.createElement(pt,ce({value:parseFloat(n),onDrag:r},i)),o.createElement(ht,ce({},e,{id:a,label:"value",innerLabelTrim:c?0:1}))))}},f(se,["sanitizeStep"]));var bt=Object.freeze({__proto__:null,schema:(e,t)=>(0,s.Z)().schema({options:(0,s.Z)().passesAnyOf((0,s.Z)().object(),(0,s.Z)().array())}).test(t),sanitize:(e,{values:t})=>{if(t.indexOf(e)<0)throw Error("Selected value doesn't match Select options");return e},format:(e,{values:t})=>t.indexOf(e),normalize:e=>{let t,n,{value:r,options:o}=e;return Array.isArray(o)?(n=o,t=o.map((e=>String(e)))):(n=Object.values(o),t=Object.keys(o)),"value"in e?n.includes(r)||(t.unshift(String(r)),n.unshift(r)):r=n[0],Object.values(o).includes(r)||(o[String(r)]=r),{value:r,settings:{keys:t,values:n}}}});const yt=ye("div",{$flexCenter:"",position:"relative","> svg":{pointerEvents:"none",position:"absolute",right:"$md"}}),Et=ye("select",{position:"absolute",top:0,left:0,width:"100%",height:"100%",opacity:0}),wt=ye("div",{display:"flex",alignItems:"center",width:"100%",height:"$rowHeight",backgroundColor:"$elevation3",borderRadius:"$sm",padding:"0 $sm",cursor:"pointer",[`${Et}:focus + &`]:{$focusStyle:""},[`${Et}:hover + &`]:{$hoverStyle:""}});function $t({displayValue:e,value:t,onUpdate:n,id:r,settings:i,disabled:a}){const{keys:l,values:s}=i,c=(0,o.useRef)();return t===s[e]&&(c.current=l[e]),o.createElement(yt,null,o.createElement(Et,{id:r,value:e,onChange:e=>n(s[Number(e.currentTarget.value)]),disabled:a},l.map(((e,t)=>o.createElement("option",{key:e,value:t},e)))),o.createElement(wt,null,c.current),o.createElement(et,{toggled:!0}))}var Ot=T({component:function(){const{label:e,value:t,displayValue:n,onUpdate:r,id:i,disabled:a,settings:l}=de();return o.createElement(nt,{input:!0},o.createElement(Xe,null,e),o.createElement($t,{id:i,value:t,displayValue:n,onUpdate:r,settings:l,disabled:a}))}},bt);var Ct=Object.freeze({__proto__:null,schema:e=>(0,s.Z)().string().test(e),sanitize:e=>{if("string"!==typeof e)throw Error("Invalid string");return e},normalize:({value:e,editable:t=!0,rows:n=!1})=>({value:e,settings:{editable:t,rows:"number"===typeof n?n:n?5:0}})});const xt=["displayValue","onUpdate","onChange","editable"],Rt=ye("div",{whiteSpace:"pre-wrap"});function St(e){let{displayValue:t,onUpdate:n,onChange:r,editable:i=!0}=e,a=f(e,xt);return i?o.createElement(Pe,ce({value:t,onUpdate:n,onChange:r},a)):o.createElement(Rt,null,t)}var kt=T({component:function(){const{label:e,settings:t,displayValue:n,onUpdate:r,onChange:i}=de();return o.createElement(nt,{input:!0},o.createElement(Xe,null,e),o.createElement(St,ce({displayValue:n,onUpdate:r,onChange:i},t)))}},Ct);var Tt=Object.freeze({__proto__:null,schema:e=>(0,s.Z)().boolean().test(e),sanitize:e=>{if("boolean"!==typeof e)throw Error("Invalid boolean");return e}});const Dt=ye("div",{position:"relative",$flex:"",height:"$rowHeight",input:{$reset:"",height:0,width:0,opacity:0,margin:0},label:{position:"relative",$flexCenter:"",userSelect:"none",cursor:"pointer",height:"$checkboxSize",width:"$checkboxSize",backgroundColor:"$elevation3",borderRadius:"$sm",$hover:""},"input:focus + label":{$focusStyle:""},"input:focus:checked + label, input:checked + label:hover":{$hoverStyle:"$accent3"},"input + label:active":{backgroundColor:"$accent1"},"input:checked + label:active":{backgroundColor:"$accent1"},"label > svg":{display:"none",width:"90%",height:"90%",stroke:"$highlight3"},"input:checked + label":{backgroundColor:"$accent2"},"input:checked + label > svg":{display:"block"}});function jt({value:e,onUpdate:t,id:n,disabled:r}){return o.createElement(Dt,null,o.createElement("input",{id:n,type:"checkbox",checked:e,onChange:e=>t(e.currentTarget.checked),disabled:r}),o.createElement("label",{htmlFor:n},o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"}))))}var Pt=T({component:function(){const{label:e,value:t,onUpdate:n,disabled:r,id:i}=de();return o.createElement(nt,{input:!0},o.createElement(Xe,null,e),o.createElement(jt,{value:t,onUpdate:n,id:i,disabled:r}))}},Tt);const _t=["locked"];function At({value:e,id:t,valueKey:n,settings:r,onUpdate:i,innerLabelTrim:a}){const l=(0,o.useRef)(e[n]);l.current=e[n];const s=(0,o.useCallback)((e=>i({[n]:ee({type:"NUMBER",value:l.current,settings:r},e)})),[i,r,n]),c=rt({type:"NUMBER",value:e[n],settings:r,setValue:s});return o.createElement(ht,{id:t,label:n,value:e[n],displayValue:c.displayValue,onUpdate:c.onUpdate,onChange:c.onChange,settings:r,innerLabelTrim:a})}const It=ye("div",{display:"grid",columnGap:"$colGap",gridAutoFlow:"column dense",alignItems:"center",variants:{withLock:{true:{gridTemplateColumns:"10px auto","> svg":{cursor:"pointer"}}}}});function Ft(e){let{locked:t}=e,n=f(e,_t);return o.createElement("svg",ce({width:"10",height:"10",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"},n),t?o.createElement("path",{d:"M5 4.63601C5 3.76031 5.24219 3.1054 5.64323 2.67357C6.03934 2.24705 6.64582 1.9783 7.5014 1.9783C8.35745 1.9783 8.96306 2.24652 9.35823 2.67208C9.75838 3.10299 10 3.75708 10 4.63325V5.99999H5V4.63601ZM4 5.99999V4.63601C4 3.58148 4.29339 2.65754 4.91049 1.99307C5.53252 1.32329 6.42675 0.978302 7.5014 0.978302C8.57583 0.978302 9.46952 1.32233 10.091 1.99162C10.7076 2.65557 11 3.57896 11 4.63325V5.99999H12C12.5523 5.99999 13 6.44771 13 6.99999V13C13 13.5523 12.5523 14 12 14H3C2.44772 14 2 13.5523 2 13V6.99999C2 6.44771 2.44772 5.99999 3 5.99999H4ZM3 6.99999H12V13H3V6.99999Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}):o.createElement("path",{d:"M9 3.63601C9 2.76044 9.24207 2.11211 9.64154 1.68623C10.0366 1.26502 10.6432 1 11.5014 1C12.4485 1 13.0839 1.30552 13.4722 1.80636C13.8031 2.23312 14 2.84313 14 3.63325H15C15 2.68242 14.7626 1.83856 14.2625 1.19361C13.6389 0.38943 12.6743 0 11.5014 0C10.4294 0 9.53523 0.337871 8.91218 1.0021C8.29351 1.66167 8 2.58135 8 3.63601V6H1C0.447715 6 0 6.44772 0 7V13C0 13.5523 0.447715 14 1 14H10C10.5523 14 11 13.5523 11 13V7C11 6.44772 10.5523 6 10 6H9V3.63601ZM1 7H10V13H1V7Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}))}function Lt({value:e,onUpdate:t,settings:n,innerLabelTrim:r}){const{id:i,setSettings:a}=de(),{lock:l,locked:s}=n;return o.createElement(It,{withLock:l},l&&o.createElement(Ft,{locked:s,onClick:()=>a({locked:!s})}),Object.keys(e).map(((a,l)=>o.createElement(At,{id:0===l?i:`${i}.${a}`,key:a,valueKey:a,value:e,settings:n[a],onUpdate:t,innerLabelTrim:r}))))}const zt=(e,t)=>{const n={};let r=0,o=1/0;Object.entries(e).forEach((([e,i])=>{n[e]=ae(T({value:i},t[e])).settings,r=Math.max(r,n[e].step),o=Math.min(o,n[e].pad)}));for(let i in n){const{step:e,min:a,max:l}=t[i]||{};isFinite(e)||isFinite(a)&&isFinite(l)||(n[i].step=r,n[i].pad=o)}return n},Nt=["lock"],Mt=["value"];function Ut(e){const t=(0,s.Z)().array().length(e).every.number();return n=>t.test(n)||(t=>{if(!t||"object"!==typeof t)return!1;const n=Object.values(t);return n.length===e&&n.every((e=>isFinite(e)))})(n)}function Vt(e,t,n){return function(e){return Array.isArray(e)?"array":"object"}(e)===t?e:"array"===t?Object.values(e):function(e,t){return e.reduce(((e,n,r)=>Object.assign(e,{[t[r]]:n})),{})}(e,n)}const Ht=e=>!!e&&("step"in e||"min"in e||"max"in e);function Bt(e){return{schema:Ut(e.length),normalize:t=>{let{value:n}=t;return function(e,t,n=[]){const{lock:r=!1}=t,o=f(t,Nt),i=Array.isArray(e)?"array":"object",a="object"===i?Object.keys(e):n,l=Vt(e,"object",a),s=Ht(o)?a.reduce(((e,t)=>Object.assign(e,{[t]:o})),{}):o;return{value:"array"===i?e:l,settings:T(T({},zt(l,s)),{},{format:i,keys:a,lock:r,locked:!1})}}(n,f(t,Mt),e)},format:(e,t)=>((e,t)=>Vt(e,"object",t.keys))(e,t),sanitize:(e,t,n)=>((e,t,n)=>{const r=Vt(e,"object",t.keys);for(let a in r)r[a]=ie(r[a],t[a]);const o=Object.keys(r);let i={};if(o.length===t.keys.length)i=r;else{const e=Vt(n,"object",t.keys);if(1===o.length&&t.locked){const t=o[0],n=r[t],a=e[t],l=0!==a?n/a:1;for(let r in e)r===t?i[t]=n:i[r]=e[r]*l}else i=T(T({},e),r)}return Vt(i,t.format,t.keys)})(e,t,n)}}var Wt=n(16765),Gt=n(83933),Kt=n(55651),Yt=n(2652),Zt=n.n(Yt),Jt=n(24699),Xt=n(69135);function qt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Qt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?qt(Object(n),!0).forEach((function(t){en(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):qt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function en(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function tn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,i=[],a=!0,l=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(s){l=!0,o=s}finally{try{a||null==n.return||n.return()}finally{if(l)throw o}}return i}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return nn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return nn(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var rn="file-invalid-type",on="file-too-large",an="file-too-small",ln="too-many-files",sn=function(e){e=Array.isArray(e)&&1===e.length?e[0]:e;var t=Array.isArray(e)?"one of ".concat(e.join(", ")):e;return{code:rn,message:"File type must be ".concat(t)}},cn=function(e){return{code:on,message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},un=function(e){return{code:an,message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},dn={code:ln,message:"Too many files"};function fn(e,t){var n="application/x-moz-file"===e.type||(0,Xt.Z)(e,t);return[n,n?null:sn(t)]}function pn(e,t,n){if(gn(e.size))if(gn(t)&&gn(n)){if(e.size>n)return[!1,cn(n)];if(e.size<t)return[!1,un(t)]}else{if(gn(t)&&e.size<t)return[!1,un(t)];if(gn(n)&&e.size>n)return[!1,cn(n)]}return[!0,null]}function gn(e){return void 0!==e&&null!==e}function hn(e){return"function"===typeof e.isPropagationStopped?e.isPropagationStopped():"undefined"!==typeof e.cancelBubble&&e.cancelBubble}function mn(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,(function(e){return"Files"===e||"application/x-moz-file"===e})):!!e.target&&!!e.target.files}function vn(e){e.preventDefault()}function bn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return t.some((function(t){return!hn(e)&&t&&t.apply(void 0,[e].concat(r)),hn(e)}))}}function yn(e){return e="string"===typeof e?e.split(","):e,[{description:"everything",accept:Array.isArray(e)?e.filter((function(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||/\w+\/[-+.\w]+/g.test(e)})).reduce((function(e,t){return Qt(Qt({},e),{},en({},t,[]))}),{}):{}}]}var En=["children"],wn=["open"],$n=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],On=["refKey","onChange","onClick"];function Cn(e){return function(e){if(Array.isArray(e))return Sn(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Rn(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function xn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,i=[],a=!0,l=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(s){l=!0,o=s}finally{try{a||null==n.return||n.return()}finally{if(l)throw o}}return i}(e,t)||Rn(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Rn(e,t){if(e){if("string"===typeof e)return Sn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Sn(e,t):void 0}}function Sn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function kn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Tn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?kn(Object(n),!0).forEach((function(t){Dn(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):kn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Dn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function jn(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var Pn=(0,o.forwardRef)((function(e,t){var n=e.children,r=In(jn(e,En)),i=r.open,a=jn(r,wn);return(0,o.useImperativeHandle)(t,(function(){return{open:i}}),[i]),o.createElement(o.Fragment,null,n(Tn(Tn({},a),{},{open:i})))}));Pn.displayName="Dropzone";var _n={disabled:!1,getFilesFromEvent:Jt.R,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!0};Pn.defaultProps=_n,Pn.propTypes={children:Zt().func,accept:Zt().oneOfType([Zt().string,Zt().arrayOf(Zt().string)]),multiple:Zt().bool,preventDropOnDocument:Zt().bool,noClick:Zt().bool,noKeyboard:Zt().bool,noDrag:Zt().bool,noDragEventsBubbling:Zt().bool,minSize:Zt().number,maxSize:Zt().number,maxFiles:Zt().number,disabled:Zt().bool,getFilesFromEvent:Zt().func,onFileDialogCancel:Zt().func,onFileDialogOpen:Zt().func,useFsAccessApi:Zt().bool,onDragEnter:Zt().func,onDragLeave:Zt().func,onDragOver:Zt().func,onDrop:Zt().func,onDropAccepted:Zt().func,onDropRejected:Zt().func,validator:Zt().func};var An={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,draggedFiles:[],acceptedFiles:[],fileRejections:[]};function In(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=Tn(Tn({},_n),e),n=t.accept,r=t.disabled,i=t.getFilesFromEvent,a=t.maxSize,l=t.minSize,s=t.multiple,c=t.maxFiles,u=t.onDragEnter,d=t.onDragLeave,f=t.onDragOver,p=t.onDrop,g=t.onDropAccepted,h=t.onDropRejected,m=t.onFileDialogCancel,v=t.onFileDialogOpen,b=t.useFsAccessApi,y=t.preventDropOnDocument,E=t.noClick,w=t.noKeyboard,$=t.noDrag,O=t.noDragEventsBubbling,C=t.validator,x=(0,o.useMemo)((function(){return"function"===typeof v?v:Ln}),[v]),R=(0,o.useMemo)((function(){return"function"===typeof m?m:Ln}),[m]),S=(0,o.useRef)(null),k=(0,o.useRef)(null),T=xn((0,o.useReducer)(Fn,An),2),D=T[0],j=T[1],P=D.isFocused,_=D.isFileDialogActive,A=D.draggedFiles,I=(0,o.useRef)("undefined"!==typeof window&&window.isSecureContext&&b&&"showOpenFilePicker"in window),F=function(){!I.current&&_&&setTimeout((function(){k.current&&(k.current.files.length||(j({type:"closeDialog"}),R()))}),300)};(0,o.useEffect)((function(){return window.addEventListener("focus",F,!1),function(){window.removeEventListener("focus",F,!1)}}),[k,_,R,I]);var L=(0,o.useRef)([]),z=function(e){S.current&&S.current.contains(e.target)||(e.preventDefault(),L.current=[])};(0,o.useEffect)((function(){return y&&(document.addEventListener("dragover",vn,!1),document.addEventListener("drop",z,!1)),function(){y&&(document.removeEventListener("dragover",vn),document.removeEventListener("drop",z))}}),[S,y]);var N=(0,o.useCallback)((function(e){e.preventDefault(),e.persist(),q(e),L.current=[].concat(Cn(L.current),[e.target]),mn(e)&&Promise.resolve(i(e)).then((function(t){hn(e)&&!O||(j({draggedFiles:t,isDragActive:!0,type:"setDraggedFiles"}),u&&u(e))}))}),[i,u,O]),M=(0,o.useCallback)((function(e){e.preventDefault(),e.persist(),q(e);var t=mn(e);if(t&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(n){}return t&&f&&f(e),!1}),[f,O]),U=(0,o.useCallback)((function(e){e.preventDefault(),e.persist(),q(e);var t=L.current.filter((function(e){return S.current&&S.current.contains(e)})),n=t.indexOf(e.target);-1!==n&&t.splice(n,1),L.current=t,t.length>0||(j({isDragActive:!1,type:"setDraggedFiles",draggedFiles:[]}),mn(e)&&d&&d(e))}),[S,d,O]),V=(0,o.useCallback)((function(e,t){var r=[],o=[];e.forEach((function(e){var t=xn(fn(e,n),2),i=t[0],s=t[1],c=xn(pn(e,l,a),2),u=c[0],d=c[1],f=C?C(e):null;if(i&&u&&!f)r.push(e);else{var p=[s,d];f&&(p=p.concat(f)),o.push({file:e,errors:p.filter((function(e){return e}))})}})),(!s&&r.length>1||s&&c>=1&&r.length>c)&&(r.forEach((function(e){o.push({file:e,errors:[dn]})})),r.splice(0)),j({acceptedFiles:r,fileRejections:o,type:"setFiles"}),p&&p(r,o,t),o.length>0&&h&&h(o,t),r.length>0&&g&&g(r,t)}),[j,s,n,l,a,c,p,g,h,C]),H=(0,o.useCallback)((function(e){e.preventDefault(),e.persist(),q(e),L.current=[],mn(e)&&Promise.resolve(i(e)).then((function(t){hn(e)&&!O||V(t,e)})),j({type:"reset"})}),[i,V,O]),B=(0,o.useCallback)((function(){if(I.current){j({type:"openDialog"}),x();var e={multiple:s,types:yn(n)};window.showOpenFilePicker(e).then((function(e){return i(e)})).then((function(e){V(e,null),j({type:"closeDialog"})})).catch((function(e){var t;(t=e)instanceof DOMException&&("AbortError"===t.name||t.code===t.ABORT_ERR)?(R(e),j({type:"closeDialog"})):function(e){return e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)}(e)&&(I.current=!1,k.current&&(k.current.value=null,k.current.click()))}))}else k.current&&(j({type:"openDialog"}),x(),k.current.value=null,k.current.click())}),[j,x,R,b,V,n,s]),W=(0,o.useCallback)((function(e){S.current&&S.current.isEqualNode(e.target)&&(" "!==e.key&&"Enter"!==e.key&&32!==e.keyCode&&13!==e.keyCode||(e.preventDefault(),B()))}),[S,B]),G=(0,o.useCallback)((function(){j({type:"focus"})}),[]),K=(0,o.useCallback)((function(){j({type:"blur"})}),[]),Y=(0,o.useCallback)((function(){E||(!function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return function(e){return-1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")}(e)||function(e){return-1!==e.indexOf("Edge/")}(e)}()?B():setTimeout(B,0))}),[E,B]),Z=function(e){return r?null:e},J=function(e){return w?null:Z(e)},X=function(e){return $?null:Z(e)},q=function(e){O&&e.stopPropagation()},Q=(0,o.useMemo)((function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,n=void 0===t?"ref":t,o=e.role,i=e.onKeyDown,a=e.onFocus,l=e.onBlur,s=e.onClick,c=e.onDragEnter,u=e.onDragOver,d=e.onDragLeave,f=e.onDrop,p=jn(e,$n);return Tn(Tn(Dn({onKeyDown:J(bn(i,W)),onFocus:J(bn(a,G)),onBlur:J(bn(l,K)),onClick:Z(bn(s,Y)),onDragEnter:X(bn(c,N)),onDragOver:X(bn(u,M)),onDragLeave:X(bn(d,U)),onDrop:X(bn(f,H)),role:"string"===typeof o&&""!==o?o:"button"},n,S),r||w?{}:{tabIndex:0}),p)}}),[S,W,G,K,Y,N,M,U,H,w,$,r]),ee=(0,o.useCallback)((function(e){e.stopPropagation()}),[]),te=(0,o.useMemo)((function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,r=void 0===t?"ref":t,o=e.onChange,i=e.onClick,a=jn(e,On);return Tn(Tn({},Dn({accept:n,multiple:s,type:"file",style:{display:"none"},onChange:Z(bn(o,H)),onClick:Z(bn(i,ee)),tabIndex:-1},r,k)),a)}}),[k,n,s,H,r]),ne=A.length,re=ne>0&&function(e){var t=e.files,n=e.accept,r=e.minSize,o=e.maxSize,i=e.multiple,a=e.maxFiles;return!(!i&&t.length>1||i&&a>=1&&t.length>a)&&t.every((function(e){var t=tn(fn(e,n),1)[0],i=tn(pn(e,r,o),1)[0];return t&&i}))}({files:A,accept:n,minSize:l,maxSize:a,multiple:s,maxFiles:c}),oe=ne>0&&!re;return Tn(Tn({},D),{},{isDragAccept:re,isDragReject:oe,isFocused:P&&!r,getRootProps:Q,getInputProps:te,rootRef:S,inputRef:k,open:Z(B)})}function Fn(e,t){switch(t.type){case"focus":return Tn(Tn({},e),{},{isFocused:!0});case"blur":return Tn(Tn({},e),{},{isFocused:!1});case"openDialog":return Tn(Tn({},An),{},{isFileDialogActive:!0});case"closeDialog":return Tn(Tn({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":var n=t.isDragActive,r=t.draggedFiles;return Tn(Tn({},e),{},{draggedFiles:r,isDragActive:n});case"setFiles":return Tn(Tn({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections});case"reset":return Tn({},An);default:return e}}function Ln(){}function zn(e){let t;const n=new Set,r=(e,r)=>{const o="function"===typeof e?e(t):e;if(o!==t){const e=t;t=r?o:Object.assign({},t,o),n.forEach((n=>n(t,e)))}},o=()=>t,i={setState:r,getState:o,subscribe:(e,r,i)=>r||i?((e,r=o,i=Object.is)=>{console.warn("[DEPRECATED] Please use `subscribeWithSelector` middleware");let a=r(t);function l(){const n=r(t);if(!i(a,n)){const t=a;e(a=n,t)}}return n.add(l),()=>n.delete(l)})(e,r,i):(n.add(e),()=>n.delete(e)),destroy:()=>n.clear()};return t=e(r,o,i),i}const Nn="undefined"===typeof window||!window.navigator||/ServerSideRendering|^Deno\//.test(window.navigator.userAgent)?o.useEffect:o.useLayoutEffect;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;var Mn=n(32597),Un=n.n(Mn);const Vn=(...e)=>e.filter(Boolean).join(".");function Hn(e,t){return(0,o.useMemo)(e,function(e,t){const n=(0,o.useRef)();return(t?a.J:l)(e,n.current)||(n.current=e),n.current}(t,!0))}function Bn(e,t,n){const r=e.useStore((e=>function(e,t){return Object.entries((n=e,r=t,r.reduce(((e,t)=>(n&&n.hasOwnProperty(t)&&(e[t]=n[t]),e)),{}))).reduce(((e,[,{value:t,disabled:n,key:r}])=>(e[r]=n?void 0:t,e)),{});var n,r}(T(T({},n),e.data),t)),l);return r}function Wn(e=3){const t=(0,o.useRef)(null),n=(0,o.useRef)(null),[r,i]=(0,o.useState)(!1),a=(0,o.useCallback)((()=>i(!0)),[]),l=(0,o.useCallback)((()=>i(!1)),[]);return(0,o.useLayoutEffect)((()=>{if(r){const{bottom:r,top:o,left:i}=t.current.getBoundingClientRect(),{height:a}=n.current.getBoundingClientRect(),l=r+a>window.innerHeight-40?"up":"down";n.current.style.position="fixed",n.current.style.zIndex="10000",n.current.style.left=i+"px","down"===l?n.current.style.top=r+e+"px":n.current.style.bottom=window.innerHeight-o+e+"px"}}),[e,r]),{popinRef:t,wrapperRef:n,shown:r,show:a,hide:l}}(0,Wt.l7)([Gt.Z]);const Gn={rgb:"toRgb",hsl:"toHsl",hsv:"toHsv",hex:"toHex"};s.Z.extend({color:()=>e=>(0,Wt.Vi)(e).isValid()});function Kn(e,{format:t,hasAlpha:n,isString:r}){const o=e[Gn[t]+(r&&"hex"!==t?"String":"")]();return"object"!==typeof o||n?o:function(e,t){const n=T({},e);return t.forEach((t=>t in e&&delete n[t])),n}(o,["a"])}const Yn=(e,t)=>{const n=(0,Wt.Vi)(e);if(!n.isValid())throw Error("Invalid color");return Kn(n,t)};var Zn=Object.freeze({__proto__:null,schema:e=>(0,s.Z)().color().test(e),sanitize:Yn,format:(e,t)=>Kn((0,Wt.Vi)(e),T(T({},t),{},{isString:!0,format:"hex"})),normalize:({value:e})=>{const t=(0,Wt.y6)(e),n={format:"name"===t?"hex":t,hasAlpha:"object"===typeof e?"a"in e:"hex"===t&&8===e.length||/^(rgba)|(hsla)|(hsva)/.test(e),isString:"string"===typeof e};return{value:Yn(e,n),settings:n}}});const Jn=ye("div",{position:"relative",boxSizing:"border-box",borderRadius:"$sm",overflow:"hidden",cursor:"pointer",height:"$rowHeight",width:"$rowHeight",backgroundColor:"#fff",backgroundImage:'url(\'data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>\')',$inputStyle:"",$hover:"",zIndex:1,variants:{active:{true:{$inputStyle:"$accent1"}}},"&::before":{content:'""',position:"absolute",top:0,bottom:0,right:0,left:0,backgroundColor:"currentColor",zIndex:1}}),Xn=ye("div",{position:"relative",display:"grid",gridTemplateColumns:"$sizes$rowHeight auto",columnGap:"$colGap",alignItems:"center"}),qn=ye("div",{width:"$colorPickerWidth",height:"$colorPickerHeight",".react-colorful":{width:"100%",height:"100%",boxShadow:"$level2",cursor:"crosshair"},".react-colorful__saturation":{borderRadius:"$sm $sm 0 0"},".react-colorful__alpha, .react-colorful__hue":{height:10},".react-colorful__last-control":{borderRadius:"0 0 $sm $sm"},".react-colorful__pointer":{height:12,width:12}});function Qn(e,t){return"rgb"!==t?(0,Wt.Vi)(e).toRgb():e}function er({value:e,displayValue:t,settings:n,onUpdate:r}){const{emitOnEditStart:i,emitOnEditEnd:a}=de(),{format:l,hasAlpha:s}=n,{popinRef:c,wrapperRef:u,shown:d,show:f,hide:p}=Wn(),g=(0,o.useRef)(0),[h,m]=(0,o.useState)((()=>Qn(e,l))),v=s?Kt.Jg:Kt.Ts,b=()=>{p(),a(),window.clearTimeout(g.current)};return(0,o.useEffect)((()=>()=>window.clearTimeout(g.current)),[]),o.createElement(o.Fragment,null,o.createElement(Jn,{ref:c,active:d,onClick:()=>(m(Qn(e,l)),f(),void i()),style:{color:t}}),d&&o.createElement(Ke,null,o.createElement(Be,{onPointerUp:b}),o.createElement(qn,{ref:u,onMouseEnter:()=>window.clearTimeout(g.current),onMouseLeave:e=>0===e.buttons&&void(g.current=window.setTimeout(b,500))},o.createElement(v,{color:h,onChange:r}))))}var tr=T({component:function(){const{value:e,displayValue:t,label:n,onChange:r,onUpdate:i,settings:a}=de();return o.createElement(nt,{input:!0},o.createElement(Xe,null,n),o.createElement(Xn,null,o.createElement(er,{value:e,displayValue:t,onChange:r,onUpdate:i,settings:a}),o.createElement(Pe,{value:t,onChange:r,onUpdate:i})))}},Zn);var nr=T({component:function(){const{label:e,displayValue:t,onUpdate:n,settings:r}=de();return o.createElement(nt,{input:!0},o.createElement(Xe,null,e),o.createElement(Lt,{value:t,settings:r,onUpdate:n}))}},Bt(["x","y","z"]));const rr=ye("div",{$flexCenter:"",position:"relative",backgroundColor:"$elevation3",borderRadius:"$sm",cursor:"pointer",height:"$rowHeight",width:"$rowHeight",touchAction:"none",$draggable:"",$hover:"","&:active":{cursor:"none"},"&::after":{content:'""',backgroundColor:"$accent2",height:4,width:4,borderRadius:2}}),or=ye("div",{$flexCenter:"",width:"$joystickWidth",height:"$joystickHeight",borderRadius:"$sm",boxShadow:"$level2",position:"fixed",zIndex:1e4,overflow:"hidden",$draggable:"",transform:"translate(-50%, -50%)",variants:{isOutOfBounds:{true:{backgroundColor:"$elevation1"},false:{backgroundColor:"$elevation3"}}},"> div":{position:"absolute",$flexCenter:"",borderStyle:"solid",borderWidth:1,borderColor:"$highlight1",backgroundColor:"$elevation3",width:"80%",height:"80%","&::after,&::before":{content:'""',position:"absolute",zindex:10,backgroundColor:"$highlight1"},"&::before":{width:"100%",height:1},"&::after":{height:"100%",width:1}},"> span":{position:"relative",zindex:100,width:10,height:10,backgroundColor:"$accent2",borderRadius:"50%"}});function ir({value:e,settings:t,onUpdate:n}){const r=(0,o.useRef)(),i=(0,o.useRef)(0),a=(0,o.useRef)(0),l=(0,o.useRef)(1),[s,c]=(0,o.useState)(!1),[u,d]=(0,o.useState)(!1),[f,p]=it(),g=(0,o.useRef)(null),h=(0,o.useRef)(null);(0,o.useLayoutEffect)((()=>{if(s){const{top:e,left:t,width:n,height:r}=g.current.getBoundingClientRect();h.current.style.left=t+n/2+"px",h.current.style.top=e+r/2+"px"}}),[s]);const{keys:[m,v],joystick:b}=t,y="invertY"===b?1:-1,{[m]:{step:E},[v]:{step:w}}=t,$=xe("sizes","joystickWidth"),O=xe("sizes","joystickHeight"),C=.8*parseFloat($)/2,x=.8*parseFloat(O)/2,R=(0,o.useCallback)((()=>{r.current||(d(!0),i.current&&p({x:i.current*C}),a.current&&p({y:a.current*-x}),r.current=window.setInterval((()=>{n((e=>{const t=E*i.current*l.current,n=y*w*a.current*l.current;return Array.isArray(e)?{[m]:e[0]+t,[v]:e[1]+n}:{[m]:e[m]+t,[v]:e[v]+n}}))}),16))}),[C,x,n,p,E,w,m,v,y]),S=(0,o.useCallback)((()=>{window.clearTimeout(r.current),r.current=void 0,d(!1)}),[]);(0,o.useEffect)((()=>{function e(e){l.current=ne(e)}return window.addEventListener("keydown",e),window.addEventListener("keyup",e),()=>{window.clearTimeout(r.current),window.removeEventListener("keydown",e),window.removeEventListener("keyup",e)}}),[]);const k=ot((({first:t,active:r,delta:[o,s],movement:[u,d]})=>{t&&c(!0);const f=D(u,-C,C),g=D(d,-x,x);i.current=Math.abs(u)>Math.abs(f)?Math.sign(u-f):0,a.current=Math.abs(d)>Math.abs(g)?Math.sign(g-d):0;let h=e[m],b=e[v];r?(i.current||(h+=o*E*l.current,p({x:f})),a.current||(b-=y*s*w*l.current,p({y:g})),i.current||a.current?R():S(),n({[m]:h,[v]:b})):(c(!1),i.current=0,a.current=0,p({x:0,y:0}),S())}));return o.createElement(rr,ce({ref:g},k()),s&&o.createElement(Ke,null,o.createElement(or,{ref:h,isOutOfBounds:u},o.createElement("div",null),o.createElement("span",{ref:f}))))}const ar=ye("div",{display:"grid",columnGap:"$colGap",variants:{withJoystick:{true:{gridTemplateColumns:"$sizes$rowHeight auto"},false:{gridTemplateColumns:"auto"}}}});const lr=["joystick"],sr=Bt(["x","y"]);var cr=T(T({component:function(){const{label:e,displayValue:t,onUpdate:n,settings:r}=de();return o.createElement(nt,{input:!0},o.createElement(Xe,null,e),o.createElement(ar,{withJoystick:!!r.joystick},r.joystick&&o.createElement(ir,{value:t,settings:r,onUpdate:n}),o.createElement(Lt,{value:t,settings:r,onUpdate:n})))}},sr),{},{normalize:e=>{let{joystick:t=!0}=e,n=f(e,lr);const{value:r,settings:o}=sr.normalize(n);return{value:r,settings:T(T({},o),{},{joystick:t})}}});var ur=Object.freeze({__proto__:null,sanitize:e=>{if(void 0!==e){if(e instanceof File)try{return URL.createObjectURL(e)}catch(t){return}if("string"===typeof e&&0===e.indexOf("blob:"))return e;throw Error("Invalid image format [undefined | blob |\xa0File].")}},schema:(e,t)=>"object"===typeof t&&"image"in t,normalize:({image:e})=>({value:e})});const dr=ye("div",{position:"relative",display:"grid",gridTemplateColumns:"$sizes$rowHeight auto 20px",columnGap:"$colGap",alignItems:"center"}),fr=ye("div",{$flexCenter:"",overflow:"hidden",height:"$rowHeight",background:"$elevation3",textAlign:"center",color:"inherit",borderRadius:"$sm",outline:"none",userSelect:"none",cursor:"pointer",$inputStyle:"",$hover:"",$focusWithin:"",$active:"$accent1 $elevation1",variants:{isDragAccept:{true:{$inputStyle:"$accent1",backgroundColor:"$elevation1"}}}}),pr=ye("div",{boxSizing:"border-box",borderRadius:"$sm",height:"$rowHeight",width:"$rowHeight",$inputStyle:"",backgroundSize:"cover",backgroundPosition:"center",variants:{hasImage:{true:{cursor:"pointer",$hover:"",$active:""}}}}),gr=ye("div",{$flexCenter:"",width:"$imagePreviewWidth",height:"$imagePreviewHeight",borderRadius:"$sm",boxShadow:"$level2",pointerEvents:"none",$inputStyle:"",backgroundSize:"cover",backgroundPosition:"center"}),hr=ye("div",{fontSize:"0.8em",height:"100%",padding:"$rowGap $md"}),mr=ye("div",{$flexCenter:"",top:"0",right:"0",marginRight:"$sm",height:"100%",cursor:"pointer",variants:{disabled:{true:{color:"$elevation3",cursor:"default"}}},"&::after,&::before":{content:'""',position:"absolute",height:2,width:10,borderRadius:1,backgroundColor:"currentColor"},"&::after":{transform:"rotate(45deg)"},"&::before":{transform:"rotate(-45deg)"}});var vr=T({component:function(){const{label:e,value:t,onUpdate:n,disabled:r}=de(),{popinRef:i,wrapperRef:a,shown:l,show:s,hide:c}=Wn(),u=(0,o.useCallback)((e=>{e.length&&n(e[0])}),[n]),d=(0,o.useCallback)((e=>{e.stopPropagation(),n(void 0)}),[n]),{getRootProps:f,getInputProps:p,isDragAccept:g}=In({maxFiles:1,accept:"image/*",onDrop:u,disabled:r});return o.createElement(nt,{input:!0},o.createElement(Xe,null,e),o.createElement(dr,null,o.createElement(pr,{ref:i,hasImage:!!t,onPointerDown:()=>!!t&&s(),onPointerUp:c,style:{backgroundImage:t?`url(${t})`:"none"}}),l&&!!t&&o.createElement(Ke,null,o.createElement(Be,{onPointerUp:c,style:{cursor:"pointer"}}),o.createElement(gr,{ref:a,style:{backgroundImage:`url(${t})`}})),o.createElement(fr,f({isDragAccept:g}),o.createElement("input",p()),o.createElement(hr,null,g?"drop image":"click or drop")),o.createElement(mr,{onClick:d,disabled:!t})))}},ur);const br=(0,s.Z)().number(),yr=e=>({min:e[0],max:e[1]}),Er=(e,{bounds:[t,n]},r)=>{const o=Array.isArray(e)?yr(e):e,i={min:r[0],max:r[1]},{min:a,max:l}=T(T({},i),o);return[D(Number(a),t,Math.max(t,l)),D(Number(l),Math.min(n,a),n)]};var wr=Object.freeze({__proto__:null,schema:(e,t)=>(0,s.Z)().array().length(2).every.number().test(e)&&(0,s.Z)().schema({min:br,max:br}).test(t),format:yr,sanitize:Er,normalize:({value:e,min:t,max:n})=>{const r={min:t,max:n},o=[t,n],i=T(T({},zt(yr(e),{min:r,max:r})),{},{bounds:o});return{value:Er(yr(e),i,e),settings:i}}});const $r=["value","bounds","onDrag"],Or=["bounds"],Cr=ye("div",{display:"grid",columnGap:"$colGap",gridTemplateColumns:"auto calc($sizes$numberInputMinWidth * 2 + $space$rowGap)"});function xr(e){let{value:t,bounds:[n,r],onDrag:i}=e,a=f(e,$r);const l=(0,o.useRef)(null),s=(0,o.useRef)(null),c=(0,o.useRef)(null),u=(0,o.useRef)(0),d=xe("sizes","scrubberWidth"),p=ot((({event:e,first:o,xy:[f],movement:[p],memo:g={}})=>{if(o){const{width:o,left:i}=l.current.getBoundingClientRect();u.current=o-parseFloat(d);const a=(null===e||void 0===e?void 0:e.target)===s.current||(null===e||void 0===e?void 0:e.target)===c.current;g.pos=I((f-i)/o,n,r);const p=Math.abs(g.pos-t.min)-Math.abs(g.pos-t.max);g.key=p<0||0===p&&g.pos<=t.min?"min":"max",a&&(g.pos=t[g.key])}const h=g.pos+I(p/u.current,0,r-n);return i({[g.key]:mt(h,a[g.key])}),g})),g=`calc(${A(t.min,n,r)} * (100% - ${d} - 8px) + 4px)`,h=`calc(${1-A(t.max,n,r)} * (100% - ${d} - 8px) + 4px)`;return o.createElement(dt,ce({ref:l},p()),o.createElement(ct,null,o.createElement(ft,{style:{left:g,right:h}})),o.createElement(ut,{position:"left",ref:s,style:{left:g}}),o.createElement(ut,{position:"right",ref:c,style:{right:h}}))}var Rr=T({component:function(){const{label:e,displayValue:t,onUpdate:n,settings:r}=de(),i=f(r,Or);return o.createElement(o.Fragment,null,o.createElement(nt,{input:!0},o.createElement(Xe,null,e),o.createElement(Cr,null,o.createElement(xr,ce({value:t},r,{onDrag:n})),o.createElement(Lt,{value:t,settings:i,onUpdate:n,innerLabelTrim:0}))))}},wr);const Sr=["type","value"],kr=["onChange","transient","onEditStart","onEditEnd"],Tr=function(){const e=function(e){const t="function"===typeof e?zn(e):e,n=(e=t.getState,n=Object.is)=>{const[,r]=(0,o.useReducer)((e=>e+1),0),i=t.getState(),a=(0,o.useRef)(i),l=(0,o.useRef)(e),s=(0,o.useRef)(n),c=(0,o.useRef)(!1),u=(0,o.useRef)();let d;void 0===u.current&&(u.current=e(i));let f=!1;(a.current!==i||l.current!==e||s.current!==n||c.current)&&(d=e(i),f=!n(u.current,d)),Nn((()=>{f&&(u.current=d),a.current=i,l.current=e,s.current=n,c.current=!1}));const p=(0,o.useRef)(i);Nn((()=>{const e=()=>{try{const e=t.getState(),n=l.current(e);s.current(u.current,n)||(a.current=e,u.current=n,r())}catch(e){c.current=!0,r()}},n=t.subscribe(e);return t.getState()!==p.current&&e(),n}),[]);const g=f?d:u.current;return(0,o.useDebugValue)(g),g};return Object.assign(n,t),n[Symbol.iterator]=function(){console.warn("[useStore, api] = create() is deprecated and will be removed in v4");const e=[n,t];return{next(){const t=e.length<=0;return{value:e.shift(),done:t}}}},n}((t=()=>({data:{}}),(e,n,r)=>{const o=r.subscribe;return r.subscribe=(e,t,n)=>{let i=e;if(t){const o=(null==n?void 0:n.equalityFn)||Object.is;let a=e(r.getState());i=n=>{const r=e(n);if(!o(a,r)){const e=a;t(a=r,e)}},(null==n?void 0:n.fireImmediately)&&t(a,a)}return o(i)},t(e,n,r)}));var t;const n=(()=>{const e=new Map;return{on:(t,n)=>{let r=e.get(t);void 0===r&&(r=new Set,e.set(t,r)),r.add(n)},off:(t,n)=>{const r=e.get(t);void 0!==r&&(r.delete(n),0===r.size&&e.delete(t))},emit:(t,...n)=>{const r=e.get(t);if(void 0!==r)for(const e of r)e(...n)}}})();this.storeId="_"+Math.random().toString(36).substr(2,9),this.useStore=e;const r={},i=new Set;this.getVisiblePaths=()=>{const e=this.getData(),t=Object.keys(e),n=[];Object.entries(r).forEach((([e,r])=>{r.render&&t.some((t=>0===t.indexOf(e)))&&!r.render(this.get)&&n.push(e+".")}));const o=[];return i.forEach((t=>{t in e&&e[t].__refCount>0&&n.every((e=>-1===t.indexOf(e)))&&(!e[t].render||e[t].render(this.get))&&o.push(t)})),o},this.setOrderedPaths=e=>{e.forEach((e=>i.add(e)))},this.orderPaths=e=>(this.setOrderedPaths(e),e),this.disposePaths=t=>{e.setState((e=>{const n=e.data;return t.forEach((e=>{if(e in n){const t=n[e];t.__refCount--,0===t.__refCount&&t.type in W&&delete n[e]}})),{data:n}}))},this.dispose=()=>{e.setState((()=>({data:{}})))},this.getFolderSettings=e=>r[e]||{},this.getData=()=>e.getState().data,this.addData=(t,n)=>{e.setState((e=>{const r=e.data;return Object.entries(t).forEach((([e,t])=>{let o=r[e];if(o){const{type:e,value:r}=t,i=f(t,Sr);e!==o.type?m(p.INPUT_TYPE_OVERRIDE,e):((0===o.__refCount||n)&&Object.assign(o,i),o.__refCount++)}else r[e]=T(T({},t),{},{__refCount:1})})),{data:r}}))},this.setValueAtPath=(t,n,r)=>{e.setState((e=>{const o=e.data;return q(o[t],n,t,this,r),{data:o}}))},this.setSettingsAtPath=(t,n)=>{e.setState((e=>{const r=e.data;return r[t].settings=T(T({},r[t].settings),n),{data:r}}))},this.disableInputAtPath=(t,n)=>{e.setState((e=>{const r=e.data;return r[t].disabled=n,{data:r}}))},this.set=(t,n)=>{e.setState((e=>{const r=e.data;return Object.entries(t).forEach((([e,t])=>{try{q(r[e],t,void 0,void 0,n)}catch(o){0}})),{data:r}}))},this.getInput=e=>{try{return this.getData()[e]}catch(t){m(p.PATH_DOESNT_EXIST,e)}},this.get=e=>{var t;return null===(t=this.getInput(e))||void 0===t?void 0:t.value},this.emitOnEditStart=e=>{n.emit(`onEditStart:${e}`,this.get(e),e,T(T({},this.getInput(e)),{},{get:this.get}))},this.emitOnEditEnd=e=>{n.emit(`onEditEnd:${e}`,this.get(e),e,T(T({},this.getInput(e)),{},{get:this.get}))},this.subscribeToEditStart=(e,t)=>{const r=`onEditStart:${e}`;return n.on(r,t),()=>n.off(r,t)},this.subscribeToEditEnd=(e,t)=>{const r=`onEditEnd:${e}`;return n.on(r,t),()=>n.off(r,t)};const a=(e,t,n)=>{const o={};return Object.entries(e).forEach((([e,i])=>{if(""===e)return m(p.EMPTY_KEY);let l=Vn(t,e);if(i.type===W.FOLDER){const e=a(i.schema,l,n);Object.assign(o,e),l in r||(r[l]=i.settings)}else if(e in n)m(p.DUPLICATE_KEYS,e,l,n[e].path);else{const t=X(i,e,l,o);if(t){const{type:r,options:i,input:a}=t,{onChange:s,transient:c,onEditStart:u,onEditEnd:d}=i,p=f(i,kr);o[l]=T(T(T({type:r},p),a),{},{fromPanel:!0}),n[e]={path:l,onChange:s,transient:c,onEditStart:u,onEditEnd:d}}else m(p.UNKNOWN_INPUT,l,i)}})),o};this.getDataFromSchema=e=>{const t={};return[a(e,"",t),t]}},Dr=new Tr;const jr={collapsed:!1};function Pr(e,t){return{type:W.FOLDER,schema:e,settings:T(T({},jr),t)}}const _r={disabled:!1};function Ar(e,t){return{type:W.BUTTON,onClick:e,settings:T(T({},_r),t)}}const Ir=e=>"__levaInput"in e,Fr=["type","label","path","valueKey","value","settings","setValue","disabled"];function Lr(e){let{type:t,label:n,path:r,valueKey:i,value:a,settings:l,setValue:s,disabled:c}=e,u=f(e,Fr);const{displayValue:d,onChange:g,onUpdate:h}=rt({type:t,value:a,settings:l,setValue:s}),v=$[t].component;return v?o.createElement(ue.Provider,{value:T({key:i,path:r,id:""+r,label:n,displayValue:d,value:a,onChange:g,onUpdate:h,settings:l,setValue:s,disabled:c},u)},o.createElement(He,{disabled:c},o.createElement(v,null))):(m(p.NO_COMPONENT_FOR_TYPE,t,r),null)}const zr=ye("button",{display:"block",$reset:"",fontWeight:"$button",height:"$rowHeight",borderStyle:"none",borderRadius:"$sm",backgroundColor:"$elevation1",color:"$highlight1","&:not(:disabled)":{color:"$highlight3",backgroundColor:"$accent2",cursor:"pointer",$hover:"$accent3",$active:"$accent3 $accent1",$focus:""}});const Nr=ye("div",{$flex:"",justifyContent:"flex-end",gap:"$colGap"}),Mr=ye("button",{$reset:"",cursor:"pointer",borderRadius:"$xs","&:hover":{backgroundColor:"$elevation3"}});const Ur=ye("canvas",{height:"$monitorHeight",width:"100%",display:"block",borderRadius:"$sm"});const Vr=(0,o.forwardRef)((function({initialValue:e},t){const n=xe("colors","highlight3"),r=xe("colors","elevation2"),i=xe("colors","highlight1"),[a,l]=(0,o.useMemo)((()=>[(0,Wt.Vi)(i).alpha(.4).toRgbString(),(0,Wt.Vi)(i).alpha(.1).toRgbString()]),[i]),s=(0,o.useRef)([e]),c=(0,o.useRef)(e),u=(0,o.useRef)(e),d=(0,o.useRef)(),f=(0,o.useCallback)(((e,t)=>{if(!e)return;const{width:o,height:i}=e,d=new Path2D,f=o/100,p=.05*i;for(let n=0;n<s.current.length;n++){const e=f*n,t=i-A(s.current[n],c.current,u.current)*(i-2*p)-p;d.lineTo(e,t)}t.clearRect(0,0,o,i);const g=new Path2D(d);g.lineTo(f*(s.current.length+1),i),g.lineTo(0,i),g.lineTo(0,0);const h=t.createLinearGradient(0,0,0,i);h.addColorStop(0,a),h.addColorStop(1,l),t.fillStyle=h,t.fill(g),t.strokeStyle=r,t.lineJoin="round",t.lineWidth=14,t.stroke(d),t.strokeStyle=n,t.lineWidth=2,t.stroke(d)}),[n,r,a,l]),[p,g]=function(e){const t=(0,o.useRef)(null),n=(0,o.useRef)(null),r=(0,o.useRef)(!1);return(0,o.useEffect)((()=>{const o=te((()=>{t.current.width=t.current.offsetWidth*window.devicePixelRatio,t.current.height=t.current.offsetHeight*window.devicePixelRatio,e(t.current,n.current)}),250);return window.addEventListener("resize",o),r.current||(o(),r.current=!0),()=>window.removeEventListener("resize",o)}),[e]),(0,o.useEffect)((()=>{n.current=t.current.getContext("2d")}),[]),[t,n]}(f);return(0,o.useImperativeHandle)(t,(()=>({frame:e=>{(void 0===c.current||e<c.current)&&(c.current=e),(void 0===u.current||e>u.current)&&(u.current=e),function(e,t){e.push(t),e.length>100&&e.shift()}(s.current,e),d.current=requestAnimationFrame((()=>f(p.current,g.current)))}})),[p,g,f]),(0,o.useEffect)((()=>()=>cancelAnimationFrame(d.current)),[]),o.createElement(Ur,{ref:p})})),Hr=e=>Number.isFinite(e)?e.toPrecision(2):e.toString(),Br=(0,o.forwardRef)((function({initialValue:e},t){const[n,r]=(0,o.useState)(Hr(e));return(0,o.useImperativeHandle)(t,(()=>({frame:e=>r(Hr(e))})),[]),o.createElement("div",null,n)}));function Wr(e){return"function"===typeof e?e():e.current}const Gr=["type","label","key"],Kr={[W.BUTTON]:function({onClick:e,settings:t,label:n}){const r=he();return o.createElement(nt,null,o.createElement(zr,{disabled:t.disabled,onClick:()=>e(r.get)},n))},[W.BUTTON_GROUP]:function(e){const{label:t,opts:n}=(({label:e,opts:t})=>{let n="string"===typeof e&&""===e.trim()?null:e,r=t;return"object"===typeof t.opts&&(void 0!==r.label&&(n=t.label),r=t.opts),{label:n,opts:r}})(e),r=he();return o.createElement(nt,{input:!!t},t&&o.createElement(Xe,null,t),o.createElement(Nr,null,Object.entries(n).map((([e,t])=>o.createElement(Mr,{key:e,onClick:()=>t(r.get)},e)))))},[W.MONITOR]:function({label:e,objectOrFn:t,settings:n}){const r=(0,o.useRef)(),i=(0,o.useRef)(Wr(t));return(0,o.useEffect)((()=>{const e=window.setInterval((()=>{var e;document.hidden||null===(e=r.current)||void 0===e||e.frame(Wr(t))}),n.interval);return()=>window.clearInterval(e)}),[t,n.interval]),o.createElement(nt,{input:!0},o.createElement(Xe,{align:"top"},e),n.graph?o.createElement(Vr,{ref:r,initialValue:i.current}):o.createElement(Br,{ref:r,initialValue:i.current}))}},Yr=o.memo((({path:e})=>{const[t,{set:n,setSettings:r,disable:i,storeId:a,emitOnEditStart:s,emitOnEditEnd:c}]=function(e){const t=he(),[n,r]=(0,o.useState)(lt(t.getData(),e)),i=(0,o.useCallback)((n=>t.setValueAtPath(e,n,!0)),[e,t]),a=(0,o.useCallback)((n=>t.setSettingsAtPath(e,n)),[e,t]),s=(0,o.useCallback)((n=>t.disableInputAtPath(e,n)),[e,t]),c=(0,o.useCallback)((()=>t.emitOnEditStart(e)),[e,t]),u=(0,o.useCallback)((()=>t.emitOnEditEnd(e)),[e,t]);return(0,o.useEffect)((()=>{r(lt(t.getData(),e));const n=t.useStore.subscribe((t=>lt(t.data,e)),r,{equalityFn:l});return()=>n()}),[t,e]),[n,{set:i,setSettings:a,disable:s,storeId:t.storeId,emitOnEditStart:c,emitOnEditEnd:u}]}(e);if(!t)return null;const{type:u,label:d,key:g}=t,h=f(t,Gr);if(u in W){const t=Kr[u];return o.createElement(t,ce({label:d,path:e},h))}return u in $?o.createElement(Lr,ce({key:a+e,type:u,label:d,storeId:a,path:e,valueKey:g,setValue:n,setSettings:r,disable:i,emitOnEditStart:s,emitOnEditEnd:c},h)):(v(p.UNSUPPORTED_INPUT,u,e),null)}));function Zr({toggle:e,toggled:t,name:n}){return o.createElement(Fe,{onClick:()=>e()},o.createElement(et,{toggled:t}),o.createElement("div",null,n))}const Jr=({name:e,path:t,tree:n})=>{const r=he(),i=Vn(t,e),{collapsed:a,color:l}=r.getFolderSettings(i),[s,c]=(0,o.useState)(!a),u=(0,o.useRef)(null),d=xe("colors","folderWidgetColor"),f=xe("colors","folderTextColor");return(0,o.useLayoutEffect)((()=>{u.current.style.setProperty("--leva-colors-folderWidgetColor",l||d),u.current.style.setProperty("--leva-colors-folderTextColor",l||f)}),[l,d,f]),o.createElement(Ae,{ref:u},o.createElement(Zr,{name:e,toggled:s,toggle:()=>c((e=>!e))}),o.createElement(Xr,{parent:i,tree:n,toggled:s}))},Xr=o.memo((({isRoot:e=!1,fill:t=!1,flat:n=!1,parent:r,tree:i,toggled:a})=>{const{wrapperRef:l,contentRef:s}=function(e){const t=(0,o.useRef)(null),n=(0,o.useRef)(null),r=(0,o.useRef)(!0);return(0,o.useLayoutEffect)((()=>{e||(t.current.style.height="0px",t.current.style.overflow="hidden")}),[]),(0,o.useEffect)((()=>{if(r.current)return void(r.current=!1);let o;const i=t.current,a=()=>{e&&(i.style.removeProperty("height"),i.style.removeProperty("overflow"),n.current.scrollIntoView({behavior:"smooth",block:"nearest"}))};i.addEventListener("transitionend",a,{once:!0});const{height:l}=n.current.getBoundingClientRect();return i.style.height=l+"px",e||(i.style.overflow="hidden",o=window.setTimeout((()=>i.style.height="0px"),50)),()=>{i.removeEventListener("transitionend",a),clearTimeout(o)}}),[e]),{wrapperRef:t,contentRef:n}}(a),c=he(),u=([e,t])=>{var n;return(Ir(t)?null===(n=c.getInput(t.path))||void 0===n?void 0:n.order:c.getFolderSettings(Vn(r,e)).order)||0},d=Object.entries(i).sort(((e,t)=>u(e)-u(t)));return o.createElement(Ie,{ref:l,isRoot:e,fill:t,flat:n},o.createElement(Le,{ref:s,isRoot:e,toggled:a},d.map((([e,t])=>Ir(t)?o.createElement(Yr,{key:t.path,valueKey:t.valueKey,path:t.path}):o.createElement(Jr,{key:e,name:e,path:r,tree:t})))))})),qr=ye("div",{position:"relative",fontFamily:"$mono",fontSize:"$root",color:"$rootText",backgroundColor:"$elevation1",variants:{fill:{false:{position:"fixed",top:"10px",right:"10px",zIndex:1e3,width:"$rootWidth"},true:{position:"relative",width:"100%"}},flat:{false:{borderRadius:"$lg",boxShadow:"$level1"}},oneLineLabels:{true:{[`${Ne}`]:{gridTemplateColumns:"auto",gridAutoColumns:"minmax(max-content, 1fr)",gridAutoRows:"minmax($sizes$rowHeight), auto)",rowGap:0,columnGap:0,marginTop:"$rowGap"}}},hideTitleBar:{true:{$$titleBarHeight:"0px"},false:{$$titleBarHeight:"$sizes$titleBarHeight"}}},"&,*,*:after,*:before":{boxSizing:"border-box"},"*::selection":{backgroundColor:"$accent2"}}),Qr=ye("i",{$flexCenter:"",width:40,userSelect:"none",cursor:"pointer","> svg":{fill:"$highlight1",transition:"transform 350ms ease, fill 250ms ease"},"&:hover > svg":{fill:"$highlight3"},variants:{active:{true:{"> svg":{fill:"$highlight2"}}}}}),eo=ye("div",{display:"flex",alignItems:"stretch",justifyContent:"space-between",height:"$titleBarHeight",variants:{mode:{drag:{cursor:"grab"}}}}),to=ye("div",{$flex:"",position:"relative",width:"100%",overflow:"hidden",transition:"height 250ms ease",color:"$highlight3",paddingLeft:"$md",[`> ${Qr}`]:{height:30},variants:{toggled:{true:{height:30},false:{height:0}}}}),no=ye("input",{$reset:"",flex:1,position:"relative",height:30,width:"100%",backgroundColor:"transparent",fontSize:"10px",borderRadius:"$root","&:focus":{},"&::placeholder":{color:"$highlight2"}}),ro=ye("div",{touchAction:"none",$flexCenter:"",flex:1,"> svg":{fill:"$highlight1"},color:"$highlight1",variants:{drag:{true:{$draggable:"","> svg":{transition:"fill 250ms ease"},"&:hover":{color:"$highlight3"},"&:hover > svg":{fill:"$highlight3"}}},filterEnabled:{false:{paddingRight:40}}}}),oo=o.forwardRef((({setFilter:e,toggle:t},n)=>{const[r,i]=(0,o.useState)(""),a=(0,o.useMemo)((()=>te(e,250)),[e]);return(0,o.useEffect)((()=>{a(r)}),[r,a]),o.createElement(o.Fragment,null,o.createElement(no,{ref:n,value:r,placeholder:"[Open filter with CMD+SHIFT+L]",onPointerDown:e=>e.stopPropagation(),onChange:e=>{const n=e.currentTarget.value;t(!0),i(n)}}),o.createElement(Qr,{onClick:()=>(e(""),void i("")),style:{visibility:r?"visible":"hidden"}},o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",height:"14",width:"14",viewBox:"0 0 20 20",fill:"currentColor"},o.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"}))))}));function io({setFilter:e,onDrag:t,onDragStart:n,onDragEnd:r,toggle:i,toggled:a,title:l,drag:s,filterEnabled:c,from:u}){const[d,f]=(0,o.useState)(!1),p=(0,o.useRef)(null);(0,o.useEffect)((()=>{var e,t;d?null===(e=p.current)||void 0===e||e.focus():null===(t=p.current)||void 0===t||t.blur()}),[d]);const g=ot((({offset:[e,o],first:i,last:a})=>{t({x:e,y:o}),i&&n({x:e,y:o}),a&&r({x:e,y:o})}),{filterTaps:!0,from:({offset:[e,t]})=>[(null===u||void 0===u?void 0:u.x)||e,(null===u||void 0===u?void 0:u.y)||t]});return(0,o.useEffect)((()=>{const e=e=>{"L"===e.key&&e.shiftKey&&e.metaKey&&f((e=>!e))};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)}),[]),o.createElement(o.Fragment,null,o.createElement(eo,{mode:s?"drag":void 0},o.createElement(Qr,{active:!a,onClick:()=>i()},o.createElement(et,{toggled:a,width:12,height:8})),o.createElement(ro,ce({},s?g():{},{drag:s,filterEnabled:c}),void 0===l&&s?o.createElement("svg",{width:"20",height:"10",viewBox:"0 0 28 14",xmlns:"http://www.w3.org/2000/svg"},o.createElement("circle",{cx:"2",cy:"2",r:"2"}),o.createElement("circle",{cx:"14",cy:"2",r:"2"}),o.createElement("circle",{cx:"26",cy:"2",r:"2"}),o.createElement("circle",{cx:"2",cy:"12",r:"2"}),o.createElement("circle",{cx:"14",cy:"12",r:"2"}),o.createElement("circle",{cx:"26",cy:"12",r:"2"})):l),c&&o.createElement(Qr,{active:d,onClick:()=>f((e=>!e))},o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",height:"20",viewBox:"0 0 20 20"},o.createElement("path",{d:"M9 9a2 2 0 114 0 2 2 0 01-4 0z"}),o.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a4 4 0 00-3.446 6.032l-2.261 2.26a1 1 0 101.414 1.415l2.261-2.261A4 4 0 1011 5z",clipRule:"evenodd"})))),o.createElement(to,{toggled:d},o.createElement(oo,{ref:p,setFilter:e,toggle:i})))}const ao=["store","hidden","theme","collapsed"];function lo(e){let{store:t,hidden:n=!1,theme:r,collapsed:i=!1}=e,a=f(e,ao);const l=Hn((()=>function(e){const t=me();if(!e)return{theme:t,className:""};Object.keys(e).forEach((n=>{Object.assign(t[n],e[n])}));const n=we(t);return{theme:t,className:n.className}}(r)),[r]),[s,c]=(0,o.useState)(!i),u="object"===typeof i?!i.collapsed:s,d=(0,o.useMemo)((()=>"object"===typeof i?e=>{"function"===typeof e?i.onChange(!e(!i.collapsed)):i.onChange(!e)}:c),[i]);return!t||n?null:o.createElement(fe.Provider,{value:l},o.createElement(so,ce({store:t},a,{toggled:u,setToggle:d,rootClass:l.className})))}const so=o.memo((({store:e,rootClass:t,fill:n=!1,flat:r=!1,neverHide:i=!1,oneLineLabels:a=!1,titleBar:s={title:void 0,drag:!0,filter:!0,position:void 0,onDrag:void 0,onDragStart:void 0,onDragEnd:void 0},hideCopyButton:c=!1,toggled:u,setToggle:d})=>{var f,p;const g=(e=>{const[t,n]=(0,o.useState)(e.getVisiblePaths());return(0,o.useEffect)((()=>{n(e.getVisiblePaths());const t=e.useStore.subscribe(e.getVisiblePaths,n,{equalityFn:l});return()=>t()}),[e]),t})(e),[h,m]=(0,o.useState)(""),v=(0,o.useMemo)((()=>((e,t)=>{const n={},r=t?t.toLowerCase():null;return e.forEach((e=>{const[t,o]=function(e){const t=e.split(".");return[t.pop(),t.join(".")||void 0]}(e);(!r||t.toLowerCase().indexOf(r)>-1)&&Un()(n,o,{[t]:{__levaInput:!0,path:e}})})),n})(g,h)),[g,h]),[b,y]=it(),E=i||g.length>0,w="object"===typeof s&&s.title||void 0,$="object"!==typeof s||(null===(f=s.drag)||void 0===f||f),O="object"!==typeof s||(null===(p=s.filter)||void 0===p||p),C="object"===typeof s&&s.position||void 0,x="object"===typeof s&&s.onDrag||void 0,R="object"===typeof s&&s.onDragStart||void 0,S="object"===typeof s&&s.onDragEnd||void 0;return o.useEffect((()=>{y({x:null===C||void 0===C?void 0:C.x,y:null===C||void 0===C?void 0:C.y})}),[C,y]),Ce(),o.createElement(ge.Provider,{value:{hideCopyButton:c}},o.createElement(qr,{ref:b,className:t,fill:n,flat:r,oneLineLabels:a,hideTitleBar:!s,style:{display:E?"block":"none"}},s&&o.createElement(io,{onDrag:e=>{y(e),null===x||void 0===x||x(e)},onDragStart:e=>null===R||void 0===R?void 0:R(e),onDragEnd:e=>null===S||void 0===S?void 0:S(e),setFilter:m,toggle:e=>d((t=>null!==e&&void 0!==e?e:!t)),toggled:u,title:w,drag:$,filterEnabled:O,from:C}),E&&o.createElement(pe.Provider,{value:e},o.createElement(Xr,{isRoot:!0,fill:n,flat:r,tree:v,toggled:u}))))})),co=["isRoot"];let uo=!1,fo=null;function po(e){let{isRoot:t=!1}=e,n=f(e,co);return(0,o.useEffect)((()=>(uo=!0,!t&&fo&&(fo.remove(),fo=null),()=>{t||(uo=!1)})),[t]),o.createElement(lo,ce({store:Dr},n))}function go(e){(0,o.useEffect)((()=>{e&&!uo&&(fo||(fo=document.getElementById("leva__root")||Object.assign(document.createElement("div"),{id:"leva__root"}),document.body&&(document.body.appendChild(fo),function(e,t){const n=console.error;console.error=()=>{},r.render(e,t),console.error=n}(o.createElement(po,{isRoot:!0}),fo))),uo=!0)}),[e])}function ho(e,t,n,r,i){const{folderName:a,schema:s,folderSettings:c,hookSettings:u,deps:d}=function(e,t,n,r,o){let i,a,l,s,c;return"string"===typeof e?(a=e,i=t,Array.isArray(n)?c=n:n&&("store"in n?(s=n,c=r):(l=n,Array.isArray(r)?c=r:(s=r,c=o)))):(i=e,Array.isArray(t)?c=t:(s=t,c=n)),{schema:i,folderName:a,folderSettings:l,hookSettings:s,deps:c||[]}}(e,t,n,r,i),f="function"===typeof s,p=(0,o.useRef)(!1),g=(0,o.useRef)(!0),h=Hn((()=>{p.current=!0;const e="function"===typeof s?s():s;return a?{[a]:Pr(e,c)}:e}),d);go(!(null!==u&&void 0!==u&&u.store));const[m]=(0,o.useState)((()=>(null===u||void 0===u?void 0:u.store)||Dr)),[v,b]=(0,o.useMemo)((()=>m.getDataFromSchema(h)),[m,h]),[y,E,w,$,O]=(0,o.useMemo)((()=>{const e=[],t=[],n={},r={},o={};return Object.values(b).forEach((({path:i,onChange:a,onEditStart:l,onEditEnd:s,transient:c})=>{e.push(i),a?(n[i]=a,c||t.push(i)):t.push(i),l&&(r[i]=l),s&&(o[i]=s)})),[e,t,n,r,o]}),[b]),C=(0,o.useMemo)((()=>m.orderPaths(y)),[y,m]),x=Bn(m,E,v),R=(0,o.useCallback)((e=>{const t=Object.entries(e).reduce(((e,[t,n])=>Object.assign(e,{[b[t].path]:n})),{});m.set(t,!1)}),[m,b]),S=(0,o.useCallback)((e=>m.get(b[e].path)),[m,b]);return(0,o.useEffect)((()=>{const e=!g.current&&p.current;return m.addData(v,e),g.current=!1,p.current=!1,()=>m.disposePaths(C)}),[m,C,v]),(0,o.useEffect)((()=>{const e=[];return Object.entries(w).forEach((([t,n])=>{n(m.get(t),t,T({initial:!0,get:m.get},m.getInput(t)));const r=m.useStore.subscribe((e=>{const n=e.data[t];return[n.disabled?void 0:n.value,n]}),(([e,r])=>n(e,t,T({initial:!1,get:m.get},r))),{equalityFn:l});e.push(r)})),()=>e.forEach((e=>e()))}),[m,w]),(0,o.useEffect)((()=>{const e=[];return Object.entries($).forEach((([t,n])=>e.push(m.subscribeToEditStart(t,n)))),Object.entries(O).forEach((([t,n])=>e.push(m.subscribeToEditEnd(t,n)))),()=>e.forEach((e=>e()))}),[$,O,m]),f?[x,R,S]:x}C(G.SELECT,Ot),C(G.IMAGE,vr),C(G.NUMBER,vt),C(G.COLOR,tr),C(G.STRING,kt),C(G.BOOLEAN,Pt),C(G.INTERVAL,Rr),C(G.VECTOR3D,nr),C(G.VECTOR2D,cr)},69135:function(e,t){t.Z=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(","),r=e.name||"",o=(e.type||"").toLowerCase(),i=o.replace(/\/.*$/,"");return n.some((function(e){var t=e.trim().toLowerCase();return"."===t.charAt(0)?r.toLowerCase().endsWith(t):t.endsWith("/*")?i===t.replace(/\/.*$/,""):o===t}))}return!0}}}]);
//# sourceMappingURL=leva.a322d85979b25e1398547b2c90d519b0.js.map