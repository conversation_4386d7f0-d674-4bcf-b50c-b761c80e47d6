<!DOCTYPE html>
<html lang="en">

  <head>
    <script>
      function setUtmCookies() {

        function getParameterByName(name, url = window.location.href) {
          name = name.replace(/[\[]/, "\\[").replace(/[\]]/, "\\]");
          var regex = new RegExp("[\\?&]" + name + "=([^&#]*)"),
            results = regex.exec(url);
          if (!results) return null;
          if (!results[1]) return '';
          return results[1];
        }

        function setCookie(name, value, days) {
          var expires = "";
          if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
          }
          document.cookie = name + "=" + (value || "") + expires + "; path=/; domain=smartreach.io";
        }

        var utm_source = getParameterByName("utm_source");
        var utm_medium = getParameterByName("utm_medium");
        var utm_campaign = getParameterByName("utm_campaign");
        var utm_term = getParameterByName("utm_term");
        var utm_content = getParameterByName("utm_content");
        var utm_agid = getParameterByName("utm_agid");
        var utm_device = getParameterByName("device");
        var utm_match_type = getParameterByName("match_type");
        var utm_placement = getParameterByName("placement");
        var utm_network = getParameterByName("network");

        var xpdays = 90;

        if (utm_source) {
          setCookie("utm_source", utm_source, xpdays)
        }

        if (utm_medium) {
          setCookie("utm_medium", utm_medium, xpdays)
        }

        if (utm_campaign) {
          setCookie("utm_campaign", utm_campaign, xpdays)
        }

        if (utm_term) {
          setCookie("utm_term", utm_term, xpdays)
        }

        if (utm_content) {
          setCookie("utm_content", utm_content, xpdays)
        }

        if (utm_agid) {
          setCookie("utm_agid", utm_agid, xpdays)
        }

        if (utm_device) {
          setCookie("utm_device", utm_device, xpdays)
        }

        if (utm_match_type) {
          setCookie("utm_match_type", utm_match_type, xpdays)
        }

        if (utm_placement) {
          setCookie("utm_placement", utm_placement, xpdays)
        }

        if (utm_network) {
          setCookie("utm_network", utm_network, xpdays)
        }
      };

      setUtmCookies()

    </script>


    <!-- Crisp Chat -->
    <script type="text/javascript">
      window.$crisp = [];
      window.CRISP_WEBSITE_ID = "15ece199-3c4c-4c8a-8e3e-f9ce85b8e3c9";
      (function () {
        d = document; s = d.createElement("script");
        s.src = "https://client.crisp.chat/l.js";
        s.async = 1;
        d.getElementsByTagName("head")[0].appendChild(s);
      })();
    </script>

    <script>
      function onFSPopupClosed(orderReference) {
        if (orderReference) {
          console.log(orderReference.reference);
          fastspring.builder.reset();
          // window.location.replace("http://furiousfalcon.com/?orderId=" + orderReference.reference);
          location.reload();
        } else {
          console.log("no order ID");
          // location.reload();
        }
      }
    </script>

    <script id="fsc-api" src="https://d1f8f9xcsvx3ha.cloudfront.net/sbl/0.7.4/fastspring-builder.min.js"
      type="text/javascript" data-storefront="smartreach.onfastspring.com/popup-test-1"
      data-popup-closed="onFSPopupClosed" data-access-key="UNCC8ADEQ4GTC8Y4HIEMLQ">
      </script>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="Content-Security-Policy" content="
      style-src 'unsafe-inline' 'self'
      https://smartreach.io
      https://dev.visualwebsiteoptimizer.com
      https://*.smartreach.io
      https://*.intercom.io
      wss://*.intercom.io
      https://*.crisp.chat
      wss://*.crisp.chat
      wss://client.relay.crisp.chat
      https://*.onfastspring.com
      https://*.fastspring.com
      https://d1f8f9xcsvx3ha.cloudfront.net
      https://*.gstatic.com
      https://*.inspectlet.com
      wss://*.inspectlet.com
      https://snap.licdn.com
      https://sreml.com
      https://*.sreml.com
      https://*.pusher.com
      wss://*.pusher.com
      https://cdn.firstpromoter.com
      https://*.bing.com
      https://*.google.co.in
      https://*.google.com
      https://*.googletagmanager.com
      https://*.googleapis.com
      https://pagead2.googlesyndication.com
      https://*.calendly.com
      https://*.intercomcdn.com
      https://*.cloudfront.net
      https://sentry.io
      https://*.ingest.sentry.io
      https://zapier.com
      https://*.zapier.com
      https://zapier-images.imgix.net
      https://*.amazonaws.com
      https://*.cloudflare.com
      https://*.google-analytics.com
      https://*.linkedin.com
      https://*.doubleclick.net
      https://*.oribi.io
      https://*.adsymptotic.com
      https://ipinfo.io
      https://ipapi.co/
      https://www.youtube.com
      https://www.loom.com
      ;

      script-src 'unsafe-eval' 'unsafe-eval' 'unsafe-inline' blob: *
      ;

      img-src 'self' https: data:;

      connect-src 'self'
      https://stats.g.doubleclick.net
      https://dev.visualwebsiteoptimizer.com
      https://sdk.twilio.com
      wss://voice-js.roaming.twilio.com
      https://smartreach.io
      https://*.smartreach.io
      https://*.intercom.io
      wss://*.intercom.io
      https://*.crisp.chat
      wss://*.crisp.chat
      wss://client.relay.crisp.chat
      https://*.onfastspring.com
      https://*.fastspring.com
      https://d1f8f9xcsvx3ha.cloudfront.net
      https://*.gstatic.com
      https://*.inspectlet.com
      wss://*.inspectlet.com
      https://snap.licdn.com
      https://sreml.com
      https://*.sreml.com
      https://*.pusher.com
      wss://*.pusher.com
      https://cdn.firstpromoter.com
      https://*.bing.com
      https://*.google.co.in
      https://*.google.com
      https://*.googletagmanager.com
      https://*.googleapis.com
      https://google.com
      https://pagead2.googlesyndication.com
      https://*.calendly.com
      https://*.intercomcdn.com
      https://*.cloudfront.net
      https://sentry.io
      https://*.ingest.sentry.io
      https://zapier.com
      https://*.zapier.com
      https://zapier-images.imgix.net
      https://*.amazonaws.com
      https://*.cloudflare.com
      https://*.google-analytics.com
      https://analytics.google.com
      https://*.linkedin.com
      https://*.doubleclick.net
      https://*.oribi.io
      https://*.adsymptotic.com
      https://ipinfo.io
      https://ipapi.co/
      https://www.youtube.com
      https://www.loom.com
      ;

      font-src 'self' data:
      https://cdn.smartreach.io
      https://dev.visualwebsiteoptimizer.com
      https://cdn-staging-5.sreml.com
      https://fonts.gstatic.com
      https://*.intercomcdn.com
      https://*.crisp.chat
      https://*.onfastspring.com
      https://*.fastspring.com
      https://d1f8f9xcsvx3ha.cloudfront.net
      wss://*.crisp.chat
      ;

      frame-src
      https://js.stripe.com/
      https://*.google.com
      https://dev.visualwebsiteoptimizer.com
      https://*.doubleclick.net
      https://www.youtube.com
      https://*.intercom.io
      wss://*.intercom.io
      https://*.intercomcdn.com
      https://*.crisp.chat
      wss://*.crisp.chat
      https://*.onfastspring.com
      https://*.fastspring.com
      https://d1f8f9xcsvx3ha.cloudfront.net
      https://www.loom.com
      https://calendly.com/
      ;
      media-src
      https://dev.visualwebsiteoptimizer.com
      https://storage.googleapis.com
      https://api.twilio.com/;
      object-src 'none';
      base-uri 'self';
      form-action 'self';

      default-src 'self';
    " />
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="google-site-verification" content="EUWz_5XvDcZtYDFfjjdeK0T73M5oh-FlFwRYsTUdT0E" />
    <link rel="icon" type="image/png" href="https://app.smartreach.io/assets/favicon-32x32.png" sizes="32x32" />
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,500,700,400italic,700italic&display=swap"
      rel="stylesheet">
    <!-- <link href="https://fonts.googleapis.com/css?family=Lato:400,400i,700,900&display=swap" rel="stylesheet" /> -->
    <link href="https://fonts.googleapis.com/css?family=Muli:400i&display=swap" rel="stylesheet" />
    <!-- <link
    href="https://fonts.googleapis.com/css2?family=Mulish:wght@700&family=PT+Sans:wght@400;700&family=Playfair+Display:wght@700&family=Poppins&display=swap"
    rel="stylesheet" /> -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://dev.visualwebsiteoptimizer.com" />

    <link href="https://app.smartreach.io/" rel="canonical" />
    <!-- Open Graph data -->

    <!--<head prefix="og: http://ogp.me/ns# fb: http://ogp.me/ns/fb# article:http://ogp.me/ns/article#">-->
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://app.smartreach.io/assets/Favicon.png" />
    <meta property="og:site_name" content="SmartReach.io" />
    <!--<meta property="fb:app_id" content="Your FB_APP_ID" />-->
    <!-- Twitter Card data -->
    <!-- <meta name="twitter:card" content="Sales automation software that lets you schedule and send personalized cold emails & follow-ups automatically from your mailbox and boost your reply rates."> -->
    <meta name="twitter:site" content="@smartreachio">
    <meta name="twitter:creator" content="@smartreachio">
    <!-- Twitter Summary card images must be at least 120x120px -->
    <meta name="twitter:image" content="https://app.smartreach.io/assets/home_page_Favicon3.png">

    <!-- <link rel="alternate" hreflang="x-default" href="https://smartreach.io/" />
    <link rel="alternate" hreflang="en" href="http://example.com/en/" />
    <link rel="alternate" hreflang="en-us" href="http://example.com/en-us/" /> -->


    <!-- fb app id for fbstart -->
    <meta property="fb:app_id" content="***************" />

    <style>
      /* HTML: <div class="loader"></div> */
      .loader {
        width: 25px;
        height: 25px;
        border-radius: 50%;
        border: 4px solid transparent;
        /* Transparent border */
        border-top-color: #CFD3DA;
        /* Black arc */
        border-right-color: #CFD3DA;
        /* Additional black arc */
        border-bottom-color: #CFD3DA;
        /* Additional black arc */
        -webkit-animation: spin 1s infinite linear;
                animation: spin 1s infinite linear;
      }

      @-webkit-keyframes spin {
        to {
          -webkit-transform: rotate(1turn);
                  transform: rotate(1turn);
        }
      }

      @keyframes spin {
        to {
          -webkit-transform: rotate(1turn);
                  transform: rotate(1turn);
        }
      }
    </style>

    <script type="module" crossorigin src="https://cdn.smartreach.io/sr/public/public/assets/index.D3YmW_aA.js"></script>
    <link rel="stylesheet" crossorigin href="https://cdn.smartreach.io/sr/public/public/assets/index.CbAjTemb.css">
  </head>

  <body>
    <div id="root">
      <div
        style="height:100vh; width: 100%; display: flex; align-items: center; justify-content: center;position: relative; ">
        <div style="position: absolute;">
          <div class="loader"></div>
        </div>
      </div>
    </div>


    <!-- Google Tag Manager -->
    <script type="text/javascript">
      window.addEventListener("load", (event) => {
        (function (w, d, s, l, i) {
          w[l] = w[l] || []; w[l].push({
            'gtm.start':
              new Date().getTime(), event: 'gtm.js'
          }); var f = d.getElementsByTagName(s)[0],
            j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
              'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-TFKPVQT');
      });
    </script>
    <!-- End Google Tag Manager -->

    <!-- Global site tag (gtag.js) - Google Ads: 757297556 -->
    <script type="text/javascript" async src="https://www.googletagmanager.com/gtag/js?id=AW-757297556"></script>
    <script type="text/javascript">
      window.dataLayer = window.dataLayer || [];
      function gtag() { dataLayer.push(arguments); }
      gtag('js', new Date());

      gtag('config', 'AW-757297556');
    </script>

    <!--  -->
    <script type="text/javascript">

      // Event snippet for Adwords_Signup conversion page
      // In your html page, add the snippet and call gtag_report_conversion when someone clicks on the chosen link or button.
      function gtag_report_conversion_Adwords_Signup(url) {
        var callback = function () {
          if (typeof (url) != 'undefined') {
            window.location = url;
          }
        };
        gtag('event', 'conversion', {
          'send_to': 'AW-757297556/--nJCKunxpcBEJTjjekC',
          'event_callback': callback
        });
        return false;
      }


      // Event snippet for Purchase conversion page
      // In your html page, add the snippet and call gtag_report_conversion when someone clicks on the chosen link or button.
      function gtag_report_conversion_Start_subscription(url) {
        var callback = function () {
          // if (typeof (url) != 'undefined') { window.location = url; }
        };

        gtag('event', 'conversion', {
          'send_to': 'AW-757297556/_j_zCN6vmcABEJTjjekC',
          'value': 1.0,
          'currency': 'INR',
          'transaction_id': '',
          'event_callback': callback
        }); return false;
      }
    </script>

    <!-- Begin Bind Ads Code -->
    <script type="text/javascript">
      window.addEventListener("load", (event) => {
        (function (w, d, t, r, u) { var f, n, i; w[u] = w[u] || [], f = function () { var o = { ti: "134604743" }; o.q = w[u], w[u] = new UET(o), w[u].push("pageLoad") }, n = d.createElement(t), n.src = r, n.async = 1, n.onload = n.onreadystatechange = function () { var s = this.readyState; s && s !== "loaded" && s !== "complete" || (f(), n.onload = n.onreadystatechange = null) }, i = d.getElementsByTagName(t)[0], i.parentNode.insertBefore(n, i) })(window, document, "script", "//bat.bing.com/bat.js", "uetq");
      });
    </script>

    <script type="text/javascript">
      function reportCustomSignUpEvent() {
        window.uetq = window.uetq || [];
        window.uetq.push('event', 'signup', { 'event_category': 'signup', 'event_label': 'signup', 'event_value': 1 });
      }
    </script>
    <!-- End Bind Ads Code -->



    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TFKPVQT" height="0" width="0"
        style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->


    <!-- firstpromoter -->
    <script type="text/javascript">
      window.addEventListener("load", (event) => {
        (function () { var t = document.createElement("script"); t.type = "text/javascript", t.async = !0, t.src = 'https://cdn.firstpromoter.com/fprom.js', t.onload = t.onreadystatechange = function () { var t = this.readyState; if (!t || "complete" == t || "loaded" == t) try { $FPROM.init("blmil0u1", ".smartreach.io") } catch (t) { } }; var e = document.getElementsByTagName("script")[0]; e.parentNode.insertBefore(t, e) })();
      });
    </script>

    <!-- Begin Inspectlet Asynchronous Code -->
    <!-- <script type="text/javascript">
     window.addEventListener("load", (event) => {
      if ((location.hostname === 'smartreach.io') || (location.hostname === 'app.smartreach.io')) {
        (function () {
          window.__insp = window.__insp || [];
          __insp.push(['wid', 1101168947]); var ldinsp = function () { if (typeof window.__inspld != "undefined") return; window.__inspld = 1; var insp = document.createElement('script'); insp.type = 'text/javascript'; insp.async = true; insp.id = "inspsync"; insp.src = ('https:' == document.location.protocol ? 'https' : 'http') + '://cdn.inspectlet.com/inspectlet.js?wid=1101168947&r=' + Math.floor(new Date().getTime() / 3600000); var x = document.getElementsByTagName('script')[0]; x.parentNode.insertBefore(insp, x); };
          setTimeout(ldinsp, 0);
        })();
      }
    });
    </script> -->
    <!-- End Inspectlet Asynchronous Code -->

    <!-- Begin Inspectlet Asynchronous Code -->
    <!-- <script type="text/javascript">
(function() {
window.__insp = window.__insp || [];
__insp.push(['wid', 1305628327]);
var ldinsp = function(){
if(typeof window.__inspld != "undefined") return; window.__inspld = 1; var insp = document.createElement('script'); insp.type = 'text/javascript'; insp.async = true; insp.id = "inspsync"; insp.src = ('https:' == document.location.protocol ? 'https' : 'http') + '://cdn.inspectlet.com/inspectlet.js?wid=1305628327&r=' + Math.floor(new Date().getTime()/3600000); var x = document.getElementsByTagName('script')[0]; x.parentNode.insertBefore(insp, x); };
setTimeout(ldinsp, 0);
})();
</script> -->
    <!-- End Inspectlet Asynchronous Code -->

    <!--<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.3/jquery.min.js"></script>
    <script type="text/javascript" src="http://arrow.scrolltotop.com/arrow79.js"></script>
    <noscript>Not seeing a <a href="http://www.scrolltotop.com/">Scroll to Top Button</a>? Go to our FAQ page for more info.</noscript>-->


    <!--
  <script>
    if ((location.hostname === 'smartreach.io') || (location.hostname === 'app.smartreach.io')) {

      // Loggly
      var _LTracker = _LTracker || [];
      _LTracker.push({
        'logglyKey': '************************************',
        'sendConsoleErrors': true,
        'tag': 'loggly-jslogger'
      });

    }
  </script>
-->



    <!-- Loggly -->
    <!-- <script type="text/javascript" src="https://cloudfront.loggly.com/js/loggly.tracker-latest.min.js" async></script> -->


    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script type="text/javascript" async src="https://www.googletagmanager.com/gtag/js?id=UA-109139137-1"></script>
    <script type="text/javascript">
      window.dataLayer = window.dataLayer || [];
      function gtag() { dataLayer.push(arguments); }
      gtag('js', new Date());

      gtag('config', 'UA-109139137-1');
    </script>

    <!-- Intercom -->
    <!-- <script type="text/javascript">
      //Set your APP_ID
      window.addEventListener("load", (event) => {
      var APP_ID = "xmya8oga";

      (function () { var w = window; var ic = w.Intercom; if (typeof ic === "function") { ic('reattach_activator'); ic('update', w.intercomSettings); } else { var d = document; var i = function () { i.c(arguments); }; i.q = []; i.c = function (args) { i.q.push(args); }; w.Intercom = i; var l = function () { var s = d.createElement('script'); s.type = 'text/javascript'; s.async = true; s.src = 'https://widget.intercom.io/widget/' + APP_ID; var x = d.getElementsByTagName('script')[0]; x.parentNode.insertBefore(s, x); }; if (document.readyState === 'complete') { l(); } else if (w.attachEvent) { w.attachEvent('onload', l); } else { w.addEventListener('load', l, false); } } })();
      });
      </script> -->


    <!-- Calendly -->
    <script type="text/javascript" src="https://assets.calendly.com/assets/external/widget.js"></script>


    <!-- google tag - remarketing -->
    <!-- Global site tag (gtag.js) - Google Ads: 757297556 -->
    <!-- <script async src="https://www.googletagmanager.com/gtag/js?id=AW-757297556"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'AW-757297556');
  </script> -->


    <script type="text/javascript" src="https://www.google.com/recaptcha/api.js?onload=onloadCallback&render=explicit"
      async defer>
      </script>

    <script type="text/javascript">
      window.addEventListener("load", (event) => {
        var buttonId = 'basicDetailsButton';
        function trackingListener() {
          var capterra_vkey = '7294f8f86909ba2e17fd76064f3d834f',
            capterra_vid = '2118167',
            ct = document.createElement('img');
          ct.src = 'https://ct.capterra.com/capterra_tracker.gif?vid='
            + capterra_vid + '&vkey=' + capterra_vkey;
          document.body.appendChild(ct);
        };
        var button = document.getElementById(buttonId);
        if (!!button) {
          button.addEventListener(
            'click',
            trackingListener
          );
        }
      });
    </script>

    <script type="text/javascript">
      _linkedin_partner_id = "3643196";
      window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];
      window._linkedin_data_partner_ids.push(_linkedin_partner_id);
    </script>
    <script type="text/javascript">
      window.addEventListener("load", (event) => {
        (function (l) {
          if (!l) {
            window.lintrk = function (a, b) { window.lintrk.q.push([a, b]) };
            window.lintrk.q = []
          }
          var s = document.getElementsByTagName("script")[0];
          var b = document.createElement("script");
          b.type = "text/javascript"; b.async = true;
          b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js";
          s.parentNode.insertBefore(b, s);
        })(window.lintrk);
      });
    </script>
    <noscript>
      <img height="1" width="1" style="display:none;" alt=""
        src="https://px.ads.linkedin.com/collect/?pid=3643196&fmt=gif" />
    </noscript>

    <!-- Vite entry point -->
  </body>

</html>