"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["react-virtuoso"],{26246:function(e,t,o){o.d(t,{OO:function(){return ao}});var n=o(67557),r=o(89526),i=o(73961);function l(e,t){return o=>e(t(o))}function s(e,t){return t(e)}function c(e,t){return o=>e(t,o)}function a(e,t){return()=>e(t)}function u(e,t){return t(e),e}function d(...e){return e}function h(e){e()}function f(e){return()=>e}function m(e){return void 0!==e}function g(){}function p(e,t){return e(1,t)}function x(e,t){e(0,t)}function v(e){e(2)}function I(e){return e(4)}function T(e,t){return p(e,c(t,0))}function w(e,t){const o=e(1,(e=>{o(),t(e)}));return o}function S(){const e=[];return(t,o)=>{switch(t){case 2:return void e.splice(0,e.length);case 1:return e.push(o),()=>{const t=e.indexOf(o);t>-1&&e.splice(t,1)};case 0:return void e.slice().forEach((e=>{e(o)}));default:throw new Error(`unrecognized action ${t}`)}}}function C(e){let t=e;const o=S();return(e,n)=>{switch(e){case 1:n(t);break;case 0:t=n;break;case 4:return t}return o(e,n)}}function H(e){return u(S(),(t=>T(e,t)))}function b(e,t){return u(C(t),(t=>T(e,t)))}function y(e,...t){const o=function(...e){return t=>e.reduceRight(s,t)}(...t);return(t,n)=>{switch(t){case 1:return p(e,o(n));case 2:return void v(e)}}}function R(e,t){return e===t}function z(e=R){let t;return o=>n=>{e(t,n)||(t=n,o(n))}}function E(e){return t=>o=>{e(o)&&t(o)}}function k(e){return t=>l(t,e)}function B(e){return t=>()=>t(e)}function L(e,t){return o=>n=>o(t=e(t,n))}function O(e){return t=>o=>{e>0?e--:t(o)}}function F(e){let t,o=null;return n=>r=>{o=r,t||(t=setTimeout((()=>{t=void 0,n(o)}),e))}}function M(e){let t,o;return n=>r=>{t=r,o&&clearTimeout(o),o=setTimeout((()=>{n(t)}),e)}}function P(...e){const t=new Array(e.length);let o=0,n=null;const r=Math.pow(2,e.length)-1;return e.forEach(((e,i)=>{const l=Math.pow(2,i);p(e,(e=>{const s=o;o|=l,t[i]=e,s!==r&&o===r&&n&&(n(),n=null)}))})),e=>i=>{const l=()=>e([i].concat(t));o===r?l():n=l}}function W(...e){return function(t,o){switch(t){case 1:return function(...e){return()=>{e.map(h)}}(...e.map((e=>p(e,o))));case 2:return;default:throw new Error(`unrecognized action ${t}`)}}}function j(e,t=R){return y(e,z(t))}function A(...e){const t=S(),o=new Array(e.length);let n=0;const r=Math.pow(2,e.length)-1;return e.forEach(((e,i)=>{const l=Math.pow(2,i);p(e,(e=>{o[i]=e,n|=l,n===r&&x(t,o)}))})),function(e,i){switch(e){case 1:return n===r&&i(o),p(t,i);case 2:return v(t);default:throw new Error(`unrecognized action ${e}`)}}}function D(e,t=[],{singleton:o}={singleton:!0}){return{id:V(),constructor:e,dependencies:t,singleton:o}}const V=()=>Symbol();function N(e,t){const o={},n={};let r=0;const i=e.length;for(;r<i;)n[e[r]]=1,r+=1;for(const l in t)n.hasOwnProperty(l)||(o[l]=t[l]);return o}const G="undefined"!==typeof document?r.useLayoutEffect:r.useEffect;function _(e,t,o){const i=Object.keys(t.required||{}),l=Object.keys(t.optional||{}),s=Object.keys(t.methods||{}),d=Object.keys(t.events||{}),h=r.createContext({});function m(e,o){e.propsReady&&x(e.propsReady,!1);for(const n of i){x(e[t.required[n]],o[n])}for(const n of l)if(n in o){x(e[t.optional[n]],o[n])}e.propsReady&&x(e.propsReady,!0)}function T(e){return d.reduce(((o,n)=>(o[n]=function(e){let t,o;const n=()=>t&&t();return function(r,i){switch(r){case 1:if(i){if(o===i)return;return n(),o=i,t=p(e,i),t}return n(),g;case 2:return n(),void(o=null);default:throw new Error(`unrecognized action ${r}`)}}}(e[t.events[n]]),o)),{})}const w=r.forwardRef(((c,g)=>{const{children:I,...w}=c,[S]=r.useState((()=>u(function(e){const t=new Map,o=({id:e,constructor:n,dependencies:r,singleton:i})=>{if(i&&t.has(e))return t.get(e);const l=n(r.map((e=>o(e))));return i&&t.set(e,l),l};return o(e)}(e),(e=>m(e,w))))),[C]=r.useState(a(T,S));G((()=>{for(const e of d)e in w&&p(C[e],w[e]);return()=>{Object.values(C).map(v)}}),[w,C,S]),G((()=>{m(S,w)})),r.useImperativeHandle(g,f(function(e){return s.reduce(((o,n)=>(o[n]=o=>{x(e[t.methods[n]],o)},o)),{})}(S)));const H=o;return(0,n.jsx)(h.Provider,{value:S,children:o?(0,n.jsx)(H,{...N([...i,...l,...d],w),children:I}):I})}));return{Component:w,usePublisher:e=>r.useCallback(c(x,r.useContext(h)[e]),[e]),useEmitterValue:r.version.startsWith("18")?e=>{const t=r.useContext(h)[e],o=r.useCallback((e=>p(t,e)),[t]);return r.useSyncExternalStore(o,(()=>I(t)),(()=>I(t)))}:e=>{const t=r.useContext(h)[e],[o,n]=r.useState(a(I,t));return G((()=>p(t,(e=>{e!==o&&n(f(e))}))),[t,o]),o},useEmitter:(e,t)=>{const o=r.useContext(h)[e];G((()=>p(o,t)),[t,o])}}}const U="undefined"!==typeof document?r.useLayoutEffect:r.useEffect;var $=(e=>(e[e.DEBUG=0]="DEBUG",e[e.INFO=1]="INFO",e[e.WARN=2]="WARN",e[e.ERROR=3]="ERROR",e))($||{});const K={0:"debug",1:"log",2:"warn",3:"error"},q=D((()=>{const e=C(3);return{log:C(((t,o,n=1)=>{var r;n>=(null!=(r=("undefined"===typeof globalThis?window:globalThis).VIRTUOSO_LOG_LEVEL)?r:I(e))&&console[K[n]]("%creact-virtuoso: %c%s %o","color: #0253b3; font-weight: bold","color: initial",t,o)})),logLevel:e}}),[],{singleton:!0});function Y(e,t,o){const n=r.useRef(null);let i=e=>{};if("undefined"!==typeof ResizeObserver){const l=r.useMemo((()=>new ResizeObserver((t=>{const n=()=>{const o=t[0].target;null!==o.offsetParent&&e(o)};o?n():requestAnimationFrame(n)}))),[e]);i=e=>{e&&t?(l.observe(e),n.current=e):(n.current&&l.unobserve(n.current),n.current=null)}}return{ref:n,callbackRef:i}}function Z(e,t,o){return Y(e,t,o).callbackRef}function X(e,t,o,n,i,l,s,c,a){const u=r.useCallback((o=>{const r=function(e,t,o,n){const r=e.length;if(0===r)return null;const i=[];for(let l=0;l<r;l++){const r=e.item(l);if(!r||void 0===r.dataset.index)continue;const s=parseInt(r.dataset.index),c=parseFloat(r.dataset.knownSize),a=t(r,o);if(0===a&&n("Zero-sized element, this should not happen",{child:r},$.ERROR),a===c)continue;const u=i[i.length-1];0===i.length||u.size!==a||u.endIndex!==s-1?i.push({startIndex:s,endIndex:s,size:a}):i[i.length-1].endIndex++}return i}(o.children,t,c?"offsetWidth":"offsetHeight",i);let a=o.parentElement;for(;!a.dataset.virtuosoScroller;)a=a.parentElement;const u="window"===a.lastElementChild.dataset.viewportType,d=s?c?s.scrollLeft:s.scrollTop:u?c?window.pageXOffset||document.documentElement.scrollLeft:window.pageYOffset||document.documentElement.scrollTop:c?a.scrollLeft:a.scrollTop,h=s?c?s.scrollWidth:s.scrollHeight:u?c?document.documentElement.scrollWidth:document.documentElement.scrollHeight:c?a.scrollWidth:a.scrollHeight,f=s?c?s.offsetWidth:s.offsetHeight:u?c?window.innerWidth:window.innerHeight:c?a.offsetWidth:a.offsetHeight;n({scrollTop:Math.max(d,0),scrollHeight:h,viewportHeight:f}),null==l||l(c?J("column-gap",getComputedStyle(o).columnGap,i):J("row-gap",getComputedStyle(o).rowGap,i)),null!==r&&e(r)}),[e,t,i,l,s,n]);return Y(u,o,a)}function J(e,t,o){return"normal"===t||(null==t?void 0:t.endsWith("px"))||o(`${e} was not resolved to pixel value correctly`,t,$.WARN),"normal"===t?0:parseInt(null!=t?t:"0",10)}function Q(e,t){return Math.round(e.getBoundingClientRect()[t])}function ee(e,t){return Math.abs(e-t)<1.01}function te(e,t,o,n=g,l,s){const c=r.useRef(null),a=r.useRef(null),u=r.useRef(null),d=r.useCallback((o=>{const n=o.target,r=n===window||n===document,l=s?r?window.pageXOffset||document.documentElement.scrollLeft:n.scrollLeft:r?window.pageYOffset||document.documentElement.scrollTop:n.scrollTop,c=s?r?document.documentElement.scrollWidth:n.scrollWidth:r?document.documentElement.scrollHeight:n.scrollHeight,d=s?r?window.innerWidth:n.offsetWidth:r?window.innerHeight:n.offsetHeight,h=()=>{e({scrollTop:Math.max(l,0),scrollHeight:c,viewportHeight:d})};o.suppressFlushSync?h():i.flushSync(h),null!==a.current&&(l===a.current||l<=0||l===c-d)&&(a.current=null,t(!0),u.current&&(clearTimeout(u.current),u.current=null))}),[e,t]);return r.useEffect((()=>{const e=l||c.current;return n(l||c.current),d({target:e,suppressFlushSync:!0}),e.addEventListener("scroll",d,{passive:!0}),()=>{n(null),e.removeEventListener("scroll",d)}}),[c,d,o,n,l]),{scrollerRef:c,scrollByCallback:function(e){s&&(e={left:e.top,behavior:e.behavior}),c.current.scrollBy(e)},scrollToCallback:function(o){const n=c.current;if(!n||(s?"offsetWidth"in n&&0===n.offsetWidth:"offsetHeight"in n&&0===n.offsetHeight))return;const r="smooth"===o.behavior;let i,l,d;n===window?(l=Math.max(Q(document.documentElement,s?"width":"height"),s?document.documentElement.scrollWidth:document.documentElement.scrollHeight),i=s?window.innerWidth:window.innerHeight,d=s?document.documentElement.scrollLeft:document.documentElement.scrollTop):(l=n[s?"scrollWidth":"scrollHeight"],i=Q(n,s?"width":"height"),d=n[s?"scrollLeft":"scrollTop"]);const h=l-i;if(o.top=Math.ceil(Math.max(Math.min(h,o.top),0)),ee(i,l)||o.top===d)return e({scrollTop:d,scrollHeight:l,viewportHeight:i}),void(r&&t(!0));r?(a.current=o.top,u.current&&clearTimeout(u.current),u.current=setTimeout((()=>{u.current=null,a.current=null,t(!0)}),1e3)):a.current=null,s&&(o={left:o.top,behavior:o.behavior}),n.scrollTo(o)}}}const oe=D((()=>{const e=S(),t=S(),o=C(0),n=S(),r=C(0),i=S(),l=S(),s=C(0),c=C(0),a=C(0),u=C(0),d=S(),h=S(),f=C(!1),m=C(!1),g=C(!1);return T(y(e,k((({scrollTop:e})=>e))),t),T(y(e,k((({scrollHeight:e})=>e))),l),T(t,r),{scrollContainerState:e,scrollTop:t,viewportHeight:i,headerHeight:s,fixedHeaderHeight:c,fixedFooterHeight:a,footerHeight:u,scrollHeight:l,smoothScrollTargetReached:n,horizontalDirection:m,skipAnimationFrameInResizeObserver:g,scrollTo:d,scrollBy:h,statefulScrollTop:r,deviation:o,scrollingInProgress:f}}),[],{singleton:!0}),ne={lvl:0};function re(e,t,o,n=ne,r=ne){return{k:e,v:t,lvl:o,l:n,r:r}}function ie(e){return e===ne}function le(){return ne}function se(e,t){if(ie(e))return ne;const{k:o,l:n,r:r}=e;if(t===o){if(ie(n))return r;if(ie(r))return n;{const[t,o]=fe(n);return ve(ge(e,{k:t,v:o,l:me(n)}))}}return ve(ge(e,t<o?{l:se(n,t)}:{r:se(r,t)}))}function ce(e,t){if(!ie(e))return t===e.k?e.v:t<e.k?ce(e.l,t):ce(e.r,t)}function ae(e,t,o="k"){if(ie(e))return[-1/0,void 0];if(Number(e[o])===t)return[e.k,e.v];if(Number(e[o])<t){const n=ae(e.r,t,o);return n[0]===-1/0?[e.k,e.v]:n}return ae(e.l,t,o)}function ue(e,t,o){return ie(e)?re(t,o,1):t===e.k?ge(e,{k:t,v:o}):t<e.k?xe(ge(e,{l:ue(e.l,t,o)})):xe(ge(e,{r:ue(e.r,t,o)}))}function de(e,t,o){if(ie(e))return[];const{k:n,v:r,l:i,r:l}=e;let s=[];return n>t&&(s=s.concat(de(i,t,o))),n>=t&&n<=o&&s.push({k:n,v:r}),n<=o&&(s=s.concat(de(l,t,o))),s}function he(e){return ie(e)?[]:[...he(e.l),{k:e.k,v:e.v},...he(e.r)]}function fe(e){return ie(e.r)?[e.k,e.v]:fe(e.r)}function me(e){return ie(e.r)?e.l:ve(ge(e,{r:me(e.r)}))}function ge(e,t){return re(void 0!==t.k?t.k:e.k,void 0!==t.v?t.v:e.v,void 0!==t.lvl?t.lvl:e.lvl,void 0!==t.l?t.l:e.l,void 0!==t.r?t.r:e.r)}function pe(e){return ie(e)||e.lvl>e.r.lvl}function xe(e){return we(Se(e))}function ve(e){const{l:t,r:o,lvl:n}=e;if(o.lvl>=n-1&&t.lvl>=n-1)return e;if(n>o.lvl+1){if(pe(t))return Se(ge(e,{lvl:n-1}));if(ie(t)||ie(t.r))throw new Error("Unexpected empty nodes");return ge(t.r,{l:ge(t,{r:t.r.l}),r:ge(e,{l:t.r.r,lvl:n-1}),lvl:n})}if(pe(e))return we(ge(e,{lvl:n-1}));if(ie(o)||ie(o.l))throw new Error("Unexpected empty nodes");{const t=o.l,r=pe(t)?o.lvl-1:o.lvl;return ge(t,{l:ge(e,{r:t.l,lvl:n-1}),r:we(ge(o,{l:t.r,lvl:r})),lvl:t.lvl+1})}}function Ie(e,t,o){if(ie(e))return[];const n=ae(e,t)[0];return Te(de(e,n,o),(({k:e,v:t})=>({index:e,value:t})))}function Te(e,t){const o=e.length;if(0===o)return[];let{index:n,value:r}=t(e[0]);const i=[];for(let l=1;l<o;l++){const{index:o,value:s}=t(e[l]);i.push({start:n,end:o-1,value:r}),n=o,r=s}return i.push({start:n,end:1/0,value:r}),i}function we(e){const{r:t,lvl:o}=e;return ie(t)||ie(t.r)||t.lvl!==o||t.r.lvl!==o?e:ge(t,{l:ge(e,{r:t.l}),lvl:o+1})}function Se(e){const{l:t}=e;return ie(t)||t.lvl!==e.lvl?e:ge(t,{r:ge(e,{l:t.r})})}function Ce(e,t,o,n=0){let r=e.length-1;for(;n<=r;){const i=Math.floor((n+r)/2),l=o(e[i],t);if(0===l)return i;if(-1===l){if(r-n<2)return i-1;r=i-1}else{if(r===n)return i;n=i+1}}throw new Error(`Failed binary finding record in array - ${e.join(",")}, searched for ${t}`)}function He(e,t,o){return e[Ce(e,t,o)]}const be=D((()=>({recalcInProgress:C(!1)})),[],{singleton:!0});function ye(e){const{size:t,startIndex:o,endIndex:n}=e;return e=>e.start===o&&(e.end===n||e.end===1/0)&&e.value===t}function Re(e,t){let o=0,n=0;for(;o<e;)o+=t[n+1]-t[n]-1,n++;return n-(o===e?0:1)}function ze({index:e},t){return t===e?0:t<e?-1:1}function Ee({offset:e},t){return t===e?0:t<e?-1:1}function ke(e){return{index:e.index,value:e}}function Be(e,t,o,n=0){return n>0&&(t=Math.max(t,He(e,n,ze).offset)),Te(function(e,t,o,n){const r=Ce(e,t,n),i=Ce(e,o,n,r);return e.slice(r,i+1)}(e,t,o,Ee),ke)}function Le(e,t,o,n){let r=e,i=0,l=0,s=0,c=0;if(0!==t){c=Ce(r,t-1,ze);s=r[c].offset;const e=ae(o,t-1);i=e[0],l=e[1],r.length&&r[c].size===ae(o,t)[1]&&(c-=1),r=r.slice(0,c+1)}else r=[];for(const{start:a,value:u}of Ie(o,t,1/0)){const e=a-i,t=e*l+s+e*n;r.push({offset:t,size:u,index:a}),i=a,s=t,l=u}return{offsetTree:r,lastIndex:i,lastOffset:s,lastSize:l}}function Oe(e,[t,o,n,r]){t.length>0&&n("received item sizes",t,$.DEBUG);const i=e.sizeTree;let l=i,s=0;if(o.length>0&&ie(i)&&2===t.length){const e=t[0].size,n=t[1].size;l=o.reduce(((t,o)=>ue(ue(t,o,e),o+1,n)),l)}else[l,s]=function(e,t){let o=ie(e)?0:1/0;for(const n of t){const{size:t,startIndex:r,endIndex:i}=n;if(o=Math.min(o,r),ie(e)){e=ue(e,0,t);continue}const l=Ie(e,r-1,i+1);if(l.some(ye(n)))continue;let s=!1,c=!1;for(const{start:o,end:n,value:a}of l)s?(i>=o||t===a)&&(e=se(e,o)):(c=a!==t,s=!0),n>i&&i>=o&&a!==t&&(e=ue(e,i+1,a));c&&(e=ue(e,r,t))}return[e,o]}(l,t);if(l===i)return e;const{offsetTree:c,lastIndex:a,lastSize:u,lastOffset:d}=Le(e.offsetTree,s,l,r);return{sizeTree:l,offsetTree:c,lastIndex:a,lastOffset:d,lastSize:u,groupOffsetTree:o.reduce(((e,t)=>ue(e,t,Fe(t,c,r))),le()),groupIndices:o}}function Fe(e,t,o){if(0===t.length)return 0;const{offset:n,index:r,size:i}=He(t,e,ze),l=e-r,s=i*l+(l-1)*o+n;return s>0?s+o:s}function Me(e,t,o){if(function(e){return"undefined"!==typeof e.groupIndex}(e))return t.groupIndices[e.groupIndex]+1;{let n=Pe("LAST"===e.index?o:e.index,t);return n=Math.max(0,n,Math.min(o,n)),n}}function Pe(e,t){if(!We(t))return e;let o=0;for(;t.groupIndices[o]<=e+o;)o++;return e+o}function We(e){return!ie(e.groupOffsetTree)}const je={offsetHeight:"height",offsetWidth:"width"},Ae=D((([{log:e},{recalcInProgress:t}])=>{const o=S(),n=S(),r=b(n,0),i=S(),l=S(),s=C(0),c=C([]),a=C(void 0),u=C(void 0),d=C(((e,t)=>Q(e,je[t]))),h=C(void 0),f=C(0),m={offsetTree:[],sizeTree:le(),groupOffsetTree:le(),lastIndex:0,lastOffset:0,lastSize:0,groupIndices:[]},g=b(y(o,P(c,e,f),L(Oe,m),z()),m),v=b(y(c,z(),L(((e,t)=>({prev:e.current,current:t})),{prev:[],current:[]}),k((({prev:e})=>e))),[]);T(y(c,E((e=>e.length>0)),P(g,f),k((([e,t,o])=>{const n=e.reduce(((e,n,r)=>ue(e,n,Fe(n,t.offsetTree,o)||r)),le());return{...t,groupIndices:e,groupOffsetTree:n}}))),g),T(y(n,P(g),E((([e,{lastIndex:t}])=>e<t)),k((([e,{lastIndex:t,lastSize:o}])=>[{startIndex:e,endIndex:t,size:o}]))),o),T(a,u);const w=b(y(a,k((e=>void 0===e))),!0);T(y(u,E((e=>void 0!==e&&ie(I(g).sizeTree))),k((e=>[{startIndex:0,endIndex:0,size:e}]))),o);const R=H(y(o,P(g),L((({sizes:e},[t,o])=>({changed:o!==e,sizes:o})),{changed:!1,sizes:m}),k((e=>e.changed))));p(y(s,L(((e,t)=>({diff:e.prev-t,prev:t})),{diff:0,prev:0}),k((e=>e.diff))),(e=>{const{groupIndices:o}=I(g);if(e>0)x(t,!0),x(i,e+Re(e,o));else if(e<0){const t=I(v);t.length>0&&(e-=Re(-e,t)),x(l,e)}})),p(y(s,P(e)),(([e,t])=>{e<0&&t("`firstItemIndex` prop should not be set to less than zero. If you don't know the total count, just use a very high value",{firstItemIndex:s},$.ERROR)}));const B=H(i);T(y(i,P(g),k((([e,t])=>{const o=t.groupIndices.length>0,n=[],r=t.lastSize;if(o){const o=ce(t.sizeTree,0);let i=0,l=0;for(;i<e;){const e=t.groupIndices[l],s=t.groupIndices.length===l+1?1/0:t.groupIndices[l+1]-e-1;n.push({startIndex:e,endIndex:e,size:o}),n.push({startIndex:e+1,endIndex:e+1+s-1,size:r}),l++,i+=s+1}const s=he(t.sizeTree);return i!==e&&s.shift(),s.reduce(((t,{k:o,v:n})=>{let r=t.ranges;return 0!==t.prevSize&&(r=[...t.ranges,{startIndex:t.prevIndex,endIndex:o+e-1,size:t.prevSize}]),{ranges:r,prevIndex:o+e,prevSize:n}}),{ranges:n,prevIndex:e,prevSize:0}).ranges}return he(t.sizeTree).reduce(((t,{k:o,v:n})=>({ranges:[...t.ranges,{startIndex:t.prevIndex,endIndex:o+e-1,size:t.prevSize}],prevIndex:o+e,prevSize:n})),{ranges:[],prevIndex:0,prevSize:r}).ranges}))),o);const O=H(y(l,P(g,f),k((([e,{offsetTree:t},o])=>Fe(-e,t,o)))));return T(y(l,P(g,f),k((([e,t,o])=>{if(t.groupIndices.length>0){if(ie(t.sizeTree))return t;let n=le();const r=I(v);let i=0,l=0,s=0;for(;i<-e;){s=r[l];const e=r[l+1]-s-1;l++,i+=e+1}n=he(t.sizeTree).reduce(((t,{k:o,v:n})=>ue(t,Math.max(0,o+e),n)),n);if(i!==-e){n=ue(n,0,ce(t.sizeTree,s));n=ue(n,1,ae(t.sizeTree,1-e)[1])}return{...t,sizeTree:n,...Le(t.offsetTree,0,n,o)}}{const n=he(t.sizeTree).reduce(((t,{k:o,v:n})=>ue(t,Math.max(0,o+e),n)),le());return{...t,sizeTree:n,...Le(t.offsetTree,0,n,o)}}}))),g),{data:h,totalCount:n,sizeRanges:o,groupIndices:c,defaultItemSize:u,fixedItemSize:a,unshiftWith:i,shiftWith:l,shiftWithOffset:O,beforeUnshiftWith:B,firstItemIndex:s,gap:f,sizes:g,listRefresh:R,statefulTotalCount:r,trackItemSizes:w,itemSize:d}}),d(q,be),{singleton:!0}),De="undefined"!==typeof document&&"scrollBehavior"in document.documentElement.style;function Ve(e){const t="number"===typeof e?{index:e}:e;return t.align||(t.align="start"),t.behavior&&De||(t.behavior="auto"),t.offset||(t.offset=0),t}const Ne=D((([{sizes:e,totalCount:t,listRefresh:o,gap:n},{scrollingInProgress:r,viewportHeight:i,scrollTo:l,smoothScrollTargetReached:s,headerHeight:c,footerHeight:a,fixedHeaderHeight:u,fixedFooterHeight:d},{log:h}])=>{const f=S(),m=S(),g=C(0);let v=null,I=null,H=null;function b(){v&&(v(),v=null),H&&(H(),H=null),I&&(clearTimeout(I),I=null),x(r,!1)}return T(y(f,P(e,i,t,g,c,a,h),P(n,u,d),k((([[e,t,n,i,l,c,a,u],d,h,g])=>{const T=Ve(e),{align:S,behavior:C,offset:R}=T,z=i-1,E=Me(T,t,z);let k=Fe(E,t.offsetTree,d)+c;"end"===S?(k+=h+ae(t.sizeTree,E)[1]-n+g,E===z&&(k+=a)):"center"===S?k+=(h+ae(t.sizeTree,E)[1]-n+g)/2:k-=l,R&&(k+=R);const B=t=>{b(),t?(u("retrying to scroll to",{location:e},$.DEBUG),x(f,e)):(x(m,!0),u("list did not change, scroll successful",{},$.DEBUG))};if(b(),"smooth"===C){let e=!1;H=p(o,(t=>{e=e||t})),v=w(s,(()=>{B(e)}))}else v=w(y(o,(L=150,e=>{const t=setTimeout((()=>{e(!1)}),L);return o=>{o&&(e(!0),clearTimeout(t))}})),B);var L;return I=setTimeout((()=>{b()}),1200),x(r,!0),u("scrolling from index to",{index:E,top:k,behavior:C},$.DEBUG),{top:k,behavior:C}}))),l),{scrollToIndex:f,scrollTargetReached:m,topListHeight:g}}),d(Ae,oe,q),{singleton:!0});const Ge="up",_e="down",Ue={atBottom:!1,notAtBottomBecause:"NOT_SHOWING_LAST_ITEM",state:{offsetBottom:0,scrollTop:0,viewportHeight:0,scrollHeight:0}},$e=D((([{scrollContainerState:e,scrollTop:t,viewportHeight:o,headerHeight:n,footerHeight:r,scrollBy:i}])=>{const l=C(!1),s=C(!0),c=S(),a=S(),u=C(4),d=C(0),h=b(y(W(y(j(t),O(1),B(!0)),y(j(t),O(1),B(!1),M(100))),z()),!1),f=b(y(W(y(i,B(!0)),y(i,B(!1),M(200))),z()),!1);T(y(A(j(t),j(d)),k((([e,t])=>e<=t)),z()),s),T(y(s,F(50)),a);const m=H(y(A(e,j(o),j(n),j(r),j(u)),L(((e,[{scrollTop:t,scrollHeight:o},n,r,i,l])=>{const s={viewportHeight:n,scrollTop:t,scrollHeight:o};if(t+n-o>-l){let o,n;return t>e.state.scrollTop?(o="SCROLLED_DOWN",n=e.state.scrollTop-t):(o="SIZE_DECREASED",n=e.state.scrollTop-t||e.scrollTopDelta),{atBottom:!0,state:s,atBottomBecause:o,scrollTopDelta:n}}let c;return c=s.scrollHeight>e.state.scrollHeight?"SIZE_INCREASED":n<e.state.viewportHeight?"VIEWPORT_HEIGHT_DECREASING":t<e.state.scrollTop?"SCROLLING_UPWARDS":"NOT_FULLY_SCROLLED_TO_LAST_ITEM_BOTTOM",{atBottom:!1,notAtBottomBecause:c,state:s}}),Ue),z(((e,t)=>e&&e.atBottom===t.atBottom)))),g=b(y(e,L(((e,{scrollTop:t,scrollHeight:o,viewportHeight:n})=>{if(ee(e.scrollHeight,o))return{scrollTop:t,scrollHeight:o,jump:0,changed:!1};{const r=o-(t+n)<1;return e.scrollTop!==t&&r?{scrollHeight:o,scrollTop:t,jump:e.scrollTop-t,changed:!0}:{scrollHeight:o,scrollTop:t,jump:0,changed:!0}}}),{scrollHeight:0,jump:0,scrollTop:0,changed:!1}),E((e=>e.changed)),k((e=>e.jump))),0);T(y(m,k((e=>e.atBottom))),l),T(y(l,F(50)),c);const p=C(_e);T(y(e,k((({scrollTop:e})=>e)),z(),L(((e,t)=>I(f)?{direction:e.direction,prevScrollTop:t}:{direction:t<e.prevScrollTop?Ge:_e,prevScrollTop:t}),{direction:_e,prevScrollTop:0}),k((e=>e.direction))),p),T(y(e,F(50),B("none")),p);const x=C(0);return T(y(h,E((e=>!e)),B(0)),x),T(y(t,F(100),P(h),E((([e,t])=>!!t)),L((([e,t],[o])=>[t,o]),[0,0]),k((([e,t])=>t-e))),x),{isScrolling:h,isAtTop:s,isAtBottom:l,atBottomState:m,atTopStateChange:a,atBottomStateChange:c,scrollDirection:p,atBottomThreshold:u,atTopThreshold:d,scrollVelocity:x,lastJumpDueToItemResize:g}}),d(oe)),Ke=D((([{log:e}])=>{const t=C(!1),o=H(y(t,E((e=>e)),z()));return p(t,(t=>{t&&I(e)("props updated",{},$.DEBUG)})),{propsReady:t,didMount:o}}),d(q),{singleton:!0});function qe(e,t){0==e?t():requestAnimationFrame((()=>qe(e-1,t)))}function Ye(e,t){const o=t-1;return"number"===typeof e?e:"LAST"===e.index?o:e.index}const Ze=D((([{sizes:e,listRefresh:t,defaultItemSize:o},{scrollTop:n},{scrollToIndex:r,scrollTargetReached:i},{didMount:l}])=>{const s=C(!0),c=C(0),a=C(!0);return T(y(l,P(c),E((([e,t])=>!!t)),B(!1)),s),T(y(l,P(c),E((([e,t])=>!!t)),B(!1)),a),p(y(A(t,l),P(s,e,o,a),E((([[,e],t,{sizeTree:o},n,r])=>e&&(!ie(o)||m(n))&&!t&&!r)),P(c)),(([,e])=>{w(i,(()=>{x(a,!0)})),qe(4,(()=>{w(n,(()=>{x(s,!0)})),x(r,e)}))})),{scrolledToInitialItem:s,initialTopMostItemIndex:c,initialItemFinalLocationReached:a}}),d(Ae,oe,Ne,Ke),{singleton:!0});function Xe(e){return!!e&&("smooth"===e?"smooth":"auto")}const Je=D((([{totalCount:e,listRefresh:t},{isAtBottom:o,atBottomState:n},{scrollToIndex:r},{scrolledToInitialItem:i},{propsReady:l,didMount:s},{log:c},{scrollingInProgress:a}])=>{const u=C(!1),d=S();let h=null;function f(e){x(r,{index:"LAST",align:"end",behavior:e})}function m(e){const t=w(n,(t=>{!e||t.atBottom||"SIZE_INCREASED"!==t.notAtBottomBecause||h||(I(c)("scrolling to bottom due to increased size",{},$.DEBUG),f("auto"))}));setTimeout(t,100)}return p(y(A(y(j(e),O(1)),s),P(j(u),o,i,a),k((([[e,t],o,n,r,i])=>{let l=t&&r,s="auto";return l&&(s=((e,t)=>"function"===typeof e?Xe(e(t)):t&&Xe(e))(o,n||i),l=l&&!!s),{totalCount:e,shouldFollow:l,followOutputBehavior:s}})),E((({shouldFollow:e})=>e))),(({totalCount:e,followOutputBehavior:o})=>{h&&(h(),h=null),h=w(t,(()=>{I(c)("following output to ",{totalCount:e},$.DEBUG),f(o),h=null}))})),p(y(A(j(u),e,l),E((([e,,t])=>e&&t)),L((({value:e},[,t])=>({refreshed:e===t,value:t})),{refreshed:!1,value:0}),E((({refreshed:e})=>e)),P(u,e)),(([,e])=>{I(i)&&m(!1!==e)})),p(d,(()=>{m(!1!==I(u))})),p(A(j(u),n),(([e,t])=>{e&&!t.atBottom&&"VIEWPORT_HEIGHT_DECREASING"===t.notAtBottomBecause&&f("auto")})),{followOutput:u,autoscrollToBottom:d}}),d(Ae,$e,Ne,Ze,Ke,q,oe));function Qe(e){return e.reduce(((e,t)=>(e.groupIndices.push(e.totalCount),e.totalCount+=t+1,e)),{totalCount:0,groupIndices:[]})}const et=D((([{totalCount:e,groupIndices:t,sizes:o},{scrollTop:n,headerHeight:r}])=>{const i=S(),l=S(),s=H(y(i,k(Qe)));return T(y(s,k((e=>e.totalCount))),e),T(y(s,k((e=>e.groupIndices))),t),T(y(A(n,o,r),E((([e,t])=>We(t))),k((([e,t,o])=>ae(t.groupOffsetTree,Math.max(e-o,0),"v")[0])),z(),k((e=>[e]))),l),{groupCounts:i,topItemsIndexes:l}}),d(Ae,oe));function tt(e,t){return!(!e||e[0]!==t[0]||e[1]!==t[1])}function ot(e,t){return!(!e||e.startIndex!==t.startIndex||e.endIndex!==t.endIndex)}const nt="top",rt="bottom",it="none";function lt(e,t,o){return"number"===typeof e?o===Ge&&t===nt||o===_e&&t===rt?e:0:o===Ge?t===nt?e.main:e.reverse:t===rt?e.main:e.reverse}function st(e,t){return"number"===typeof e?e:e[t]||0}const ct=D((([{scrollTop:e,viewportHeight:t,deviation:o,headerHeight:n,fixedHeaderHeight:r}])=>{const i=S(),l=C(0),s=C(0),c=C(0);return{listBoundary:i,overscan:c,topListHeight:l,increaseViewportBy:s,visibleRange:b(y(A(j(e),j(t),j(n),j(i,tt),j(c),j(l),j(r),j(o),j(s)),k((([e,t,o,[n,r],i,l,s,c,a])=>{const u=e-c,d=l+s,h=Math.max(o-u,0);let f=it;const m=st(a,nt),g=st(a,rt);return n-=c,r+=o+s,(n+=o+s)>e+d-m&&(f=Ge),(r-=c)<e-h+t+g&&(f=_e),f!==it?[Math.max(u-o-lt(i,nt,f)-m,0),u-h-s+t+lt(i,rt,f)+g]:null})),E((e=>null!=e)),z(tt)),[0,0])}}),d(oe),{singleton:!0});const at={items:[],topItems:[],offsetTop:0,offsetBottom:0,top:0,bottom:0,topListHeight:0,totalCount:0,firstItemIndex:0};function ut(e,t,o){if(0===e.length)return[];if(!We(t))return e.map((e=>({...e,index:e.index+o,originalIndex:e.index})));const n=e[0].index,r=e[e.length-1].index,i=[],l=Ie(t.groupOffsetTree,n,r);let s,c=0;for(const a of e){let e;(!s||s.end<a.index)&&(s=l.shift(),c=t.groupIndices.indexOf(s.start)),e=a.index===s.start?{type:"group",index:c}:{index:a.index-(c+1)+o,groupIndex:c},i.push({...e,size:a.size,offset:a.offset,originalIndex:a.index,data:a.data})}return i}function dt(e,t,o,n,r,i){const{lastSize:l,lastOffset:s,lastIndex:c}=r;let a=0,u=0;if(e.length>0){a=e[0].offset;const t=e[e.length-1];u=t.offset+t.size}const d=o-c,h=a,f=s+d*l+(d-1)*n-u;return{items:ut(e,r,i),topItems:ut(t,r,i),topListHeight:t.reduce(((e,t)=>t.size+e),0),offsetTop:a,offsetBottom:f,top:h,bottom:u,totalCount:o,firstItemIndex:i}}function ht(e,t,o,n,r,i){let l=0;if(o.groupIndices.length>0)for(const a of o.groupIndices){if(a-l>=e)break;l++}const s=e+l,c=Ye(t,s);return dt(Array.from({length:s}).map(((e,t)=>({index:t+c,size:0,offset:0,data:i[t+c]}))),[],s,r,o,n)}const ft=D((([{sizes:e,totalCount:t,data:o,firstItemIndex:n,gap:r},i,{visibleRange:l,listBoundary:s,topListHeight:c},{scrolledToInitialItem:a,initialTopMostItemIndex:d},{topListHeight:h},f,{didMount:g},{recalcInProgress:p}])=>{const x=C([]),v=C(0),w=S();T(i.topItemsIndexes,x);const R=b(y(A(g,p,j(l,tt),j(t),j(e),j(d),a,j(x),j(n),j(r),o),E((([e,t,,o,,,,,,,n])=>{const r=n&&n.length!==o;return e&&!t&&!r})),k((([,,[e,t],o,n,r,i,l,s,c,a])=>{const d=n,{sizeTree:h,offsetTree:f}=d,m=I(v);if(0===o)return{...at,totalCount:o};if(0===e&&0===t)return 0===m?{...at,totalCount:o}:ht(m,r,n,s,c,a||[]);if(ie(h)){if(m>0)return null;const e=dt(function(e,t,o){if(We(t)){const n=Pe(e,t);return[{index:ae(t.groupOffsetTree,n)[0],size:0,offset:0},{index:n,size:0,offset:0,data:o&&o[0]}]}return[{index:e,size:0,offset:0,data:o&&o[0]}]}(Ye(r,o),d,a),[],o,c,d,s);return e}const g=[];if(l.length>0){const e=l[0],t=l[l.length-1];let o=0;for(const n of Ie(h,e,t)){const r=n.value,i=Math.max(n.start,e),l=Math.min(n.end,t);for(let e=i;e<=l;e++)g.push({index:e,size:r,offset:o,data:a&&a[e]}),o+=r}}if(!i)return dt([],g,o,c,d,s);const p=l.length>0?l[l.length-1]+1:0,x=Be(f,e,t,p);if(0===x.length)return null;const T=o-1;return dt(u([],(o=>{for(const n of x){const r=n.value;let i=r.offset,l=n.start;const s=r.size;if(r.offset<e){l+=Math.floor((e-r.offset+c)/(s+c));const t=l-n.start;i+=t*s+t*c}l<p&&(i+=(p-l)*s,l=p);const u=Math.min(n.end,T);for(let e=l;e<=u&&!(i>=t);e++)o.push({index:e,size:s,offset:i,data:a&&a[e]}),i+=s+c}})),g,o,c,d,s)})),E((e=>null!==e)),z()),at);T(y(o,E(m),k((e=>null==e?void 0:e.length))),t),T(y(R,k((e=>e.topListHeight))),h),T(h,c),T(y(R,k((e=>[e.top,e.bottom]))),s),T(y(R,k((e=>e.items))),w);return{listState:R,topItemsIndexes:x,endReached:H(y(R,E((({items:e})=>e.length>0)),P(t,o),E((([{items:e},t])=>e[e.length-1].originalIndex===t-1)),k((([,e,t])=>[e-1,t])),z(tt),k((([e])=>e)))),startReached:H(y(R,F(200),E((({items:e,topItems:t})=>e.length>0&&e[0].originalIndex===t.length)),k((({items:e})=>e[0].index)),z())),rangeChanged:H(y(R,E((({items:e})=>e.length>0)),k((({items:e})=>{let t=0,o=e.length-1;for(;"group"===e[t].type&&t<o;)t++;for(;"group"===e[o].type&&o>t;)o--;return{startIndex:e[t].index,endIndex:e[o].index}})),z(ot))),itemsRendered:w,initialItemCount:v,...f}}),d(Ae,et,ct,Ze,Ne,$e,Ke,be),{singleton:!0}),mt=D((([{sizes:e,firstItemIndex:t,data:o,gap:n},{initialTopMostItemIndex:r},{initialItemCount:i,listState:l},{didMount:s}])=>(T(y(s,P(i),E((([,e])=>0!==e)),P(r,e,t,n,o),k((([[,e],t,o,n,r,i=[]])=>ht(e,t,o,n,r,i)))),l),{})),d(Ae,Ze,ft,Ke),{singleton:!0}),gt=D((([{scrollVelocity:e}])=>{const t=C(!1),o=S(),n=C(!1);return T(y(e,P(n,t,o),E((([e,t])=>!!t)),k((([e,t,o,n])=>{const{exit:r,enter:i}=t;if(o){if(r(e,n))return!1}else if(i(e,n))return!0;return o})),z()),t),p(y(A(t,e,o),P(n)),(([[e,t,o],n])=>e&&n&&n.change&&n.change(t,o))),{isSeeking:t,scrollSeekConfiguration:n,scrollVelocity:e,scrollSeekRangeChanged:o}}),d($e),{singleton:!0}),pt=D((([{topItemsIndexes:e}])=>{const t=C(0);return T(y(t,E((e=>e>0)),k((e=>Array.from({length:e}).map(((e,t)=>t))))),e),{topItemCount:t}}),d(ft)),xt=D((([{footerHeight:e,headerHeight:t,fixedHeaderHeight:o,fixedFooterHeight:n},{listState:r}])=>{const i=S(),l=b(y(A(e,n,t,o,r),k((([e,t,o,n,r])=>e+t+o+n+r.offsetBottom+r.bottom))),0);return T(j(l),i),{totalListHeight:l,totalListHeightChanged:i}}),d(oe,ft),{singleton:!0});function vt(e){let t,o=!1;return()=>(o||(o=!0,t=e()),t)}const It=vt((()=>/iP(ad|od|hone)/i.test(navigator.userAgent)&&/WebKit/i.test(navigator.userAgent))),Tt=D((([{scrollBy:e,scrollTop:t,deviation:o,scrollingInProgress:n},{isScrolling:r,isAtBottom:i,scrollDirection:l,lastJumpDueToItemResize:s},{listState:c},{beforeUnshiftWith:a,shiftWithOffset:u,sizes:d,gap:h},{log:f},{recalcInProgress:m}])=>{const g=H(y(c,P(s),L((([,e,t,o],[{items:n,totalCount:r,bottom:i,offsetBottom:l},s])=>{const c=i+l;let a=0;if(t===r&&e.length>0&&n.length>0){0===n[0].originalIndex&&0===e[0].originalIndex||(a=c-o,0!==a&&(a+=s))}return[a,n,r,c]}),[0,[],0,0]),E((([e])=>0!==e)),P(t,l,n,i,f,m),E((([,e,t,o,,,n])=>!n&&!o&&0!==e&&t===Ge)),k((([[e],,,,,t])=>(t("Upward scrolling compensation",{amount:e},$.DEBUG),e)))));function v(t){t>0?(x(e,{top:-t,behavior:"auto"}),x(o,0)):(x(o,0),x(e,{top:-t,behavior:"auto"}))}return p(y(g,P(o,r)),(([e,t,n])=>{n&&It()?x(o,t-e):v(-e)})),p(y(A(b(r,!1),o,m),E((([e,t,o])=>!e&&!o&&0!==t)),k((([e,t])=>t)),F(1)),v),T(y(u,k((e=>({top:-e})))),e),p(y(a,P(d,h),k((([e,{lastSize:t,groupIndices:o,sizeTree:n},r])=>{function i(e){return e*(t+r)}if(0===o.length)return i(e);{let t=0;const r=ce(n,0);let l=0,s=0;for(;l<e;){l++,t+=r;let n=o.length===s+1?1/0:o[s+1]-o[s]-1;l+n>e&&(t-=r,n=e-l+1),l+=n,t+=i(n),s++}return t}}))),(t=>{x(o,t),requestAnimationFrame((()=>{x(e,{top:t}),requestAnimationFrame((()=>{x(o,0),x(m,!1)}))}))})),{deviation:o}}),d(oe,$e,ft,Ae,q,be)),wt=D((([{didMount:e},{scrollTo:t},{listState:o}])=>{const n=C(0);return p(y(e,P(n),E((([,e])=>0!==e)),k((([,e])=>({top:e})))),(e=>{w(y(o,O(1),E((e=>e.items.length>1))),(()=>{requestAnimationFrame((()=>{x(t,e)}))}))})),{initialScrollTop:n}}),d(Ke,oe,ft),{singleton:!0}),St=D((([{viewportHeight:e},{totalListHeight:t}])=>{const o=C(!1);return{alignToBottom:o,paddingTopAddition:b(y(A(o,e,t),E((([e])=>e)),k((([,e,t])=>Math.max(0,e-t))),F(0),z()),0)}}),d(oe,xt),{singleton:!0}),Ct=D((([{scrollTo:e,scrollContainerState:t}])=>{const o=S(),n=S(),r=S(),i=C(!1),l=C(void 0);return T(y(A(o,n),k((([{viewportHeight:e,scrollTop:t,scrollHeight:o},{offsetTop:n}])=>({scrollTop:Math.max(0,t-n),scrollHeight:o,viewportHeight:e})))),t),T(y(e,P(n),k((([e,{offsetTop:t}])=>({...e,top:e.top+t})))),r),{useWindowScroll:i,customScrollParent:l,windowScrollContainerState:o,windowViewportRect:n,windowScrollTo:r}}),d(oe)),Ht=({itemTop:e,itemBottom:t,viewportTop:o,viewportBottom:n,locationParams:{behavior:r,align:i,...l}})=>e<o?{...l,behavior:r,align:null!=i?i:"start"}:t>n?{...l,behavior:r,align:null!=i?i:"end"}:null,bt=D((([{sizes:e,totalCount:t,gap:o},{scrollTop:n,viewportHeight:r,headerHeight:i,fixedHeaderHeight:l,fixedFooterHeight:s,scrollingInProgress:c},{scrollToIndex:a}])=>{const u=S();return T(y(u,P(e,r,t,i,l,s,n),P(o),k((([[e,t,o,n,r,i,l,s],a])=>{const{done:u,behavior:d,align:h,calculateViewLocation:f=Ht,...m}=e,g=Me(e,t,n-1),p=Fe(g,t.offsetTree,a)+r+i,x=f({itemTop:p,itemBottom:p+ae(t.sizeTree,g)[1],viewportTop:s+i,viewportBottom:s+o-l,locationParams:{behavior:d,align:h,...m}});return x?u&&w(y(c,E((e=>!1===e)),O(I(c)?1:2)),u):u&&u(),x})),E((e=>null!==e))),a),{scrollIntoView:u}}),d(Ae,oe,Ne,ft,q),{singleton:!0}),yt=D((([{sizes:e,sizeRanges:t},{scrollTop:o,headerHeight:n},{initialTopMostItemIndex:r},{didMount:i},{useWindowScroll:l,windowScrollContainerState:s,windowViewportRect:c}])=>{const a=S(),u=C(void 0),d=C(null),h=C(null);return T(s,d),T(c,h),p(y(a,P(e,o,l,d,h,n)),(([e,t,o,n,r,i,l])=>{const s=he(t.sizeTree).map((({k:e,v:t},o,n)=>{const r=n[o+1];return{startIndex:e,endIndex:r?r.k-1:1/0,size:t}}));n&&null!==r&&null!==i&&(o=r.scrollTop-i.offsetTop),e({ranges:s,scrollTop:o-=l})})),T(y(u,E(m),k(Rt)),r),T(y(i,P(u),E((([,e])=>void 0!==e)),z(),k((([,e])=>e.ranges))),t),{getState:a,restoreStateFrom:u}}),d(Ae,oe,Ze,Ke,Ct));function Rt(e){return{offset:e.scrollTop,index:0,align:"start"}}const zt=D((([e,t,o,n,r,i,l,s,c,a])=>({...e,...t,...o,...n,...r,...i,...l,...s,...c,...a})),d(ct,mt,Ke,gt,xt,wt,St,Ct,bt,q)),Et=D((([{totalCount:e,sizeRanges:t,fixedItemSize:o,defaultItemSize:n,trackItemSizes:r,itemSize:i,data:l,firstItemIndex:s,groupIndices:c,statefulTotalCount:a,gap:u,sizes:d},{initialTopMostItemIndex:h,scrolledToInitialItem:f,initialItemFinalLocationReached:m},g,p,x,{listState:v,topItemsIndexes:I,...w},{scrollToIndex:S},C,{topItemCount:H},{groupCounts:b},R])=>(T(w.rangeChanged,R.scrollSeekRangeChanged),T(y(R.windowViewportRect,k((e=>e.visibleHeight))),g.viewportHeight),{totalCount:e,data:l,firstItemIndex:s,sizeRanges:t,initialTopMostItemIndex:h,scrolledToInitialItem:f,initialItemFinalLocationReached:m,topItemsIndexes:I,topItemCount:H,groupCounts:b,fixedItemHeight:o,defaultItemHeight:n,gap:u,...x,statefulTotalCount:a,listState:v,scrollToIndex:S,trackItemSizes:r,itemSize:i,groupIndices:c,...w,...R,...g,sizes:d,...p})),d(Ae,Ze,oe,yt,Je,ft,Ne,Tt,pt,et,zt)),kt="-webkit-sticky",Bt="sticky",Lt=vt((()=>{if("undefined"===typeof document)return Bt;const e=document.createElement("div");return e.style.position=kt,e.style.position===kt?kt:Bt}));function Ot(e,t,o){const n=r.useRef(null),i=r.useCallback((o=>{if(null===o||!o.offsetParent)return;const r=o.getBoundingClientRect(),i=r.width;let l,s;if(t){const e=t.getBoundingClientRect(),o=r.top-e.top;l=e.height-Math.max(0,o),s=o+t.scrollTop}else l=window.innerHeight-Math.max(0,r.top),s=r.top+window.pageYOffset;n.current={offsetTop:s,visibleHeight:l,visibleWidth:i},e(n.current)}),[e,t]),{callbackRef:l,ref:s}=Y(i,!0,o),c=r.useCallback((()=>{i(s.current)}),[i,s]);return r.useEffect((()=>{if(t){t.addEventListener("scroll",c);const e=new ResizeObserver((()=>{requestAnimationFrame(c)}));return e.observe(t),()=>{t.removeEventListener("scroll",c),e.unobserve(t)}}return window.addEventListener("scroll",c),window.addEventListener("resize",c),()=>{window.removeEventListener("scroll",c),window.removeEventListener("resize",c)}}),[c,t]),l}const Ft=r.createContext(void 0),Mt=r.createContext(void 0);function Pt(e){return e}const Wt=D((([e,t])=>({...e,...t})),d(Et,D((()=>{const e=C((e=>`Item ${e}`)),t=C(null),o=C((e=>`Group ${e}`)),n=C({}),r=C(Pt),i=C("div"),l=C(g),s=(e,t=null)=>b(y(n,k((t=>t[e])),z()),t);return{context:t,itemContent:e,groupContent:o,components:n,computeItemKey:r,HeaderFooterTag:i,scrollerRef:l,FooterComponent:s("Footer"),HeaderComponent:s("Header"),TopItemListComponent:s("TopItemList"),ListComponent:s("List","div"),ItemComponent:s("Item","div"),GroupComponent:s("Group","div"),ScrollerComponent:s("Scroller","div"),EmptyPlaceholder:s("EmptyPlaceholder"),ScrollSeekPlaceholder:s("ScrollSeekPlaceholder")}})))),jt=({height:e})=>(0,n.jsx)("div",{style:{height:e}}),At={position:Lt(),zIndex:1,overflowAnchor:"none"},Dt={overflowAnchor:"none"},Vt={...Dt,display:"inline-block",height:"100%"},Nt=r.memo((function({showTopList:e=!1}){const t=io("listState"),o=ro("sizeRanges"),i=io("useWindowScroll"),l=io("customScrollParent"),s=ro("windowScrollContainerState"),c=ro("scrollContainerState"),a=l||i?s:c,u=io("itemContent"),d=io("context"),h=io("groupContent"),f=io("trackItemSizes"),m=io("itemSize"),p=io("log"),x=ro("gap"),v=io("horizontalDirection"),{callbackRef:I}=X(o,m,f,e?g:a,p,x,l,v,io("skipAnimationFrameInResizeObserver")),[T,w]=r.useState(0);lo("deviation",(e=>{T!==e&&w(e)}));const S=io("EmptyPlaceholder"),C=io("ScrollSeekPlaceholder")||jt,H=io("ListComponent"),b=io("ItemComponent"),y=io("GroupComponent"),R=io("computeItemKey"),z=io("isSeeking"),E=io("groupIndices").length>0,k=io("alignToBottom"),B=io("initialItemFinalLocationReached"),L=e?{}:{boxSizing:"border-box",...v?{whiteSpace:"nowrap",display:"inline-block",height:"100%",paddingLeft:t.offsetTop,paddingRight:t.offsetBottom,marginLeft:0!==T?T:k?"auto":0}:{marginTop:0!==T?T:k?"auto":0,paddingTop:t.offsetTop,paddingBottom:t.offsetBottom},...B?{}:{visibility:"hidden"}};return!e&&0===t.totalCount&&S?(0,n.jsx)(S,{...Kt(S,d)}):(0,n.jsx)(H,{...Kt(H,d),ref:I,style:L,"data-testid":e?"virtuoso-top-item-list":"virtuoso-item-list",children:(e?t.topItems:t.items).map((e=>{const o=e.originalIndex,n=R(o+t.firstItemIndex,e.data,d);return z?(0,r.createElement)(C,{...Kt(C,d),key:n,index:e.index,height:e.size,type:e.type||"item",..."group"===e.type?{}:{groupIndex:e.groupIndex}}):"group"===e.type?(0,r.createElement)(y,{...Kt(y,d),key:n,"data-index":o,"data-known-size":e.size,"data-item-index":e.index,style:At},h(e.index,d)):(0,r.createElement)(b,{...Kt(b,d),...qt(b,e.data),key:n,"data-index":o,"data-known-size":e.size,"data-item-index":e.index,"data-item-group-index":e.groupIndex,style:v?Vt:Dt},E?u(e.index,e.groupIndex,e.data,d):u(e.index,e.data,d))}))})})),Gt={height:"100%",outline:"none",overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},_t={outline:"none",overflowX:"auto",position:"relative"},Ut=e=>({width:"100%",height:"100%",position:"absolute",top:0,...e?{display:"flex",flexDirection:"column"}:{}}),$t={width:"100%",position:Lt(),top:0,zIndex:1};function Kt(e,t){if("string"!==typeof e)return{context:t}}function qt(e,t){return{item:"string"===typeof e?void 0:t}}const Yt=r.memo((function(){const e=io("HeaderComponent"),t=ro("headerHeight"),o=io("HeaderFooterTag"),i=Z(r.useMemo((()=>e=>t(Q(e,"height"))),[t]),!0,io("skipAnimationFrameInResizeObserver")),l=io("context");return e?(0,n.jsx)(o,{ref:i,children:(0,n.jsx)(e,{...Kt(e,l)})}):null})),Zt=r.memo((function(){const e=io("FooterComponent"),t=ro("footerHeight"),o=io("HeaderFooterTag"),i=Z(r.useMemo((()=>e=>t(Q(e,"height"))),[t]),!0,io("skipAnimationFrameInResizeObserver")),l=io("context");return e?(0,n.jsx)(o,{ref:i,children:(0,n.jsx)(e,{...Kt(e,l)})}):null}));function Xt({usePublisher:e,useEmitter:t,useEmitterValue:o}){return r.memo((function({style:r,children:i,...l}){const s=e("scrollContainerState"),c=o("ScrollerComponent"),a=e("smoothScrollTargetReached"),u=o("scrollerRef"),d=o("context"),h=o("horizontalDirection")||!1,{scrollerRef:f,scrollByCallback:m,scrollToCallback:g}=te(s,a,c,u,void 0,h);t("scrollTo",g),t("scrollBy",m);const p=h?_t:Gt;return(0,n.jsx)(c,{ref:f,style:{...p,...r},"data-testid":"virtuoso-scroller","data-virtuoso-scroller":!0,tabIndex:0,...l,...Kt(c,d),children:i})}))}function Jt({usePublisher:e,useEmitter:t,useEmitterValue:o}){return r.memo((function({style:r,children:i,...l}){const s=e("windowScrollContainerState"),c=o("ScrollerComponent"),a=e("smoothScrollTargetReached"),u=o("totalListHeight"),d=o("deviation"),h=o("customScrollParent"),f=o("context"),{scrollerRef:m,scrollByCallback:p,scrollToCallback:x}=te(s,a,c,g,h);return U((()=>(m.current=h||window,()=>{m.current=null})),[m,h]),t("windowScrollTo",x),t("scrollBy",p),(0,n.jsx)(c,{style:{position:"relative",...r,...0!==u?{height:u+d}:{}},"data-virtuoso-scroller":!0,...l,...Kt(c,f),children:i})}))}const Qt=({children:e})=>{const t=r.useContext(Ft),o=ro("viewportHeight"),i=ro("fixedItemHeight"),s=io("alignToBottom"),c=io("horizontalDirection"),a=Z(r.useMemo((()=>l(o,(e=>Q(e,c?"width":"height")))),[o,c]),!0,io("skipAnimationFrameInResizeObserver"));return r.useEffect((()=>{t&&(o(t.viewportHeight),i(t.itemHeight))}),[t,o,i]),(0,n.jsx)("div",{style:Ut(s),ref:a,"data-viewport-type":"element",children:e})},eo=({children:e})=>{const t=r.useContext(Ft),o=ro("windowViewportRect"),i=ro("fixedItemHeight"),l=io("customScrollParent"),s=Ot(o,l,io("skipAnimationFrameInResizeObserver")),c=io("alignToBottom");return r.useEffect((()=>{t&&(i(t.itemHeight),o({offsetTop:0,visibleHeight:t.viewportHeight,visibleWidth:100}))}),[t,o,i]),(0,n.jsx)("div",{ref:s,style:Ut(c),"data-viewport-type":"window",children:e})},to=({children:e})=>{const t=io("TopItemListComponent")||"div",o=io("headerHeight"),r={...$t,marginTop:`${o}px`},i=io("context");return(0,n.jsx)(t,{style:r,...Kt(t,i),children:e})},oo=r.memo((function(e){const t=io("useWindowScroll"),o=io("topItemsIndexes").length>0,r=io("customScrollParent"),i=r||t?co:so,l=r||t?eo:Qt;return(0,n.jsxs)(i,{...e,children:[o&&(0,n.jsx)(to,{children:(0,n.jsx)(Nt,{showTopList:!0})}),(0,n.jsxs)(l,{children:[(0,n.jsx)(Yt,{}),(0,n.jsx)(Nt,{}),(0,n.jsx)(Zt,{})]})]})})),{Component:no,usePublisher:ro,useEmitterValue:io,useEmitter:lo}=_(Wt,{required:{},optional:{restoreStateFrom:"restoreStateFrom",context:"context",followOutput:"followOutput",itemContent:"itemContent",groupContent:"groupContent",overscan:"overscan",increaseViewportBy:"increaseViewportBy",totalCount:"totalCount",groupCounts:"groupCounts",topItemCount:"topItemCount",firstItemIndex:"firstItemIndex",initialTopMostItemIndex:"initialTopMostItemIndex",components:"components",atBottomThreshold:"atBottomThreshold",atTopThreshold:"atTopThreshold",computeItemKey:"computeItemKey",defaultItemHeight:"defaultItemHeight",fixedItemHeight:"fixedItemHeight",itemSize:"itemSize",scrollSeekConfiguration:"scrollSeekConfiguration",headerFooterTag:"HeaderFooterTag",data:"data",initialItemCount:"initialItemCount",initialScrollTop:"initialScrollTop",alignToBottom:"alignToBottom",useWindowScroll:"useWindowScroll",customScrollParent:"customScrollParent",scrollerRef:"scrollerRef",logLevel:"logLevel",horizontalDirection:"horizontalDirection",skipAnimationFrameInResizeObserver:"skipAnimationFrameInResizeObserver"},methods:{scrollToIndex:"scrollToIndex",scrollIntoView:"scrollIntoView",scrollTo:"scrollTo",scrollBy:"scrollBy",autoscrollToBottom:"autoscrollToBottom",getState:"getState"},events:{isScrolling:"isScrolling",endReached:"endReached",startReached:"startReached",rangeChanged:"rangeChanged",atBottomStateChange:"atBottomStateChange",atTopStateChange:"atTopStateChange",totalListHeightChanged:"totalListHeightChanged",itemsRendered:"itemsRendered",groupIndices:"groupIndices"}},oo),so=Xt({usePublisher:ro,useEmitterValue:io,useEmitter:lo}),co=Jt({usePublisher:ro,useEmitterValue:io,useEmitter:lo}),ao=no,uo={items:[],offsetBottom:0,offsetTop:0,top:0,bottom:0,itemHeight:0,itemWidth:0},ho={items:[{index:0}],offsetBottom:0,offsetTop:0,top:0,bottom:0,itemHeight:0,itemWidth:0},{round:fo,ceil:mo,floor:go,min:po,max:xo}=Math;function vo(e,t,o){return Array.from({length:t-e+1}).map(((t,n)=>{const r=null===o?null:o[n+e];return{index:n+e,data:r}}))}function Io(e,t){return e&&e.column===t.column&&e.row===t.row}function To(e,t){return e&&e.width===t.width&&e.height===t.height}const wo=D((([{overscan:e,visibleRange:t,listBoundary:o},{scrollTop:n,viewportHeight:r,scrollBy:i,scrollTo:l,smoothScrollTargetReached:s,scrollContainerState:c,footerHeight:a,headerHeight:u},d,h,{propsReady:f,didMount:m},{windowViewportRect:g,useWindowScroll:v,customScrollParent:I,windowScrollContainerState:R,windowScrollTo:L},M])=>{const W=C(0),D=C(0),V=C(uo),N=C({height:0,width:0}),G=C({height:0,width:0}),_=S(),U=S(),$=C(0),K=C(null),q=C({row:0,column:0}),Y=S(),Z=S(),X=C(!1),J=C(0),Q=C(!0),ee=C(!1),te=C(!1);p(y(m,P(J),E((([e,t])=>!!t))),(()=>{x(Q,!1)})),p(y(A(m,Q,G,N,J,ee),E((([e,t,o,n,,r])=>e&&!t&&0!==o.height&&0!==n.height&&!r))),(([,,,,e])=>{x(ee,!0),qe(1,(()=>{x(_,e)})),w(y(n),(()=>{x(o,[0,0]),x(Q,!0)}))})),T(y(Z,E((e=>void 0!==e&&null!==e&&e.scrollTop>0)),B(0)),D),p(y(m,P(Z),E((([,e])=>void 0!==e&&null!==e))),(([,e])=>{e&&(x(N,e.viewport),x(G,null==e?void 0:e.item),x(q,e.gap),e.scrollTop>0&&(x(X,!0),w(y(n,O(1)),(e=>{x(X,!1)})),x(l,{top:e.scrollTop})))})),T(y(N,k((({height:e})=>e))),r),T(y(A(j(N,To),j(G,To),j(q,((e,t)=>e&&e.column===t.column&&e.row===t.row)),j(n)),k((([e,t,o,n])=>({viewport:e,item:t,gap:o,scrollTop:n})))),Y),T(y(A(j(W),t,j(q,Io),j(G,To),j(N,To),j(K),j(D),j(X),j(Q),j(J)),E((([,,,,,,,e])=>!e)),k((([e,[t,o],n,r,i,l,s,,c,a])=>{const{row:u,column:d}=n,{height:h,width:f}=r,{width:m}=i;if(0===s&&(0===e||0===m))return uo;if(0===f){const t=Ye(a,e);return function(e){return{...ho,items:e}}(vo(t,Math.max(t+s-1,0),l))}const g=Ho(m,f,d);let p,x;c?0===t&&0===o&&s>0?(p=0,x=s-1):(p=g*go((t+u)/(h+u)),x=g*mo((o+u)/(h+u))-1,x=po(e-1,xo(x,g-1)),p=po(x,xo(0,p))):(p=0,x=-1);const v=vo(p,x,l),{top:I,bottom:T}=So(i,n,r,v),w=mo(e/g);return{items:v,offsetTop:I,offsetBottom:w*h+(w-1)*u-T,top:I,bottom:T,itemHeight:h,itemWidth:f}}))),V),T(y(K,E((e=>null!==e)),k((e=>e.length))),W),T(y(A(N,G,V,q),E((([e,t,{items:o}])=>o.length>0&&0!==t.height&&0!==e.height)),k((([e,t,{items:o},n])=>{const{top:r,bottom:i}=So(e,n,t,o);return[r,i]})),z(tt)),o);const oe=C(!1);T(y(n,P(oe),k((([e,t])=>t||0!==e))),oe);const ne=H(y(j(V),E((({items:e})=>e.length>0)),P(W,oe),E((([{items:e},t,o])=>o&&e[e.length-1].index===t-1)),k((([,e])=>e-1)),z())),re=H(y(j(V),E((({items:e})=>e.length>0&&0===e[0].index)),B(0),z())),ie=H(y(j(V),P(X),E((([{items:e},t])=>e.length>0&&!t)),k((([{items:e}])=>({startIndex:e[0].index,endIndex:e[e.length-1].index}))),z(ot),F(0)));T(ie,h.scrollSeekRangeChanged),T(y(_,P(N,G,W,q),k((([e,t,o,n,r])=>{const i=Ve(e),{align:l,behavior:s,offset:c}=i;let a=i.index;"LAST"===a&&(a=n-1),a=xo(0,a,po(n-1,a));let u=Co(t,r,o,a);return"end"===l?u=fo(u-t.height+o.height):"center"===l&&(u=fo(u-t.height/2+o.height/2)),c&&(u+=c),{top:u,behavior:s}}))),l);const le=b(y(V,k((e=>e.offsetBottom+e.bottom))),0);return T(y(g,k((e=>({width:e.visibleWidth,height:e.visibleHeight})))),N),{data:K,totalCount:W,viewportDimensions:N,itemDimensions:G,scrollTop:n,scrollHeight:U,overscan:e,scrollBy:i,scrollTo:l,scrollToIndex:_,smoothScrollTargetReached:s,windowViewportRect:g,windowScrollTo:L,useWindowScroll:v,customScrollParent:I,windowScrollContainerState:R,deviation:$,scrollContainerState:c,footerHeight:a,headerHeight:u,initialItemCount:D,gap:q,restoreStateFrom:Z,...h,initialTopMostItemIndex:J,horizontalDirection:te,gridState:V,totalListHeight:le,...d,startReached:re,endReached:ne,rangeChanged:ie,stateChanged:Y,propsReady:f,stateRestoreInProgress:X,...M}}),d(ct,oe,$e,gt,Ke,Ct,q));function So(e,t,o,n){const{height:r}=o;if(void 0===r||0===n.length)return{top:0,bottom:0};return{top:Co(e,t,o,n[0].index),bottom:Co(e,t,o,n[n.length-1].index)+r}}function Co(e,t,o,n){const r=Ho(e.width,o.width,t.column),i=go(n/r),l=i*o.height+xo(0,i-1)*t.row;return l>0?l+t.row:l}function Ho(e,t,o){return xo(1,go((e+o)/(go(t)+o)))}const bo=D((([e,t])=>({...e,...t})),d(wo,D((()=>{const e=C((e=>`Item ${e}`)),t=C({}),o=C(null),n=C("virtuoso-grid-item"),r=C("virtuoso-grid-list"),i=C(Pt),l=C("div"),s=C(g),c=(e,o=null)=>b(y(t,k((t=>t[e])),z()),o),a=C(!1),u=C(!1);return T(j(u),a),{readyStateChanged:a,reportReadyState:u,context:o,itemContent:e,components:t,computeItemKey:i,itemClassName:n,listClassName:r,headerFooterTag:l,scrollerRef:s,FooterComponent:c("Footer"),HeaderComponent:c("Header"),ListComponent:c("List","div"),ItemComponent:c("Item","div"),ScrollerComponent:c("Scroller","div"),ScrollSeekPlaceholder:c("ScrollSeekPlaceholder","div")}})))),yo=r.memo((function(){const e=Fo("gridState"),t=Fo("listClassName"),o=Fo("itemClassName"),i=Fo("itemContent"),l=Fo("computeItemKey"),s=Fo("isSeeking"),c=Oo("scrollHeight"),a=Fo("ItemComponent"),u=Fo("ListComponent"),d=Fo("ScrollSeekPlaceholder"),h=Fo("context"),f=Oo("itemDimensions"),m=Oo("gap"),g=Fo("log"),p=Fo("stateRestoreInProgress"),x=Oo("reportReadyState"),v=Z(r.useMemo((()=>e=>{const t=e.parentElement.parentElement.scrollHeight;c(t);const o=e.firstChild;if(o){const{width:e,height:t}=o.getBoundingClientRect();f({width:e,height:t})}m({row:jo("row-gap",getComputedStyle(e).rowGap,g),column:jo("column-gap",getComputedStyle(e).columnGap,g)})}),[c,f,m,g]),!0,!1);return U((()=>{e.itemHeight>0&&e.itemWidth>0&&x(!0)}),[e]),p?null:(0,n.jsx)(u,{ref:v,className:t,...Kt(u,h),style:{paddingTop:e.offsetTop,paddingBottom:e.offsetBottom},"data-testid":"virtuoso-item-list",children:e.items.map((t=>{const c=l(t.index,t.data,h);return s?(0,n.jsx)(d,{...Kt(d,h),index:t.index,height:e.itemHeight,width:e.itemWidth},c):(0,r.createElement)(a,{...Kt(a,h),className:o,"data-index":t.index,key:c},i(t.index,t.data,h))}))})})),Ro=r.memo((function(){const e=Fo("HeaderComponent"),t=Oo("headerHeight"),o=Fo("headerFooterTag"),i=Z(r.useMemo((()=>e=>t(Q(e,"height"))),[t]),!0,!1),l=Fo("context");return e?(0,n.jsx)(o,{ref:i,children:(0,n.jsx)(e,{...Kt(e,l)})}):null})),zo=r.memo((function(){const e=Fo("FooterComponent"),t=Oo("footerHeight"),o=Fo("headerFooterTag"),i=Z(r.useMemo((()=>e=>t(Q(e,"height"))),[t]),!0,!1),l=Fo("context");return e?(0,n.jsx)(o,{ref:i,children:(0,n.jsx)(e,{...Kt(e,l)})}):null})),Eo=({children:e})=>{const t=r.useContext(Mt),o=Oo("itemDimensions"),i=Oo("viewportDimensions"),l=Z(r.useMemo((()=>e=>{i(e.getBoundingClientRect())}),[i]),!0,!1);return r.useEffect((()=>{t&&(i({height:t.viewportHeight,width:t.viewportWidth}),o({height:t.itemHeight,width:t.itemWidth}))}),[t,i,o]),(0,n.jsx)("div",{style:Ut(!1),ref:l,children:e})},ko=({children:e})=>{const t=r.useContext(Mt),o=Oo("windowViewportRect"),i=Oo("itemDimensions"),l=Fo("customScrollParent"),s=Ot(o,l,!1);return r.useEffect((()=>{t&&(i({height:t.itemHeight,width:t.itemWidth}),o({offsetTop:0,visibleHeight:t.viewportHeight,visibleWidth:t.viewportWidth}))}),[t,o,i]),(0,n.jsx)("div",{ref:s,style:Ut(!1),children:e})},Bo=r.memo((function({...e}){const t=Fo("useWindowScroll"),o=Fo("customScrollParent"),r=o||t?Wo:Po,i=o||t?ko:Eo;return(0,n.jsx)(r,{...e,children:(0,n.jsxs)(i,{children:[(0,n.jsx)(Ro,{}),(0,n.jsx)(yo,{}),(0,n.jsx)(zo,{})]})})})),{Component:Lo,usePublisher:Oo,useEmitterValue:Fo,useEmitter:Mo}=_(bo,{optional:{context:"context",totalCount:"totalCount",overscan:"overscan",itemContent:"itemContent",components:"components",computeItemKey:"computeItemKey",data:"data",initialItemCount:"initialItemCount",scrollSeekConfiguration:"scrollSeekConfiguration",headerFooterTag:"headerFooterTag",listClassName:"listClassName",itemClassName:"itemClassName",useWindowScroll:"useWindowScroll",customScrollParent:"customScrollParent",scrollerRef:"scrollerRef",logLevel:"logLevel",restoreStateFrom:"restoreStateFrom",initialTopMostItemIndex:"initialTopMostItemIndex"},methods:{scrollTo:"scrollTo",scrollBy:"scrollBy",scrollToIndex:"scrollToIndex"},events:{isScrolling:"isScrolling",endReached:"endReached",startReached:"startReached",rangeChanged:"rangeChanged",atBottomStateChange:"atBottomStateChange",atTopStateChange:"atTopStateChange",stateChanged:"stateChanged",readyStateChanged:"readyStateChanged"}},Bo),Po=Xt({usePublisher:Oo,useEmitterValue:Fo,useEmitter:Mo}),Wo=Jt({usePublisher:Oo,useEmitterValue:Fo,useEmitter:Mo});function jo(e,t,o){return"normal"===t||(null==t?void 0:t.endsWith("px"))||o(`${e} was not resolved to pixel value correctly`,t,$.WARN),"normal"===t?0:parseInt(null!=t?t:"0",10)}const Ao=D((([e,t])=>({...e,...t})),d(Et,D((()=>{const e=C((e=>(0,n.jsxs)("td",{children:["Item $",e]}))),t=C(null),o=C(null),r=C(null),i=C({}),l=C(Pt),s=C(g),c=(e,t=null)=>b(y(i,k((t=>t[e])),z()),t);return{context:t,itemContent:e,fixedHeaderContent:o,fixedFooterContent:r,components:i,computeItemKey:l,scrollerRef:s,TableComponent:c("Table","table"),TableHeadComponent:c("TableHead","thead"),TableFooterComponent:c("TableFoot","tfoot"),TableBodyComponent:c("TableBody","tbody"),TableRowComponent:c("TableRow","tr"),ScrollerComponent:c("Scroller","div"),EmptyPlaceholder:c("EmptyPlaceholder"),ScrollSeekPlaceholder:c("ScrollSeekPlaceholder"),FillerRow:c("FillerRow")}})))),Do=({height:e})=>(0,n.jsx)("tr",{children:(0,n.jsx)("td",{style:{height:e}})}),Vo=({height:e})=>(0,n.jsx)("tr",{children:(0,n.jsx)("td",{style:{height:e,padding:0,border:0}})}),No={overflowAnchor:"none"},Go=r.memo((function(){const e=Yo("listState"),t=qo("sizeRanges"),o=Yo("useWindowScroll"),i=Yo("customScrollParent"),l=qo("windowScrollContainerState"),s=qo("scrollContainerState"),c=i||o?l:s,a=Yo("itemContent"),u=Yo("trackItemSizes"),d=Yo("itemSize"),h=Yo("log"),{callbackRef:f,ref:m}=X(t,d,u,c,h,void 0,i,!1,Yo("skipAnimationFrameInResizeObserver")),[g,p]=r.useState(0);Zo("deviation",(e=>{g!==e&&(m.current.style.marginTop=`${e}px`,p(e))}));const x=Yo("EmptyPlaceholder"),v=Yo("ScrollSeekPlaceholder")||Do,I=Yo("FillerRow")||Vo,T=Yo("TableBodyComponent"),w=Yo("TableRowComponent"),S=Yo("computeItemKey"),C=Yo("isSeeking"),H=Yo("paddingTopAddition"),b=Yo("firstItemIndex"),y=Yo("statefulTotalCount"),R=Yo("context");if(0===y&&x)return(0,n.jsx)(x,{...Kt(x,R)});const z=e.offsetTop+H+g,E=e.offsetBottom,k=z>0?(0,n.jsx)(I,{height:z,context:R},"padding-top"):null,B=E>0?(0,n.jsx)(I,{height:E,context:R},"padding-bottom"):null,L=e.items.map((e=>{const t=e.originalIndex,o=S(t+b,e.data,R);return C?(0,r.createElement)(v,{...Kt(v,R),key:o,index:e.index,height:e.size,type:e.type||"item"}):(0,r.createElement)(w,{...Kt(w,R),...qt(w,e.data),key:o,"data-index":t,"data-known-size":e.size,"data-item-index":e.index,style:No},a(e.index,e.data,R))}));return(0,n.jsxs)(T,{ref:f,"data-testid":"virtuoso-item-list",...Kt(T,R),children:[k,L,B]})})),_o=({children:e})=>{const t=r.useContext(Ft),o=qo("viewportHeight"),i=qo("fixedItemHeight"),s=Z(r.useMemo((()=>l(o,(e=>Q(e,"height")))),[o]),!0,Yo("skipAnimationFrameInResizeObserver"));return r.useEffect((()=>{t&&(o(t.viewportHeight),i(t.itemHeight))}),[t,o,i]),(0,n.jsx)("div",{style:Ut(!1),ref:s,"data-viewport-type":"element",children:e})},Uo=({children:e})=>{const t=r.useContext(Ft),o=qo("windowViewportRect"),i=qo("fixedItemHeight"),l=Yo("customScrollParent"),s=Ot(o,l,Yo("skipAnimationFrameInResizeObserver"));return r.useEffect((()=>{t&&(i(t.itemHeight),o({offsetTop:0,visibleHeight:t.viewportHeight,visibleWidth:100}))}),[t,o,i]),(0,n.jsx)("div",{ref:s,style:Ut(!1),"data-viewport-type":"window",children:e})},$o=r.memo((function(e){const t=Yo("useWindowScroll"),o=Yo("customScrollParent"),i=qo("fixedHeaderHeight"),s=qo("fixedFooterHeight"),c=Yo("fixedHeaderContent"),a=Yo("fixedFooterContent"),u=Yo("context"),d=Z(r.useMemo((()=>l(i,(e=>Q(e,"height")))),[i]),!0,Yo("skipAnimationFrameInResizeObserver")),h=Z(r.useMemo((()=>l(s,(e=>Q(e,"height")))),[s]),!0,Yo("skipAnimationFrameInResizeObserver")),f=o||t?Jo:Xo,m=o||t?Uo:_o,g=Yo("TableComponent"),p=Yo("TableHeadComponent"),x=Yo("TableFooterComponent"),v=c?(0,n.jsx)(p,{style:{zIndex:2,position:"sticky",top:0},ref:d,...Kt(p,u),children:c()},"TableHead"):null,I=a?(0,n.jsx)(x,{style:{zIndex:1,position:"sticky",bottom:0},ref:h,...Kt(x,u),children:a()},"TableFoot"):null;return(0,n.jsx)(f,{...e,children:(0,n.jsx)(m,{children:(0,n.jsxs)(g,{style:{borderSpacing:0,overflowAnchor:"none"},...Kt(g,u),children:[v,(0,n.jsx)(Go,{},"TableBody"),I]})})})})),{Component:Ko,usePublisher:qo,useEmitterValue:Yo,useEmitter:Zo}=_(Ao,{required:{},optional:{restoreStateFrom:"restoreStateFrom",context:"context",followOutput:"followOutput",firstItemIndex:"firstItemIndex",itemContent:"itemContent",fixedHeaderContent:"fixedHeaderContent",fixedFooterContent:"fixedFooterContent",overscan:"overscan",increaseViewportBy:"increaseViewportBy",totalCount:"totalCount",topItemCount:"topItemCount",initialTopMostItemIndex:"initialTopMostItemIndex",components:"components",groupCounts:"groupCounts",atBottomThreshold:"atBottomThreshold",atTopThreshold:"atTopThreshold",computeItemKey:"computeItemKey",defaultItemHeight:"defaultItemHeight",fixedItemHeight:"fixedItemHeight",itemSize:"itemSize",scrollSeekConfiguration:"scrollSeekConfiguration",data:"data",initialItemCount:"initialItemCount",initialScrollTop:"initialScrollTop",alignToBottom:"alignToBottom",useWindowScroll:"useWindowScroll",customScrollParent:"customScrollParent",scrollerRef:"scrollerRef",logLevel:"logLevel"},methods:{scrollToIndex:"scrollToIndex",scrollIntoView:"scrollIntoView",scrollTo:"scrollTo",scrollBy:"scrollBy",getState:"getState"},events:{isScrolling:"isScrolling",endReached:"endReached",startReached:"startReached",rangeChanged:"rangeChanged",atBottomStateChange:"atBottomStateChange",atTopStateChange:"atTopStateChange",totalListHeightChanged:"totalListHeightChanged",itemsRendered:"itemsRendered",groupIndices:"groupIndices"}},$o),Xo=Xt({usePublisher:qo,useEmitterValue:Yo,useEmitter:Zo}),Jo=Jt({usePublisher:qo,useEmitterValue:Yo,useEmitter:Zo})}}]);
//# sourceMappingURL=react-virtuoso.541f35b4525dae7a0fe317ccee842600.js.map