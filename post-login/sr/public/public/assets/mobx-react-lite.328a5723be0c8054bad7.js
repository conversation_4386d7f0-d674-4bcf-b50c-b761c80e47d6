"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["mobx-react-lite"],{83315:function(e,r,t){t.d(r,{Qj:function(){return O},FY:function(){return s},Pi:function(){return g}});var n=t(59621),o=t(89526);if(!o.useState)throw new Error("mobx-react-lite requires React with Hooks support");if(!n.rC)throw new Error("mobx-react-lite@3 requires mobx at least version 6 to be available");var i=t(73961);function a(e){e()}function u(e){return(0,n.Gf)(e)}var c=function(){function e(e){var r=this;Object.defineProperty(this,"finalize",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,"registrations",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"sweepTimeout",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"sweep",{enumerable:!0,configurable:!0,writable:!0,value:function(e){void 0===e&&(e=1e4),clearTimeout(r.sweepTimeout),r.sweepTimeout=void 0;var t=Date.now();r.registrations.forEach((function(n,o){t-n.registeredAt>=e&&(r.finalize(n.value),r.registrations.delete(o))})),r.registrations.size>0&&r.scheduleSweep()}}),Object.defineProperty(this,"finalizeAllImmediately",{enumerable:!0,configurable:!0,writable:!0,value:function(){r.sweep(0)}})}return Object.defineProperty(e.prototype,"register",{enumerable:!1,configurable:!0,writable:!0,value:function(e,r,t){this.registrations.set(t,{value:r,registeredAt:Date.now()}),this.scheduleSweep()}}),Object.defineProperty(e.prototype,"unregister",{enumerable:!1,configurable:!0,writable:!0,value:function(e){this.registrations.delete(e)}}),Object.defineProperty(e.prototype,"scheduleSweep",{enumerable:!1,configurable:!0,writable:!0,value:function(){void 0===this.sweepTimeout&&(this.sweepTimeout=setTimeout(this.sweep,1e4))}}),e}(),f=new("undefined"!==typeof FinalizationRegistry?FinalizationRegistry:c)((function(e){var r;null===(r=e.reaction)||void 0===r||r.dispose(),e.reaction=null})),l=!1;function s(){return l}var p=function(e,r){var t="function"===typeof Symbol&&e[Symbol.iterator];if(!t)return e;var n,o,i=t.call(e),a=[];try{for(;(void 0===r||r-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(u){o={error:u}}finally{try{n&&!n.done&&(t=i.return)&&t.call(i)}finally{if(o)throw o.error}}return a};function d(e){return"observer".concat(e)}var b=function(){};function m(){return new b}function y(e,r){if(void 0===r&&(r="observed"),s())return e();var t=p(o.useState(m),1)[0],i=p(o.useState(),2)[1],a=function(){return i([])},c=o.useRef(null);c.current||(c.current={reaction:null,mounted:!1,changedBeforeMount:!1});var l,b,y=c.current;if(y.reaction||(y.reaction=new n.le(d(r),(function(){y.mounted?a():y.changedBeforeMount=!0})),f.register(t,y,y)),o.useDebugValue(y.reaction,u),o.useEffect((function(){return f.unregister(y),y.mounted=!0,y.reaction?y.changedBeforeMount&&(y.changedBeforeMount=!1,a()):(y.reaction=new n.le(d(r),(function(){a()})),a()),function(){y.reaction.dispose(),y.reaction=null,y.mounted=!1,y.changedBeforeMount=!1}}),[]),y.reaction.track((function(){try{l=e()}catch(r){b=r}})),b)throw b;return l}var v="function"===typeof Symbol&&Symbol.for,w=v?Symbol.for("react.forward_ref"):"function"===typeof o.forwardRef&&(0,o.forwardRef)((function(e){return null})).$$typeof,h=v?Symbol.for("react.memo"):"function"===typeof o.memo&&(0,o.memo)((function(e){return null})).$$typeof;function g(e,r){var t;if(h&&e.$$typeof===h)throw new Error("[mobx-react-lite] You are trying to use `observer` on a function component wrapped in either another `observer` or `React.memo`. The observer already applies 'React.memo' for you.");if(s())return e;var n=null!==(t=null===r||void 0===r?void 0:r.forwardRef)&&void 0!==t&&t,i=e,a=e.displayName||e.name;if(w&&e.$$typeof===w&&(n=!0,"function"!==typeof(i=e.render)))throw new Error("[mobx-react-lite] `render` property of ForwardRef was not a function");var u,c,f=function(e,r){return y((function(){return i(e,r)}),a)};return""!==a&&(f.displayName=a),e.contextTypes&&(f.contextTypes=e.contextTypes),n&&(f=(0,o.forwardRef)(f)),f=(0,o.memo)(f),u=e,c=f,Object.keys(u).forEach((function(e){j[e]||Object.defineProperty(c,e,Object.getOwnPropertyDescriptor(u,e))})),f}var j={$$typeof:!0,render:!0,compare:!0,type:!0,displayName:!0};function O(e){var r=e.children,t=e.render,n=r||t;return"function"!==typeof n?null:y(n)}O.displayName="Observer";var S,P;(P=i.unstable_batchedUpdates)||(P=a),(0,n.jQ)({reactionScheduler:P});S=f.finalizeAllImmediately}}]);
//# sourceMappingURL=mobx-react-lite.e68b91df7eb55bcd4316ff613e200b29.js.map