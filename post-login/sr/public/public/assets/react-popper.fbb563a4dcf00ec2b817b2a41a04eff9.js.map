{"version": 3, "file": "react-popper.xxxxxxxxxxxxxxxxxxxx.js", "mappings": "uSACWA,EAA8B,kBAC9BC,EAAoC,kBACxC,SAASC,EAAQC,GACtB,IAAIC,EAAWD,EAAKC,SAEhBC,EAAkB,WAAe,MACjCC,EAAgBD,EAAgB,GAChCE,EAAmBF,EAAgB,GAEnCG,EAAe,UAAa,GAChC,aAAgB,WACd,OAAO,WACLA,EAAaC,SAAU,CACzB,CACF,GAAG,IACH,IAAIC,EAAyB,eAAkB,SAAUC,GAClDH,EAAaC,SAChBF,EAAiBI,EAErB,GAAG,IACH,OAAoB,gBAAoBX,EAA4BY,SAAU,CAC5EC,MAAOP,GACO,gBAAoBL,EAAkCW,SAAU,CAC9EC,MAAOH,GACNN,GACL,CCpBO,IAAIU,EAAc,SAAqBC,GAC5C,OAAOC,MAAMC,QAAQF,GAAOA,EAAI,GAAKA,CACvC,EAMWG,EAAa,SAAoBC,GAC1C,GAAkB,oBAAPA,EAAmB,CAC5B,IAAK,IAAIC,EAAOC,UAAUC,OAAQC,EAAO,IAAIP,MAAMI,EAAO,EAAIA,EAAO,EAAI,GAAII,EAAO,EAAGA,EAAOJ,EAAMI,IAClGD,EAAKC,EAAO,GAAKH,UAAUG,GAG7B,OAAOL,EAAGM,WAAM,EAAQF,EAC1B,CACF,EAKWG,EAAS,SAAgBC,EAAKhB,GAEvC,GAAmB,oBAARgB,EACT,OAAOT,EAAWS,EAAKhB,GAET,MAAPgB,IACLA,EAAIlB,QAAUE,EAEpB,EAKWiB,EAAc,SAAqBC,GAC5C,OAAOA,EAAQC,QAAO,SAAUC,EAAK5B,GACnC,IAAI6B,EAAM7B,EAAK,GACXU,EAAQV,EAAK,GAEjB,OADA4B,EAAIC,GAAOnB,EACJkB,CACT,GAAG,CAAC,EACN,EAKWE,EAA8C,qBAAXC,QAA0BA,OAAOC,UAAYD,OAAOC,SAASC,cAAgB,kBAAwB,Y,0CC/C/IC,EAAkB,GACXC,EAAY,SAAmBC,EAAkBC,EAAeC,QACzD,IAAZA,IACFA,EAAU,CAAC,GAGb,IAAIC,EAAc,SAAa,MAC3BC,EAAsB,CACxBC,cAAeH,EAAQG,cACvBC,UAAWJ,EAAQI,WAAa,SAChCC,SAAUL,EAAQK,UAAY,WAC9BC,UAAWN,EAAQM,WAAaV,GAG9BhC,EAAkB,WAAe,CACnC2C,OAAQ,CACNC,OAAQ,CACNC,SAAUP,EAAoBG,SAC9BK,KAAM,IACNC,IAAK,KAEPC,MAAO,CACLH,SAAU,aAGdI,WAAY,CAAC,IAEXC,EAAQlD,EAAgB,GACxBmD,EAAWnD,EAAgB,GAE3BoD,EAAsB,WAAc,WACtC,MAAO,CACLC,KAAM,cACNC,SAAS,EACTC,MAAO,QACPzC,GAAI,SAAYhB,GACd,IAAIoD,EAAQpD,EAAKoD,MACbM,EAAWC,OAAOC,KAAKR,EAAMM,UACjC,aAAmB,WACjBL,EAAS,CACPR,OAAQpB,EAAYiC,EAASG,KAAI,SAAUC,GACzC,MAAO,CAACA,EAASV,EAAMP,OAAOiB,IAAY,CAAC,EAC7C,KACAX,WAAY1B,EAAYiC,EAASG,KAAI,SAAUC,GAC7C,MAAO,CAACA,EAASV,EAAMD,WAAWW,GACpC,MAEJ,GACF,EACAC,SAAU,CAAC,iBAEf,GAAG,IACCC,EAAgB,WAAc,WAChC,IAAIC,EAAa,CACfxB,cAAeD,EAAoBC,cACnCC,UAAWF,EAAoBE,UAC/BC,SAAUH,EAAoBG,SAC9BC,UAAW,GAAGsB,OAAO1B,EAAoBI,UAAW,CAACU,EAAqB,CACxEC,KAAM,cACNC,SAAS,MAIb,OAAI,IAAQjB,EAAYjC,QAAS2D,GACxB1B,EAAYjC,SAAW2D,GAE9B1B,EAAYjC,QAAU2D,EACfA,EAEX,GAAG,CAACzB,EAAoBC,cAAeD,EAAoBE,UAAWF,EAAoBG,SAAUH,EAAoBI,UAAWU,IAC/Ha,EAAoB,WAmBxB,OAlBArC,GAA0B,WACpBqC,EAAkB7D,SACpB6D,EAAkB7D,QAAQ8D,WAAWJ,EAEzC,GAAG,CAACA,IACJlC,GAA0B,WACxB,GAAwB,MAApBM,GAA6C,MAAjBC,EAAhC,CAIA,IACIgC,GADe/B,EAAQgC,cAAgB,MACTlC,EAAkBC,EAAe2B,GAEnE,OADAG,EAAkB7D,QAAU+D,EACrB,WACLA,EAAeE,UACfJ,EAAkB7D,QAAU,IAC9B,CARA,CASF,GAAG,CAAC8B,EAAkBC,EAAeC,EAAQgC,eACtC,CACLlB,MAAOe,EAAkB7D,QAAU6D,EAAkB7D,QAAQ8C,MAAQ,KACrEP,OAAQO,EAAMP,OACdM,WAAYC,EAAMD,WAClBqB,OAAQL,EAAkB7D,QAAU6D,EAAkB7D,QAAQkE,OAAS,KACvEC,YAAaN,EAAkB7D,QAAU6D,EAAkB7D,QAAQmE,YAAc,KAErF,EChGIC,EAAO,WAEX,EAEIC,EAAe,WACjB,OAAOC,QAAQC,QAAQ,KACzB,EAEI,EAAkB,GACf,SAASC,EAAO9E,GACrB,IAAI+E,EAAiB/E,EAAK0C,UACtBA,OAA+B,IAAnBqC,EAA4B,SAAWA,EACnDC,EAAgBhF,EAAK2C,SACrBA,OAA6B,IAAlBqC,EAA2B,WAAaA,EACnDC,EAAiBjF,EAAK4C,UACtBA,OAA+B,IAAnBqC,EAA4B,EAAkBA,EAC1D7C,EAAmBpC,EAAKoC,iBACxBK,EAAgBzC,EAAKyC,cACrByC,EAAWlF,EAAKkF,SAChBjF,EAAWD,EAAKC,SAChBE,EAAgB,aAAiBN,GAEjCK,EAAkB,WAAe,MACjCmC,EAAgBnC,EAAgB,GAChCiF,EAAmBjF,EAAgB,GAEnCkF,EAAmB,WAAe,MAClCC,EAAeD,EAAiB,GAChCE,EAAkBF,EAAiB,GAEvC,aAAgB,WACd7D,EAAO2D,EAAU7C,EACnB,GAAG,CAAC6C,EAAU7C,IACd,IAAIC,EAAU,WAAc,WAC1B,MAAO,CACLI,UAAWA,EACXC,SAAUA,EACVF,cAAeA,EACfG,UAAW,GAAGsB,OAAOtB,EAAW,CAAC,CAC/BW,KAAM,QACNC,QAAyB,MAAhB6B,EACT/C,QAAS,CACPwB,QAASuB,MAIjB,GAAG,CAAC3C,EAAWC,EAAUF,EAAeG,EAAWyC,IAE/CE,EAAapD,EAAUC,GAAoBjC,EAAekC,EAAeC,GACzEc,EAAQmC,EAAWnC,MACnBP,EAAS0C,EAAW1C,OACpB4B,EAAcc,EAAWd,YACzBD,EAASe,EAAWf,OAEpBgB,EAAgB,WAAc,WAChC,MAAO,CACLhE,IAAK2D,EACLM,MAAO5C,EAAOC,OACdJ,UAAWU,EAAQA,EAAMV,UAAYA,EACrCgD,iBAAkBtC,GAASA,EAAMuC,cAAcC,KAAOxC,EAAMuC,cAAcC,KAAKF,iBAAmB,KAClGG,kBAAmBzC,GAASA,EAAMuC,cAAcC,KAAOxC,EAAMuC,cAAcC,KAAKC,kBAAoB,KACpGC,WAAY,CACVL,MAAO5C,EAAOK,MACd1B,IAAK8D,GAEPb,YAAaA,GAAeC,EAC5BF,OAAQA,GAAUG,EAEtB,GAAG,CAACQ,EAAkBG,EAAiB5C,EAAWU,EAAOP,EAAQ2B,EAAQC,IACzE,OAAO9D,EAAYV,EAAZU,CAAsB6E,EAC/B,C,sBCvEO,SAASO,EAAU/F,GACxB,IAAIC,EAAWD,EAAKC,SAChBiF,EAAWlF,EAAKkF,SAChB9E,EAAmB,aAAiBN,GACpCkG,EAAa,eAAkB,SAAUxF,GAC3Ce,EAAO2D,EAAU1E,GACjBO,EAAWX,EAAkBI,EAC/B,GAAG,CAAC0E,EAAU9E,IAWd,OARA,aAAgB,WACd,OAAO,WACL,OAAOmB,EAAO2D,EAAU,KAC1B,CACF,GAAG,IACH,aAAgB,WACd,IAAQe,QAAQ7F,GAAmB,mEACrC,GAAG,CAACA,IACGO,EAAYV,EAAZU,CAAsB,CAC3Ba,IAAKwE,GAET,C,oBCvBA,IAAIE,EAAoC,qBAAZC,QACxBC,EAAwB,oBAARC,IAChBC,EAAwB,oBAARC,IAChBC,EAAwC,oBAAhBC,eAAgCA,YAAYC,OAIxE,SAASC,EAAMC,EAAGC,GAEhB,GAAID,IAAMC,EAAG,OAAO,EAEpB,GAAID,GAAKC,GAAiB,iBAALD,GAA6B,iBAALC,EAAe,CAC1D,GAAID,EAAEE,cAAgBD,EAAEC,YAAa,OAAO,EAE5C,IAAI3F,EAAQ4F,EAAGnD,EA6BXoD,EA5BJ,GAAInG,MAAMC,QAAQ8F,GAAI,CAEpB,IADAzF,EAASyF,EAAEzF,SACG0F,EAAE1F,OAAQ,OAAO,EAC/B,IAAK4F,EAAI5F,EAAgB,IAAR4F,KACf,IAAKJ,EAAMC,EAAEG,GAAIF,EAAEE,IAAK,OAAO,EACjC,OAAO,CACT,CAuBA,GAAIX,GAAWQ,aAAaP,KAASQ,aAAaR,IAAM,CACtD,GAAIO,EAAEK,OAASJ,EAAEI,KAAM,OAAO,EAE9B,IADAD,EAAKJ,EAAElF,YACEqF,EAAIC,EAAGE,QAAQC,UACjBN,EAAEO,IAAIL,EAAErG,MAAM,IAAK,OAAO,EAEjC,IADAsG,EAAKJ,EAAElF,YACEqF,EAAIC,EAAGE,QAAQC,UACjBR,EAAMI,EAAErG,MAAM,GAAImG,EAAEQ,IAAIN,EAAErG,MAAM,KAAM,OAAO,EACpD,OAAO,CACT,CAEA,GAAI4F,GAAWM,aAAaL,KAASM,aAAaN,IAAM,CACtD,GAAIK,EAAEK,OAASJ,EAAEI,KAAM,OAAO,EAE9B,IADAD,EAAKJ,EAAElF,YACEqF,EAAIC,EAAGE,QAAQC,UACjBN,EAAEO,IAAIL,EAAErG,MAAM,IAAK,OAAO,EACjC,OAAO,CACT,CAGA,GAAI8F,GAAkBC,YAAYC,OAAOE,IAAMH,YAAYC,OAAOG,GAAI,CAEpE,IADA1F,EAASyF,EAAEzF,SACG0F,EAAE1F,OAAQ,OAAO,EAC/B,IAAK4F,EAAI5F,EAAgB,IAAR4F,KACf,GAAIH,EAAEG,KAAOF,EAAEE,GAAI,OAAO,EAC5B,OAAO,CACT,CAEA,GAAIH,EAAEE,cAAgBQ,OAAQ,OAAOV,EAAEW,SAAWV,EAAEU,QAAUX,EAAEY,QAAUX,EAAEW,MAK5E,GAAIZ,EAAEa,UAAY9D,OAAO+D,UAAUD,SAAgC,oBAAdb,EAAEa,SAA+C,oBAAdZ,EAAEY,QAAwB,OAAOb,EAAEa,YAAcZ,EAAEY,UAC3I,GAAIb,EAAEe,WAAahE,OAAO+D,UAAUC,UAAkC,oBAAff,EAAEe,UAAiD,oBAAfd,EAAEc,SAAyB,OAAOf,EAAEe,aAAed,EAAEc,WAKhJ,IADAxG,GADAyC,EAAOD,OAAOC,KAAKgD,IACLzF,UACCwC,OAAOC,KAAKiD,GAAG1F,OAAQ,OAAO,EAE7C,IAAK4F,EAAI5F,EAAgB,IAAR4F,KACf,IAAKpD,OAAO+D,UAAUE,eAAeC,KAAKhB,EAAGjD,EAAKmD,IAAK,OAAO,EAKhE,GAAIb,GAAkBU,aAAaT,QAAS,OAAO,EAGnD,IAAKY,EAAI5F,EAAgB,IAAR4F,KACf,IAAiB,WAAZnD,EAAKmD,IAA+B,QAAZnD,EAAKmD,IAA4B,QAAZnD,EAAKmD,KAAiBH,EAAEkB,YAarEnB,EAAMC,EAAEhD,EAAKmD,IAAKF,EAAEjD,EAAKmD,KAAM,OAAO,EAK7C,OAAO,CACT,CAEA,OAAOH,IAAMA,GAAKC,IAAMA,CAC1B,CAGAkB,EAAOC,QAAU,SAAiBpB,EAAGC,GACnC,IACE,OAAOF,EAAMC,EAAGC,EAClB,CAAE,MAAOoB,GACP,IAAMA,EAAMC,SAAW,IAAIC,MAAM,oBAO/B,OADAC,QAAQC,KAAK,mDACN,EAGT,MAAMJ,CACR,CACF,C", "sources": ["webpack://heaplabs-coldemail-app/./node_modules/react-popper/lib/esm/Manager.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/lib/esm/utils.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/lib/esm/usePopper.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/lib/esm/Popper.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/lib/esm/Reference.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/node_modules/react-fast-compare/index.js"], "names": ["ManagerReferenceNodeContext", "ManagerReferenceNodeSetterContext", "Manager", "_ref", "children", "_React$useState", "referenceNode", "setReferenceNode", "hasUnmounted", "current", "handleSetReferenceNode", "node", "Provider", "value", "unwrapArray", "arg", "Array", "isArray", "safeInvoke", "fn", "_len", "arguments", "length", "args", "_key", "apply", "setRef", "ref", "fromEntries", "entries", "reduce", "acc", "key", "useIsomorphicLayoutEffect", "window", "document", "createElement", "EMPTY_MODIFIERS", "usePopper", "referenceElement", "popper<PERSON>lement", "options", "prevOptions", "optionsWithDefaults", "onFirstUpdate", "placement", "strategy", "modifiers", "styles", "popper", "position", "left", "top", "arrow", "attributes", "state", "setState", "updateStateModifier", "name", "enabled", "phase", "elements", "Object", "keys", "map", "element", "requires", "popperOptions", "newOptions", "concat", "popperInstanceRef", "setOptions", "popperInstance", "createPopper", "destroy", "update", "forceUpdate", "NOOP", "NOOP_PROMISE", "Promise", "resolve", "<PERSON><PERSON>", "_ref$placement", "_ref$strategy", "_ref$modifiers", "innerRef", "setPopperElement", "_React$useState2", "arrowElement", "setArrowElement", "_usePopper", "childrenProps", "style", "hasPopperEscaped", "modifiersData", "hide", "isReferenceHidden", "arrowProps", "Reference", "ref<PERSON><PERSON><PERSON>", "Boolean", "hasElementType", "Element", "hasMap", "Map", "hasSet", "Set", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "equal", "a", "b", "constructor", "i", "it", "size", "next", "done", "has", "get", "RegExp", "source", "flags", "valueOf", "prototype", "toString", "hasOwnProperty", "call", "$$typeof", "module", "exports", "error", "message", "match", "console", "warn"], "sourceRoot": ""}