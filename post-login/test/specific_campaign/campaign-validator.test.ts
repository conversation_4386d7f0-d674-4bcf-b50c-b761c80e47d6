

// npm test -- campaign-validator.test.ts

import { Campaigns } from '@sr/shared-product-components';

import { CampaignValidator } from "../../client/containers/campaign/new-multichannel-flow/campaign-validator";

// Mock data for testing
const mockCampaignInfo: Campaigns.ICurrentCampaign = {
  contentTabInfo: {
    stepVariants: [
      { step_type: 'send_email' },
      { step_type: 'auto_linkedin_view_profile' },
      { step_type: 'auto_send_linkedin_connection_request' }
    ]
  },
  basicInfo: {
    settings: {
      campaign_linkedin_settings: [
        { automation_enabled: false }
      ]
    }
  }
} as any;

const mockCampaignInfoWithAutomation: Campaigns.ICurrentCampaign = {
  contentTabInfo: {
    stepVariants: [
      { step_type: 'send_email' },
      { step_type: 'auto_linkedin_view_profile' }
    ]
  },
  basicInfo: {
    settings: {
      campaign_linkedin_settings: [
        { automation_enabled: true }
      ]
    }
  }
} as any;

const mockNavigationProps = {
  history: { push: jest.fn() } as any,
  match: { path: '/test-path' } as any,
  logInStore: { getCurrentTeamId: () => 'test-team-id' } as any
};

describe('CampaignValidator', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateLinkedinAutomationStatus', () => {
    it('should return false when campaign has LinkedIn automation steps but automation is disabled', () => {
      const result = CampaignValidator.validateLinkedinAutomationStatus(mockCampaignInfo);
      expect(result).toBe(false);
    });

    it('should return true when campaign has LinkedIn automation steps and automation is enabled', () => {
      const result = CampaignValidator.validateLinkedinAutomationStatus(mockCampaignInfoWithAutomation);
      expect(result).toBe(true);
    });

    it('should return true when campaign has no LinkedIn automation steps', () => {
      const campaignWithoutLinkedInSteps = {
        ...mockCampaignInfo,
        contentTabInfo: {
          stepVariants: [
            { step_type: 'send_email' },
            { step_type: 'manual_task' }
          ]
        }
      } as any;

      const result = CampaignValidator.validateLinkedinAutomationStatus(campaignWithoutLinkedInSteps);
      expect(result).toBe(true);
    });
  });

  describe('validateCampaign', () => {
    it('should return validation errors when LinkedIn automation validation fails', () => {
      const errors = CampaignValidator.validateCampaign(mockCampaignInfo, mockNavigationProps);

      expect(errors).toHaveLength(1);
      expect(errors[0].issue).toBe("LinkedIn Account with Automation is Required");
      expect(errors[0].actionType).toBe('secondary');
    });

    it('should return no validation errors when all validations pass', () => {
      const errors = CampaignValidator.validateCampaign(mockCampaignInfoWithAutomation, mockNavigationProps);

      expect(errors).toHaveLength(0);
    });

    it('should call navigation action when onAction is triggered', () => {
      const errors = CampaignValidator.validateCampaign(mockCampaignInfo, mockNavigationProps);

      expect(errors).toHaveLength(1);

      // Trigger the action
      if (errors[0].onAction) {
        errors[0].onAction();
      }

      expect(mockNavigationProps.history.push).toHaveBeenCalledTimes(1);
      const callArgs = (mockNavigationProps.history.push as jest.Mock).mock.calls[0][0];

      // The navigation action should be called with the correct path
      // It could be a string or an object depending on the router implementation
      if (typeof callArgs === 'string') {
        expect(callArgs).toContain('channel_setup/linkedin');
      } else if (typeof callArgs === 'object' && callArgs.pathname) {
        expect(callArgs.pathname).toContain('channel_setup/linkedin');
      } else {
        // Just verify that the navigation was called
        expect(mockNavigationProps.history.push).toHaveBeenCalled();
      }
    });
  });

  describe('extensibility', () => {
    it('should be easy to add new validation methods', () => {
      // This test demonstrates how the class can be extended
      // Future developers can add new validation methods following this pattern

      // Example: Testing that the validateCampaign method can handle multiple validation errors
      const campaignWithMultipleIssues = {
        ...mockCampaignInfo,
        // This would trigger LinkedIn automation validation error
        // Future validation methods could add more errors to the array
      } as any;

      const errors = CampaignValidator.validateCampaign(campaignWithMultipleIssues, mockNavigationProps);

      // Currently only LinkedIn validation exists, but this structure supports multiple validations
      expect(Array.isArray(errors)).toBe(true);
      expect(errors.length).toBeGreaterThanOrEqual(0);
    });
  });
});
