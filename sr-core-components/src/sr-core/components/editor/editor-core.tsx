import * as React from 'react';
import * as _ from 'lodash';
// import * as CONSTANTS from '../../../constants';

import { Editor } from '@tinymce/tinymce-react';
import { SRLoaderDefault } from '@sr/design-component';
import { ITinyMceOptions } from '../../models/editor';

interface IEditorCoreProps {

  // use this function to add buttons to editor toolbar on initialization
  onEditorSetup?: (tinymceEditor: any) => any;

  autoFocus: boolean;
  onEditorFocus: any;
  onEditorChange: (newBody: string) => any;

  body: string;

  orgId: number;
  accountId: number;

  editorDisabled: boolean;

  getTinymceOptions(data: {
    autoFocusOnEditor: boolean;
    accountId: number;
  }): ITinyMceOptions;
  
  TINYMCE_URL: string;
}

interface IEditorCoreStates {
  isLoadingEditor?: boolean;
}

export class EditorCore extends React.Component<IEditorCoreProps, IEditorCoreStates> {

  constructor(props: IEditorCoreProps) {
    super(props);

    this.state = {
      isLoadingEditor: false,
    };

  }

  /*
  componentDidMount() {
    // (window as any).tinymce.execCommand('fontName', false, "Arisl");
    //   ed.on('init', function (ed) {
    //     ed.target.editorCommands.execCommand("fontName", false, "Arial");
    // });
    // this.getSelectedTemplateLabel(this.props.templates || []);
  }
  */


  render() {

    const {
      body,
      autoFocus,
      accountId,
      orgId,
      editorDisabled: editorDisabled
    } = this.props;


    // enable fullpage plugin
    const enableFullpagePlugin = (orgId ? true : true)//CONSTANTS.enableFullPagePluginTinymceForOrg(orgId);
    /////

    const getTinymceOptions = this.props.getTinymceOptions

    const TINYMCE_OPTIONS = getTinymceOptions({
      autoFocusOnEditor: autoFocus,
      accountId: accountId,
      
    });

    const TINYMCE_URL = this.props.TINYMCE_URL

    TINYMCE_OPTIONS.setup = (editor: any) => {

      editor.on('init', (_: any) => {
        this.setState({ isLoadingEditor: false });
      });

      // this function is used to add buttons to editor toolbar - like 'insert merge-tag' / 'insert template' etc.
      if (this.props.onEditorSetup) {
        this.props.onEditorSetup(editor);
      }

    }

    if (enableFullpagePlugin) {
      const defaultPlugins = TINYMCE_OPTIONS.plugins;
      TINYMCE_OPTIONS.plugins = `${defaultPlugins} fullpage`;
    }


    return (
      <>
        {/* editor is disabled initially when fetching the email draft */}
        {(this.state.isLoadingEditor || editorDisabled) &&
          <div style={{ marginTop: '1em', marginBottom: '1em' }}>
            <SRLoaderDefault />
          </div>
        }

        <div style={{ display: this.state.isLoadingEditor ? 'none' : 'inherit'}}>
          <Editor
            tinymceScriptSrc={TINYMCE_URL}
            value={body}
            onEditorChange={this.props.onEditorChange}
            init={TINYMCE_OPTIONS}
            onFocus={this.props.onEditorFocus}
          />
        </div>
      </>
    );
  }
}
