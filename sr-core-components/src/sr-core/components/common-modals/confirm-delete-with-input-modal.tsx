import * as React from 'react'
import * as _ from 'lodash';
import { SrModal, SRButtonFilled, SRButtonOutline, SRInput } from '@sr/design-component'

interface IConfirmDeleteWithInputModalProps {
  onConfirm: () => void;
  onCancel: () => void;
  heading: string;
  modalLoading: boolean;
}

interface IConfirmDeleteWithInputModalStates {
  deleteInputValue: string;
}

export class ConfirmDeleteWithInputModal extends React.Component<
  IConfirmDeleteWithInputModalProps,
  IConfirmDeleteWithInputModalStates
> {

  constructor(props: IConfirmDeleteWithInputModalProps) {
    super(props);

    this.state = {
      deleteInputValue: '',
    }

    this.deleteInputChange = this.deleteInputChange.bind(this);
  }

  deleteInputChange(e: React.ChangeEvent<HTMLInputElement>) {
    this.setState({ deleteInputValue: e.target.value });
  }

  render(): JSX.Element {

    const props = this.props;

    return <SrModal
    onClose={props.onCancel}
    title ={<div className='sr-h3 text-white'>
        {this.props.heading}
    </div>}
    size='small'
    isNegative
    content = {
      <div>  
        <div className='mb-6'>
          {props.children}
          <div className='mb-2 sr-h6'>Type the word "delete" below to delete</div>
          <SRInput type='text' autoFocus handleChange={this.deleteInputChange} name='srinput' width='fluid' placeholder='delete'/>          
        </div>

        <div className='sr-align-right mb-2'>
          <SRButtonOutline
            onClick= {props.onCancel}
            disable={props.modalLoading}
            text= "Cancel"
            width='default'
            type="button"
            className='mr-2'
          />
          <SRButtonFilled
            onClick= {props.onConfirm}
            loading={props.modalLoading}
            text= 'Delete'
            type="button"
            width='default'
            disable={(_.lowerCase(this.state.deleteInputValue)) !== 'delete'}
            isNegative
            />
      </div>  
  </div>
  }
  />;

  }
}
