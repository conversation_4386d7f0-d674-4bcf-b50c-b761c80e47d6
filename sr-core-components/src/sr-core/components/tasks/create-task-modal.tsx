import * as React from "react";
import { SRButtonOutline, SRIconGeneral, SRIconLinkedin, SRIconPhone, SRIconWhatsapp, SRTooltip2, SrIconMail, SrIconSms } from "@sr/design-component";
// import { Button } from "semantic-ui-react";
// import { TailwindModal2 } from "../tailwind-components/TailwindModal";
import * as _ from "lodash";
import { Field, Form, Formik, FormikHelpers } from "formik";
// import { getFormattedDate } from "../FormatDateTime";
import { SRSearchDropdown } from "@sr/design-component";
// import { Button } from "../tailwind-components/tailwind-buttons";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import * as moment_ from "moment-timezone";
import { classNames } from "@sr/design-component";
import { ISRDropdownOption, SRButtonFilled, SRSimpleSelectionDropdown, SrModal,
  // SrSearchableDropdown,
  // SrNavBar
} from "@sr/design-component";
import { ITinyMceOptions } from "../../models/editor";
import { ITemplateTags } from "../../models/column";
import { IServerResponse } from "../../models/server";
import { Campaigns } from "../../models/campaigns";
import { Prospects } from "../../models/prospect";
import { Task } from "../../models/task";
import { Alerts } from "../../models/alerts";
import { IEmailBodyPromptRequest, IGenerateSubjectRequest, IRegenerateEmailBodyRequest, IRegenerateSubjectRequest } from "../../models/gpt";
import { LogIn } from "../../models/login";
import { commonSearchFilterObj } from "../../utils/utils";
import CreateEditManualEmailTask from "./create-edit-manual-email-task";
import { OpenMailFormNew } from "../mail-form/mail-form-multichannel-new";

const moment = moment_

interface ICreateTaskModalProps {
  onClose: () => void;
  pushToAlterStore?: (msg: string, isSuccess: boolean, delayInSec: number) => void;
  alertStore?: Alerts.IAlertStore
  task?: Task.ITask;
  taskCreatedUpdatedSuccess:()=>void;
  getTinymceOptions(data: {
    autoFocusOnEditor: boolean;
    accountId: number;
  }): ITinyMceOptions;

  TINYMCE_URL: string;
  getTags(): IServerResponse<ITemplateTags>;
  createTask(task_data: Task.INewTask): IServerResponse<{}>
  updateTask(taskId: string, task_data: Task.IUpdateTask): IServerResponse<{}>
  searchProspects(data: Prospects.ISearchAPIRequest, to_create_task?: boolean): IServerResponse<{
    total_count?: number;
    max_count?: number;
    rows_per_page: number;
    prospects: Prospects.IProspectObject[];
    show_load_more: boolean;
  }>
  accountId: number;
  selectedProspect?: Prospects.IProspectObject;
  orgId: number;
  enable_native_calling?: boolean;
  enable_calendar?: boolean;
  timezone: string;
  extensionView?: boolean
  templates: Campaigns.ITemplatesForCategory[]
  generateAIContentProps?: {
    campaignStore: Campaigns.ICampaignStore;
    enableContentAI: boolean;
    saveAsTemplate: (template: Campaigns.ICampaignStepTemplateFELib) => void
    enableFullpagePlugin: boolean
    rephraseText(text: String): IServerResponse<{
      rephrased_text: string;
    }>
    shortenText(text: String): IServerResponse<{
      shortened_text: string;
    }>
    regenerateEmailBodyThroughCommand(data: IRegenerateEmailBodyRequest): IServerResponse<{
      email_body: string;
    }>
    regenerateSubject(data: IRegenerateSubjectRequest): IServerResponse<{
      email_subject: string;
    }>
    generateEmailBodyThroughPrompt(data: IEmailBodyPromptRequest): IServerResponse<{
      email_body: string;
    }>
    generateEmailSubjectThroughGPT(data: IGenerateSubjectRequest): IServerResponse<{
      email_subject: string;
    }>
    logInStore?: LogIn.ILogInStore
  }
}
interface IFormErrors {
  [fieldName: string]: string;
}
type IErrors = IFormErrors;
type IStepNumber = 'one' | 'two';
interface ICreateTaskModalState {
  selectedTaskChannelType: Task.ITaskChannelType;
  isEdit:boolean;
  initialValue: ITaskFormikFields;
  isSubmitting: boolean;
  isProspectSearching: boolean;
  formikErrors?:IFormErrors;
  prospectResults?: Prospects.IProspectObject[];
  selectedAssignee?: {id:number,text:string};
  selectedProspect?:{id:number,text:string};
  searchProspectQuery:string;
  emailSubject?:string;
  emailBody?:string;
  liActionType:'send_linkedin_connection_request' |'send_linkedin_message' | 'linkedin_view_profile' | 'send_linkedin_inmail';
  step_number: IStepNumber;
  showButtons: boolean
}
interface ITaskFormikFields {
  action: 'send_linkedin_connection_request' |'send_linkedin_message' | 'linkedin_view_profile' | 'send_linkedin_inmail';
  li_msg?: string;
  email_body?: string;
  sms_body?: string;
  wp_msg?: string;
  call_script?: string,
  notes?: string;
  date: Date;
  subject?: string;
  inMailSubject?:string;
  priority: Task.ITaskPriority;
}

/*
 declare type Itabs = {
    name: string;
    href: string;
    count: number;
    current: boolean;
  }[];
*/

// function getTabs(step_number: IStepNumber){

// const tabs = [
//   {

//   name :  "Task Configuration",
//   href: `https://www.w3schools.com/`,
//   count: 0,
//   current: step_number == "one"
// },
// {
//   name :  "Content",
//   href: `https://www.w3schools.com/`,
//   count: 0,
//   current: step_number == 'two'
// }
// ]

// return tabs;
// }

// function getPriorityOptions(){
//   const options: ISRDropdownOption[] = [
//     {
//     displayText: "Critical",
//     value: "critical"
//   },
//   {
//     displayText: "High",
//     value: "high"
//   },
//   {
//     displayText: "Normal",
//     value: "normal"
//   },
//   {
//     displayText: "Low",
//     value: "low"
//   }
// ]

// return options;
// }


function isEmailTask(task: Task.ITask): task is Task.ITask & { task_data: Task.ISendEmailTaskData } {
  return task.task_type === 'manual_send_email';
}

function isMagicContentTask(task: Task.ITask): task is Task.ITask & { task_data: Task.IAutoEmailMagicContentTaskData | Task.IManualEmailMagicContentTaskData } {
  return task.task_type === 'auto_email_magic_content' || task.task_type === 'manual_email_magic_content';
}

function isLinkedInMessageTask(task: Task.ITask): task is Task.ITask & { task_data: Task.ILinkedinSendMessageTaskData } {
  return task.task_type === 'send_linkedin_message';
}

function isWhatsAppTask(task: Task.ITask): task is Task.ITask & { task_data: Task.ISendWhatsAppMessageTaskData } {
  return task.task_type === 'send_whatsapp_message';
}

function isSmsTask(task: Task.ITask): task is Task.ITask & { task_data: Task.ISendSmsTaskData } {
  return task.task_type === 'send_sms';
}

function isCallTask(task: Task.ITask): task is Task.ITask & { task_data: Task.ICallTaskData } {
  return task.task_type === 'call';
}

// function isLinkedInConnectionTask(task: Task.ITask): task is Task.ITask & { task_data: Task.ILinkedinConnectionRequestTaskData } {
//   return task.task_type === 'send_linkedin_connection_request';
// }

// function isGenericTask(task: Task.ITask): task is Task.ITask & { task_data: Task.IGenericTaskData } {
//   return task.task_type === 'general_task';
// }

function isLinkedInInmailTask(task: Task.ITask): task is Task.ITask & { task_data: Task.ILinkedinSendInMail } {
  return task.task_type === 'send_linkedin_inmail';
}

export class CreateTaskModal extends React.Component<
  ICreateTaskModalProps,
  ICreateTaskModalState
> {
  constructor(props: ICreateTaskModalProps) {
    super(props);

    let initialEmailBody: string | undefined;
    let initialEmailSubject: string | undefined;

    if (props.task) {
      if (isEmailTask(props.task)) {
        initialEmailBody = props.task.task_data.body;
        initialEmailSubject = props.task.task_data.subject;
      } else if (isLinkedInMessageTask(props.task) || isWhatsAppTask(props.task) ||
                isSmsTask(props.task) || isCallTask(props.task)) {
        initialEmailBody = props.task.task_data.body;
      } else if (isLinkedInInmailTask(props.task)) {
        initialEmailBody = props.task.task_data.body;
        initialEmailSubject = props.task.task_data.subject;
      } else if (isMagicContentTask(props.task)) {
        initialEmailBody = props.task.task_data.generated_body;
        initialEmailSubject = props.task.task_data.generated_subject;
      }
    }

    this.state = {
      selectedTaskChannelType: this.getTaskTypeFromChannel(),
      isEdit: !!this.props.task,
      initialValue: this.getTaskFormInitialValue(props.task),
      isSubmitting: false,
      isProspectSearching: false,
      prospectResults: [],
      liActionType: this.getLiActionType(),
      searchProspectQuery: "",
      emailBody: initialEmailBody,
      emailSubject: initialEmailSubject,
      step_number: 'one',
      selectedAssignee: this.getInitialAssigneeDetails(),
      showButtons: true
    };

    this.onChangeTaskTab = this.onChangeTaskTab.bind(this);
    this.disabledChannels = this.disabledChannels.bind(this);
    this.handleSubmit = this.handleSubmit.bind(this);
    this.createTask = this.createTask.bind(this);
    this.updateTask = this.updateTask.bind(this);
    this.handleProspectSearchChange = _.debounce(
      this.handleProspectSearchChange.bind(this),
      1000
    );
    this.getProspectOptions = this.getProspectOptions.bind(this);
    this.validateDefs = this.validateDefs.bind(this);
    this.getTaskFormInitialValue = this.getTaskFormInitialValue.bind(this);
    this.getTaskTypeFromChannel = this.getTaskTypeFromChannel.bind(this);
    this.getInitialAssigneeDetails = this.getInitialAssigneeDetails.bind(this);
    this.getInitialProspectDetails = this.getInitialProspectDetails.bind(this);
    this.updateProspectSearchQuery = this.updateProspectSearchQuery.bind(this)
    this.getActionOptions = this.getActionOptions.bind(this)

    this.handleLiActionChange = this.handleLiActionChange.bind(this)
    this.getLiActionType = this.getLiActionType.bind(this)
    this.handleEmailSubjectChange = this.handleEmailSubjectChange.bind(this)
    this.handleEmailBodyChange = this.handleEmailBodyChange.bind(this)
    this.handleDueDateChange = this.handleDueDateChange.bind(this);
    this.handleNext = this.handleNext.bind(this);
    this.showCreateTaskButtons = this.showCreateTaskButtons.bind(this);
    this.handleProspectSelect = this.handleProspectSelect.bind(this)
    this.handleAssigneeSelect = this.handleAssigneeSelect.bind(this)
    this.getAssigneeOptions = this.getAssigneeOptions.bind(this)
  }
  getLiActionType(){
    if(!!this.props.task){
      const task_details = this.props.task
      if (task_details.task_type === "send_linkedin_message") {
        return "send_linkedin_message";
      }else if (task_details.task_type === "send_linkedin_connection_request") {
        return "send_linkedin_connection_request";
      } else if (task_details.task_type === "send_linkedin_inmail") {
        return "send_linkedin_inmail";
      } else if (task_details.task_type === "linkedin_view_profile") {
        return "linkedin_view_profile";
      } else return "send_linkedin_connection_request"
    }else{
      return "send_linkedin_connection_request"
    }
  }

  getInitialAssigneeDetails() {
    const task = this.props.task;
    if (!!task) {
      const selectedAssignee = _.find(this.props.generateAIContentProps?.logInStore?.getCurrentTeamObj.all_members, (t) => {
        return t.user_id === task.assignee.id
      })
      const initialAssigneeValue = {
        id: selectedAssignee!.user_id,
        text: selectedAssignee!.first_name + " " + selectedAssignee!.last_name + " <" + selectedAssignee!.email + ">"}

      return initialAssigneeValue
    } else{
      const account = this.props.generateAIContentProps?.logInStore?.accountInfo
      const assigneeId = account ? {
        id: account!.internal_id,
        text: account!.first_name + " " + account!.last_name + " <" + account!.email + ">"} : undefined;
      ;
      return assigneeId;
    }
  }

  getInitialProspectDetails(){
    const task = this.props.task;
    if (!!task?.prospect) {
      const initialProspectValue = {
        id: task.prospect.id,
        text: task.prospect.name || "",
      }
      return initialProspectValue
    }

    else if (!!this.props.selectedProspect) {
      const prospect = this.props.selectedProspect
      return {
        id: prospect.id,
        text: prospect.first_name + " " + prospect.last_name
      }
    }
    else return
  }

  componentDidMount() {
    const initial_prospect_details = this.getInitialProspectDetails();

    this.setState({
      selectedAssignee: this.getInitialAssigneeDetails(),
      selectedProspect: initial_prospect_details,
      prospectResults: this.props.selectedProspect ? [this.props.selectedProspect] : undefined,
      searchProspectQuery: initial_prospect_details?.text || ""
    })
  }

  getTaskTypeFromChannel(): Task.ITaskChannelType {
    const taskType = this.props.task?.task_type;
    if (!!taskType) {
      if (
        taskType === "send_linkedin_connection_request" ||
        taskType == "send_linkedin_message" ||
        taskType == "send_linkedin_inmail" ||
        taskType == "linkedin_view_profile"
      ) {
        return "linkedin";
      } else if (taskType === "general_task") {
        return "generic";
      } else if (taskType == "send_sms") {
        return "sms";
      } else if (taskType == "send_whatsapp_message") {
        return "whatsApp";
      } else if (taskType == "manual_send_email") {
        return "email";
      }
      else if (taskType == "call") {
        return "call"
      }
    }
    return "email";
  }

  getCreateTaskFormData(): ITaskFormikFields {
    const initialValues: ITaskFormikFields = {
      action: "send_linkedin_connection_request",
      priority: "normal",
      li_msg: "",
      email_body: "",
      sms_body: "",
      wp_msg: "",
      call_script: "",
      notes: "",
      date: new Date,
      subject: "",
      inMailSubject:""
    };
    return initialValues;
  }

  getTaskFormInitialValue(task_details: Task.ITask | undefined) {
    const initialValues: ITaskFormikFields = this.getCreateTaskFormData()
    if (!!task_details) {
      if (task_details.task_type === "manual_send_email") {
        const emailTask = task_details as Task.ITask & { task_data: Task.ISendEmailTaskData };
        initialValues.email_body = emailTask.task_data.body;
        initialValues.subject = emailTask.task_data.subject;
      } else if (task_details.task_type === "send_linkedin_message") {
        const linkedinTask = task_details as Task.ITask & { task_data: Task.ILinkedinSendMessageTaskData };
        initialValues.li_msg = linkedinTask.task_data.body;
        initialValues.action = "send_linkedin_message";
      } else if (task_details.task_type === "send_whatsapp_message") {
        const whatsappTask = task_details as Task.ITask & { task_data: Task.ISendWhatsAppMessageTaskData };
        initialValues.wp_msg = whatsappTask.task_data.body;
      } else if (task_details.task_type === "send_sms") {
        const smsTask = task_details as Task.ITask & { task_data: Task.ISendSmsTaskData };
        initialValues.sms_body = smsTask.task_data.body;
      } else if (task_details.task_type === "call"){
        const callTask = task_details as Task.ITask & { task_data: Task.ICallTaskData };
        initialValues.call_script = callTask.task_data.body;
      } else if (task_details.task_type === "send_linkedin_connection_request") {
        const connectionTask = task_details as Task.ITask & { task_data: Task.ILinkedinConnectionRequestTaskData };
        initialValues.li_msg = connectionTask.task_data.request_message;
        initialValues.action = "send_linkedin_connection_request";
      } else if (task_details.task_type === "general_task") {
        const genericTask = task_details as Task.ITask & { task_data: Task.IGenericTaskData };
        initialValues.notes = genericTask.task_data.task_notes;
      } else if (task_details.task_type === "send_linkedin_inmail") {
        const inmailTask = task_details as Task.ITask & { task_data: Task.ILinkedinSendInMail };
        initialValues.li_msg = inmailTask.task_data.body;
        initialValues.inMailSubject = inmailTask.task_data.subject;
        initialValues.action = "send_linkedin_inmail";
      } else if (task_details.task_type === "linkedin_view_profile") {
        initialValues.action = "linkedin_view_profile";
      }
      initialValues.date = new Date(task_details.due_at!);
      initialValues.priority = task_details.priority;
      return initialValues;
    } else {
      return initialValues;
    }
  }
  getTaskMenuItems() {
    let navigation: {
      name: string;
      icon: React.ReactElement;
      type: Task.ITaskChannelType;
      active: boolean;
    }[] = [];

    navigation = [
      {
        name: "Email",
        icon: <SrIconMail />,
        type: "email",
        active: true,
      },
      {
        name: "Linkedin",
        icon: <SRIconLinkedin />,
        type: "linkedin",
        active: true,
      },
      {
        name: "SMS",
        icon: <SrIconSms />,
        type: "sms",
        active: true,
      },
      {
        name: "WhatsApp",
        icon: <SRIconWhatsapp />,
        type: "whatsApp",
        active: true,
      },
      {
        name: "Generic",
        icon: <SRIconGeneral />,
        type: "generic",
        active: true,
      },
    ];
    if (this.props.enable_native_calling) {
      navigation.push({
        name: "Call",
        icon: <SRIconPhone />,
        type: "call",
        active: true
      })
    }

    return navigation;
  }

  updateTask(taskID:string,newTaskData:Task.IUpdateTask) {
    console.log("updating task")
    this.setState({ isSubmitting: true });
    const updateTask = this.props.updateTask
    const pushToAlterStore = this.props.pushToAlterStore

    updateTask(taskID,newTaskData)
      .then((_) => {
        this.setState({ isSubmitting: false });
        this.props.onClose();
        this.props.taskCreatedUpdatedSuccess();
        (pushToAlterStore && pushToAlterStore("task updated", true, 1))
      })
      .catch((err) => {
        this.setState({ isSubmitting: false });
        (pushToAlterStore && pushToAlterStore(err.response.data.message, false, 2))
      });
  }
  createTask(task: Task.INewTask) {
    console.log("creating task")
    this.setState({ isSubmitting: true });
    const createTask = this.props.createTask
    const pushToAlterStore = this.props.pushToAlterStore

    createTask(task)
      .then((_) => {
        this.setState({ isSubmitting: false });
        this.props.onClose();
        this.props.taskCreatedUpdatedSuccess();
        (pushToAlterStore && pushToAlterStore("Task created", true, 2))
      })
      .catch((err) => {
        this.setState({ isSubmitting: false });
        (pushToAlterStore && pushToAlterStore(err.response.data.message, false, 2))
      });
  }

  /* We need to tweak this*/
  getTaskTypeByChannelType(
    selectedTaskType: Task.ITaskChannelType,
    _: string
  ): Task.ITaskType | undefined {
    if (selectedTaskType === "email") {
      return "manual_send_email";
    } else if (selectedTaskType == "linkedin") {
      return this.state.liActionType
    } else if (selectedTaskType == "generic") {
      return "general_task";
    } else if (selectedTaskType === "sms") {
      return "send_sms";
    } else if (selectedTaskType === "whatsApp") {
      return "send_whatsapp_message";
    } else if (selectedTaskType === "call") {
      return "call"
    } else return;
  }
  getAssigneeOptions() {
    var assigneeOption: ISRDropdownOption[] = []
    var teamMembers = this.props.generateAIContentProps?.logInStore?.getCurrentTeamObj.all_members

    _.map(teamMembers, (t) => {
      assigneeOption.push({
        displayText: t.first_name + " " + t.last_name + " <" + t.email + ">" || "",
        value: t.user_id,
        displayElement:
          <div>
            <span>{t.first_name + " " + t.last_name}</span>
            <span className="sr-h7 !font-normal ml-2">{t.email}</span>
          </div>

      })
    })
    return assigneeOption
  }

  validateDefs(values: ITaskFormikFields) {
    const errors: IErrors = {} as IErrors;
    const selectedTaskChannel = this.state.selectedTaskChannelType;
    const prospect = this.state.selectedProspect
    if (selectedTaskChannel === "linkedin" && this.state.liActionType!="linkedin_view_profile" && !values.li_msg) {
      errors["li_msg"] = "Message cannot be empty";
    }
    if (selectedTaskChannel === "sms" && !values.sms_body) {
      errors["sms_body"] = "SMS body is required";
    }
    if (selectedTaskChannel === "call" && !values.call_script) {
      errors["call_script"] = "Call script is required";
    }
    if (selectedTaskChannel === "whatsApp" && !values.wp_msg) {
      errors["wp_msg"] = "message cannot be empty";
    }
    if (selectedTaskChannel === "generic" && !values.notes) {
      errors["notes"] = "Task Description cannot be empty";
    }
    if(prospect == undefined){
      errors["prospect"] = "Please select a prospect";
    }
    // if (!values.date) {
    //   errors["date"] = "Please select due date";
    // }
    this.setState({formikErrors:errors})
    return errors;
  }

  handleSubmit(values: any, _: FormikHelpers<any>) {
    if(this.state.step_number == "two" || this.state.liActionType === 'linkedin_view_profile'){

    const selectedTaskChannel = this.state.selectedTaskChannelType;
    let task_data: Task.ITaskData | undefined;
    const selectedTaskType = this.getTaskTypeByChannelType(
      selectedTaskChannel,
      values.action
    );
    const pushToAlterStore = this.props.pushToAlterStore
    if (!!selectedTaskType) {
      if (selectedTaskType == "send_linkedin_message") {
        task_data = {
          task_type: selectedTaskType,
          body: values.li_msg,
        };
      } else if (selectedTaskType == "send_linkedin_connection_request") {
        task_data = {
          task_type: selectedTaskType,
          request_message: values.li_msg,
        };
      } else if (selectedTaskType == "manual_send_email") {
        const subject = this.state.emailSubject;
        const body = this.state.emailBody;
        if(!!subject && !!body && subject.length>0 && body.length>0){
          task_data = {
            task_type: selectedTaskType,
            subject: subject,
            body: body,
          };
        }
      } else if (selectedTaskType == "send_sms") {
        task_data = {
          task_type: selectedTaskType,
          body: values.sms_body,
        };
      } else if (selectedTaskType == "call") {
        task_data = {
          task_type: selectedTaskType,
          body: values.call_script,
        };
      } else if (selectedTaskType == "send_whatsapp_message") {
        task_data = {
          task_type: selectedTaskType,
          body: values.wp_msg,
        };
      } else if (selectedTaskType == "general_task") {
        task_data = {
          task_type: selectedTaskType,
          task_notes: values.notes,
        };
      } else if (selectedTaskType == "send_linkedin_inmail") {
        task_data = {
          task_type: selectedTaskType,
          subject:values.inMailSubject,
          body:values.li_msg
        };
      } else if (selectedTaskType == "linkedin_view_profile") {
        task_data = {
          task_type: selectedTaskType
        };
      }
      if (!!task_data) {
        const assignee = this.state.selectedAssignee
        const prospect = this.state.selectedProspect
        const due_at = this.state.initialValue.date
        if (!!prospect) {
          if (this.state.isEdit) {
            let updateTask: Task.IUpdateTask = {
              task_data: task_data,
              status: {
                status_type: "due",
                due_at: due_at,
              },
              assignee_id: assignee?.id || this.props.accountId,
              prospect_id: prospect.id,
              priority: values.priority,
            };
            this.updateTask(this.props.task?.task_id!,updateTask);
          }
          else{
            let newTask: Task.INewTask = {
              task_type: selectedTaskType,
              task_data: task_data,
              status: {
                status_type: "due",
                due_at: due_at,
              },
              created_via: "manual",
              assignee_id: assignee?.id || this.props.accountId,
              prospect_id: prospect.id,
              priority: values.priority,
              is_auto_task:false,
              note: values.notes
            };
            this.createTask(newTask);
          }
        }else{
          (pushToAlterStore && pushToAlterStore("Please select a Prospect", true, 3))
        }
      } else {
        (pushToAlterStore && pushToAlterStore("Please provide all the details", false, 3))
      }
    } else {
      (pushToAlterStore && pushToAlterStore("Invalid task type", false, 2))
    }
    } else{
      this.setState({ step_number: "two"})
    }
  }

  handleEmailSubjectChange(subject: string) {
    this.setState({emailSubject: subject})
  }

  handleEmailBodyChange(body: string) {
    this.setState({ emailBody: body });
  }
  onChangeTaskTab(selectedTaskType: Task.ITaskChannelType) {
    this.setState({ selectedTaskChannelType: selectedTaskType });
  }

  disabledChannels(name: Task.ITaskChannelType) {
    return (
      !!this.props.task &&
      this.props.task.task_type !== this.getTaskTypeByChannelType(name, "")
    );
  }

  getProspectOptions() {
    const prospectResults = this.state.prospectResults;
    let prospectOptions: ISRDropdownOption[] = [];

    if (this.props.task) {
      const prospect = this.props.task.prospect
      prospectOptions.push({
        displayText: prospect.name || "",
        value: prospect.id,
        displayElement:
          <div>
            <span>{prospect.name}</span>
            <span className="sr-h7 !font-normal ml-2">{prospect.email}</span>
          </div>

      })
    }
    if (this.props.selectedProspect && !prospectResults?.find(p => p.id == this.props.selectedProspect?.id)) {
      const prospect = this.props.selectedProspect
      prospectOptions.push({
        displayText: prospect.first_name + " " + prospect.last_name,
        value: prospect.id,
        displayElement:
          <div>
            <span>{prospect.first_name + " " + prospect.last_name}</span>
            <span className="sr-h7 !font-normal ml-2">{prospect.email}</span>
          </div>
      })

    }

    _.map(prospectResults, (prospect) => {
      prospectOptions.push({
        displayText: prospect.first_name + " " + prospect.last_name,
        value: prospect.id,
        displayElement:
          <div>
            <span>{prospect.first_name + " " + prospect.last_name}</span>
            <span className="sr-h7 !font-normal ml-2">{prospect.email}</span>
          </div>

      });
    });
    // console.log(prospectResults,prospectOptions)
    return prospectOptions;
  }

  handleProspectSearchChange(data: string) {

    this.setState({ prospectResults: [], selectedProspect: undefined, isProspectSearching: true });

    const prospectSearchString = data;

    const query = commonSearchFilterObj(prospectSearchString)


    const searchProspects = this.props.searchProspects

    searchProspects({
        page: 1,
        query: query,
      }, true)
      .then((results) => {
        this.setState({
          prospectResults: results.data.prospects,
          isProspectSearching: false,
        });
      });
  }


  updateProspectSearchQuery(query:string){
    console.log(query)
    this.setState({searchProspectQuery:query})
    this.handleProspectSearchChange(query)
  }
  // updateAssigneeQuery(query:string){
  //   console.log(query)
  //   this.setState({searchProspectQuery:query})
  //   this.handleProspectSearchChange(query)
  // }

  getActionOptions(){
    const sendLiConnection:{value:"send_linkedin_connection_request",displayText:string}  = {
      value:"send_linkedin_connection_request",
      displayText:"Connection Request"
    }
    const sendLiMessage:{value:"send_linkedin_message",displayText:string} = {
      value:"send_linkedin_message",
      displayText:"Send Message"
    }
    const viewLiProfile:{value:'linkedin_view_profile',displayText:string} = {
      value:'linkedin_view_profile',
      displayText:"View Profile"
    }
    const sendInMail:{value:'send_linkedin_inmail',displayText:string} = {
      value:'send_linkedin_inmail',
      displayText:"Send InMail"
    }
    if(this.state.isEdit){
      if(this.state.initialValue?.action=="send_linkedin_connection_request"){
        return [sendLiConnection]
      }else if(this.state.initialValue?.action=="linkedin_view_profile"){
        return [viewLiProfile]
      }else if(this.state.initialValue?.action=="send_linkedin_inmail"){
        return [sendInMail]
      }else{
        return [sendLiMessage]
      }
    }else{

      return [sendLiConnection,sendLiMessage,viewLiProfile,sendInMail]
    }
  }
  handleLiActionChange(  e:any) {
    this.setState({liActionType:e.value})
  }

  handleDueDateChange( date: Date | null){
    if(!!date){

      const initialValue = this.state.initialValue;
      if(!!initialValue){
        if(!!initialValue.date){
          initialValue.date = date;
        }
      }
      this.setState({initialValue: initialValue});
    }
  }

  handleNext(){

    const pushToAlterStore = this.props.pushToAlterStore
    if(this.state.selectedProspect == undefined){
      (pushToAlterStore && pushToAlterStore("Please select a prospect", false, 3))
    }else{
      this.setState({ step_number: "two"});
    }

  }

  getTitileForTask(taskType: Task.ITaskChannelType) {

    switch (taskType) {
      case 'email': return 'Email'
      case 'call': return 'Call'
      case 'generic': return 'Generic'
      case 'linkedin': return 'LinkedIn'
      case 'sms': return 'SMS'
      case 'whatsApp': return 'WhatsApp'
    }

  }

  showCreateTaskButtons(flag: boolean) {
    this.setState({ showButtons: flag })
  }

  handleProspectSelect(data: ISRDropdownOption) {
    // const selectedProspectId: number = data.id
    this.setState({
      selectedProspect: {
        id: data.value as number,
        text: data.displayText
      }
    });
  }
  handleAssigneeSelect(data: ISRDropdownOption) {
    // const selectedProspectId: number = data.id
    this.setState({
      selectedAssignee: {
        id: data.value as number,
        text: data.displayText
      }
    });
  }

  render() {

    const props = this.props
    const state = this.state;
    const taskMenuItems = this.getTaskMenuItems();
    const selectedTaskType = this.state.selectedTaskChannelType;
    const initialValues = state.initialValue;
    const timeZone = this.props.timezone
    const minDate = !!timeZone ? moment().tz(timeZone).startOf('day').utc() : moment(new Date()).startOf('day').utc();
    const maxDate = !!timeZone ? moment().tz(timeZone).add(12, 'months').endOf('day').utc(): moment(new Date()).add(12, 'months').endOf('day').utc();

    const enable_native_calling = this.props.enable_native_calling

    const dateShow = !!initialValues ? initialValues?.date : new Date;
    const getTask = this.getTitileForTask(state.selectedTaskChannelType)
    const title = (state.isEdit ? "Edit " : "Create ") + `${getTask} Task`;
    const liActionType = this.state.liActionType
    const extensionView = !!this.props.extensionView

    return (
      <div>
        <SrModal
          showCloseButton={true}
          onClose={props.onClose}
          title={title}
          content={
            <>

              <Formik
                initialValues={initialValues}
                enableReinitialize={false}
                validate={this.validateDefs}
                onSubmit={this.handleSubmit}
              >
                <Form>

                  {!!props.task == false &&

                  <div className="flex-col my-5 px-4">
                    {/* {
                    <SrNavBar
                      tabs={getTabs(state.step_number)}
                      currentItem={state.step_number}
                      handleOnClickNavBarMenuItems={(e) => {console.log(e)}}
                    />
                    } */}

                    {state.step_number == 'one' &&
                    <>
                    <label className="text-sr-gray-90 block mb-1" htmlFor="select-prospect">
                      Select channel
                    </label>
                    <div className={classNames(enable_native_calling? "grid grid-cols-6 gap-1": "grid grid-cols-5 gap-4")}>
                      {!extensionView && _.map(taskMenuItems, (menuItem) => {
                        return (
                          <div
                            key={menuItem.type}
                            className={
                              (menuItem.type == state.selectedTaskChannelType
                                ? "bg-gray-100 text-sr-gray-100"
                                : "hover:bg-gray-50 hover:text-sr-gray-100") +
                              " p-4 rounded-md flex flex-col text-sr-gray-90 justify-center items-center text-sm font-medium hover:cursor-pointer"
                            }
                            aria-current={menuItem.active ? "page" : undefined}
                            onClick={() => this.onChangeTaskTab(menuItem.type)}
                          >
                            {menuItem.icon}
                            <div className="mt-2">{menuItem.name}</div>
                          </div>
                        );
                      })}
                      {extensionView && _.map(taskMenuItems, (menuItem) => {
                        return (
                          <SRTooltip2
                            text={menuItem.name}
                            direction="top center"
                          >
                            <div
                              key={menuItem.type}
                              className={
                                (menuItem.type == state.selectedTaskChannelType
                                  ? "bg-gray-100 text-sr-gray-100"
                                  : "hover:bg-gray-50 hover:text-sr-gray-100") +
                                " p-4 rounded-md flex flex-col text-sr-gray-90 justify-center items-center text-sm font-medium hover:cursor-pointer"
                              }
                              aria-current={menuItem.active ? "page" : undefined}
                              onClick={() => this.onChangeTaskTab(menuItem.type)}
                            >
                              {menuItem.icon}
                            </div>
                          </SRTooltip2>
                        );
                      })}
                    </div>
                    </>
                    }
                  </div>

                  }

                  {state.step_number == "one" && <> <div className="search-prospect px-4 mb-5">
                    <label className="text-sr-gray-90 block mb-1" htmlFor="select-prospect">
                      Select prospect
                    </label>
                    <SRSearchDropdown
                      width="fluid"
                      placeholder="Search by name, email, company, phone or linkedin url "
                      loading={this.state.isProspectSearching}
                      selectedValue={this.state.selectedProspect?.id || ""}
                      onSearchChange={(event) => {
                        this.updateProspectSearchQuery(event.target.value)
                      }
                      }
                      doNotFilterInternally
                      handleChange={(e) => this.handleProspectSelect(e)}
                      options={this.getProspectOptions()}
                      largerFontSize
                    />
                  </div>
                    {/*
                    we Will be pre-selecting the loggedIn user as the assignee for newly created tasks,
                    hence commenting out this.*/}
                    {this.props.generateAIContentProps  &&

                    <div className="search-prospect px-4 mb-5">
                      <label className="text-sr-gray-90 block mb-1" htmlFor="select-prospect">
                        Select Assignee
                      </label>
                      <SRSearchDropdown
                        placeholder={"Select Assignee"}
                        selectedValue={this.state.selectedAssignee?.id || 0}
                        handleChange={this.handleAssigneeSelect}
                        // onSearchChange={(e) => this.updateAssigneeQuery(e.target.value)}
                        width="fluid"
                        dropdownMenuClassName="px-2 py-4"
                        options={this.getAssigneeOptions()}
                      />
                    </div>
                    }
                  </>
                  }

                  <div className="px-4">
                    {state.selectedTaskChannelType === "linkedin" && (
                      <>
                        {state.step_number == "one" && <div className="mb-2.5">
                          <label className="text-sr-default-grey block" htmlFor="touch-type">
                            Touch Type
                          </label>
                          <SRSimpleSelectionDropdown
                            // name={"action"}
                            handleChange={this.handleLiActionChange}
                            selectedValue={liActionType}
                            width="fluid"
                            options={this.getActionOptions()}
                            largerFontSize
                          />
                          {/* <div className="flex-1">
                            <label className="label-formik" htmlFor="action">
                              Touch Type
                            </label>
                            <Field
                              as="select"
                              name="action"
                              onChange={this.handleLiActionChange}
                              value={this.state.liActionType}
                              placeholder="Select Action"
                              className={!!state.formikErrors && !!state.formikErrors.action ? "input-formik-error ": " " + " input-formik  w-full h-11"}
                            >
                          {  _.map(this.getActionOptions(),(data)=>
                              <option value={data.value}>{data.text}</option>
                            )}
                            </Field>
                          </div> */}
                        </div>
                        }
                        {state.step_number == "two" && liActionType==="send_linkedin_inmail" &&
                           <div className="mb-2.5 flex items-end">
                           <div className="flex-1">
                           <label className="text-sr-gray-90 block mb-1" htmlFor="select-prospect">
                            Subject
                          </label>
                             <Field
                               type="text"
                               name="inMailSubject"
                               className={(!!state.formikErrors && !!state.formikErrors.subject ? "input-formik-error": "") + " input-formik  w-full " }
                             />
                           </div>
                         </div>
                        }
                        {state.step_number == "two" && liActionType!="linkedin_view_profile" &&
                          <div className="mb-2.5 flex items-end">
                            <div className="flex-1">
                            <label className="text-sr-gray-90 block mb-1" htmlFor="select-prospect">
                              Body
                            </label>
                              <Field
                                as="textarea"
                                name="li_msg"
                                className={(!!state.formikErrors && !!state.formikErrors.li_msg ? "input-formik-error" : "") + " input-formik w-full h-24" }
                              />
                            </div>
                          </div>
                        }
                      </>
                    )}

                    {state.step_number == 'two' && selectedTaskType === "email" && (
                      <div className="">
                        {!extensionView && <OpenMailFormNew
                          type={'manual-task'}
                          isNotFirstEmailStep={false}
                          changeTitle={() => {}}
                          campaignStore={this.props.generateAIContentProps!.campaignStore}
                          enableContentAI={!!this.props.generateAIContentProps!.enableContentAI}
                          templates={this.props.templates}
                          accountId={this.props.accountId}
                          saveAsTemplate={this.props.generateAIContentProps!.saveAsTemplate}
                          action={this.state.isEdit ? 'Edit' : 'Add'}
                          isOwner={false}
                          onSubmit={() => {}}
                          onBack={() => this.setState({ step_number : 'one'})}
                          isSaving={state.isSubmitting}
                          onSubjectChange={this.handleEmailSubjectChange}
                          onBodyChange={this.handleEmailBodyChange}
                          getTinymceOptions={props.getTinymceOptions}
                          TINYMCE_URL={props.TINYMCE_URL}
                          enableFullpagePlugin={props.generateAIContentProps!.enableFullpagePlugin}
                          rephraseText={props.generateAIContentProps!.rephraseText}
                          shortenText={props.generateAIContentProps!.shortenText}
                          // regenerateEmailBodyThroughCommand={props.generateAIContentProps!.regenerateEmailBodyThroughCommand}
                          regenerateSubject={props.generateAIContentProps!.regenerateSubject}
                          generateEmailBodyThroughPrompt={props.generateAIContentProps!.generateEmailBodyThroughPrompt}
                          generateEmailSubjectThroughGPT={props.generateAIContentProps!.generateEmailSubjectThroughGPT}
                          logInStore={props.generateAIContentProps!.logInStore}
                          alertStore={props.alertStore}
                          getTags={props.getTags}
                          hideButons={{
                            deleteButton: true,
                            sendTestEmail: true
                          }}
                          showCreateTaskButtons={this.showCreateTaskButtons}
                        />}
                        {extensionView && <CreateEditManualEmailTask
                          subject={this.state.emailSubject}
                          body={this.state.emailBody}
                          onBodyChange={this.handleEmailBodyChange}
                          onSubjectChange={this.handleEmailSubjectChange}
                          getTinymceOptions={this.props.getTinymceOptions}
                          TINYMCE_URL={this.props.TINYMCE_URL}
                          enable_calendar={this.props.enable_calendar}
                          orgId={this.props.orgId}
                          accountId={this.props.accountId}
                          templates={this.props.templates}
                          getTags={this.props.getTags}
                          />}
                      </div>
                    )}
                    {state.step_number == 'two' && selectedTaskType === "generic" && (
                      <>
                        <div className="mb-2.5 flex items-end">
                          <div className="flex-1">
                            <label className="label-formik" htmlFor="notes">
                              Task Description
                            </label>
                            <Field
                              as="textarea"
                              name="notes"
                              className={(!!state.formikErrors && !!state.formikErrors.notes ? "input-formik-error": "") + " input-formik  w-full h-24 " }
                            />
                          </div>
                        </div>
                      </>
                    )}
                    {state.step_number == 'two' && selectedTaskType === "sms" && (
                      <>
                        <div className="mb-2.5 flex items-end ">
                          <div className="flex-1">
                          <label className="text-sr-gray-90 block mb-1" htmlFor="select-prospect">
                            Body
                          </label>
                            <Field
                              as="textarea"
                              name="sms_body"
                              className={(!!state.formikErrors && !!state.formikErrors.sms_body ? "input-formik-error" : "") + " input-formik  w-full h-24 "}
                            />
                          </div>
                        </div>
                      </>
                    )}
                    {state.step_number == 'two' && selectedTaskType === "call" && (
                      <>
                        <div className="mb-2.5 flex items-end ">
                          <div className="flex-1">
                            <label className="label-formik" htmlFor="call_script">
                              Call script
                            </label>
                            <Field
                              as="textarea"
                              name="call_script"
                              className={(!!state.formikErrors && !!state.formikErrors.call_script ? "input-formik-error" : "") + " input-formik  w-full h-24 "}
                            />
                          </div>
                        </div>
                      </>
                    )}
                    {state.step_number == 'two' && selectedTaskType === "whatsApp" && (
                      <>
                        <div className="mb-2.5 flex items-end ">
                          <div className="flex-1">
                          <label className="text-sr-gray-90 block mb-1" htmlFor="select-prospect">
                            Body
                          </label>
                            <Field
                              as="textarea"
                              name="wp_msg"
                              className={(!!state.formikErrors && !!state.formikErrors.wp_msg ? "input-formik-error" : "") + " input-formik  w-full h-24 "}
                            />
                          </div>
                        </div>
                      </>
                    )}
                    {state.step_number == "one" &&
                    <div className="mb-2.5">
                        <label className="text-sr-gray-90 block mb-1" htmlFor="due-at">
                          Due date
                        </label>
                        <DatePicker
                        // wrapperClassName="!block"
                        selected={dateShow}
                        onChange={this.handleDueDateChange}
                        showTimeSelect
                        className="block !w-full mb-2.5 rounded-md sr-p-basic border border-gray-300 bg-white py-2 pl-3 pr-10 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
                        timeFormat="HH:mm"
                        timeIntervals={15}
                        timeCaption="time"
                        dateFormat="MMMM d, yyyy h:mm aa"
                        maxDate={maxDate.toDate()} // 3 months from now
                        minDate={minDate.toDate()} // start of today
                        popperPlacement="top"
                        preventOpenOnFocus
                        shouldCloseOnSelect
                      />
                    </div>
                    }
                    {/* 17-Sept-2024: Commented out priority option as we have removed it from add-campaign-step modal as well */}
                    {/* {state.step_number == "one" && <div className="mb-2.5">
                      <label className="text-sr-gray-90 block mb-1" htmlFor="priority">
                        Priority
                      </label>
                      <SRFormSelectDropDown
                        name={"priority"}
                        width="fluid"
                        options={getPriorityOptions()}
                      />
                    </div>
                    } */}
                    {<div className="mt-8">

                      { state.showButtons &&
                        (state.step_number == 'two' && liActionType !== 'linkedin_view_profile' ?

                      <div className="text-right my-1">
                        <SRButtonOutline
                          isPrimary
                          type="button"
                          onClick={() => this.setState({ step_number : 'one'})}
                          icon="sr_icon_chevron_left"
                          iconPosition="left"
                          disable={state.isSubmitting}
                          text="Back"
                          className="mx-2 right w-1/6 !min-w-[66px]"
                        />
                        <SRButtonOutline
                          isPrimary
                          type="button"
                          onClick={props.onClose}
                          disable={state.isSubmitting}
                          text="Cancel"
                          className="right w-1/6 !min-w-[66px]"
                        />
                        <SRButtonFilled
                          type="submit"
                          isPrimary={true}
                          text={state.isEdit ? "Save" : "Create"}
                          className="mx-2 right w-1/6 !min-w-[66px]"
                          loading={state.isSubmitting}
                        />
                      </div>

                      :

                      <div className="text-right my-1">
                        <SRButtonOutline
                          isPrimary
                          type="button"
                          onClick={props.onClose}
                          disable={state.isSubmitting}
                          text="Cancel"
                          className="right w-1/6 !min-w-[66px]"
                        />
                        {liActionType !== 'linkedin_view_profile' && <SRButtonFilled
                          type="button"
                          isPrimary={true}
                          onClick={this.handleNext}
                          text="Next"
                          className="ml-2 right w-1/6 !min-w-[66px]"
                        />}
                        {liActionType == 'linkedin_view_profile' && <SRButtonFilled
                          type="submit"
                          isPrimary={true}
                          text={state.isEdit ? "Save" : "Create"}
                          className="ml-2 right w-1/6 !min-w-[66px]"
                          loading={state.isSubmitting}
                        />}
                      </div>)
                      }
                    </div>}
                  </div>
                </Form>
              </Formik>
            </>
          }
        />
      </div>
    );
  }
}
