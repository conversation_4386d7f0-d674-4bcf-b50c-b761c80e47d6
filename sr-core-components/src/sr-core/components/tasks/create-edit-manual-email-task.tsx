import * as React from "react";
import { EditorCore } from "../editor/editor-core";
import * as _ from "lodash";
import { insertText } from "../../utils/utils";
import { ITinyMceOptions } from "../../models/editor";
import { IAPIResponse } from "../../models/server";
import { ITemplateTags } from "../../models/column"; 
import { Campaigns } from "../../models/campaigns";



interface ISendManualEmailTaskProps {
  subject?: string;
  body?: string;
  onBodyChange: (body: string) => void;
  onSubjectChange: (subject: string) => void;
  getTinymceOptions(data: {
    autoFocusOnEditor: boolean;
    accountId: number;
  }): ITinyMceOptions;
  
  TINYMCE_URL: string;
  enable_calendar?: boolean;
  orgId: number;
  accountId: number;
  templates: Campaigns.ITemplatesForCategory[]
  getTags(): Promise<IAPIResponse<ITemplateTags>>
}

interface ISendManualEmailTaskState {
  sendingMail: boolean;
  insertInSubjectOrBody: "subject" | "body";
  availableTags?:string[];
}
export default class CreateEditManualEmailTask extends React.Component<
  ISendManualEmailTaskProps,
  ISendManualEmailTaskState
> {
  refs: any;
  constructor(props: ISendManualEmailTaskProps) {
    super(props);

    this.state = {
      sendingMail: false,
      insertInSubjectOrBody: "body",
    };

    this.onSubjectChange = this.onSubjectChange.bind(this);
    this.onBodyChange = this.onBodyChange.bind(this);
    this.onEditorSetup = this.onEditorSetup.bind(this);
    this.onSelectTemplateNew = this.onSelectTemplateNew.bind(this);
  }
  filterCalendarDataBasedOnFlag(allTags: string[]) {
    const filterdTags =  allTags.filter((tag:string) =>{
      return (tag === "calendar_link" ? !!this.props.enable_calendar :  true)
    })
    return filterdTags;
  }

  componentDidMount() {

    const getTags = this.props.getTags
      
    getTags()
      .then((res) => {
        this.setState({ availableTags: this.filterCalendarDataBasedOnFlag(res.data.template_tags) });
        console.log(res);
      })
      .catch((err) => {
        console.log(err);
      });
  }
  onSubjectChange(e: any) {
    this.props.onSubjectChange(e.nativeEvent.target.value);
  }

  onBodyChange(newBody: string) {
    this.props.onBodyChange(newBody);
  }

  subjectOrEditorOnFocus(from: string, _: any) {
    if (from === "subject") {
      this.setState({ insertInSubjectOrBody: "subject" });
    } else if (from === "editor") {
      this.setState({ insertInSubjectOrBody: "body" });
    }
  }

  onSelectTemplateNew(data: any) {
    this.props.onBodyChange(data.body);
    this.props.onSubjectChange(data.subject);
  }

  onInsertMergeTagNew(option: string) {
    let mergeTag = "";
    console.log("merge tag value", option);
    if (option === "unsubscribe_link") {
      mergeTag = `<span>To unsubscribe, <a href='{{unsubscribe_link}}'>click here</a></span>`;
    } else if(option === 'calendar_link') {
      mergeTag = `<span>Book meeting,<a href='{{calendar_link}}'>click here</a></span>`;
    } 
    else {
      mergeTag = "{{" + option + "}}";
    }
    if (this.state.insertInSubjectOrBody === "subject") {
      const element = document.getElementById("subject");
      (element as any).focus();
      this.props.onSubjectChange(insertText(element, mergeTag));
      (element as any).blur();
      (element as any).focus();
    } else if (this.state.insertInSubjectOrBody === "body") {
      console.log("on insert merge tag", "insert-in-body-" + mergeTag);
      (window as any).tinymce.execCommand("mceInsertContent", false, mergeTag);
    }
  }
  onEditorSetup(editor: any) {
    const self = this;

    editor.ui.registry.addMenuButton("insertMergeTagButton", {
      text: "Merge-tag",
      tooltip: "Insert merge-tag",
      fetch: function (callback: any) {
        var items = _.map(
          self.state.availableTags || [],
          (tag: string, _) => {
            return {
              type: "menuitem",
              text: tag,
              onAction: function () {
                self.onInsertMergeTagNew(tag);
              },
            };
          }
        );
        callback(items);
      },
    });

    editor.ui.registry.addMenuButton("selectTemplateButton", {
      text: "Template",
      tooltip: "Select a template",
      fetch: function (callback: any) {
        var items = _.map(
          this.props.templates || [],
          (templateCategory: Campaigns.ITemplatesForCategory) => {
            if (templateCategory.templatesData.length !== 0) {
              return {
                type: "nestedmenuitem",
                text: templateCategory.category,
                getSubmenuItems: function () {
                  var subItems = _.map(
                    templateCategory.templatesData,
                    (template) => {
                      return {
                        type: "menuitem",
                        text: template.label,
                        onAction: function () {
                          self.onSelectTemplateNew(template);
                        },
                      };
                    }
                  );

                  return subItems;
                },
              };
            } else {
              return;
            }
          }
        );
        callback(items);
      },
    });
  }


  render() {
    const subject = this.props.subject;
    const body = this.props.body;
    const autoFocusOnEditor = subject && length ? true : false;
    const orgId = this.props.orgId;
    const accountId = this.props.accountId;
    const editorDisabled = false;

    return (
      <>
        <div className="content">
          <div className="mb-2.5 flex items-end">
            <div className="flex-1">
            <label className="text-sr-gray-90 block mb-1" htmlFor="select-prospect">
              Subject
            </label>
              <input
                className="input-formik  w-full"
                type="text"
                id="subject"
                placeholder="Enter Subject"
                value={subject}
                onChange={this.onSubjectChange}
                onFocus={this.subjectOrEditorOnFocus.bind(this, "subject")}
              />
            </div>
          </div>
          <label className="text-sr-gray-90 block mb-1" htmlFor="select-prospect">
            Body
          </label>
          <div className="editor-strip editor-modal">
            <div className="subject-body-padding">
              <EditorCore
                onEditorSetup={this.onEditorSetup}
                autoFocus={autoFocusOnEditor}
                onEditorFocus={this.subjectOrEditorOnFocus.bind(
                  this,
                  "editor"
                )}
                onEditorChange={this.onBodyChange}
                body={body || ""}
                orgId={orgId}
                accountId={accountId}
                editorDisabled={editorDisabled}
                getTinymceOptions={this.props.getTinymceOptions}
                TINYMCE_URL={this.props.TINYMCE_URL}
              />
            </div>
          </div>
        </div>
      </>
    );
  }
}
