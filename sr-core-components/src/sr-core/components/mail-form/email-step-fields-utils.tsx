import { <PERSON><PERSON>utton<PERSON>illed, SRButtonOutline, SRFormCheckboxGroup, SRFormInput, SRMessageBox } from "@sr/design-component";
import { Formik,Form } from "formik";
import * as React from "react";
import { validateEmail } from "../../utils/validations";
import { SRPopover } from "@sr/design-component";
import { SRFormSearchDropDown } from "@sr/design-component";
import * as _ from "lodash";
import { Campaigns } from "../../models/campaigns";

type SendTestMailPopupProps={
  disable:boolean,
  loading:boolean,
  can_send_test_email:boolean,
  campaign_email_settings: Campaigns.ICampaignEmailSettings[],
  onSubmit:(testEmail:string, campaign_email_settings_id: number) => void,
  defaultTestEmail?:string
}

export const SendTestMailPopup = (props:SendTestMailPopupProps)=>{
  const opportunityTypeOptions = _.map(props.campaign_email_settings, (sender) => {
    return({
      value: sender.id,
      displayText: sender.sender_email,
    })
  })
  const buttonRef = React.useRef<HTMLDivElement>(null);

  return <SRPopover         
      triggerElement={
        <div ref={buttonRef}>
          <SRButtonOutline disable={props.disable} type='button' text='Send test mail'
            data-event-name='click_send_test_email_btn'
            loading={props.loading} 
            width='default'
            className='ml-[5px]' />
        </div>
      }
      className="!min-w-[350px] !w-[250px] !bg-white rounded-[4px] border-sr-lighter-grey border !text-left p-[15px]"
      direction='top'
      >
      <Formik
        initialValues={{
          testEmailAddress: props.defaultTestEmail!,
          campaign_email_settings_id: props.campaign_email_settings.length > 0? props.campaign_email_settings[0].id: 0
        }}
        onSubmit={()=>{}}
        validate={(values:{testEmailAddress:string, campaign_email_settings_id: number})=>{
          if (!validateEmail(values.testEmailAddress)){
            return {testEmailAddress:'Please enter a vaild email address'}
          }
          return {}
        }}
        validateOnChange={false}
        validateOnBlur={true}>
          {({
            values,
            isValid
          }) => 
            <Form>
            <div className="pb-[15px]">
              <SRFormSearchDropDown
                inline={true}
                label="From Email"
                name="campaign_email_settings_id"
                placeholder="Test mail will be sent from"
                width="fluid"
                options={opportunityTypeOptions}
                dropdownMenuClassName="h-[120px]"
              />
            </div>
            <div className="pb-[15px]">
              <SRFormInput
                name='testEmailAddress'
                placeholder='To Email'
                width='fluid'
                label='To Email'
              />
            </div>
    
              <SRButtonFilled
                // 04-09-2024: Moved 'onSubmit' logic from onSubmit in 'Formik' component to onClick in this button as this formik is nested in another parent formik.
                // While clicking on type=submit button causes the addition of form params in the URL and page reload.
                onClick={() => {
                  if (buttonRef.current && isValid && values.testEmailAddress) {
                    // 06-10-2023 : gu/To close popup manually inside the panel, we need this approch
                    // Ref: https://github.com/tailwindlabs/headlessui/issues/427#issuecomment-1713095330
                    buttonRef.current.click();
                    props.onSubmit(values.testEmailAddress, values.campaign_email_settings_id);
                  }
                }}
                disable={!props.can_send_test_email} 
                text="Send Test Email" 
                isPrimary 
                className='sr-filled-button-lg mb-2' 
                width="fluid"
              />
              {
                !props.can_send_test_email &&
                <div className='sr-p text-sr-default-red mb-4'>
                  Please update the sending email address in email setup to use this feature
                </div>
              }
            </Form>}

      </Formik>
    </SRPopover>
}


type saveAsTemplatePopupProps={
  disable:boolean, 
  isTemplate:boolean,
  isSharedWithTeam:boolean,
  loading:boolean,
  onSubmit:(label:string,shared_with_team:boolean,update_for_all_linked_emails:boolean,closePoup:()=>void) => void,
  defaultLabel?:string,
  labelError:boolean,
  initialTemplateId?:string | number,
  updateForAllLinkedEmails?:boolean
}

export const SaveAsTemplatePopup = (props:saveAsTemplatePopupProps)=>{
  const buttonRef = React.useRef<HTMLDivElement>(null);

  const closePoup=()=>{
    if (buttonRef.current) {
      buttonRef.current.click();
    }
  }

  return <SRPopover         
      triggerElement={
        <div ref={buttonRef}>
          <SRButtonOutline disable={props.disable} type='button' 
            text={props.isTemplate ? 'Save template' : 'Save as new template'}
            loading={props.loading} isPrimary={props.isTemplate} 
            width='default' className='ml-[5px]' />
        </div>
      }
      className="!min-w-[300px] !bg-white rounded-[4px] border-sr-lighter-grey border !text-left"
      direction='top'
      >
      <Formik
        initialValues={{
          label: props.defaultLabel!,
          ShareWithTeam:[...(props.isSharedWithTeam?['Share with team']:[]),
                         ...(props.updateForAllLinkedEmails?['Update all campaigns']:[])],
        }}
        onSubmit={()=>{}}
        validateOnChange={false}
        validateOnBlur={true}>
          {({
            values,
            isValid
          }) => 
            
            <Form>
            <div className="sr-h4 mb-4 border-b-[1px]">{props.isTemplate ? 'Save template' : 'Save as new template'}</div>
            {
              props.labelError&&<SRMessageBox
                type="error"
                width="fluid"
                content={[
                  {
                    element: <div className="text-left sr-p"><b className="mr-2">Error:</b>Check the template for missing fields body, subject, label.</div>
                  }
                ]}
              /> 
            }
            <SRFormInput
                  name='label'
                  placeholder='Template Name'
                  width='fluid'
                  label='Template Name'
                  inputClassName="!mb-0"  
                  autofocus 
                />
            <SRFormCheckboxGroup
                  groupName='ShareWithTeam'
                  options={[
                    { name: 'Share with team', displayText: 'Share with team', disabled: false},
                    ...((props.isTemplate&&!!props.initialTemplateId)?[{name: 'Update all campaigns', displayText: 'Update all campaign email steps linked with this template', disabled: false}]:[])
                  ]}
                  labelPosition='right'
                  checkboxClassName="!justify-start"
                  labelClassName="!text-lg text-sr-text-grey"
                  widthClassName="!w-full !m-0 !p-0"
                />
            <div className="flex justify-end gap-2">
              <SRButtonOutline onClick={closePoup}  
                text="Close" isPrimary className='sr-filled-button-lg mb-2' />
              <SRButtonFilled 
                // 04-09-2024: Moved 'onSubmit' logic from onSubmit in 'Formik' component to onClick in this button as this formik is nested in another parent formik.
                // While clicking on type=submit button causes the addition of form params in the URL and page reload.
                onClick={() => {
                  if(isValid)
                    props.onSubmit(values.label,values.ShareWithTeam.indexOf("Share with team")!==-1,
                                values.ShareWithTeam.indexOf("Update all campaigns")!==-1,closePoup)
                }}
                loading={props.loading}  
                text="Save" isPrimary 
                className='sr-filled-button-lg mb-2'
              />
            </div>
          </Form>
          }
      </Formik>
    </SRPopover>
}