import { ISRDropdownOption, SRButtonFilled, SRPopover, SRSimpleSelectionDropdown, SrAIIcon, classNames } from "@sr/design-component"
import * as React from 'react';
import { capitalizationOptions, getCharDropdown, subjectLineTypeOptions } from "../../utils/gptUtils";

export interface GenerateSubjectFieldValues {
  subject_line_type?: string,
  max_chars: number,
  capitalization?: string,
  avoid_words?: string,
  use_emoticons: boolean
}

interface GPTGenerateSubjectPopoverProps {
  generateSubjectThroughGPT: (data: GenerateSubjectFieldValues) => void
  isLoadingGenerateSubjectButton: boolean
  className?:string
}

export const GPTGenerateSubjectPopover = (props: GPTGenerateSubjectPopoverProps) => {

  const [gptSubjectType, setGptSubjectType] = React.useState('')
  const [gptMaxNoOfCharsInSub, setGptMaxNoOfCharsInSub] = React.useState(60)
  const [gptCapitalization, setGptCapitalization] = React.useState('')
  const [gptAvoidWords, setGptAvoidWords] = React.useState('')
  const [gptUseEmoticonsInSubject, setGptUseEmoticonsInSubject] = React.useState(false)

  const generateSubject = () => {
    const data: GenerateSubjectFieldValues = {
      subject_line_type: gptSubjectType !== '' ? gptSubjectType : undefined,
      max_chars: gptMaxNoOfCharsInSub,
      capitalization: gptCapitalization !== '' ? gptCapitalization : undefined,
      avoid_words: gptAvoidWords !== '' ? gptAvoidWords : undefined,
      use_emoticons: gptUseEmoticonsInSubject
    }

    props.generateSubjectThroughGPT(data)
  }

  return (
    <SRPopover
      className='min-w-[420px] p-3 !rounded-lg !bg-white'
      triggerElement={
        // <SRButtonOutline
        //   icon="sr_ai_icon"
        //   className='ml-[8px] w-[150px]'
        //   text="Generate Subject"
        // />
        <div className={classNames(props.className,"flex hover:underline pt-[6px] cursor-pointer")}>
         <div> <SrAIIcon className="h-[20px] w-[20px] text-sr-violet-90 mr-[5px]"/> </div>
          <div className="text-sr-violet-90 sr-p-basic"> Generate with AI</div>
        </div>
      }
      direction='bottom-right'
    >
      <div className="grid gap-2">
        <div className="sr-h5 text-sr-grey-100">Subject line preferences</div>
        {/* <hr /> */}
       <div className="sr-p-basic text-sr-grey-90"> Type of subject line</div>
        {/* <br /> */}
        <SRSimpleSelectionDropdown
          dropdownButtonClassName='min-h-[34px] min-w-[400px]'
          dropdownMenuClassName='max-h-[120px] min-w-[400px] sr-p-basic text-sr-grey-90'
          selectedValue={gptSubjectType}
          options={subjectLineTypeOptions}
          handleChange={(option: ISRDropdownOption) => {
            setGptSubjectType(option.displayText)
          }}
        />
        <div className='flex'>
          <div className="sr-p-basic text-sr-grey-90">
            Max no. of characters
            <SRSimpleSelectionDropdown
              dropdownButtonClassName='min-h-[34px] w-[160px]'
              dropdownMenuClassName='max-h-[115px] w-[160px] sr-p-basic text-sr-grey-90'
              selectedValue={gptMaxNoOfCharsInSub}
              options={getCharDropdown()}
              handleChange={(option: ISRDropdownOption) => {
                setGptMaxNoOfCharsInSub(+option.value)
              }}
            />
          </div>

          <div className="sr-p-basic text-sr-grey-90">
            Capitalization
            <SRSimpleSelectionDropdown
              dropdownButtonClassName='min-h-[34px] w-[160px]'
              dropdownMenuClassName='max-h-[115px] w-[160px] sr-p-basic text-sr-grey-90'
              selectedValue={gptCapitalization}
              options={capitalizationOptions}
              handleChange={(option: ISRDropdownOption) => {
                setGptCapitalization(option.displayText)
              }}
            />
          </div>
        </div>
        <div>
          <input
            type="text"
            className='my-[8px] min-h-[34px] w-[400px] sr-p-basic rounded'
            placeholder='Add words you wish to avoid (seperated by comma)'
            value={gptAvoidWords}
            onChange={(e) => setGptAvoidWords(e.target.value)}
          />
        </div>

        <div className='inline-flex items-center my-[8px]'>
          <input
            type="checkbox"
            className='h-[20px] w-[20px] rounded-[4px] border-sr-light-grey focus:border-sr-violet-90 !text-sr-violet-90'
            onChange={() => {
              setGptUseEmoticonsInSubject(!gptUseEmoticonsInSubject)
            }}
          />

          <div className="text-sm flex items-center">
            <label className="sr-h6 !font-normal mx-2 text-sr-grey-90">
              Use emoticons in subject
            </label>
          </div>
        </div>
        <br />
        <SRButtonFilled
         icon="sr_ai_icon"
         iconClassName="mt-[1px] mr-[8px]"
          className='w-[400px] !bg-sr-violet-90 !text-white'
          text="Generate Subject Line"
          loading={props.isLoadingGenerateSubjectButton}
          onClick={generateSubject}
          isPrimary={true}
        />
      </div>
    </SRPopover>
  )
}