import { SRButtonOutline, SRSpinner } from '@sr/design-component'
import * as React from 'react'
import { Campaigns } from '../../models/campaigns'
import { IRegenerateEmailBodyRequest } from '../../models/gpt'
import { IServerResponse } from '../../models/server'

interface IGPTAddCommandAndRegenerateProps {
  campaignStore: Campaigns.ICampaignStore
  emailBody: string
  backToEmailDraft: () => void
  changeTitle: (showAITitle: boolean) => void;
  regenerateEmailBodyThroughCommand(data: IRegenerateEmailBodyRequest): IServerResponse<{
    email_body: string
  }>
}

export default function GPTAddCommandAndRegenerate(props: IGPTAddCommandAndRegenerateProps) {

  const [command, setCommand] = React.useState<string>('')
  const [isLoading, setIsLoading] = React.useState<boolean>(false)

  const regenerateBody = () => {

    setIsLoading(true)
    let data: IRegenerateEmailBodyRequest

    // Show error if command is empty
    data = {
      previous_email_body: props.emailBody,
      command: command
    }

    props.regenerateEmailBodyThroughCommand(data)
      .then(res => {
        setIsLoading(false)
        setCommand('')
        props.campaignStore.getEmailBodyVersions.forEach((body, index) => {
          if (body === props.emailBody) {
            props.campaignStore.updateEmailBodyVersion(index, res.data.email_body.split("\n").join("<br />"))
            props.campaignStore.addToUndoStack(index + 1, data.previous_email_body)
          }
        })
        props.backToEmailDraft()
        props.changeTitle(false)
      })
      .catch(err => {
        console.error(err)
        setIsLoading(false)
      })
  }

  return (
    <div>
      {isLoading &&
        <SRSpinner />
      }

      {!isLoading &&
        <div>
          <div className='flex flex-col my-[16px] w-full'>
            Add Command
            <div className='flex-1 my-[8px]'>
              <input
                type='text'
                value={command}
                className='h-[35px] w-full'
                onChange={(e) => setCommand(e.target.value)}
              />
            </div>
          </div>

          <div className='flex my-[16px] justify-between'>
            <SRButtonOutline
              icon='sr_icon_chevron_left'
              text='Back to Email Draft'
              className='bg-sr-light-blue text-sr-dark-blue border-none'
              onClick={props.backToEmailDraft}
            />
            <SRButtonOutline
              icon="sr_ai_icon"
              iconClassName="mt-[1px] mr-[8px]"
              text='Generate Content'
              className='mx-[8px]'
              isPurple={true}
              onClick={regenerateBody}
            />
          </div>
        </div>
      }

    </div>
  )
}
