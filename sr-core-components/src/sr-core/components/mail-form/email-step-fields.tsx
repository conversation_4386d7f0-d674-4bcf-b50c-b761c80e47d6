import { Editor } from '@tinymce/tinymce-react';
import * as React from 'react';
import * as _ from 'lodash'
import { checkPermission } from '../../utils/permissions_related';
import { ConfirmDeleteWithInputModal } from '../common-modals/confirm-delete-with-input-modal';
import { insertText } from '../../utils/insertAtCursor';
import { getBody, getSubject } from '../../utils/campaignStepData';
import { SRButtonFilled, SRButtonTonal, SRLoaderDefault } from '@sr/design-component';
import { OnDeleteStepText } from "../../utils/deleteStepModalText";
import { GPTGenerateSubjectPopover, GenerateSubjectFieldValues } from './gpt-generate-subject-popover';
import { classNames } from '@sr/design-component';
import { SRButtonOutline } from '@sr/design-component';
import { SRMessageBox } from '@sr/design-component';
import { SaveAsTemplatePopup, SendTestMailPopup } from './email-step-fields-utils';
import { IHideButtons } from './mail-form-multichannel';
import { IServerResponse } from '../../models/server';
import { ITinyMceOptions } from '../../models/editor';
import { LogIn } from '../../models/login';
import { IGenerateSubjectRequest, IRegenerateEmailBodyRequest, IRegenerateSubjectRequest } from '../../models/gpt'
import { Campaigns } from '../../models/campaigns';
import { Alerts } from '../../models/alerts';

interface IVersionList {
  displayText: string,
  onClick: () => void
}
interface ITagItem {
  type: string;
  text: string;
  onAction: () => void;
}

interface IEmailStepFieldsProps {
  campaignStore?: Campaigns.ICampaignStore
  logInStore?: LogIn.ILogInStore
  alertStore?: Alerts.IAlertStore
  type: Campaigns.IEditorModalType;
  isNotFirstEmailStep: boolean;
  showOrHideEmailBodyPromptFields: (value: boolean) => void;
  showOrHideAddCommandSection: (value: boolean, emailBody: string) => void;
  availableTags: string[];
  email?: Campaigns.IStepVariant;
  initialTemplate?: Campaigns.ICampaignStepTemplateApp;
  isSaving: boolean;
  isSavingTemplate: boolean;
  isSendingTestEmail: boolean;
  isDeleting: boolean;
  isOwner: boolean
  showDelaySetting?: boolean
  testEmail?: string
  templates?: Campaigns.ITemplatesForCategory[];
  unlinkTemplate: (campaignId: number | string, stepId: number | string, variantId: number | string) => void
  delayDays?: number
  priority?: Campaigns.priority
  touchType?: Campaigns.ICampaignStepType
  shareWithTeam: (templateId: number | string, shared_with_team: boolean) => void
  sharingTemplateId?: number | string;
  changeEmailBody: (email_body: string) => void
  onSendTestMail: (data: Campaigns.ITestMail) => void
  onSaveAsTemplate: (template: Campaigns.ICampaignStepTemplateFELib) => void
  onDeleteCampaignStep: (stepId: number | string, variantId: number | string) => void
  onDeleteTemplate: (templateId: number | string | undefined) => void
  onSaveStepVariant?: (data: Campaigns.ISaveStepData) => void;
  onBack?: () => void;
  hideButtons?: IHideButtons;
  onSubjectChange?: (subject: string) => void
  onBodyChange?: (subject: string) => void;
  getTinymceOptions(data: {
    autoFocusOnEditor: boolean;
    accountId: number;
  }): ITinyMceOptions;

  TINYMCE_URL: string;
  enableFullpagePlugin: boolean;
  rephraseText(text: String): IServerResponse<{
    rephrased_text: string;
  }>
  shortenText(text: String): IServerResponse<{
    shortened_text: string;
  }>
  regenerateEmailBodyThroughCommand(data: IRegenerateEmailBodyRequest): IServerResponse<{
    email_body: string;
  }>
  regenerateSubject(data: IRegenerateSubjectRequest): IServerResponse<{
    email_subject: string;
  }>
  generateEmailSubjectThroughGPT(data: IGenerateSubjectRequest): IServerResponse<{
    email_subject: string;
  }>
  showCreateTaskButtons?(flag: boolean): void
}

interface IEmailStepFieldsState {
  subject: string
  body: string
  emailBodyVersions: IVersionList[]
  selectedBodyVersion: number,
  selectedSubjectVersion: number,
  label: string
  isSharedWithTeam: boolean
  delayDays: number
  labelError: boolean
  testEmailId?: string
  deleteConfirmModal: boolean
  isLoadingEditor: boolean
  insertInSubjectOrBody: string
  isLoadingGenerateSubjectButton: boolean,
  showRewriteSubject: boolean,
  searchTemplate: string
  isSearch: boolean
  selectedTemplateId?: number
  isSelectedTemplateFromLibrary: boolean
  updateForAllLinkedEmails: boolean
}

export class EmailStepFields extends React.Component<IEmailStepFieldsProps, IEmailStepFieldsState> {

  constructor(props: IEmailStepFieldsProps) {
    super(props)

    const email = props.email || {} as Campaigns.IStepVariant;
    const initialTemplate = props.initialTemplate || {} as Campaigns.ICampaignStepTemplateApp;
    let emailBodyVersions = props.campaignStore!.getEmailBodyVersions.map((emailBody, index) => {
      return ({
        displayText: "AI Content - v" + (index + 1),
        onClick: () => {
          this.setState({ body: emailBody, selectedBodyVersion: (index + 1) })
        }
      })
    }).reverse()
    const isCampaignStep = props.type === 'campaign-step' || props.type === 'step-variant';
    const isTemplate = props.type === 'template';
    // const stepTitle = (email) ? email.stepTitle : '';
    const subject = (isCampaignStep) ? (this.props.isNotFirstEmailStep && getSubject(email) === '') ? "{{previous_subject}}" : (getSubject(email)) : (isTemplate ? initialTemplate.subject : '');
    let body: string;
    const emailVersionBody = props.campaignStore!.getEmailBodyVersions.length > 0 ? props.campaignStore!.getEmailBodyVersions.slice(-1)[0] : ""
    if (isCampaignStep) {
      if (props.campaignStore!.getEmailBodyVersions.length > 0) {
        body = emailVersionBody
      }
      else {
        body = getBody(email)
      }
    }
    else if (isTemplate) {
      body = !!initialTemplate.body ? initialTemplate.body : emailVersionBody
    }
    else {
      body = emailVersionBody
    }
    const label = (isCampaignStep) ? ('') : (isTemplate ? initialTemplate.label : '');
    const delayDays = (email) ? Math.ceil(email.step_delay / 86400) : 1; // for testing
    const isSharedWithTeam = (isCampaignStep) ? (false) : (isTemplate ? initialTemplate.shared_with_team : false);
    const selectedTemplateId = (isCampaignStep) ? email.template_id : undefined;
    // const selectedTemplateLabel = this.getSelectedTemplateLabel(props.templates || [], selectedTemplateId || null);
    const isSelectedTemplateFromLibrary = (isCampaignStep) ? !email.template_id : false;

    this.state = {
      subject,
      selectedBodyVersion: props.campaignStore!.getEmailBodyVersions.length, // 0 -> "Your Draft", 1 -> "AI-version1", ...
      selectedSubjectVersion: -1,
      body,
      emailBodyVersions,
      label,
      delayDays,
      isSharedWithTeam,
      labelError: false,
      testEmailId: props.testEmail,
      searchTemplate: '',
      isSearch: false,
      deleteConfirmModal: false,
      insertInSubjectOrBody: 'body',
      isLoadingGenerateSubjectButton: false,
      showRewriteSubject: false,
      selectedTemplateId,
      isSelectedTemplateFromLibrary,
      isLoadingEditor: true,
      updateForAllLinkedEmails: false,
    };

    this.saveCampaignStep = this.saveCampaignStep.bind(this);
    this.saveAsTemplateV2 = this.saveAsTemplateV2.bind(this);
    this.onDelayChange = this.onDelayChange.bind(this);
    this.onSubjectChange = this.onSubjectChange.bind(this);
    this.onBodyChange = this.onBodyChange.bind(this);
    this.validateCampaignStep = this.validateCampaignStep.bind(this);
    this.confirmSendTestmail = this.confirmSendTestmail.bind(this);
    this.onChangeTestmail = this.onChangeTestmail.bind(this);
    this.getSearchedTemplates = this.getSearchedTemplates.bind(this);
    this.setSearchOn = this.setSearchOn.bind(this);
    this.showDeleteConfirmModal = this.showDeleteConfirmModal.bind(this);
    this.closeDeleteConfirmModal = this.closeDeleteConfirmModal.bind(this);
    this.delete = this.delete.bind(this);
    this.regenerateBody = this.regenerateBody.bind(this);
    this.deleteVersion = this.deleteVersion.bind(this);
    this.showUpdatedEmailBodyVersions = this.showUpdatedEmailBodyVersions.bind(this);
    this.undoChange = this.undoChange.bind(this);
    this.redoChange = this.redoChange.bind(this);
    this.subjectOrEditorOnFocus = this.subjectOrEditorOnFocus.bind(this);
    this.onLabelChange = this.onLabelChange.bind(this);
    this.validateTemplate = this.validateTemplate.bind(this);
    this.onShareWithTeamChange = this.onShareWithTeamChange.bind(this);
    this.onInsertMergeTag = this.onInsertMergeTag.bind(this);
    this.unlinkTemplate = this.unlinkTemplate.bind(this);
    this.onSelectTemplateNew = this.onSelectTemplateNew.bind(this);
    this.generateSubjectThroughGPT = this.generateSubjectThroughGPT.bind(this);
    this.regenerateSubject = this.regenerateSubject.bind(this);
    this.showPreviousSubject = this.showPreviousSubject.bind(this);
    this.showNextSubject = this.showNextSubject.bind(this);
    this.onUpdateForAllLinkedEmails = this.onUpdateForAllLinkedEmails.bind(this);
    this.calendarMergeTagFilter = this.calendarMergeTagFilter.bind(this)
  }

  onInsertMergeTag(option: string) {
    let mergeTag = '';
    console.log('merge tag value', option);
    if (option === 'unsubscribe_link') {
      mergeTag = `<span>To unsubscribe, <a href='{{unsubscribe_link}}'>click here</a></span>`
    } else if (option === 'calendar_link') {
      mergeTag = `<span>Book meeting,<a href='{{calendar_link}}'>click here</a></span>`
    }
    else {
      mergeTag = '{{' + option + '}}';
    }
    if (this.state.insertInSubjectOrBody === 'subject') {
      const element = document.getElementById('subject');
      (element as any).focus();
      this.setState({ subject: insertText(element, mergeTag) }, () => this.props.onSubjectChange?.(insertText(element, mergeTag)));
      (element as any).blur();
      (element as any).focus();
    } else if (this.state.insertInSubjectOrBody === 'body') {
      console.log('on insert merge tag', 'insert-in-body-' + mergeTag);
      (window as any).tinymce.execCommand('mceInsertContent', false, mergeTag);
    }
  }

  getSearchedTemplates() {
    const templates = this.props.templates || [];
    const search = _.toLower(this.state.searchTemplate);
    let searchResultTemplates: Campaigns.ITemplatesForCategory[] = [];
    console.log('get searched templates', templates);
    if (search !== '') {
      _.forEach(templates, (obj) => {

        if (_.includes(_.toLower(obj.category), search)) {
          searchResultTemplates.push(obj);
        } else {
          let tempTemplate: Campaigns.ITemplatesForCategory = { category: obj.category, templatesData: [] };
          _.forEach(obj.templatesData, (template) => {
            if (_.includes(_.toLower(template.label), search)) {
              tempTemplate.templatesData.push(template);
            }
          });
          if (tempTemplate.templatesData.length !== 0) {
            searchResultTemplates.push(tempTemplate);
          }
        }
      });
    } else {
      searchResultTemplates = templates;
    }
    return searchResultTemplates;
  }

  onSelectTemplateNew(data: any) {
    const s = this;
    s.setState(
      { subject: data.subject, body: (data.body), selectedTemplateId: data.id, isSelectedTemplateFromLibrary: data.template_is_from_library },
      () => {
        this.props.onSubjectChange?.(data.subject)
        this.props.onBodyChange?.(data.body)
      }
    );
  }

  getSelectedTemplateLabel(templates: Campaigns.ITemplatesForCategory[], templateId: number | string | null) {

    const flatArray = _.flatMap(templates, (t) => t.templatesData);
    const index = _.findIndex(flatArray, (elem) => elem.id === templateId);
    const templateLabel = ((index !== -1) ? flatArray[index].label : '');

    return templateLabel;
  }

  unlinkTemplate() {
    const email = this.props.email || {} as Campaigns.IStepVariant;
    if (!!this.props.email?.template_id) { //already linkedd
      this.props.unlinkTemplate(email.campaign_id as number, email.step_id as number, email.id);
    } else { //selected only, not linked
      this.setState({ selectedTemplateId: undefined })
    }
    this.setState({
      subject: "",
      body: ""
    })
  }

  setSearchOn() {
    this.setState({ isSearch: true });
  }

  onDelayChange(e: any) {
    const s = this;
    const delayDays = parseInt(e.nativeEvent.target.value, 10);
    s.setState({
      delayDays: delayDays,
    });
  }

  onSubjectChange(e: any) {
    const s = this;
    const value = e.nativeEvent.target.value;
    s.setState({
      subject: value,
    },
    () => this.props.onSubjectChange?.(value)
  );
  }

  subjectOrEditorOnFocus(from: string, e: any) {
    if (from === 'subject') {
      this.setState({ insertInSubjectOrBody: 'subject' });
    } else if (from === 'editor') {
      this.setState({ insertInSubjectOrBody: 'body' });
    }
  }

  onBodyChange(newBody: string) {
    if (this.state.selectedBodyVersion === 0) {
      this.props.campaignStore?.updateUserEmailBodyDraft(newBody)
    }
    else {
      this.props.campaignStore?.updateEmailBodyVersion(this.state.selectedBodyVersion - 1, newBody)
    }
    this.setState(
      { body: newBody },
      () => this.props.onBodyChange?.(newBody)
    );
  }

  shareTemplate(templateId: number | string, shared_with_team: boolean) {
    this.props.shareWithTeam(templateId, shared_with_team);
  }

  showDeleteConfirmModal() {
    this.setState({ deleteConfirmModal: true });
  }


  confirmSendTestmail(testEmailId: string, campaign_email_settings_id: number) {
    const newBody = this.state.body || '';
    const data: Campaigns.ITestMail = {
      step_type: this.props.touchType || "send_email",
      subject: this.state.subject || '',
      body: newBody,
      to_email: testEmailId,
      campaign_email_settings_id: campaign_email_settings_id
    };

    this.props.onSendTestMail(data);
  }

  onChangeTestmail(e: any, data: any) {
    this.setState({ testEmailId: data.value });
  }


  saveAsTemplateV2(label: string, isSharedWithTeam: boolean, updateForAllLinkedEmails: boolean, closePopup: () => void) {
    const body = this.state.body || '';
    this.setState(
      {
        label: label,
        updateForAllLinkedEmails: updateForAllLinkedEmails,
        isSharedWithTeam: isSharedWithTeam
      }
    )
    if (this.validateTemplateV2(label)) {
      let template: Campaigns.ICampaignStepTemplateFELib = this.props.initialTemplate || {} as Campaigns.ICampaignStepTemplateApp;
      template.label = label;
      template.subject = this.state.subject || '';
      template.body = body;
      template.shared_with_team = isSharedWithTeam;
      template.update_for_all_linked_emails = updateForAllLinkedEmails;

      this.props.onSaveAsTemplate(template);
      closePopup();

    } else {
      this.setState({ labelError: true });
      // body or label doesn't exist
    }
  }


  validateTemplate() {
    const alertStore = this.props.alertStore || {} as Alerts.IAlertStore;
    if (!this.state.label) {
      return false;
    } else if (!this.state.subject) {
      alertStore.pushAlert({ status: 'error', message: 'Subject can not be empty' });
      return false;
    } else {
      return true;
    }
  }

  validateTemplateV2(label: string) {
    const alertStore = this.props.alertStore || {} as Alerts.IAlertStore;
    if (!label) {
      return false;
    } else if (!this.state.subject) {
      alertStore.pushAlert({ status: 'error', message: 'Subject can not be empty' });
      return false;
    } else {
      return true;
    }
  }


  onLabelChange(e: any, data: any) {
    this.setState({ label: data.value, labelError: false });
  }

  onShareWithTeamChange(e: any, data: any) {
    this.setState({ isSharedWithTeam: data.checked });
  }

  onUpdateForAllLinkedEmails(e: any, data: any) {
    this.setState({ updateForAllLinkedEmails: data.checked });
  }

  closeDeleteConfirmModal() {
    this.setState({ deleteConfirmModal: false });
  }

  delete() {
    const type = this.props.type;

    if (type === 'campaign-step' || type === 'step-variant') {
      const email = this.props.email || {} as Campaigns.IStepVariant;
      const stepId = email.step_id;
      const varintId = email.id;

      this.props.onDeleteCampaignStep(stepId as number, varintId);
      this.closeDeleteConfirmModal();

    } else if (type === 'template') {
      const initialTemplate = this.props.initialTemplate || {} as Campaigns.ICampaignStepTemplateApp;
      const templateId = initialTemplate.id;
      this.props.onDeleteTemplate(templateId);
      this.closeDeleteConfirmModal();
    }

  }

  deleteVersion() {
    this.setState({ isLoadingEditor: true })
    this.props.campaignStore?.deleteEmailBodyVersion(this.state.selectedBodyVersion - 1)
    this.showUpdatedEmailBodyVersions()
    this.setState({ isLoadingEditor: false })
  }

  generateSubjectThroughGPT(data: GenerateSubjectFieldValues) {

    this.setState({ isLoadingGenerateSubjectButton: true })

    const request_data: IGenerateSubjectRequest = {
      email_body: this.state.body,
      subject_line_type: data.subject_line_type,
      max_chars: data.max_chars,
      capitalization: data.capitalization,
      avoid_words: data.avoid_words,
      use_emoticons: data.use_emoticons
    }

    this.props.generateEmailSubjectThroughGPT(request_data)
      .then(res => {
        this.props.campaignStore?.setNewVersionOfSubject(res.data.email_subject)
        this.setState(
          { isLoadingGenerateSubjectButton: false, subject: res.data.email_subject, showRewriteSubject: true, selectedSubjectVersion: 0 },
          () => this.props.onSubjectChange?.(res.data.email_subject)
        )
      })
      .catch(err => {
        console.error(err)
        this.setState({ isLoadingGenerateSubjectButton: false })
      })
  }

  validateCampaignStep() {
    const alertStore = this.props.alertStore || {} as Alerts.IAlertStore;
    if (!this.state.delayDays && !this.props.delayDays) {
      alertStore.pushAlert({ status: 'error', message: 'Delay should be a non zero integer value' });
      return false;
    } else if (!this.state.subject) {
      alertStore.pushAlert({ status: 'error', message: 'Subject can not be empty' });
      return false;
    } else {
      return true;
    }
  }

  onSelectAIOption(tag: string, selection: any) {
    if (tag === "Rephrase") {
      this.props.rephraseText(selection.getContent())
        .then(res => {
          this.props.campaignStore!.addToUndoStack(this.state.selectedBodyVersion, this.state.body)
          selection.setContent(res.data.rephrased_text)
        })
        .catch(err => {
          console.error(err)
        })
    }
    else if (tag === "Shorten") {
      this.props.shortenText(selection.getContent())
        .then(res => {
          this.props.campaignStore!.addToUndoStack(this.state.selectedBodyVersion, this.state.body)
          selection.setContent(res.data.shortened_text)
        })
        .catch(err => {
          console.error(err)
        })
    }
  }

  undoChange() {
    this.setState(
      { body: this.props.campaignStore!.popFromUndoStack(this.state.selectedBodyVersion, this.state.body) },
      () => this.props.onBodyChange?.(this.props.campaignStore!.popFromUndoStack(this.state.selectedBodyVersion, this.state.body))
    )
  }

  redoChange() {
    this.setState(
      { body: this.props.campaignStore!.popFromRedoStack(this.state.selectedBodyVersion, this.state.body) },
      () => this.props.onBodyChange?.(this.props.campaignStore!.popFromRedoStack(this.state.selectedBodyVersion, this.state.body))
    )
  }

  showUpdatedEmailBodyVersions() {
    this.setState({
      selectedBodyVersion: this.props.campaignStore!.getEmailBodyVersions.length,
      emailBodyVersions: this.props.campaignStore!.getEmailBodyVersions.map((emailBody, index) => {
        return ({
          displayText: "AI Content - v" + (index + 1),
          onClick: () => {
            this.setState(
              { body: emailBody, selectedBodyVersion: (index + 1) },
              () => this.props.onBodyChange?.(emailBody)
            )
          }
        })
      }).reverse(),
      body: (this.props.campaignStore!.getEmailBodyVersions.length > 0 ? this.props.campaignStore!.getEmailBodyVersions[this.props.campaignStore!.getEmailBodyVersions.length - 1] : this.props.campaignStore!.getUserEmailBodyDraft)
    })
  }

  regenerateBody() {
    this.setState({ isLoadingEditor: true })
    this.props.regenerateEmailBodyThroughCommand({ previous_email_body: this.state.body })
      .then(res => {
        this.props.campaignStore?.setNewVersionOfEmailBody(res.data.email_body.split("\n").join("<br />"))
        this.showUpdatedEmailBodyVersions();
        this.setState({ isLoadingEditor: false })
      })
      .catch(err => {
        console.error(err)
        this.setState({ isLoadingEditor: false })
      })
  }

  regenerateSubject() {
    this.setState({ isLoadingGenerateSubjectButton: true })
    const data: IRegenerateSubjectRequest = {
      email_body: this.state.body,
      previous_subjects: this.props.campaignStore?.getSubjectVersions || []
    }

    this.props.regenerateSubject(data)
      .then((res) => {
        this.props.campaignStore?.setNewVersionOfSubject(res.data.email_subject)
        this.setState(
          { subject: res.data.email_subject, isLoadingGenerateSubjectButton: false, selectedSubjectVersion: (this.props.campaignStore?.getSubjectVersions.length || 0) - 1 },
          () => this.props.onSubjectChange?.(res.data.email_subject)
        )
      })
      .catch(err => {
        console.error(err)
        this.setState({ isLoadingGenerateSubjectButton: false })
      })
  }

  showPreviousSubject() {
    const currVersion = this.state.selectedSubjectVersion - 1
    this.setState(
      { selectedSubjectVersion: currVersion, subject: this.props.campaignStore!.getSubjectVersions[currVersion] },
      () => this.props.onSubjectChange?.(this.props.campaignStore!.getSubjectVersions[currVersion])
    )
  }

  showNextSubject() {
    const currVersion = this.state.selectedSubjectVersion + 1
    this.setState(
      { selectedSubjectVersion: currVersion, subject: this.props.campaignStore!.getSubjectVersions[currVersion] },
      () => this.props.onSubjectChange?.(this.props.campaignStore!.getSubjectVersions[currVersion])
    )
  }

  saveCampaignStep() {
    const body = this.state.body || '';
    const email = this.props.email || {} as Campaigns.IStepVariant;
    const stepData =
      {
      step_type: this.props.touchType || "send_email" ,
      subject: this.state.subject || '',
      body: body,
      text_preview: body,
      }

    if (!this.props.onSaveStepVariant) {
      console.error("this.props.onSaveStepVariant does not exist.")
    } else if (this.validateCampaignStep()) {
      const delaySeconds = Math.ceil(this.props.delayDays || 0) * 86400;
      const priority = this.props.priority || "normal"

      const templateId = this.state.selectedTemplateId
      const isSelectedTemplateFromLibrary = this.state.isSelectedTemplateFromLibrary
      const variantId = email.id
      const stepId = email.step_id

      const data: Campaigns.ISaveStepData = {
        step_data: stepData as Campaigns.IStepVariantData,
        delaySeconds: delaySeconds,
        stepId: stepId,
        priority: priority,
        variantId: variantId,
        templateId: templateId,
        isSelectedTemplateFromLibrary: isSelectedTemplateFromLibrary
      }

      this.props.onSaveStepVariant(data);
    }
  }

  componentDidUpdate(prevProps: IEmailStepFieldsProps, prevState: IEmailStepFieldsState) {
    const oldTemplates = prevProps.templates || [];
    const newTemplates = this.props.templates || [];

    const newTemplateAll = _.flatMap(newTemplates, (t) => { return t.templatesData });
    const oldTemplateAll = _.flatMap(oldTemplates, (t) => { return t.templatesData });

    if (newTemplateAll.length !== oldTemplateAll.length) {
      const newlyAddedTemplateObj = _.find(newTemplateAll, (t) => {
        return t.label === this.state.label
      })
      if (!!newlyAddedTemplateObj) {
        this.setState({ selectedTemplateId: (parseInt(String(newlyAddedTemplateObj.id!))), isSelectedTemplateFromLibrary: false });
      }
    }

  }

  calendarMergeTagFilter(item: ITagItem, logInStore: LogIn.ILogInStore) {
    return (
      (item.text.includes("calendar") &&
        logInStore.getAccountInfo.org.org_metadata.enable_calendar) ||
      !item.text.includes("calendar")
    );
  }

  componentDidMount(): void {
      this.props.onBodyChange?.(this.state.body)
      this.props.showCreateTaskButtons?.(true)
  }

  render() {

    const state = this.state;
    const isCampaignStep = this.props.type === 'campaign-step' || this.props.type === 'step-variant';
    const isTemplate = this.props.type === 'template';
    const availableTags = this.props.availableTags;
    const email = this.props.email || {} as Campaigns.IStepVariant;
    const initialTemplate = this.props.initialTemplate || {} as Campaigns.ICampaignStepTemplateApp;
    const subject = state.subject;
    const body = state.body;
    const label = state.label;
    const isSharedWithTeam = state.isSharedWithTeam;
    const disableButtons = this.props.isSaving || this.props.isSavingTemplate || this.props.isSendingTestEmail || this.props.isDeleting;
    const logInStore = this.props.logInStore || {} as LogIn.ILogInStore;
    const permissions = logInStore.getTeamRolePermissions.permissions;
    const isOwner = (this.props.isOwner || false);
    const showDelaySetting = this.props.showDelaySetting;
    const hasSavedAIVersions = (this.props.campaignStore!.getEmailBodyVersions.length > 0)
    const canEditTemplate = (checkPermission(permissions.edit_templates, isOwner) && !initialTemplate.is_from_library);
    const labelError = state.labelError;
    const testEmailId = state.testEmailId;
    const deleteConfirmModal = state.deleteConfirmModal;
    const showDeleteButton = !!this.props.hideButtons ? !!!this.props.hideButtons.deleteButton : isCampaignStep ? (email.id !== 0) : (isTemplate ? (!!initialTemplate.id) : false);
    // console.log('debug showDeleteButton', showDeleteButton);
    const showSaveAsTemplateButton = !!this.props.hideButtons ? !!!this.props.hideButtons.saveTemplateButton : true
    const showSendTestEmailButton = !!this.props.hideButtons ? !!!this.props.hideButtons.sendTestEmail : true
    const showTemplateShareButton = (isTemplate ? (!!initialTemplate.id) : false);
    const accountInfo = (this.props.logInStore || {} as LogIn.ILogInStore).getAccountInfo;
    const accountId = accountInfo.internal_id;
    // enable fullpage plugin

    const campaign_email_settings = this.props.campaignStore?.getBasicInfo?.settings?.campaign_email_settings
    const can_send_test_email = !_.isEmpty(campaign_email_settings)
    /////

    // set to autofocus on "body editor" if subject is defined, else set autofocus on subject
    const autoFocusOnEditor = (subject && subject.length) ? true : false;

    this.props.changeEmailBody(body);
    const TINYMCE_OPTIONS = this.props.getTinymceOptions({
      autoFocusOnEditor, accountId
    });

    const self = this;
    TINYMCE_OPTIONS.setup = (editor: any) => {

      editor.on('init', (args: any) => {
        this.setState({ isLoadingEditor: false });
      });

      editor.ui.registry.addMenuButton('insertMergeTagButton', {
        text: 'Insert merge-tag',
        tooltip: 'Insert merge-tag',
        fetch: function (callback: any) {
          var items = _.map(availableTags, (tag: string, index) => {
            return (
              {
                type: 'menuitem',
                text: tag,
                onAction: function () {
                  self.onInsertMergeTag(tag)
                }
              }
            );
          }).filter((item: ITagItem) => self.calendarMergeTagFilter(item, logInStore))
          callback(items);
        }
      })


        editor.ui.registry.addIcon('sr_ai_icon',
          `<svg width="20" height="21" viewBox="0 0 20 21" fill="#E4C0FD" xmlns="http://www.w3.org/2000/svg" className={classNames(props.className, "h-[inherit] w-[inherit] text-[inherit]")}>
            <g clip-path="url(#clip0_6688_11936)">
              <path d="M5 18L17.5 5.5L15 3L2.5 15.5L5 18Z" stroke="#A00FFA" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
              <path d="M12.5 5.5L15 8" stroke="#A00FFA" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
              <path d="M7.50065 3C7.50065 3.44203 7.67625 3.86595 7.98881 4.17851C8.30137 4.49107 8.72529 4.66667 9.16732 4.66667C8.72529 4.66667 8.30137 4.84226 7.98881 5.15482C7.67625 5.46738 7.50065 5.89131 7.50065 6.33333C7.50065 5.89131 7.32506 5.46738 7.0125 5.15482C6.69993 4.84226 6.27601 4.66667 5.83398 4.66667C6.27601 4.66667 6.69993 4.49107 7.0125 4.17851C7.32506 3.86595 7.50065 3.44203 7.50065 3Z" stroke="#A00FFA" stroke-linecap="round" stroke-linejoin="round" />
              <path d="M15.8327 11.3333C15.8327 11.7754 16.0083 12.1993 16.3208 12.5118C16.6334 12.8244 17.0573 13 17.4993 13C17.0573 13 16.6334 13.1756 16.3208 13.4882C16.0083 13.8007 15.8327 14.2246 15.8327 14.6667C15.8327 14.2246 15.6571 13.8007 15.3445 13.4882C15.032 13.1756 14.608 13 14.166 13C14.608 13 15.032 12.8244 15.3445 12.5118C15.6571 12.1993 15.8327 11.7754 15.8327 11.3333Z" stroke="#A00FFA" stroke-linecap="round" stroke-linejoin="round" />
            </g>
            <defs>
              <clipPath id="clip0_6688_11936">
                <rect width="20" height="20" fill="#E4C0FD" transform="translate(0 0.5)" />
              </clipPath>
            </defs>
          </svg>`
        );

        editor.ui.registry.addMenuButton('aiOptions', {
          icon: 'sr_ai_icon',
          tooltip: 'Use AI to improve selected text',
          fetch: function (callback: any) {
            var items = _.map(["Rephrase", "Shorten"], (tag: string, index) => {
              return (
                {
                  type: 'menuitem',
                  text: tag,
                  onAction: function () {
                    self.onSelectAIOption(tag, editor.selection)
                  }
                }
              );
            })
            callback(items);
          }
        })

        editor.ui.registry.addMenuItem('rephrase', {
          text: 'Rephrase',
          onAction: function () {
            self.onSelectAIOption('Rephrase', editor.selection)
          }
        });

        editor.ui.registry.addContextMenu('rephrase', {
          update: function (element: any) {
            return 'rephrase';
          }
        });

        editor.ui.registry.addMenuItem('shorten', {
          text: 'Shorten',
          onAction: function () {
            self.onSelectAIOption('Shorten', editor.selection)
          }
        });

        editor.ui.registry.addContextMenu('shorten', {
          update: function (element: any) {
            console.log(element)
            return 'shorten';
          }
        });

      editor.ui.registry.addMenuButton('selectTemplateButton', {
        text: 'Select template',
        tooltip: 'Select a template',
        fetch: function (callback: any) {
          var items = _.map(self.getSearchedTemplates(), (templateCategory: Campaigns.ITemplatesForCategory, index) => {
            console.log('templates dropdown render', templateCategory);
            if (templateCategory.templatesData.length !== 0) {
              return (
                {
                  type: 'nestedmenuitem',
                  text: templateCategory.category,
                  getSubmenuItems: function () {
                    var subItems = _.map(templateCategory.templatesData, (template, index) => {
                      return (
                        {
                          type: 'menuitem',
                          text: template.label,
                          onAction: function () {
                            self.onSelectTemplateNew(template)
                          }
                        });
                    })

                    return subItems;
                  }
                }
              );
            }
            else {
              return;
            }
          });
          callback(items);
        }
      })
    }

    if (this.props.enableFullpagePlugin) {
      const defaultPlugins = TINYMCE_OPTIONS.plugins;
      TINYMCE_OPTIONS.plugins = `${defaultPlugins} fullpage`;
    }

    this.props.changeEmailBody(body);
    const selectedTemplateLabel = this.getSelectedTemplateLabel(this.props.templates || [], this.state.selectedTemplateId || null);
    console.log('Merge tags', availableTags);
    return (
      <div>
        <div>
          <div className='content'>

            {!!this.state.selectedTemplateId &&
              <div className="text-sr-subtext-grey sr-h6 !font-normal mt-[10px] flex items-center">
                <div className='w-10/12 bg-sr-header-grey text-sr-subtext-grey py-[6px] px-[12px] rounded-[5px]'>
                  Selected template: <b className='mx-[2px] text-sr-text-grey'>{selectedTemplateLabel}</b>&nbsp;
                </div>
                {checkPermission(permissions.edit_campaigns, isOwner) &&
                  (<div className='float-right w-max ml-auto'>
                    <SRButtonOutline
                      onClick={this.unlinkTemplate}
                      text='Un-Link Template'
                      isPrimary={true} />
                  </div>)}
              </div>
            }

            {showDelaySetting && isCampaignStep &&
              <div className='delay-strip delay-setting'>
                <strong>If no reply is received for previous email,</strong> this email will be sent after &nbsp;
                {!!this.props.delayDays ? this.props.delayDays : this.state.delayDays} day(s) from previous email
              </div>
            }
            <div className='my-4'>
              <div className="text-sr-subtext-grey sr-h6 !font-normal my-[5px]">Subject:</div>
              <div className='ui fluid input subject draft-jpuri-input'>
                {/* {enableContentAI &&
                    <SRButtonOutline
                      icon='sr_icon_chevron_left'
                      className='mr-[8px]'
                      onClick={this.showPreviousSubject}
                      disable={this.state.selectedSubjectVersion === 0}
                    />
                  }
                  {enableContentAI &&
                    <SRButtonOutline
                      icon='sr_icon_chevron_right'
                      className='mx-[8px]'
                      onClick={this.showNextSubject}
                      disable={this.state.selectedSubjectVersion === (this.props.campaignStore?.getSubjectVersions.length || 0) - 1}
                    />
                  } */}
                <input className='h-[32px]' type='text' id='subject' placeholder='Enter Subject' value={subject} autoFocus={!autoFocusOnEditor} onChange={this.onSubjectChange} onFocus={this.subjectOrEditorOnFocus.bind(this, 'subject')} autoComplete="off" />
                  <SRButtonOutline
                    icon='sr_icon_chevron_left'
                    className='border-none ml-[-60px]'
                    onClick={this.showPreviousSubject}
                    disable={this.state.selectedSubjectVersion <= 0}
                  />

                  <SRButtonOutline
                    icon='sr_icon_chevron_right'
                    className='border-none'
                    onClick={this.showNextSubject}
                    disable={this.state.selectedSubjectVersion === (this.props.campaignStore?.getSubjectVersions.length || 0) - 1}
                  />

                { body.trim().length > 0 &&
                  <div>
                    {this.state.showRewriteSubject ?
                      <SRButtonOutline
                        icon='sr_ai_icon'
                        className='ml-[8px]'
                        text="Rewrite Subject"
                        onClick={this.regenerateSubject}
                        loading={this.state.isLoadingGenerateSubjectButton}
                      />
                      :
                      <GPTGenerateSubjectPopover
                        generateSubjectThroughGPT={this.generateSubjectThroughGPT}
                        isLoadingGenerateSubjectButton={this.state.isLoadingGenerateSubjectButton}
                      />
                    }

                  </div>
                }
              </div>
            </div>
            <div className='editor-strip editor-modal'>
              <div>
                <div className='email-editor'>
                  <div className='subject-body-padding'>
                    {this.state.isLoadingEditor &&
                      <div style={{ marginTop: '2em', marginBottom: '2em' }}>
                        <SRLoaderDefault />
                      </div>
                    }
                    <div style={{ display: this.state.isLoadingEditor ? 'none' : 'inherit' }}>
                      {hasSavedAIVersions && this.state.selectedBodyVersion > 0 &&
                        <div className='flex'>
                          <SRButtonOutline
                            icon="sr_icon_reply"
                            className='m-[8px]'
                            onClick={this.undoChange}
                          />
                          <SRButtonOutline
                            icon="sr_icon_forward"
                            className='m-[8px]'
                            onClick={this.redoChange}
                          />
                          <SRButtonFilled
                            text="Copy Text to Clipboard"
                            // isPrimary={true}
                            className='m-[8px]'
                            onClick={() => {
                              navigator.clipboard.writeText(body.split("<br />").join("\n"))
                              this.props.alertStore?.pushAlert({ status: 'success', message: 'Copied Text to clipboard' })
                            }}
                          />
                          <SRButtonOutline
                            text="Regenerate"
                            className='m-[8px]'
                            onClick={this.regenerateBody}
                          />
                          <SRButtonOutline
                            text="Edit Inputs"
                            className='m-[8px]'
                            onClick={() => this.props.showOrHideEmailBodyPromptFields(true)}
                          />
                          <SRButtonOutline
                            icon='sr_icon_add'
                            text="Add Command"
                            className='m-[8px]'
                            onClick={() => this.props.showOrHideAddCommandSection(true, this.state.body)}
                          />
                          <SRButtonOutline
                            icon="sr_icon_delete"
                            className='m-[8px]'
                            iconClassName='text-sr-dark-red'
                            onClick={this.deleteVersion}
                          />
                        </div>
                      }
                      <div className='flex'>
                        <div className='flex-1'>
                          <Editor
                            tinymceScriptSrc={this.props.TINYMCE_URL}
                            value={body}
                            onEditorChange={this.onBodyChange}
                            init={TINYMCE_OPTIONS}
                            onFocus={this.subjectOrEditorOnFocus.bind(this, 'editor')}
                          />
                        </div>
                          <div className='max-w-[158px] flex flex-col justify-between'>
                            <div>
                              <ul className='list-none' key={this.state.emailBodyVersions.length}>
                                <li className="flex">
                                  <SRButtonOutline
                                    className={classNames(this.state.selectedBodyVersion === 0 ? 'bg-sr-lightest-purple !text-sr-default-purple border-none' : '', 'w-[150px] ml-[8px]')}
                                    text="Your Draft"
                                    onClick={() => {
                                      this.props.campaignStore?.updateEmailBodyVersion(this.state.selectedBodyVersion - 1, this.state.body)
                                      this.setState(
                                        { body: this.props.campaignStore!.getUserEmailBodyDraft, selectedBodyVersion: 0 },
                                        () => this.props.onBodyChange?.(this.props.campaignStore!.getUserEmailBodyDraft)
                                      )
                                    }}
                                    toolTip={{
                                      text: 'Copy Your Preferred Version to "Your Draft" to "Save Step"'
                                    }
                                    }
                                  />
                                </li>
                                {this.state.emailBodyVersions.map((version, index) => {
                                  return (<li className='flex'>
                                    <SRButtonOutline
                                      className={classNames(this.state.selectedBodyVersion === this.state.emailBodyVersions.length - index ? 'bg-sr-lightest-purple !text-sr-default-purple border-none' : '', 'w-[150px] mt-[8px] ml-[8px]')}
                                      text={version.displayText}
                                      onClick={version.onClick}
                                      toolTip={{
                                        text: "AI Versions won't be available once you exit. Do save final version to 'Your Draft'."
                                      }
                                      }
                                    />
                                  </li>)
                                })}
                              </ul>
                            </div>
                            <div className='ml-[10px]'>
                              <SRMessageBox
                                width='fluid'
                                className='!mb-0 !p-1'
                                content={[{
                                  element: <p className='!text-sm'>AI versions won't be available once you save the step or reload the page.</p>
                                }]}
                                type='warning'
                              />
                            </div>
                          </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>

          {this.state.selectedBodyVersion !== 0 &&
            <div className='flex float-right mt-8 w-fit'>
              <SRButtonOutline
                text="Go To Your Draft"
                width='default'
                className='mx-[8px]'
                onClick={() => {
                  this.setState(
                    { selectedBodyVersion: 0, body: this.props.campaignStore!.getUserEmailBodyDraft },
                    () => this.props.onBodyChange?.(this.props.campaignStore!.getUserEmailBodyDraft)
                  )
                }}
              />

              <SRButtonFilled
                text="Copy To Your Draft"
                isPrimary={true}
                width='default'
                className='mx-[8px]'
                onClick={() => {
                  this.props.campaignStore?.updateUserEmailBodyDraft(this.state.body)
                  this.props.alertStore?.pushAlert({ status: 'success', message: 'Copied Text to Your Draft' })
                }}
              />
            </div>
          }

          {this.state.selectedBodyVersion === 0 &&

            <div className='flex'>
              {this.props.type !== 'template' && this.props.type !== 'manual-task' &&
                <div className='actions py-[16px] text-left w-fit mr-auto'>
                  <SRButtonTonal
                    text="Back"
                    onClick={this.props.onBack}
                    isPrimary={true}
                    width="default"
                    icon="sr_icon_chevron_left"
                    iconClassName="!h-[20px]"
                  />
                </div>
              }

              <div className='ml-auto actions py-[16px] text-right w-fit'>

                {/*1. template share button */}
                {showTemplateShareButton &&
                  <SRButtonOutline
                    onClick={this.shareTemplate.bind(this, initialTemplate.id!, !initialTemplate.shared_with_team)}
                    text={initialTemplate.shared_with_team ? 'Shared with team' : 'Share with team'}
                    className='share-with-team ml-[5px]'
                    icon={initialTemplate.shared_with_team ? 'checkmark box' : 'square outline'}
                    loading={this.props.sharingTemplateId === initialTemplate.id}
                    disable={disableButtons || !canEditTemplate}
                    width='default' />
                }

                {/* 2. delete button */}
                {showDeleteButton && isCampaignStep &&
                  <SRButtonOutline disable={disableButtons || !checkPermission(permissions.edit_campaigns, isOwner)} text='Delete'
                    loading={this.props.isDeleting} onClick={this.showDeleteConfirmModal} width='default' className='ml-[5px]' />
                }

                {showDeleteButton && isTemplate &&

                  <SRButtonOutline disable={disableButtons || !canEditTemplate} type='button' text='Delete'
                    loading={this.props.isDeleting} onClick={this.showDeleteConfirmModal} width='default' className='ml-[5px]' />
                }

                {/* 3. send test email button */}
                {showSendTestEmailButton && isCampaignStep &&
                  <SendTestMailPopup disable={disableButtons || !checkPermission(permissions.edit_campaigns, isOwner)} loading={this.props.isSendingTestEmail}
                    onSubmit={this.confirmSendTestmail} can_send_test_email={can_send_test_email} defaultTestEmail={testEmailId} campaign_email_settings={this.props.campaignStore?.getBasicInfo.settings.campaign_email_settings!} />
                }

                {/* 4.Save campaign step as template button */}
                {showSaveAsTemplateButton && isCampaignStep && <SaveAsTemplatePopup isSharedWithTeam={isSharedWithTeam} disable={disableButtons || !checkPermission(permissions.edit_templates, isOwner)}
                  isTemplate={isTemplate} loading={this.props.isSavingTemplate} defaultLabel={label}
                  onSubmit={this.saveAsTemplateV2} labelError={labelError} />
                }

                {isTemplate && <SaveAsTemplatePopup isSharedWithTeam={isSharedWithTeam} disable={disableButtons || !checkPermission(permissions.edit_templates, isOwner)}
                  isTemplate={isTemplate} loading={this.props.isSavingTemplate} defaultLabel={label}
                  onSubmit={this.saveAsTemplateV2} updateForAllLinkedEmails={this.state.updateForAllLinkedEmails}
                  labelError={labelError} initialTemplateId={initialTemplate.id} />
                }

                {/* 5. Save campaignstep button */}
                {isCampaignStep &&
                  <SRButtonFilled
                    disable={disableButtons || !checkPermission(permissions.edit_campaigns, isOwner)}
                    loading={this.props.isSaving}
                    width='default' isPrimary={true}
                    onClick={this.saveCampaignStep}
                    text={this.props.email?.id ? 'Update Step' : 'Save Step'}
                    className='ml-[5px]' />
                }

              </div>
            </div>
          }


          {deleteConfirmModal &&
            <ConfirmDeleteWithInputModal
              heading={`Delete ${isTemplate ? 'template' : 'campaign step'}?`}
              modalLoading={this.props.isDeleting}
              onCancel={this.closeDeleteConfirmModal}
              onConfirm={this.delete}

            >
              {isTemplate ?
                <p>Data related to this template will be deleted permanently.</p>
                :
                OnDeleteStepText('email_channel')
              }
            </ConfirmDeleteWithInputModal>
          }
        </div>
      </div >
    )
  }

}