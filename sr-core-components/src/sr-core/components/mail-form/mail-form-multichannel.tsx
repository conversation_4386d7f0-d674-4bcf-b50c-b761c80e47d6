import * as React from "react";
import { Formik, Form } from "formik";
import { EmailStepFields } from "./email-step-fields";
import { NewSettingsData, channel_type } from "../../utils/campaignStepData";
import { SRSpinner } from "@sr/design-component";
import { ISRDropdownOption, SRDropdownMenu } from "@sr/design-component";
import { GPTEmailBodyPromptFields } from "./gpt-email-body-promt-fields";
import GPTAddCommandAndRegenerate from "./gpt-add-command-and-regenerate";
import { IServerResponse } from '../../models/server';
import { ITinyMceOptions } from '../../models/editor';
import { LogIn } from '../../models/login';
import { IEmailBodyPromptRequest, IGenerateSubjectRequest, IRegenerateEmailBodyRequest, IRegenerateSubjectRequest } from "../../models/gpt";
import { Alerts } from "../../models/alerts";
import { Campaigns } from "../../models/campaigns";
import { ITemplateTags } from "../../models/column";

export interface IHideButtons {
  deleteButton?: boolean;
  saveTemplateButton?: boolean;
  sendTestEmail?: boolean;
}

interface IProps {
  type: Campaigns.IEditorModalType
  changeTitle: (showAITitle: boolean) => void;
  emailVariant?: Campaigns.IStepVariant;
  campaignStore: Campaigns.ICampaignStore;
  isNotFirstEmailStep: boolean;
  enableContentAI: boolean;
  templates: Campaigns.ITemplatesForCategory[];
  initialTemplate?: Campaigns.ICampaignStepTemplateApp;
  showDelaySetting?: boolean
  accountId: number | string
  onSendTestMail?: (data: Campaigns.ITestMail) => void
  onDeleteCampaignStep?: (stepId: number | string, variantId: number | string) => void
  testEmail?: string
  unlinkTemplate?: (campaignId: number | string, stepId: number | string, variantId: number | string) => void
  isOwner: boolean
  saveAsTemplate: (template: Campaigns.ICampaignStepTemplateFELib) => void;
  action: string;
  onSubmit: (data: Campaigns.ISaveStepData) => void;
  newSettings?: NewSettingsData;
  onBack?: () => void;
  isSaving?: boolean
  isDeleting?: boolean
  isSavingTemplate?: boolean
  isSendingTestEmail?: boolean
  stepIndex?: number
  hideButons?: IHideButtons;
  onSubjectChange?: (subject: string) => void
  onBodyChange?: (subject: string) => void
  getTinymceOptions(data: {
    autoFocusOnEditor: boolean;
    accountId: number;
  }): ITinyMceOptions;
  TINYMCE_URL: string;
  enableFullpagePlugin: boolean
  rephraseText(text: String): IServerResponse<{
    rephrased_text: string;
  }>
  shortenText(text: String): IServerResponse<{
    shortened_text: string;
  }>
  regenerateEmailBodyThroughCommand(data: IRegenerateEmailBodyRequest): IServerResponse<{
    email_body: string;
  }>
  regenerateSubject(data: IRegenerateSubjectRequest): IServerResponse<{
    email_subject: string;
  }>
  generateEmailBodyThroughPrompt(data: IEmailBodyPromptRequest): IServerResponse<{
    email_body: string;
  }>
  generateEmailSubjectThroughGPT(data: IGenerateSubjectRequest): IServerResponse<{
    email_subject: string;
  }>
  getTags(channelType?: channel_type | undefined): IServerResponse<ITemplateTags>
  logInStore?: LogIn.ILogInStore
  alertStore?: Alerts.IAlertStore
  showCreateTaskButtons?(flag: boolean): void
}
// type IErrors = Settings.IEmailDataErrors;


export const OpenMailForm = (props: IProps) => {


  const [isLoading, setIsLoading] = React.useState(false)
  const [emailBody, setEmailBody] = React.useState<string>('')

  React.useEffect(() => {
    setIsLoading(true)
    props.getTags('email_channel')
      .then((columnsApiResponse) => {
        //props.campaignStore?.updateAvailableTags([]);
        props.campaignStore?.updateAvailableTags(columnsApiResponse.data.template_tags);
        setIsLoading(false)
        //console.log("available tags", availableTags)
      })
      .catch((e: any) => {
        setIsLoading(false);
      })
  }, [])

  const [day, setDay] = React.useState((props.newSettings?.step_delay || props.emailVariant?.step_delay || 86400) / 86400);
  const [priority, setPriority] = React.useState<Campaigns.priority>(props.newSettings?.priority || props.emailVariant?.priority || "normal");
  const [touchType, setTouchType] = React.useState<Campaigns.ICampaignStepType>(props.emailVariant?.step_data?.step_type || props.newSettings?.action as Campaigns.ICampaignStepType || "send_email")
  const [showEmailStepModalContent, setShowEmailStepModalContent] = React.useState<boolean>(true)
  const [showEmailBodyPromptFields, setShowEmailBodyPromptFields] = React.useState<boolean>(false)
  const [showAddCommandAndRegenerateModal, setShowAddCommandAndRegenerateModal] = React.useState<boolean>(false)


  const enableContentAI = props.enableContentAI

  const changeEmailBody = (email_body: string) => {
    setEmailBody(email_body)
  }

  const generateEmailBody = (data: IEmailBodyPromptRequest) => {

    setShowEmailBodyPromptFields(false)
    setIsLoading(true)
    props.changeTitle(false);

    props.generateEmailBodyThroughPrompt(data)
      .then(res => {
        props.campaignStore.setNewVersionOfEmailBody(res.data.email_body.split("\n").join("<br />"))
        setIsLoading(false)
        setShowEmailStepModalContent(true);
      })
      .catch(err => {
        console.error(err)
        setShowEmailBodyPromptFields(true)
        setIsLoading(false)
      })
  }

  const backToEmailDraft = () => {
    setShowEmailStepModalContent(true);
    setShowEmailBodyPromptFields(false);
    setShowAddCommandAndRegenerateModal(false);
    props.changeTitle(false)
  }

  const initialValues = {
    day: day,
    priority: priority,
    touch_type: touchType
  };

  function handleOnChange(e: any) {
    const element = e.target
    if (element.name === "day") {
      setDay(element.value)
    } else if (element.name === "priority") {
      setPriority(element.value)
    } else if (element.name === "touch_type") {
      setTouchType(element.value)
    }
  }

  const showOrHideEmailBodyPromptFields = (value: boolean) => {
    setShowEmailBodyPromptFields(value)
    setShowEmailStepModalContent(false)
  }

  const showOrHideAddCommandSection = (value: boolean, emailBody: string) => {
    setShowAddCommandAndRegenerateModal(value)
    setEmailBody(emailBody)
    setShowEmailStepModalContent(false)
  }

  function onClickOptionWithAI(option: ISRDropdownOption) {
    props.changeTitle(true);
    if (option.value === "generate") {
      setShowEmailStepModalContent(false)
      setShowEmailBodyPromptFields(true)
    }
    else if (option.value === "edit_prompts") {
      setShowEmailStepModalContent(false);
      setShowEmailBodyPromptFields(true)
    }
  }

  function getWriteWithAIoptions() {
    const writeWithAIOptions: ISRDropdownOption[] = []

    if (props.campaignStore.getEmailBodyVersions.length <= 0) {

      // writing first version

      writeWithAIOptions.push({
        displayText: "Generate email",
        value: "generate"
      })

    } else {

      // one version already exists

      writeWithAIOptions.push({
        displayText: "Generate another version",
        value: "edit_prompts"
      })

      /* NOTE: 16-May-2024: we do not need to give the new prompts option at all.
      writeWithAIOptions.push({
        displayText: "Use new prompts",
        value: "generate"
      })
      */
    }

    return writeWithAIOptions
  }
  // function validateDefs(values: any) {
  //   const errors: IErrors = {} as IErrors;
  //   // console.log(values.email, values);
  //   errors["day"] = "IMAP host is required";

  // }
  return (
    <div className="">
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={() => { }}
        className="inline"
        // validate={validateDefs}

        validateOnChange
      >
        {({ isSubmitting }) => (
          <Form onChange={(e) => handleOnChange(e)}>
            {isLoading ?
              <SRSpinner />
              :

              <div className="mt-6">
                {showEmailStepModalContent &&

                  <div>
                    <div className="flex justify-between pt-[10px] text-sr-subtext-grey sr-h5">
                      {props.type === "campaign-step" &&
                        <div>
                          Step {(props.stepIndex || 0) + 1} :
                          <b className="text-sr-text-grey"> Email Step - {touchType === 'manual_send_email' ? 'Manual' : 'Auto'}</b>
                        </div>
                      }

                      {enableContentAI &&
                        <div className="ml-auto">
                          <SRDropdownMenu
                            icon="sr_ai_icon"
                            iconClassName="mt-[1px] mr-[8px]"
                            menuButtonText="Write with AI"
                            dropdownMenuClassName="min-w-[200px]"
                            menuButtonClassName="text-sr-default-purple border-sr-default-purple w-[150px]"
                            options={getWriteWithAIoptions()}
                            onClickOption={onClickOptionWithAI}
                          />
                        </div>
                      }
                    </div>



                    <EmailStepFields
                      type={props.type}
                      showOrHideEmailBodyPromptFields={showOrHideEmailBodyPromptFields}
                      showOrHideAddCommandSection={showOrHideAddCommandSection}
                      availableTags={props.campaignStore.getAvailableTags}
                      isNotFirstEmailStep={props.isNotFirstEmailStep}
                      email={props.emailVariant}
                      templates={props.templates}
                      initialTemplate={props.initialTemplate}
                      showDelaySetting={props.showDelaySetting}
                      isSaving={props.isSaving || false}
                      isSavingTemplate={props.isSavingTemplate || false}
                      isSendingTestEmail={props.isSendingTestEmail || false}
                      isDeleting={props.isDeleting || false}
                      testEmail={props.testEmail}
                      unlinkTemplate={props.unlinkTemplate ? props.unlinkTemplate : () => { }}
                      shareWithTeam={() => { }}
                      onSendTestMail={props.onSendTestMail ? props.onSendTestMail : () => { }}
                      changeEmailBody={changeEmailBody}
                      onDeleteCampaignStep={props.onDeleteCampaignStep ? props.onDeleteCampaignStep : () => { }}
                      isOwner={props.isOwner}
                      onSaveAsTemplate={props.saveAsTemplate}
                      delayDays={day}
                      priority={priority}
                      touchType={touchType}
                      onSaveStepVariant={props.onSubmit}
                      onBack={props.onBack}
                      onDeleteTemplate={() => { }}
                      hideButtons={props.hideButons}
                      onBodyChange={props.onBodyChange}
                      onSubjectChange={props.onSubjectChange}
                      getTinymceOptions={props.getTinymceOptions}
                      TINYMCE_URL={props.TINYMCE_URL}
                      enableFullpagePlugin={props.enableFullpagePlugin}
                      rephraseText={props.rephraseText}
                      shortenText={props.shortenText}
                      regenerateEmailBodyThroughCommand={props.regenerateEmailBodyThroughCommand}
                      regenerateSubject={props.regenerateSubject}
                      generateEmailSubjectThroughGPT={props.generateEmailSubjectThroughGPT}
                      logInStore={props.logInStore}
                      campaignStore={props.campaignStore}
                      alertStore={props.alertStore}
                      showCreateTaskButtons={props.showCreateTaskButtons}
                    />
                  </div >

                }

              </div >
            }

          </Form >
        )}
      </Formik >

      {showEmailBodyPromptFields &&
        <GPTEmailBodyPromptFields
          campaignStore={props.campaignStore}
          backToEmailDraft={backToEmailDraft}
          generateEmailBody={generateEmailBody}
          showCreateTaskButtons={props.showCreateTaskButtons}
        />
      }

      {
        showAddCommandAndRegenerateModal &&
        <GPTAddCommandAndRegenerate
          campaignStore={props.campaignStore}
          emailBody={emailBody}
          changeTitle={props.changeTitle}
          backToEmailDraft={backToEmailDraft}
          regenerateEmailBodyThroughCommand={props.regenerateEmailBodyThroughCommand}
        />
      }

    </div >
  );
};