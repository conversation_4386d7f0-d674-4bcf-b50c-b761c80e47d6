import { ISRDropdownOption, SRButtonOutline, SRInput, SRMultiSelectDropdown, 
    SRSimpleSelectionDropdown, SRTooltip, SrInfo, SRTextArea, classNames } from '@sr/design-component';
import * as React from "react";
import { departmentOptions, emailStructureOptions, emailTypeOptions, getMergeTagsToUseOptions, industryOptions, recipientPositionOptions, voiceToneOptions, writingStyleOptions } from '../../utils/gptUtils';
import { Campaigns } from '../../models/campaigns';
import { IEmailBodyPromptRequest } from '../../models/gpt';

interface IEmailBodyPromptProps {
  campaignStore: Campaigns.ICampaignStore
  backToEmailDraft: () => void;
  generateEmailBody: (data: IEmailBodyPromptRequest) => void
  showCreateTaskButtons?(flag: boolean): void
}

export const GPTEmailBodyPromptFields = (props: IEmailBodyPromptProps) => {

  const previousEmailBodyPrompt = props.campaignStore.getEmailBodyPrompt

  const [recipientPosition, setRecipientPosition] = React.useState(previousEmailBodyPrompt.recipient_position || '')
  const [department, setDepartment] = React.useState(previousEmailBodyPrompt.department || '')
  const [industry, setIndustry] = React.useState(previousEmailBodyPrompt.industry || '')

  const [emailType, setEmailType] = React.useState(previousEmailBodyPrompt.email_type || '')
  const [writingStyle, setWritingStyle] = React.useState(previousEmailBodyPrompt.writing_style || '')
  const [emailStructure, setEmailStructure] = React.useState(previousEmailBodyPrompt.email_structure || '')
  const [mergeTagsToUse, setMergeTagsToUse] = React.useState<string[]>(previousEmailBodyPrompt.merge_tags_to_use || [])
  const [maxNumberOfWords, setMaxNumberOfWords] = React.useState(previousEmailBodyPrompt.max_number_of_words || 200)
  const [voiceTone, setVoiceTone] = React.useState(previousEmailBodyPrompt.tone_of_voice || '')

  const [ignoreResponses, setIgnoreResponses] = React.useState<boolean>(previousEmailBodyPrompt.ignore_dropdowns || false)
  const [additionalComment, setAdditionalComment] = React.useState(previousEmailBodyPrompt.additional_comment || '')

  const [showCommentEmptyError, setShowCommentEmptyError] = React.useState(false)

  React.useEffect(() => {
    props.showCreateTaskButtons?.(false)
  }, [])

  const generateContent = () => {

  let data: IEmailBodyPromptRequest
  if (ignoreResponses) {
  data = {
    merge_tags_to_use: [],
    additional_comment: additionalComment !== '' ? additionalComment : undefined
  }
  }
  else {
  data = {
    recipient_position: recipientPosition !== '' ? recipientPosition : undefined,
    department: department !== '' ? department : undefined,
    industry: industry !== '' ? industry : undefined,
    email_type: emailType !== '' ? emailType : undefined,
    writing_style: writingStyle !== '' ? writingStyle : undefined,
    email_structure: emailStructure !== '' ? emailStructure : undefined,
    merge_tags_to_use: mergeTagsToUse.map(tag => { return "{{" + tag + "}}" }),
    max_number_of_words: maxNumberOfWords,
    tone_of_voice: voiceTone !== '' ? voiceTone : undefined,
    additional_comment: additionalComment !== '' ? additionalComment : undefined
  }
  }

  const prompt: Campaigns.IEmailBodyPrompt = {
  recipient_position: recipientPosition,
  department: department,
  industry: industry,
  email_type: emailType,
  writing_style: writingStyle,
  email_structure: emailStructure,
  merge_tags_to_use: mergeTagsToUse,
  max_number_of_words: maxNumberOfWords,
  tone_of_voice: voiceTone,
  ignore_dropdowns: ignoreResponses,
  additional_comment: additionalComment
  }

  if (data.additional_comment) {
  props.campaignStore.setEmailBodyPrompt(prompt)

  props.generateEmailBody(data)
  }
  else {
  setShowCommentEmptyError(true)
  }

  }

  return (
  <div className="px-4 my-[8px]">


    <div>
      <div className='mt-[24px]'>
        <b>1. What do you want to convey in the email? * </b>
        <div className='mt-[8px]'>
          <SRTextArea
            name='AdditionalComment'
            placeholder='For example, write how recruitment automation will help recruitment leaders ensure their teams hire ideal candidates and reduce hiring cycles'
            handleChange={e => {
              setAdditionalComment(e.target.value);
            }}
            value={additionalComment}
            width='fluid'
            rows={3}
          />
          {showCommentEmptyError &&
            <p className='text-sr-dark-red text-sm'>Please provide a short description of the message you want to convey.</p>
          }
        </div>

        <div>
          <div className='inline-flex items-center my-[16px]'>
            <input
              name="ignore-inputs"
              type="checkbox"
              className='h-[20px] w-[20px] rounded-[4px] border-sr-light-grey focus:border-sr-default-blue text-sr-default-blue'
              onChange={() => setIgnoreResponses(!ignoreResponses)}
              checked={!ignoreResponses}
            />

            <label className="sr-h6 !font-normal mx-2">
              Use the inputs selected below
            </label>
          </div>
        </div>
      </div>

      <div className='mt-[8px]'>
        <div className='flex'>
          <b className='mr-[2px]'>2. Audience</b> (optional)
          <SRTooltip
            direction='top-left'
            text='If your preferred option is not present leave it as "No Prompt" and mention it in the “What do you want to convey” section'
          >
            <SrInfo className='!h-[16px] !w-[16px] !mx-[4px] !my-[1px]' />
          </SRTooltip>
        </div>

        <div className={classNames('grid grid-cols-3 gap-8', ignoreResponses ? 'opacity-[0.5]' : '')}>
          <div className='mt-[16px]'>
            Recipient Position
            <SRSimpleSelectionDropdown
              selectedValue={recipientPosition}
              options={recipientPositionOptions}
              disabled={ignoreResponses}
              dropdownButtonClassName='min-w-[250px] min-h-[32px]'
              handleChange={(option: ISRDropdownOption) => {
                setRecipientPosition(option.value + '')
              }}
            />
          </div>

          <div className='mt-[16px]'>
            Department
            <SRSimpleSelectionDropdown
              selectedValue={department}
              options={departmentOptions}
              disabled={ignoreResponses}
              dropdownButtonClassName='min-w-[250px] min-h-[32px]'
              handleChange={(option: ISRDropdownOption) => {
                setDepartment(option.value + '')
              }}
            />
          </div>

          <div className='mt-[16px]'>
            Industry
            <SRSimpleSelectionDropdown
              selectedValue={industry}
              options={industryOptions}
              disabled={ignoreResponses}
              dropdownButtonClassName='min-w-[250px] min-h-[32px]'
              handleChange={(option: ISRDropdownOption) => {
                setIndustry(option.value + '')
              }}
            />
          </div>
        </div>
      </div>

      <div className='mt-[24px]'>
        <div className='flex'>
          <b className='mr-[2px]'>3. Email Content</b> (optional)

          <SRTooltip
            direction='top-left'
            text='If your preferred option is not present leave it as "No Prompt" and mention it in the “What do you want to convey” section'
          >
            <SrInfo className='!h-[16px] !w-[16px] !mx-[4px] my-[2px]' />
          </SRTooltip>
        </div>
        <div className={classNames('grid grid-cols-3 gap-8', ignoreResponses ? 'opacity-[0.5]' : '')}>
          <div className='mt-[16px]'>
            Email Type
            <SRSimpleSelectionDropdown
              selectedValue={emailType}
              options={emailTypeOptions}
              disabled={ignoreResponses}
              dropdownButtonClassName='min-w-[250px] min-h-[32px]'
              handleChange={(option: ISRDropdownOption) => {
                setEmailType(option.value + '')
              }}
            />
          </div>

          <div className='mt-[16px]'>
            Writing Style
            <SRSimpleSelectionDropdown
              selectedValue={writingStyle}
              options={writingStyleOptions}
              disabled={ignoreResponses}
              dropdownButtonClassName='min-w-[250px] min-h-[32px]'
              handleChange={(option: ISRDropdownOption) => {
                setWritingStyle(option.value + '')
              }}
            />
          </div>

          <div className='mt-[16px]'>
            Email Structure
            <SRSimpleSelectionDropdown
              selectedValue={emailStructure}
              options={emailStructureOptions}
              disabled={ignoreResponses}
              dropdownButtonClassName='min-w-[250px] min-h-[32px]'
              handleChange={(option: ISRDropdownOption) => {
                setEmailStructure(option.value + '')
              }}
            />
          </div>
        </div>

        <div className={classNames('grid grid-cols-3 gap-8', ignoreResponses ? 'opacity-[0.5]' : '')}>
          <div className='mt-[16px]'>
            Merge tags to use
            <SRMultiSelectDropdown
              placeholder='Select Merge-tags'
              options={getMergeTagsToUseOptions(props.campaignStore.getAvailableTags)}
              selectedOptions={mergeTagsToUse.map(tag => { return { displayText: tag, value: tag } })}
              onClose={() => { }}
              disabled={ignoreResponses}
              dropdownButtonClassName='min-w-[250px] min-h-[32px]'
              handleChange={(selectedOption: ISRDropdownOption[]) => {
                setMergeTagsToUse(selectedOption.map(tag => { return tag.displayText }))
              }}
            />
          </div>

          <div className='mt-[16px]'>
            Maximum Number of Words
            <SRInput
            className='h-[32px] min-w-[250px]'
            type='number'
            name='MaxNumberOfWords'
            handleChange={ e => {
              setMaxNumberOfWords(Number(e.target.value));
            }}
            selectedValue={String(maxNumberOfWords)}
            disabled={ignoreResponses}
            />
          </div>

          <div className='mt-[16px]'>
            Tone of Voice
            <SRSimpleSelectionDropdown
              selectedValue={voiceTone}
              options={voiceToneOptions}
              disabled={ignoreResponses}
              dropdownButtonClassName='min-w-[250px]  min-h-[32px]'
              handleChange={(option: ISRDropdownOption) => {
                setVoiceTone(option.value + '')
              }}
            />
          </div>
        </div>
      </div>
    </div>


  <div className='flex justify-between mt-8'>
    <SRButtonOutline
      icon='sr_icon_chevron_left'
      text='Back to Email Draft'
      className='bg-sr-light-blue text-sr-dark-blue border-none'
      onClick={props.backToEmailDraft}
    />
    <SRButtonOutline
      icon="sr_ai_icon"
      iconClassName="mt-[1px] mr-[8px]"
      text='Generate Content'
      onClick={generateContent}
      isPurple={true}
    />
  </div>
  </div >
  )

}