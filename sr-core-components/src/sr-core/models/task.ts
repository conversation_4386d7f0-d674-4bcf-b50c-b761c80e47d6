export declare namespace Task {

  type ITaskChannelType = "email" | "whatsApp" | "sms" | "linkedin" | "generic" | "call"

  type ITaskStatusType = 'archived' | 'due' | 'snoozed' | 'done' | 'skipped' | "failed" | "pending_approval" | "approved";

  type ITaskPriority = 'critical' | 'high' | 'normal' | 'low'

  type ITaskCreatedVia = 'manual' | 'scheduler' | 'call-action'

  type ITaskType =
    "manual_send_email" |
    "send_email" |  // auto email
    "send_linkedin_connection_request" |
    "send_linkedin_message" |
    "send_linkedin_inmail" |
    "linkedin_view_profile" |
    "general_task" |
    "send_whatsapp_message" |
    "send_sms" |
    "call" |
    "auto_send_linkedin_connection_request" |
    "auto_send_linkedin_message" |
    "auto_send_linkedin_inmail" |
    "auto_linkedin_view_profile" |

    "move_to_another_campaign" |
    "auto_email_magic_content" |
    "manual_email_magic_content";

  type ITimeRange = "today" | "due";


  interface ITaskStatusObject {
    status_type: ITaskStatusType
  }

  interface IFailed extends ITaskStatusObject {
    status_type: 'failed',
    failure_reason: string,
    failed_at: Date
  }

  interface IArchived extends ITaskStatusObject {
    status_type: 'archived',
    archive_at: Date
  }

  interface IDue extends ITaskStatusObject {
    status_type: 'due',
    due_at: Date
  }

  interface IPendingApproval extends ITaskStatusObject {
    status_type: 'pending_approval';
    due_at: Date;
  }

  interface IApproved extends ITaskStatusObject {
    status_type: 'approved';
    done_at: Date;
    done_by?: number;
  }

  interface ISnoozed extends ITaskStatusObject {
    status_type: 'snoozed',
    snoozed_till: Date
  }

  interface IDone extends ITaskStatusObject {
    status_type: 'done',
    done_at: Date,
    done_by?: number
  }

  interface ISkipped extends ITaskStatusObject {
    status_type: 'skipped',
    skipped_at: Date,
    skipped_by: number
  }

  type ITaskStatus = IArchived | IDone | IDue | ISkipped | ISnoozed | IFailed | IPendingApproval | IApproved;

  interface ITaskDataObject {
    task_type: ITaskType;
  }

  interface ILinkedinConnectionRequestTaskData extends ITaskDataObject {
    task_type: 'send_linkedin_connection_request',
    request_message?: string;
    subject?: string;
    body?: string;
    task_notes?: string;

  }
  //
  interface ILinkedinSendMessageTaskData extends ITaskDataObject {
    task_type: 'send_linkedin_message',
    request_message?: string;
    subject?: string;
    body: string;
    task_notes?: string;

  }

  interface IGenericTaskData extends ITaskDataObject {
    task_type: 'general_task',
    body?: string;
    subject?: string;
    request_message?: string;
    task_notes?: string;

  }

  interface ISendEmailTaskData extends ITaskDataObject {
    task_type: 'manual_send_email',
    subject: string;
    email_message_id?: number;
    body: string;
    request_message?: string;
    task_notes?: string;
  }

  interface IProspect {
    id: number,
    name?: string,
    email?: string,
    company?: string,
    phone_number?: string,
    linkedin_url?: string,
    timezone?: string,
    designation?: string
  }

  interface IAssignee {
    id: number,
    name: string
  }

  interface ISendSmsTaskData extends ITaskDataObject {
    task_type: 'send_sms',
    subject?: string;
    body: string;
    request_message?: string;
    task_notes?: string;
  }

  interface ICallTaskData extends ITaskDataObject {
    task_type: 'call';
    subject?: string;
    body: string;
    recording_link?: string;
    request_message?: string;
    task_notes?: string;
  }

  interface ISendWhatsAppMessageTaskData extends ITaskDataObject {
    task_type: 'send_whatsapp_message',
    subject?: string;
    body: string;
    request_message?: string;
    task_notes?: string;
  }

  interface ILinkedinViewProfile extends ITaskDataObject {
    task_type: 'linkedin_view_profile',
    subject?: string;
    body?: string;
    request_message?: string;
    task_notes?: string;
  }

  interface ILinkedinSendInMail extends ITaskDataObject {
    task_type: 'send_linkedin_inmail',
    subject?: string;
    body: string;
    request_message?: string;
    task_notes?: string;
  }

  interface IAutoEmailMagicContentTaskData extends ITaskDataObject {
    task_type: 'auto_email_magic_content';
    generated_subject?: string;
    generated_body?: string;
    emails_scheduled_uuid: string;
    email_scheduled_text_body: string;
    email_scheduled_base_body: string;
  }

  interface IManualEmailMagicContentTaskData extends ITaskDataObject {
    task_type: 'manual_email_magic_content';
    generated_subject?: string;
    generated_body?: string;
    email_message_id?: number;
    emails_scheduled_uuid: string;
    email_scheduled_text_body: string;
    email_scheduled_base_body: string;
  }

  type ITaskData = ILinkedinConnectionRequestTaskData | IGenericTaskData | ISendEmailTaskData | ILinkedinSendMessageTaskData | ISendSmsTaskData | ISendWhatsAppMessageTaskData | ILinkedinViewProfile | ILinkedinSendInMail | ICallTaskData | IAutoEmailMagicContentTaskData | IManualEmailMagicContentTaskData;

  interface IAssignee {
    id: number,
    name: string
  }
  interface ITask {
    campaign_id?: number;
    task_id: string;
    step_id?: number;
    task_type: ITaskType,
    task_data: ITaskData,
    status: ITaskStatus,
    assignee: IAssignee,
    team_id: number,
    created_at: number,
    updated_at: number,
    prospect: IProspect,
    priority: ITaskPriority,
    note?: string,
    due_at?: Date,
    campaign_name?: string,
    reply_sentiment_uuid?: string
  }

  interface INotes {
    note?: string
  }

  interface INewTask {
    task_type: ITaskType,
    task_data: ITaskData,
    status: ITaskStatus,
    created_via: ITaskCreatedVia,
    assignee_id: number,
    prospect_id?: number,
    priority: ITaskPriority,
    note?: string,
    is_auto_task: boolean
  }
  interface IUpdateTask {
    task_data: ITaskData,
    status: ITaskStatus,
    assignee_id: number,
    prospect_id?: number,
    priority: ITaskPriority,
    note?: string,
  }

  interface ITaskFilter {
    assignee_id: number,
    sub_task_type?: ISubTaskType,
    task_priority?: ITaskPriority[],
    // task_status?: ITaskStatusType[],
    // task_ids?: string[],
    time_based_task_type: ITimeRange
  }

  interface ITaskByTaskId {
    task: ITask;
  }

  interface INavLinks {
    prev?: string,
    next?: string,
  }

  interface ITaskResponse {
    tasks: ITask[],
    links: INavLinks
  }

  type ITaskMenuBarType = "today_and_due" | "today" | "upcoming" | "due" | "completed" | "skipped" | "snoozed" | "archived" | "failed" | "all";
  type ISubTaskType = "all" | "email" | "linkedin" | "sms" | "whatsapp" | "general" | "call";
  type ITimeBasedTasktype = "today" | "upcoming" | "due" | "completed" | "skipped";

  interface ITaskQuery {
    time_based_task_type: Task.ITaskMenuBarType,
    task_priority?: Task.ITaskPriority,
    assignee_id?: number,
    campaign_id?: number,
    sub_task_type?: Task.ISubTaskType,
    reply_sentiment_uuid?: string,
    duration_from?: number;
    duration_to?: number;
    task_status?: string;
  }

  interface ITaskCount {
    task_count: ITaskNavBarCount
  }

  interface ITaskNavBarCount {
    todayAndDue: ISubTaskCount,
    today: ISubTaskCount,
    upcoming: ISubTaskCount,
    due: ISubTaskCount,
    completed: ISubTaskCount,
    skipped: ISubTaskCount,
    snoozed: ISubTaskCount,
    archived?: ISubTaskCount,
    failed: ISubTaskCount
  }

  interface ISubTaskCount {
    all: number,
    email: number,
    linkedin: number,
    sms: number,
    whatsapp: number,
    generic: number,
    call: number,
    approval_email?: number,
    approval_linkedin?: number,
    approval_sms?: number,
    approval_whatsapp?: number,
    approval_generic?: number,
    approval_call?: number
  }

  type ITaskStatusChange = 'done_and_proceed' | 'done_and_complete' | 'skip'

}