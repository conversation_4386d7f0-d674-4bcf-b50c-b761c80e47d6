export declare namespace Form {

  interface IFieldProps {
    name: string;
    placeholder?: string;
    label?: string | JSX.Element;
    disabled?: boolean;
    initialValue?: any;
    className?: string;
    style?: any;
    icon?: String;
    popuptext?: any;//accept html as well
    // input field
    type?: 'text' | 'email' | 'password' | 'number' | 'checkbox';

    // dropdown field
    options?: { id: string | number, name: string }[];
    onChange?: (fieldName: string, newValue: any, oldValue: any) => any;

    //dropdown which allows additions
    additionLabel?: string;
    onAddItem?: any;

    //to add a none option
    addNoneOption?: boolean;

    loading?: boolean;//for semanticdropdown

    autoFocus?: boolean;
  }

  interface IFieldState {
    value: string | boolean | number;
    error: string;
    touched: boolean;
  }

  interface IFormFieldState extends IFieldState {
    state_type: 'field';
  }

  interface IFormUpdateFieldState {
    value?: string;
    error?: string;
    touched?: boolean;
  }

  interface IFormProps {
    id?: string;
    validateDefs: (values: IFormValues) => IFormErrors;
    onSubmit: (values: IFormValues, resetFormFn: () => any) => void;
    initialValues?: {
      [fieldName: string]: any
    };
    onFormChange?: (values: IFormValues) => any;
  }

  // interface IFormState {
  //   [fieldName: string]: IFormFieldState | boolean;
  //   formInitialized: boolean;

  // }

  interface IFormValues {
    [fieldName: string]: any;
  }


  interface IFormErrors {
    [fieldName: string]: string;
  }


  interface IFormFieldContextTypes {
    onFocus: Function;
    onChange: Function;
    onBlur: Function;
    fields: any;
    initializeFieldState: Function;
  }
}