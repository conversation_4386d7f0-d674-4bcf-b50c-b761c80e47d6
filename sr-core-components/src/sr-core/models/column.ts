import { HLDataGrid } from "./data-grid";
import { SRSearch } from "./search";

export interface ITemplateTags {
  template_tags: string[];
}

export declare namespace ColumnDef {


  type IColumnType = 'number' | 'text' | 'date' | 'boolean' | 'list' | 'magic';

  type IColumnDropdownType = 'campaign' |
    'prospect_list' |
    'prospect_category' |
    'prospect_timezone' |
    'owner' |
    'campaign_step' |
    'current_sender_email'|
    'latest_reply_sentiment'|

    // all campaigns page
    'campaign_status' |
    'prospect_source' |
    'inbox_tab' |
    'prospect_account_id' |
    'tag_list' |

    // inbox page
    'email_inbox'|
    'current_step_type'
    ;


  type ICustomColumnType = 'prospect' | 'account';


  type DropdownOption = { key: string; value: string; text: string | JSX.Element; };
  type DropdownOptions = DropdownOption[];

  interface IColumnDefServer {
    id?: number;
    name: string;
    display_name: string;
    field_type: IColumnType; // should be IColumnType, was causing problem with 'date' type in UpdateProspectModal component;
    is_custom: boolean;
    allowed_filter_operators: SRSearch.IProspectColumnOperator[];
    // show_only_in_all_view: boolean;
    show_in_datagrid: boolean;
    show_only_in_campaign_tab: boolean;
    show_only_if_multichannel_enabled:boolean;
    filterable: boolean;
    sortable: boolean;
    dropdown_type?: IColumnDropdownType;
    search_type?: IColumnDropdownType;
  }

  interface IColumnDefDataGrid extends HLDataGrid.IColumn {
    display_name: string;
    field_type: any;
    is_custom: boolean;
    hide?: boolean;
    width?: number;

    updatedAt?: number // NOTE: do not remove this.

    show_in_datagrid: boolean;
    show_only_in_campaign_tab: boolean;
    show_only_if_multichannel_enabled:boolean;


  }

  interface IAddColumnResponse {
    column: IColumnDefServer;
  }
  interface IOptions {
    key: string,
    value: string,
    name?: string,
    text: JSX.Element | string
  }
}
