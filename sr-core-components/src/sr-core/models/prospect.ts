import { Alerts } from "./alerts";
import { Campaigns } from "./campaigns";
import { ColumnDef } from "./column";
import { Form } from "./form";
import { LogIn } from "./login";
import { ReplySentiments } from "./reply-sentiments";
import { SRSearch } from "./search";
import { Tags } from "./tags";
import { Teams } from "./teams";

export declare namespace Prospects {

  type IProspectDeduplicationColumns = 'email' | 'phone' | 'linkedin_url' | 'company_firstname_lastname';

  type IProspectStatus = 'unassigned' | 'assigned';

  type IProspectView = 'global_prospects_page' |
    'campaign_prospect_page' |
    'campaign_failed_prospects_tab' |
    'assign_more_modal' |
    'prospect_account_prospects_page' |
    'email_finder'
    ;

  type ICPErrorTabs = 'to_check' | 'to_check_fields' | 'failed_validation';
  type ICustomDGFilterClause = 'AND' | 'OR';
  type IColumnType = 'number' | 'text' | 'date' | 'boolean' | 'list' | 'magic';
  type ISortDirection = 'NONE' | 'ASC' | 'DESC';

  interface ICreateProspect {
    first_name: string;
    last_name: string;
    email: string;
    custom_fields: { [key: string]: string | number | null };
    [fieldName: string]: string | number | { [key: string]: string | number | null } | null;
  }

  interface ICreateProspectManually {
    prospect: ICreateProspect,
    ignoreProspectsInOtherCampaigns?: IIgnoreProspectsType
  }

  interface IUpdateProspect {

    email: string;
    first_name?: string;
    last_name?: string;
    custom_fields: { [key: string]: string | number | null };

    prospect_category_id_custom?: number;

    list?: string;
    company?: string;
    city?: string;
    country?: string;
    timezone?: string;

  }

  interface IProspectActiveCampaign {
    auto_reply: boolean;
    bounced: boolean;
    campaign_name: string;
    campaign_id: number;
    clicked: boolean;
    completed: boolean;
    invalid_email: boolean;
    opened: boolean;
    opted_out: boolean;
    out_of_office: boolean;
    replied: boolean;
    replied_marked_by_adminid?: number;
    sent: boolean;
    to_check: boolean;
    to_check_fields: any;//check
    will_resume_at: number;
    will_resume_at_tz: string;
    total_opens_in_campaign: number;
    total_clicks_in_campaign: number;


    // these keys wont come if we dont pass custom_flags.active_campaigns_detailed as true in the search api
    step?: string;
    current_holiday_name?: string;
    sending_holiday_calendar_name?: string;
    sender_email?: string
    current_step_type?: Campaigns.ICampaignStepType;
    scheduler_status?: Campaigns.ISchedulerStatus;
  }
  interface IProspectTitleData {
    first_name?: string;
    last_name?: string;
    email: string;
    phone?: string;
    linkedin_url?: string;
    company?: string;
  }
  interface IProspectObject extends IProspectTitleData {
    id: number;
    owner_id?: number;

    first_name?: string;
    last_name?: string;
    email: string;

    custom_fields: { [key: string]: string | number | null };

    list?: string;

    company?: string;
    job_title?: string;
    phone?: string;
    phone_2?: string;
    phone_3?: string;
    last_contacted_at_phone?: string;
    linkedin_url?: string;

    city?: string;
    state?: string;
    country?: string;
    timezone?: string;

    created_at: number;

    prospect_category: string;

    latest_reply_sentiment_uuid?: string;

    prospect_uuid: string

  }

  type IMagicColStatus = 'pending' | 'queued' | 'completed' | 'failed'

  interface IMagicColumn {
    failed_message?: string,
    column_output?: string,
    status: IMagicColStatus,
  }


  interface IProspectObjectInternal {

    // ta_id: Long,

    owner_name: string,
    owner_email: string,

    email_domain: string,
    invalid_email?: boolean,

    last_contacted_at?: number,
    last_replied_at?: number,
    last_opened_at?: number,
    last_call_made_at?: number,

    list_id?: number,

    prospect_category_id_custom: number,
    prospect_category_label_color: string,

    prospect_source?: string,


    prospect_account_id?: number,
    prospect_account_uuid?: string,
    prospect_account?: string,

    latest_reply_sentiment?: ReplySentiments.IReplySentimentTypeForTeam;
    magic_columns: { [magic_column: string]: IMagicColumn },

    total_opens: number,
    total_clicks: number,

    /* only in case of specific campaign prospect table in frontend */
    current_campaign_id?: number,

    tags?: Tags.IProspectTag[],
    flags: {
      will_delete?: boolean;
      email_bounced?: boolean;
      force_send_invalid_email?: boolean
    }
  }

  interface IProspect extends IProspectObject {
    internal: IProspectObjectInternal
  }


  interface IProspectObjectInternalWithActiveCampaigns extends IProspectObjectInternal {

    active_campaigns: IProspectActiveCampaign[],

  }

  interface IProspectSearchResult extends IProspectObject {
    internal: IProspectObjectInternalWithActiveCampaigns;
  }

  interface ISecondaryProspectEmail {
    prospect_email_id: number,
    email: string
  }


  interface IFailedProspects {
    id: number;
    email: string;
    error: string;
  }


  interface IModalProps {
    modalLoading: boolean;
  }

  // ADD FILTER PROSPECT MODAL

  interface IAddFilterProspectProps extends IModalProps {
    filterObj: SRSearch.ICustomDGFilter;
    selectedSavedFilters: string;
    selectedSavedFiltersIsSharedWithTeam: boolean;
    columns: ColumnDef.IColumnDefServer[];
    // behavioralColumns: IColumnDefServer[];
    basicCampaignList: { id: number, name: string }[];
    applyFilterLoading: boolean;
    timezoneList: { text: string, value: string }[];
    countries: string[];
    prospectLists: { id: string, name: string }[];
    campaignSteps: Campaigns.ICampaignStep[];
    prospectCategoryList: { id: number, name: string, labelColor: string }[];
    teamMemberList: LogIn.IMember[];
    isAssignMoreModal: boolean;
    isFailedProspects: boolean;
    // ignoreFieldsOnAddProspect: string[];
    isCampaign: boolean;
    campaignId: string | number;
    onSubmit: (isSaveAndApllyFilter: boolean, isSharedWithTeam: boolean, label?: string) => void;
    onClose: () => void;
    onClearFilter: () => void;
    updateFilterObj: (newFilterObj: SRSearch.ICustomDGFilter) => void;
  }
  // ADD PROSPECTS MODAL

  interface IAddProspectFormValues extends Form.IFormValues {
    // email: string;
    // first_name: string;
    // last_name: string;
    // source: string;
  }

  interface IAddProspectFormErrors extends Form.IFormErrors {
    // email: string;
    // first_name: string;
    // last_name: string;
    // source: string;
  }

  interface IAddProspectProps extends IModalProps {
    columns: ColumnDef.IColumnDefServer[];
    timezoneList: { text: string, value: string }[];
    countries: string[];
    prospectLists: { id: string, name: string }[];
    // ignoreFieldsOnAddProspect: string[];
    onSubmit: (values: any) => void;
    onClose: () => void;
    onAddListItem?: (value: string) => void;

    initial_owner_account_id?: number;
    all_members?: LogIn.ITeamMember[];
    // canEditProspectOwner?: boolean;

    ignore_prospects_in_other_campaigns_for_manual?: IIgnoreProspectsType;
    handleChangeIgnoreProspectsRadio: (value: Prospects.IIgnoreProspectsType) => void;
    force_update_duplicate_prospects?: boolean;
    changeUpdateDuplicates: (e: any, data: any) => void
    dataGridName: IProspectView;
  }

  // UPLOAD PROSPECT MODAL

  interface IUploadProspectsProps {
    columns: ColumnDef.IColumnDefServer[];
    isCampaign: boolean;
    campaignId: number;
    ignoreFields: string[];
    /*
    onSubmit: (data: {
      file: File,
      csvColMappingArray: any,
      listName: string,
      hasMoreRows: boolean,
      // encoding: string,
      force_update_prospects?: boolean,
      force_change_ownership?: boolean,
      ignore_prospects_active_in_other_campaigns?: boolean,
      owner_id?: number,
      campaign_id?: number,
      tags?: string[]
    }) => Promise<any>;
    */
    onSubmitV2: () => void;
    onClose: () => void;
    onFinish: (val: boolean) => any;
    alertStore?: Alerts.IAlertStore;
    logInStore?: LogIn.ILogInStore;
    teamStore?: Teams.ITeamStore;
    prospectLists: { id: string, name: string }[];

    initial_owner_account_id?: number;
    all_members?: LogIn.ITeamMember[];
  }


  interface IUploadFormValues extends Form.IFormValues {

  }
  interface IUploadFormErrors extends Form.IFormErrors {

  }


  type ICustomFieldType = 'number' | 'text';

  interface INewCustomField {
    type: ICustomFieldType;
    name: string;
    key: string;
  }


  interface ILastColumnMapResponse {
    last_column_map: { [key: string]: string };
  }

  interface IUploadProspectsState {
    name?: string;
    size?: number;
    listName?: string;
    force_update_duplicate_prospects?: boolean;
    basicCampaignList: { id: number, name: string }[];
    isLoadingBasicCampaignList: boolean;
    force_change_ownership?: boolean,
    // force_assign_to_campaign?: boolean,
    ignore_prospects_active_in_other_campaigns?: boolean;
    ignore_email_empty_rows?: boolean;
    srColumns?: { name: string, key: string, field_type: string, is_new: boolean }[];
    uploadStep?: number;
    CSVColumns?: { id: string, name: string, fieldName: string }[];
    lastCsvColumnMap?: ILastColumnMapResponse;
    newFields?: INewCustomField[];
    file?: File;
    values?: { optionid: string, fieldname: string }[];
    newfieldId?: number[];
    isOpen?: boolean;
    modalSize?: string;
    submitData?: any[];
    firstRow?: any;
    newColumn?: { name: string, field_type: string };
    onSaveClick?: boolean;
    modalLoading?: boolean;
    uploadStats?: IUploadStats;
    filteredColumns?: { name: string, key: string, field_type: string, is_new: boolean }[];
    uploadedToAws?: boolean;
    hasMoreRows?: boolean;
    // encoding?: string;
    prospectLists: { id: string, name: string }[];
    prospectTags: Tags.IProspectTag[];
    ownerId?: number;
    campaignId?: number;
    selectedTags: { displayText: string, value: string }[];
    listSearchTerm: '';
    isValidTag?: boolean;
    showCustomColumnModal: boolean;
    customModalIndex: number;
    additionalTagText: string;

    ignore_prospects_in_other_campaigns?: IIgnoreProspectsType;
    encDeduplicationColumnsMapError: boolean;
    selectedUniqueColumns: ISrProspectColumns[];
  }

  interface IUploadStats {
    // total_rows: number;
    // total_errors: number;
    // total_duplicates: number;
    // total_saved: number;
    total_rows: number,
    total_empty_or_invalid_rows: number,
    total_duplicates_found: number,
    totaL_duplicates_ignored_for_no_edit_permission: number,
    total_duplicates_updated: number,
    total_created: number,
  }
  // ASSIGN PROSPECTS MODAL
  type IAssignProspectsActionType = 'assignToCampaign' | 'assignToTeamMember' | 'unassignFromCamapign';
  interface IAssignProspectsAPIData {
    campaign?: string;
    member?: number | string;
    actionType: IAssignProspectsActionType;
    ignore_prospects_active_in_other_campaigns: boolean;
    number_of_prospects: number | null;
  }

  interface IAssignProspectsValues extends Form.IFormValues {
    campaign?: string;
    member?: number | string;
    actionType: IAssignProspectsActionType;
    // assign_to: string;
    // force_assign: boolean;
    ignore_prospects_active_in_other_campaigns: boolean;
    number_of_prospects: number | null;
  }


  interface IAssignProspectsErrors extends Form.IFormErrors {
    // id: string;
    campaign: string;
    member: string;
    number_of_prospects: string;
  }


  interface IAssignProspectsProps extends IModalProps {
    team: { id: number | string, name: string }[];
    // isTeamAdmin: boolean;
    onSubmit: (values: IAssignProspectsValues) => void;
    onClose: () => void;
    rowSelectCount: number;
    // membersCanAssign: boolean;
    prospectsAssigned: boolean;
    // currentAccountId: number | string;
    // allow_assigning_prospects_to_multiple_campaigns: boolean;
    isChangeOwnerModal: boolean;
    isUnassignCampaignModal: boolean;
    currentCampaignId?: number | undefined;
  }


  // DELETE PROSPECTS MODAL

  interface IDeleteProspectsProps extends IModalProps {
    onSubmit: (e: any) => void;
    onClose: () => void;
    rowSelectCount: number;
    type: string;
  }
  interface IBlackListProspectsProps extends IModalProps {
    onSubmit: (e: any) => void;
    onClose: () => void;
    rowSelectCount: number;
  }



  // UPDATE PROSPECT MODAL

  interface IUpdateProspectProps extends IModalProps {
    onSubmit: any;
    timezoneList: { value: string, text: string }[];
    onClose: () => void;
    columns: ColumnDef.IColumnDefServer[];
    ignoreFieldsOnAddProspect: string[];
    prospectData: any;
    canEdit: boolean;
    fromInbox?: boolean;
    prospectInfoLoading?: boolean;
    prospectLists: { id: string, name: string }[];
    onAddListItem?: (value: string) => void;
    // isCampaign?: boolean;
    campaignId?: number;
    prospectCategores?: LogIn.IProspectCategory[];
  }

  interface IUpdateProspectPropsV2 extends IModalProps {
    onSubmit: any;
    timezoneList: { value: string, text: string }[];
    onClose: () => void;
    columns: ColumnDef.IColumnDefServer[];
    ignoreFieldsOnAddProspect: string[];
    prospectId: string;
    canEdit: boolean;
    fromInbox?: boolean;
    prospectLists: { id: string, name: string }[];
    onAddListItem?: (value: string) => void;
  }


  interface IUpdateProspectFormValues extends Form.IFormValues {
    email: string;
  }

  interface IUpdateProspectFormErrors extends Form.IFormErrors {
    email: string;
  }

  // ADD COLUMN PROSPECT MODAL
  interface IAddColumnValues extends Form.IFormValues {
    name: string;
    field_type: string;
  }

  interface IAddColumnErrors extends Form.IFormErrors {
    name: string;
    field_type: string;
  }

  // todo nxd - last step - remove this out.
  interface IAddColumnProspectProps extends IModalProps {
    onSubmit: (e: any) => void;
    onClose: () => void;
    existingColumns: string[];
    header: string;
    buttonText: string;
    className?: string;
    isAccountsRelated?: boolean;
  }

  interface ITimezone {
    text: string;
    value: string;
  }

  interface IColumnReactTable {
    Header: string;
    accessor: string;
    minWidth: number;
    maxWidth: number;
  }

  interface IProspectSavedFiltersLists {
    id: string,
    name: string,
    filters: SRSearch.ICustomDGFilter,
    shared_with_team: boolean
  }


  type INewProspectStatus = 'replied' | 'pause' | 'unpause';

  type IIgnoreProspectsType = 'ignore_prospects_active_in_other_campaigns' | 'ignore_prospects_added_in_other_campaigns' | 'do_not_ignore';

  type csvUploadType = 'prospect' | 'dnc' | 'dnc_agency' | 'bulk_email';

  type ISrProspectColumns = 'email' | 'phone' | 'linkedin_url' | 'company_firstname_lastname'
  interface IUploadCsvRequestData {
    list_name?: string;
    campaign_id?: number;
    column_map: Object;
    owner_id?: number; // so that old ui doesnt break
    tags?: string; // todo: this needs to go as an array ideally
    force_change_ownership?: boolean;
    ignore_email_empty_rows?: boolean;
    force_update_prospects?: boolean;
    ignore_prospects_active_in_other_campaigns?: boolean;
    ignore_prospects_in_other_campaigns?: IIgnoreProspectsType;
    deduplication_columns?: ISrProspectColumns[]
  }

  interface IUploadCsvRequestBody extends IUploadCsvRequestData {
    file_url: string;
    csv_upload_type?: csvUploadType;
  }

  interface IExistingProspect {
    prospect_id: number,
    email: string,
    name?: string
  }

  interface IExistingProspectList {
    prospects: IExistingProspect[]
  }

  interface IProspectActionModalProps {
    selectedProspectIds: number[];
    selectedProspectUuids: string[];
    isSelectAllProspectsInAppliedFilters: boolean;
    filterObj: SRSearch.ICustomDGFilter;
    isCampaign: () => boolean;
    campaignId?: number;
  }

  interface IProspectColumnOperator {
    display_name: string;
    key: string;
  }

  interface ICustomDGSubFilterItem {
    field_display_name: string;
    field: string;
    allowedFilterOperators: IProspectColumnOperator[];
    operator: string;//TODO
    value_display_name: string;
    value: string;
    field_type: IColumnType;
    is_custom: boolean;
  }

  interface ICustomDGSubFilter {
    clause: ICustomDGFilterClause;
    filters: ICustomDGSubFilterItem[];
  }

  interface ICustomDGFilter {
    search?: string;
    clause: ICustomDGFilterClause;
    filters: ICustomDGSubFilter[];
  }

  interface IProspectSearchSort {
    field: string;
    is_custom: boolean;
    is_numeric: boolean;
    direction: ISortDirection;
  }

  interface ISearchAPIRequest {
    page: number;
    sort?: IProspectSearchSort;
    is_campaign?: number | undefined;
    query: ICustomDGFilter | undefined;
  }

  interface IProspectEmailDetails {
    email: string
    is_valid: boolean
    is_primary: boolean
  }

  interface IProspectBasicInfo {
    id: number;
    owner_name: string;
    first_name?: string;
    last_name?: string;
    email: IProspectEmailDetails[];
    company?: string;
    job_title?: string;
    phone?: string;
    linkedin_url?: string;
    city?: string;
    state?: string;
    country?: string;
  }
}
