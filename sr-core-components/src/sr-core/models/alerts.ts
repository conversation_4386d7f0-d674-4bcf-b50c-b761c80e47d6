export declare namespace Alerts {
  type IAlertType = 'success' | 'error' | 'warning' | 'info';
  type IWarningCode = 'prospect_limit_80_percent' | 'prospect_limit_exceeded' | 'low_calling_credit' | 'calling_feature_suspended';
  type ToastPosition = 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right';
  
  interface IAlert {
    status: IAlertType;
    message: string | number | JSX.Element; //FIXME: Sepearte the jsx.element
    position?: ToastPosition;
  }

  interface IBannerAlert extends IAlert {
    id: number | string;
    canClose: boolean;
  }

  interface IWarningBanner {
    warning_at: number;
    warning_code: IWarningCode;
    warning_msg: string;
    upgrade_now_prompt: boolean;
    add_call_credit_button: boolean;
    new_prospects_paused_till?: number;
  }

  interface IErrorBanner {
    error_at?: number;
    error_code: string;
    error_msg: string;
    upgrade_now_prompt: boolean;
  }
  
  interface IAlertStore {
    alert: IAlert;
    pushAlert: (newAlert: Alerts.IAlert) => void;
    getAlerts: IAlert;
    resetAlerts: any;

    bannerAlerts: IBannerAlert[];
    updateBannerAlerts: any;
    removeBannerAlert: any;
    resetBannerAlerts: any;
    getBannerAlerts: IBannerAlert[];

    accountErrorAlerts: IBannerAlert[];
    updateAccountErrorAlerts: any;
    removeAccountErrorAlert: any;
    resetAccountErrorAlerts: any;
    getAccountErrorAlerts: IBannerAlert[];

    warningBannerAlerts: IWarningBanner[];
    updateWarningBannerAlerts: any;
    removeWarningBannerAlert: any;
    resetWarningBannerAlerts: any;
    getWarningBannerAlerts: IWarningBanner[];

  }
}