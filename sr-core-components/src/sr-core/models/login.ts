import { Alerts } from "./alerts";
import { Campaigns } from "./campaigns";
import { Settings } from "./settings";
import { Teams } from "./teams";

export declare namespace LogIn {
  interface ILogInStore {
    isLoggedIn: boolean;
    isSupportAccount: boolean;
    accountInfo: IAccount;
    gotoHomePageSection: string;
    toRegisterEmail: string;
    currentTeamId: number;
    redirectToLoginPage: boolean;
    isTeamAdmin: boolean;
    disableAnalytics: boolean;
    planType: IPlanType;
    showPricingModal: boolean;
    checkForUpgradePrompt: boolean;
    isLoggingOut: boolean;
    // urlToRetain: string;
    showFeed: boolean;
    showFeedBubble: boolean;
    isUpdateProspectModalOpen?: boolean;//to prevent closing of feed on action in update prospect modal

    getLogInStatus: boolean;
    getAccountInfo: IAccount;
    getCurrentTeamId: number;
    getRedirectToLoginPage: boolean;
    getCurrentTeamObj: LogIn.ITeam;
    getCurrentTeamMemberObj: LogIn.IMember;
    getIsTeamAdmin: boolean;
    isOrgOwner: boolean;
    roleIsOrgOwnerOrAgencyAdminForAgency: boolean;
    getPlanType: IPlanType;
    getShowPricingModal: boolean;
    getCheckForUpgradePrompt: boolean;
    getIsLoggingOut: boolean;
    // getUrlToRetain: string;
    getShowFeedStatus: boolean;
    getShowFeedBubbleStatus: boolean;
    getisUpdateProspectModalOpen: boolean;
    getTeamRolePermissions: Settings.IRolePermissions;

    logIn: (props: { accountInfo: IAccount, via_csd?: boolean, disableAnalytics?: boolean, tid?: number }) => void;
    logOut: () => void;
    notAuthenticated: (url?: string) => void;
    changeRedirectToLoginPage: (flag: boolean) => void;
    updateAccountInfo: (accountInfo: IAccount) => void;
    updateOrg: (org: IOrg) => void;
    updateIsSupportAccount: (via_csd: boolean) => void;
    updateGotoHomePageSection: (gotoHomePageSection: string) => void;
    updateToRegisterEmail: (toRegisterEmail: string) => void;
    updateCurrentTeamId: (newTeamId: number) => void;
    updateIsTeamAdmin: (flag: boolean) => void;
    updatePlanType: (planType: IPlanType) => void;
    updateShowPricingModal: (flag: boolean) => void;
    updateCheckForUpgradePrompt: (flag: boolean) => void;
    updateIsLoggingOut: (flag: boolean) => void;
    // updateUrlToRetain: (url: string) => void;
    updateShowFeedStatus: (flag: boolean) => void;
    updateShowFeedBubbleStatus: (flag: boolean) => void;
    updateIsUpdateProspectModalOpen: (flag: boolean) => void;

    featureFlagsObj: IFeatureFlags;
    updateFeatureFlagsObj: (flags: IFeatureFlags) => void;
    getFeatureFlagsObj: IFeatureFlags;
    getLowerLimitForEmailDelay: () => number;
    getDefaultLowerLimitForEmailDelay: (service_provider?: Settings.IEmailProviderKey) => number;
    getDefaultUpperLimitForEmailDelay: (service_provider?: Settings.IEmailProviderKey) => number;
  }

  interface IFeatureFlags {
    ff_sending_holiday_calendar: boolean;
    ff_ab_testing: boolean;
    ff_reports_hot_prospect: boolean;
    ff_campaign_send_start_report: boolean
  }

  interface SRFeatureFlags {
    feature_flags: IFeatureFlags;
    org_metadata: IOrgMetaData;
    team_metadata?: Teams.ITeamMetaData;
  }

  type IAccountType = 'individual' | 'team' | 'agency';
  type Notification = 'weekly' | 'never';

  interface IAccountProfile {
    first_name: string;
    last_name: string;
    company?: string;
    timezone: string;
    country_code?: string;
    mobile_country_code?: string;
    mobile_number?: number;
    twofa_enabled: boolean;
    weekly_report_emails?: string;
    onboarding_phone_number?: string;
  }
  interface AccountAccess {
    inbox_access: boolean
  }

  interface ITeamMetaData {
    inbox_v3_enabled: boolean
  }

  interface ICalendarAccountData {
    calendar_user_id: number;
    calendar_username_slug: string;

  }

  interface IAccount {
    // plan?: string;
    // trial_ends_in_days?: number;
    user_id: string;
    internal_id: number;
    account_type: IAccountType;
    org_role?: 'owner' | 'agency_admin';
    profile: IAccountProfile;
    company: string;
    timezone: string;
    created_at: string;
    email: string;
    first_name?: string;
    last_name?: string;
    teams: ITeam[];
    org: IOrg;
    email_notification_summary: Notification;
    intercom_hash: String;
    email_verified: boolean;
    account_metadata: IAccountMetaData;
    signupType?: 'google' | 'microsoft' | 'password';
    account_access: AccountAccess;
    calendar_account_data: ICalendarAccountData;
    sr_api_key?: string;
    sr_team_id?: number;
  }

  interface IAccountMetaData {
    is_profile_onboarding_done?: boolean;
  }

  type IPlanType = 'trial' | 'free' | 'paid' | 'inactive';
  type IOnboardingStep = "about_company" | "about_me" | "demo_schedule" | "done";

  interface IOneTimePlanPurchaseOption{
    value: number,
    display_text: string,
    currency: "inr" | "usd",
    one_time_plan_type: "sr_native_calling" | "sr_lead_finder",
  }

  interface IOrgMetaData {
    show_promo_option?: boolean;
    ff_multichannel?: boolean;
    allow_user_level_api_key?: boolean;
    is_onboarding_done?: boolean;
    show_agency_pricing?: boolean;
    show_business_plans?: boolean;
    show_business_standard_plan?: boolean;
    show_business_pro_plan?: boolean;
    enable_native_calling?: boolean;
    enable_magic_column?: boolean;

    enable_lead_finder?: boolean;
    show_agency_admin_role?: boolean;
    enable_calendar?: boolean;

    show_individual_plans?: boolean;

    enable_linkedin_automation?: boolean;
    show_linkedin_inbox?: boolean;

    show_campaign_tags?: boolean;
    limit_on_prospect_accounts?: boolean;
    // ff_holiday_calendar?: boolean;
    ff_emails_sent_report?: boolean;
    show_campaign_send_start_report?: boolean;
    allowed_for_new_google_api_key?: boolean;
    show_referral_program?: boolean;
    salesforce_sandbox_enabled?: boolean;
    enable_opportunities_pipeline?: boolean;
    show_rms_ip_in_frontend?: boolean;
    show_campaign_inbox?: boolean;
    hide_google_marketplace_integration?: boolean;
    june_2024_simpler_roles_perms?: boolean;
    enable_domain_health_page?: boolean;
    max_email_sending_quota_per_day?: number;
    increase_email_delay?: boolean;
    show_inbp_logs_report?: boolean;
    show_leads_sent_for_validation_banner?: boolean;
    allow_drip_condition?: boolean;
    hide_conference_call?: boolean;
    enable_wh_auto_login?: boolean;
    show_new_create_campaign_model?: boolean;
    show_send_plain_text_email?: boolean;
    show_magic_content_steps?: boolean;
    enable_google_oauth_for_unauthorised_client_id?: boolean;
    show_purchased_domains_and_emails?: boolean;
    is_purchase_emails_and_domains_supported_by_plan?: boolean;
    is_purchase_emails_and_domains_supported_by_plan_version?: boolean;
    enable_callerid_verification?: boolean;
    is_team_inbox_enabled?: boolean;
    remove_enforced_subscription_for_inactive_org?: boolean;
    enable_wh_auto_connect?: boolean;
    allowed_for_amf?: boolean;
    max_campaign_emails?: number;
    enable_voicedrop?: boolean;
    show_manual_tasks_in_reports?: boolean;
    enable_captain_data_automation?: boolean;

    allow_using_sr_ai_api?: boolean;
    enable_calendly_integration?: boolean;
    enable_new_reply_sentiment?: boolean;
    enable_new_prospect_categories?: boolean;
    show_new_referral_pages?: boolean;
  }

  interface IOrg {
    id: number;
    name: string;

    counts: {
      current_sending_email_accounts: number;
      current_prospect_sent_count_org: number;
      total_sending_email_accounts: number;
      max_prospect_limit_org: number;
      current_li_automation_seats: number;
      max_li_automation_seats: number;
      additional_licence_count: number;
      additional_spam_tests: number;
      base_licence_count: number;
    };

    plan: {
      is_business_plan: boolean;
      plan_id: Settings.IPlanName;
      plan_name: string;
      plan_type: IPlanType;

      one_time_plan_purchase_options: IOneTimePlanPurchaseOption[];

      fs_account_id?: string;
      //fs_lookup_id?: string;
      stripe_customer_id?: boolean;

      payment_gateway?: string;
      payment_due_invoice_link?: string;
      payment_due_campaign_pause_at?: number;

    };


    trial_ends_at?: number;

    error?: string;
    paused_till?: Date;
    error_code?: string;
    is_agency: boolean;
    owner_account_id: number;


    warnings: Alerts.IWarningBanner[];
    errors: Alerts.IErrorBanner[];

    settings: {
      // enable_ab_testing: boolean;
      allow_2fa: boolean;
      show_2fa_setting: boolean;
      enforce_2fa: boolean;
      allow_native_crm_integration: boolean;
      agency_option_show: boolean;
      agency_option_allow_changing: boolean;
    }
    // errors: Alerts.IWarningBanner[]; - not used currently
    org_metadata: IOrgMetaData;
  }

  interface ITeam {
    access_members: IMember[];
    all_members: ITeamMember[];
    team_id: number;
    team_uuid: string;
    team_name: string;
    total_members: number;
    org_id: number | string;
    active: boolean;
    role: Settings.IRolePermissions;

    // team_metadata: ITeamMetaData // 12 Aug 2023: It was not being used, so removed it.

    prospect_categories_custom: IProspectCategory[];
    max_emails_per_prospect_per_day: number;
    max_emails_per_prospect_per_week: number;
    max_emails_per_prospect_account_per_day: number;
    max_emails_per_prospect_account_per_week: number;
    // allow_assigning_prospects_to_multiple_campaigns: boolean;
    reply_handling: 'pause_all_campaigns' | 'pause_specific_campaign';
    selected_calendar_data?: Campaigns.ISelectedCalendarData
    //tp_integrations: ITPIntegrations;
  }

  // interface IRole {
  //   name: 'owner' | 'admin' | 'member';
  //   permissions: IPermissions;
  // }

  type IPermissionOwnerShip = 'owned' | 'all' | 'no_access'

  interface IPermissions {
    just_loggedin: IPermissionV2;
    zapier_access: IPermissionV2;

    manage_billing: IPermissionV2;

    view_user_management: IPermissionV2;//user management & roles
    edit_user_management: IPermissionV2;

    view_prospects: IPermissionV2;
    edit_prospects: IPermissionV2;
    delete_prospects: IPermissionV2;

    view_campaigns: IPermissionV2;
    edit_campaigns: IPermissionV2;
    delete_campaigns: IPermissionV2;
    change_campaign_status: IPermissionV2;

    view_reports: IPermissionV2;
    edit_reports: IPermissionV2;
    download_reports: IPermissionV2;

    // view_inbox: IPermissionV2;
    // edit_inbox: IPermissionV2;
    send_manual_email: IPermissionV2;

    view_templates: IPermissionV2;
    edit_templates: IPermissionV2;
    delete_templates: IPermissionV2;

    view_blacklist: IPermissionV2;
    edit_blacklist: IPermissionV2;

    view_workflows: IPermissionV2;
    edit_workflows: IPermissionV2;

    // view_prospect_accounts: IPermissionV2;
    // edit_prospect_accounts: IPermissionV2;

    // view_email_accounts: IPermissionV2;
    // edit_email_accounts: IPermissionV2;
    // delete_email_accounts: IPermissionV2;

    // view_linkedin_accounts: IPermissionV2;
    // edit_linkedin_accounts: IPermissionV2;
    // delete_linkedin_accounts: IPermissionV2;

    // view_whatsapp_accounts: IPermissionV2;
    // edit_whatsapp_accounts: IPermissionV2;
    // delete_whatsapp_accounts: IPermissionV2;

    // view_sms_accounts: IPermissionV2;
    // edit_sms_accounts: IPermissionV2;
    // delete_sms_accounts: IPermissionV2;

    view_channels: IPermissionV2;
    edit_channels: IPermissionV2;
    delete_channels: IPermissionV2;

    view_team_config: IPermissionV2;//pros cat & sending limit
    edit_team_config: IPermissionV2;

    view_tasks: IPermissionV2;
    edit_tasks: IPermissionV2;
    delete_tasks: IPermissionV2;
  }

  type SrResourceTypes =
    "prospect" |
    "campaign" |
    "inbox" |
    "report" |
    "template" |
    "workflow" |
    "webhook" |
    "prospect_account" |
    "email_account" |
    "blacklist" |
    "user" |
    "team" |
    "task" |
    "other";

  type PermissionLevel =
    "view" |
    "edit" |
    "delete" |
    "other";

  type PermissionType =
    "just_loggedin" |
    "zapier_access" |
    "view_user_management" |
    "edit_user_management" |
    "manage_billing" |
    "view_prospects" |
    "edit_prospects" |
    "delete_prospects" |
    "view_campaigns" |
    "edit_campaigns" |
    "delete_campaigns" |
    "change_campaign_status" |
    "view_reports" |
    "edit_reports" |
    "download_reports" |
    // "view_inbox" |
    // "edit_inbox" |
    "send_manual_email" |
    "view_templates" |
    "edit_templates" |
    "delete_templates" |
    "view_blacklist" |
    "edit_blacklist" |
    "view_workflows" |
    "edit_workflows" |
    // "view_prospect_accounts" |
    // "edit_prospect_accounts" |
    // "view_email_accounts" |
    // "edit_email_accounts" |
    // "delete_email_accounts" |
    "view_channels" |
    "edit_channels" |
    "delete_channels" |
    "view_team_config" |
    "edit_team_config" |
    "view_tasks" |
    "edit_tasks" |
    "delete_tasks"
    ;

  interface IPermissionV2 {
    ownership: IPermissionOwnerShip;
    entity: SrResourceTypes;
    permissionLevel: PermissionLevel;
    permissionType: PermissionType
    version: 'v2'
  }

  interface IMember {
    user_id: number;
    email: string;
    first_name: string;
    last_name: string;
    team_role: 'admin' | 'member' | '' // for invite section;
  }

  interface ITeamMember {
    user_id: number;
    email: string;
    first_name: string;
    last_name: string;
    active: boolean;
  }

  interface IProspectCategory {
    id: number;
    name: string;
    text_id: string;
    label_color: string;
    is_custom: boolean;
    team_id: number;
    rank: number;
  }

  //agency related

  interface IAgencyDashboard {
    teams: IAgencyDashboardteam[];
  }

  interface IAgencyDashboardteam {

    inbox_unread_count: string,
    // rank: 0

    active: boolean;
    team_id: number;
    team: string;
    org_id: number;
    running_campaigns: number;
    total_members: number;

    total_sent: number;
    total_opened: number;
    total_bounced: number;
    total_clicked: number;
    total_opted_out: number;
    total_replied: number;

    percentage_total_opened: number;
    percentage_total_bounced: number;
    percentage_total_clicked: number;
    percentage_total_opted_out: number;
    percentage_total_replied: number;
    reply_sentiment_stats: Campaigns.IReplySentimentStats
    error: string;
  }

  interface ITPIntegrations {
    [hubspot: string]: boolean;
    pipedrive: boolean;
    zoho: boolean;
    zoho_recruit: boolean;
    salesforce: boolean;
    calendly: boolean;
  }
  //
  type IFeatureName = 'Email Automation' | 'Email Deliverability' | 'Inbox 360' | 'Smart Pause' | 'Prospect Management' | 'Team Collaboration' | 'API & Integrations' | 'Detailed Analytics';

  type IAPIKeyType = 'user' | 'team_user' | 'zapier' | 'prospectdaddy' | 'warmupbox';



  // getInviteEmailByInviteCode api response
  interface IInvitedMemberDetail {
    email: string;
    org_name: string;
    team_name: string;
    inviter_name: string;
    first_name: string;
    last_name: string;
  }

  interface IAuthMeResponse {
    account: IAccount;
    disable_analytics: boolean,
    finder_credits: number,
    finder_credits_used: number
  }


}