import { Prospects } from "./prospect";
import { ColumnDef } from "./column";
import { LogIn } from "./login";

export declare namespace SRSearch {

    // Prospect Search API
  
    interface IProspectColumnOperator {
      display_name: string;
      key: string;
    }
  
    // interface IProspectFilterFieldTypeOperators {
    //   field_type: string;
    //   operators: IProspectColumnOperator[];
    // }
  
    // interface IProspectSearchNumericFilter {
    //   filter_type: string;
    //   begin?: number;
    //   end?: number;
    //   value?: number;
    // }
  
    // interface IProspectSearchFilter {
    //   field: string;
    //   is_custom: boolean;
    //   text_filter?: string;
    //   numeric_filter?: IProspectSearchNumericFilter;
    //   array_number_filter?: number[]
    // }
  
    type ISortDirection = 'NONE' | 'ASC' | 'DESC';
  
    interface IProspectSearchSort {
      field: string;
      is_custom: boolean;
      is_numeric: boolean;
      direction: ISortDirection;
    }
  
    // interface IProspectSearchAPIRequest {
    //   page: number;
    //   sort?: IProspectSearchSort;
    //   filters: IProspectSearchFilter[]
    // }
  
  
    type ICustomDGFilterClause = 'AND' | 'OR';
  
    interface ICustomDGSubFilterItem {
      field_display_name: string;
      field: string;
      allowedFilterOperators: SRSearch.IProspectColumnOperator[];
      operator: string;//TODO
      value_display_name: string;
      value: string;
      // field_type: "text" | "email" | "password" | "number" | "checkbox" | undefined;
      field_type: ColumnDef.IColumnType;
      is_custom: boolean;
    }
  
    interface ICustomDGSubFilter {
      clause: ICustomDGFilterClause;
      filters: ICustomDGSubFilterItem[];
    }
  
    interface ICustomDGFilter {
      search?: string;
      owner_ids: number[]; // account_ids of owners
      clause: ICustomDGFilterClause;
      filters: ICustomDGSubFilter[];
    }
  
    interface ISearchAPIRequest {
      page: number;
      sort?: IProspectSearchSort;
      is_campaign?: number | undefined;
      query: ICustomDGFilter | undefined;
    }
    
    interface ISearchAPIRequestForUuid {
      page: number;
      sort?: IProspectSearchSort;
      is_campaign?: number| string | undefined;
      query: ICustomDGFilter | undefined;
    }
  
    interface IProspectSearchAPIRequest extends ISearchAPIRequest {
      custom_flags?: {
        active_campaigns_detailed: boolean
      }
    }
  
  
    /////
  
    interface IFilterFrontendConfig {
      options: ColumnDef.DropdownOptions;
      placeholder: string;
      search: boolean;
      style?: any;
      disabled?: boolean;
    }
  
    interface IColumnDefSearch extends ColumnDef.IColumnDefServer {
  
      frontend_config: IFilterFrontendConfig | null
  
    }
    interface IFilterformValues {
      name: string
      share: string
      setDefault: boolean
    }
  
    interface IFilterSidebarProps {
      key: string,
      logInStore?: LogIn.ILogInStore,
      filterObj: SRSearch.ICustomDGFilter;
      sortObj?: SRSearch.IProspectSearchSort;
      onSortChange?: (sortColumn: string, sortDirection: SRSearch.ISortDirection) => any;
      showFilters: boolean
      toggleFilterFunc:()=> void
      selectedSavedFiltersId?: string;
      selectedSavedFiltersLable?: string;
      selectedSavedFiltersIsSharedWithTeam?: boolean;
      isLoading:boolean
  
      columns: IColumnDefSearch[];
      campaignId?: number;
      campaignName?: string;
      handleSubmitSaveFilter?: (isSaveAndApllyFilter: boolean, isSharedWithTeam: boolean, label?: string) => Promise<void>;
      updateFilterObj: (newFilterObj: SRSearch.ICustomDGFilter) => void;
  
      onSavedFilterChange?: (e: any, data: any) => void;
      savedFilterList?: Prospects.IProspectSavedFiltersLists[]
  
      allowSubFilters: boolean;
      showSortOptions: boolean;
      showSaveFilterOptions: boolean;
      onFilterChangeSubmit?:(values?: IFilterformValues) => void;
  
      onInoxTabChange?: (InboxTabId: number) => void;
      onProspectCategoryChange?: (prospectCategoryIdCustom: number) => void;
    }
  
  }
  