export declare namespace ReplySentiments {
  type ReplySentimentCode = "positive" | "referral" | "objection" | "do_not_contact" | "other" | "negative" | "not_classified" | "none"
  type ReplySentimentType = 'Positive' | "Referral" | "Objection" | "Do Not Contact" | "Negative" | "Not classified" | "Other"
  
    // Added all type so that if someone passes all from getReplySentimentsForTeam() api it will give sentimes from all channels together.
    type ReplySentimentChannelType = "base" | "call_channel" | "all";
  
    interface IReplySentimentType {
      reply_sentiment_type: ReplySentimentType,
      reply_sentiment_name: string,
      reply_sentiment_channel_type: ReplySentimentChannelType
    }
    interface IReplySentimentTypeForReport extends IReplySentimentTypeForTeam {
      count: number
    }
  
  
    interface IReplySentimentTypePositive extends IReplySentimentTypeForReport {
      reply_sentiment: {
        reply_sentiment_type: "Positive",
        reply_sentiment_name: string,
        reply_sentiment_channel_type: ReplySentimentChannelType
  
      }
    }
  
    interface IRSReportPossitive {
      reply_sentiment_type: 'positive',
      reply_sentiment_for_team_report: IReplySentimentTypePositive[],
      count: number
    }
  
  
    interface IReplySentimentTypeReferral extends IReplySentimentTypeForReport {
      reply_sentiment: {
        reply_sentiment_type: "Referral",
        reply_sentiment_name: string,
        reply_sentiment_channel_type: ReplySentimentChannelType
      }
    }
  
    interface IRSReportReferral {
      reply_sentiment_type: 'referral',
      reply_sentiment_for_team_report: IReplySentimentTypeReferral[],
      count: number
    }
  
    interface IReplySentimentTypeObjection extends IReplySentimentTypeForReport {
      reply_sentiment: {
        reply_sentiment_type: "Objection",
        reply_sentiment_name: string,
        reply_sentiment_channel_type: ReplySentimentChannelType
      }
    }
  
    interface IRSReportObjection {
      reply_sentiment_type: 'objection',
      reply_sentiment_for_team_report: IReplySentimentTypeObjection[],
      count: number
    }

  interface IReplySentimentTypeNegative extends IReplySentimentTypeForReport {
    reply_sentiment: {
      reply_sentiment_type: "Negative",
      reply_sentiment_name: string,
      reply_sentiment_channel_type: ReplySentimentChannelType
    }
  }

  interface IRSReportNegative {
    reply_sentiment_type: 'negative',
    reply_sentiment_for_team_report: IReplySentimentTypeNegative[],
    count: number
  }

  interface IReplySentimentTypeNotClassified extends IReplySentimentTypeForReport {
    reply_sentiment: {
      reply_sentiment_type: "Not classified",
      reply_sentiment_name: string,
      reply_sentiment_channel_type: ReplySentimentChannelType
    }
  }

  interface IRSReportNotClassified {
    reply_sentiment_type: 'not_classified',
    reply_sentiment_for_team_report: IReplySentimentTypeNotClassified[],
    count: number
  }

  
    interface IReplySentimentTypeDoNotContact extends IReplySentimentTypeForReport {
      reply_sentiment: {
        reply_sentiment_type: "Do Not Contact",
        reply_sentiment_name: string,
        reply_sentiment_channel_type: ReplySentimentChannelType
      }
    }
  
    interface IRSReportDoNotContact {
      reply_sentiment_type: 'do_not_contact',
      reply_sentiment_for_team_report: IReplySentimentTypeDoNotContact[],
      count: number
    }
  
    interface IReplySentimentTypeOther extends IReplySentimentTypeForReport {
      reply_sentiment: {
        reply_sentiment_type: "Other",
        reply_sentiment_name: string,
        reply_sentiment_channel_type: ReplySentimentChannelType
      }
    }
  
    interface IRSReportOther {
      reply_sentiment_type: 'other',
      reply_sentiment_for_team_report: IReplySentimentTypeOther[],
      count: number
    }
  
    interface IReplySentimentStatsReport {
      positive: IRSReportPossitive,
      referral: IRSReportReferral,
      objection: IRSReportObjection,
      not_classified: IRSReportNotClassified,
      negative: IRSReportNegative,
      do_not_contact: IRSReportDoNotContact,
      other: IRSReportOther
    }
    interface team_inbox_conversations {
      team_inbox_id: number,
      conversation_ids: string[]
    }
    interface IUpdateReplySentimentForm {
      team_inbox_conversations: team_inbox_conversations[];
      reply_sentiment_uuid: string
    }
  
    interface IReplySentimentTypeForTeam {
      uuid: string,
      reply_sentiment: IReplySentimentType
    }
  }