import { Form } from "./form";
import { Tags } from "./tags";
import { Task } from "./task";

export declare namespace Campaigns {

  // copied from semantic type definition

  type SemanticWIDTHSNUMBER = 1 | 1.5 | 2 | 2.5 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 | 16;
  type SemanticWIDTHSSTRING = '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' |
    '9' | '10' | '11' | '12' | '13' | '14' | '15' | '16' |
    'one' | 'two' | 'three' | 'four' | 'five' | 'six' | 'seven' | 'eight' |
    'nine' | 'ten' | 'eleven' | 'twelve' | 'thirteen' | 'fourteen' | 'fifteen' | 'sixteen';

  type SemanticWIDTHS = SemanticWIDTHSNUMBER | SemanticWIDTHSSTRING;

  ////////////////////////////////////////

  type ITab = 'content' | 'prospects' | 'settings' | 'scheduleSettings' | 'stats' | 'emailAccountSettings' | 'preview' | 'to_check' | 'spam_test';

  interface ICampaignStore {
    currentCampaign: ICurrentCampaign;
    setAsNewCampaign: (val: boolean) => void;
    resetState: () => void;
    updateBasicInfo: (info: ICampaign) => void;
    updateStepVariants: (stepVariants: ICampaignStep[]) => void;
    // updateTab: (tab: ITab) => void;
    updateAvailableTags: (tags: string[]) => void;
    updateSendEmailDropdownError: (val: boolean) => void;
    updateReceiveEmailDropdownError: (val: boolean) => void;
    updateWhatsappSetting: (val: string) => void;
    updateCallSetting: (val: string) => void;
    updateLinkedinSetting: (val: string) => void;
    updateSmsSetting: (val: string) => void;
    updateCampaignEmailSettingIds: (val: ICampaignEmailSettings[]) => void;
    updateCampaignOwnerId: (val: number) => void
    updateMaxEmailPerDay: (val: number) => void;
    updateEmailBodyVersion: (version: number, emailBody: string) => void;
    updateUserEmailBodyDraft: (body: string) => void;
    updateTotalStepsInStats: (total_steps: number) => void;
    updateShowSoftStartSetting: (val: boolean) => void;
    addToUndoStack: (version: number, emailBody: string) => void;
    addToRedoStack: (version: number, emailBody: string) => void;
    popFromUndoStack: (version: number, currentEmailBody: string) => string;
    popFromRedoStack: (version: number, currentEmailBody: string) => string;

    setNewVersionOfEmailBody: (email_body: string) => void;
    setNewVersionOfSubject: (subject: string) => void;
    setEmailBodyPrompt: (prompt: IEmailBodyPrompt) => void;
    deleteEmailBodyVersion: (version: number) => void;
    setShowBanner: (val: boolean) => void;

    getEmailBodyPrompt: IEmailBodyPrompt;
    getSubjectVersions: string[];
    getUserEmailBodyDraft: string;
    getEmailBodyVersions: string[];
    getBasicInfo: ICampaign;
    getStepVariants: ICampaignStep[];
    getAvailableTags: string[];
    getIsNewCampaign: boolean;
    getContentTabInfo: IContentTabInfo;
    // getCurrentTab: ITab;
    getSendEmailDropdownError: boolean;
    getReceiveEmailDropdownError: boolean;
    getShowBanner: boolean;
    getShowSoftStartSetting: boolean;
  }

  interface ICurrentCampaign {
    basicInfo: Campaigns.ICampaign;
    contentTabInfo: IContentTabInfo;
    prospectsNumber: number;
    settingsTabInfo: any;
    statsTabInfo: any;
    newCampaign: boolean;
    emailBodyVersions: string[];
    subjectVersions: string[];
    undoEmailBodyStack: Map<number, string[]>;
    redoEmailBodyStack: Map<number, string[]>;
    userEmailBodyDraft: string,
    emailBodyPrompt: Campaigns.IEmailBodyPrompt,
    // currentTab: ITab;
    sendEmailDropdownError: boolean;
    receiveEmailDropdownError: boolean;
    showBanner: boolean;
  }

  interface IContentTabInfo {
    availableTags: string[];
    stepVariants: ICampaignStep[];
  }

  type ICampaignStatus = 'not_started' | 'stopped' | 'running' | 'archived' | 'scheduled' | 'under_review' | 'suspended' | 'on_hold';
  type ICampaignActionType = 'delete' | 'copy' | 'archive' | 'unarchive';



  interface ISelectedCalendarData {
    calendar_team_slug: string;
    calendar_team_id: number;
    calendar_selected_username_slug?: string;
    calendar_selected_user_id?: number;
    calendar_event_type_id?: number;
    calendar_event_type_slug?: string;
    calendar_is_individual?: boolean;
    calendar_smartreach_account_id?: number
  }

  interface ICampaignEmailSettings {
    campaign_id: number,
    sender_email_setting_id: number,
    receiver_email_setting_id: number,
    team_id: number,
    uuid: string,
    id: number,
    sender_email: string,
    receiver_email: string,
    max_emails_per_day_from_email_account?: number,
    signature?: string,
    error?: string,
    from_name: string
  }

  interface ICampaignLinkedinSettings {
    channel_setting_uuid: string;
    team_id: number;
    email: string;
    first_name: string;
    last_name: string;
    linkedin_profile_url: string;
    automation_enabled: boolean;
  }

  interface ICampaignCallSettings {
    channel_setting_uuid: string;
    team_id: number;
    phone_number?: string;
    first_name: string;
    last_name: string;
  }

  interface ICampaignWhatsappSettings {
    channel_setting_uuid: string;
    team_id: number;
    phone_number: string;
    first_name: string;
    last_name: string;
  }

  interface ICampaignSmsSettings {
    channel_setting_uuid: string;
    team_id: number;
    phone_number: string;
    first_name: string;
    last_name: string;
  }

  type campaignAiSequenceStatus = "triggered" | "finished"

  type campaign_type = "email" | "multichannel" | "drip" | "magic_content";

  interface ICampaignAIGenerationContext {
    team_type: string,
    industry: string,
    motive: string,
    solution: string,
    reason_of_reaching_out: string,
    prospects_designation: string,
    language: string,
    tone: string,
    campaign_offer: string,
    max_number_of_steps: number
  }

  type CampaignErrorType = "campaign_level_error" | "campaign_channel_error";
  type ChannelType = "email_channel" |
  "linkedin_channel" |
  "whatsapp_channel" |
  "sms_channel" |
  "call_channel" |
  "general_channel" |
  "independent_channel"

  interface ICampaignError {
      error_message: string;
      error_type: CampaignErrorType;
  }

    interface CampaignLevelError extends ICampaignError {
      error_message: string;
      campaign_status: ICampaignStatus;
      error_type: "campaign_level_error";
    }

    interface CampaignChannelError extends ICampaignError {
      channel_id: string;
      channel_type: ChannelType;
      error_type: "campaign_channel_error";
      error_message: string;
    }

    type CampaignError = CampaignLevelError | CampaignChannelError;


  interface ICampaignBasicDetails {
    id: number;
    name: string;
    status: ICampaignStatus;
    created_at: number; // incorrectly type as number
    owner_email: string;
    owner_name: string;
    owner_id: number;
    team_id: number | string;
    head_step_id?: number;
    shared_with_team: boolean;
    warmup_is_on: boolean;
    spam_test_exists: boolean;
     error: CampaignError[];
    tags: Tags.ICampaignTag[];
    campaign_has_email_step: boolean;
    ai_generation_context?: Campaigns.ICampaignAIGenerationContext;
    uuid: string

    settings: {
      timezone?: string;
      daily_from_time?: number;
      daily_till_time?: number;
      days_preference?: boolean[];
      campaign_email_settings: ICampaignEmailSettings[];
      linkedin_setting_uuid?: string;
      whatsapp_setting_uuid?: string;
      sms_setting_uuid?: string;
      email_priority?: IEmailPriority;
      max_emails_per_day?: number;
      mark_completed_after_days?: number;
      open_tracking_enabled?: boolean;
      click_tracking_enabled?: boolean;
      send_plain_text_email?: boolean;
      enable_email_validation?: boolean;
      opt_out_is_text?: boolean;
      opt_out_msg?: string;
      warmup_started_at?: number | string | Date;
      warmup_length_in_days?: number;
      warmup_starting_email_count?: number;
      show_soft_start_setting: boolean;
      append_followups?: boolean;
      ab_testing_enabled?: boolean;
      add_prospect_to_dnc_on_opt_out?: boolean;
      schedule_start_at?: number;
      schedule_start_at_tz?: string;
      sending_holiday_calendar_id?: number;
      ai_sequence_status?: campaignAiSequenceStatus;
      call_setting_uuid?: string;
      campaign_type: campaign_type;
      selected_calendar_data?: ISelectedCalendarData;
      campaign_linkedin_settings: ICampaignLinkedinSettings[];
      campaign_call_settings: ICampaignCallSettings[];
      campaign_whatsapp_settings: ICampaignWhatsappSettings[];
      campaign_sms_settings: ICampaignSmsSettings[];
      sending_mode?: 'CoPilot' | 'AutoPilot';
    }

  }
  interface IReplySentimentStats {
    positive: number;
  }

  interface ICampaignStats {
    current_prospects: number;
    total_sent: number;
    total_opened: number;
    total_replied: number;
    current_opted_out: number,
    current_to_check: number;
    current_failed_email_validation: number;

    current_do_not_contact: number;

    current_bounced: number;
    current_completed: number;
    current_in_progress: number;
    current_unsent_prospects: number;

    total_steps: number;
    total_clicked: number;
    reply_sentiment_stats: IReplySentimentStats;
    task_count?: Task.ITaskNavBarCount;
  }

  interface ICampaign extends ICampaignBasicDetails {

    stats: ICampaignStats

  }

  interface ISaveStepData {
    step_data: IStepVariantData,
    delaySeconds: number,
    stepId?: number,
    variantId?: number,
    notes?: string,
    priority: priority,
    templateId?: number,
    isSelectedTemplateFromLibrary?: boolean
  }

  interface IStepContext {
    columns_to_use: string[],
    call_to_action: string,
    step_details: string,
  }

  interface ISaveStepVariant {
    stepVariant: ICampaignStepVariantCreateOrUpdate
    campaignId: number | string,
    stepId?: number
    variantId?: number
  }

  interface ICampaignListIdName {
    id: number | string;
    name: string;
  }

  interface ITableColumn {
    field: string;//same in row and column
    // isKey: boolean;
    name: string;
    dataFormat?: (cell: any, row: any) => any;
    dataSort?: boolean;
    width?: SemanticWIDTHS;
    info?: string;
    canExport?: boolean;
  }

  interface ITableProps {
    rows: any[];
    columns: ITableColumn[];
  }

  // type IViewContent = 'scroll' | 'accordion';

  interface IContentTabStates {
    availableTags?: string[];
    campaignName?: string;
    emails?: Campaigns.ICampaignStep[];
    initialized?: boolean;
    // view?: IViewContent;
  }

  type priority = "critical" | "high" | "normal" | "low"

  interface ISendEmailStepData extends ICampaignStepDataObject {
    step_type: "send_email"
    text_preview: string
    subject: string,
    body: string,
  }

  interface ISendManualEmailStepData extends ICampaignStepDataObject {
    step_type: "manual_send_email",
    text_preview: string,
    subject: string,
    body: string,
  }

  interface ILinkedinConnectionRequestData extends ICampaignStepDataObject {
    step_type: "send_linkedin_connection_request",
    body: string,
  }

  interface ILinkedinInMailData extends ICampaignStepDataObject {
    step_type: "send_linkedin_inmail"
    subject?: string,
    body: string,
  }

  interface ILinkedinMessageData extends ICampaignStepDataObject {
    step_type: "send_linkedin_message"
    body: string
  }

  interface ILinkedinViewProfile extends ICampaignStepDataObject {
    step_type: "linkedin_view_profile"
  }

  interface IAutoLinkedinConnectionRequestData extends ICampaignStepDataObject {
    step_type: "auto_send_linkedin_connection_request",
    body?: string,
  }

  interface IAutoLinkedinInMailData extends ICampaignStepDataObject {
    step_type: "auto_send_linkedin_inmail"
    subject: string,
    body: string,
  }

  interface IAutoLinkedinMessageData extends ICampaignStepDataObject {
    step_type: "auto_send_linkedin_message"
    body: string
  }

  interface IAutoLinkedinViewProfile extends ICampaignStepDataObject {
    step_type: "auto_linkedin_view_profile"
  }

  interface IGeneralTaskData extends ICampaignStepDataObject {
    step_type: "general_task"
  }

  interface IWhatsappMessageData extends ICampaignStepDataObject {
    step_type: "send_whatsapp_message"
    body: string
  }

  interface ISmsStepData extends ICampaignStepDataObject {
    step_type: "send_sms"
    body: string
  }

  interface ICallStepData extends ICampaignStepDataObject {
    step_type: "call"
    body: string
  }

  interface IMoveToAnotherStepData extends ICampaignStepDataObject {
    step_type: "move_to_another_campaign"
    move_to_another_campaign_id: number
  }

  interface IManualEmailMagicContentStepData extends ICampaignStepDataObject {
    step_type: "manual_email_magic_content",
    step_context: IStepContext
  }

  interface IAutoEmailMagicContentStepData extends ICampaignStepDataObject {
    step_type: "auto_email_magic_content",
    step_context: IStepContext
  }

  type IStepVariantData =
    | ISendEmailStepData
    | ISendManualEmailStepData
    | IAutoLinkedinConnectionRequestData
    | IAutoLinkedinInMailData
    | IAutoLinkedinMessageData
    | IAutoLinkedinViewProfile
    | ILinkedinConnectionRequestData
    | ILinkedinMessageData
    | ILinkedinInMailData
    | ILinkedinViewProfile
    | IGeneralTaskData
    | IWhatsappMessageData
    | ISmsStepData
    | IMoveToAnotherStepData
    | ICallStepData
    | IAutoEmailMagicContentStepData
    | IManualEmailMagicContentStepData;

  interface IStepVariant {
    id: number;
    step_data: IStepVariantData
    step_delay: number;
    notes?: string;
    priority: priority;
    step_id?: number;
    step_label?: string;
    // step_delay?: number;
    campaign_id?: number;
    template_id?: number;
    template_is_from_library?: boolean;
    label?: string;
    active?: boolean;
    // stepTitle?: string;//used in front end only
  }
  type ICampaignStepType =
    | "send_email"
    | "manual_send_email"
    | "auto_send_linkedin_connection_request"
    | "auto_send_linkedin_message"
    | "auto_send_linkedin_inmail"
    | "auto_linkedin_view_profile"
    | "send_linkedin_connection_request"
    | "send_linkedin_message"
    | "send_linkedin_inmail"
    | "linkedin_view_profile"
    | "general_task"
    | "send_whatsapp_message"
    | "send_sms"
    | "move_to_another_campaign"
    | "call"
    | "manual_email_magic_content"
    | "auto_email_magic_content";

  type ISchedulerStatus = "due" | "done" | "skipped" | "pending_approval" | "approved" | "ai_content_queued"

  interface ICampaignStepDataObject {
    step_type: ICampaignStepType;
  }

  interface ICampaignStepVariantCreateOrUpdate {
    parent_id: number;
    step_data: IStepVariantData;
    step_delay: number;
    notes?: string,
    priority: priority
    template_id?: number;
    template_is_from_library?: boolean;
  }

  interface ICampaignStep {
    // stepTitle?: string;
    // subject: string;
    // body: string;
    // text_preview: string;
    delay: number;
    id: number;
    label: string;
    step_type: ICampaignStepType;
    campaign_id: number;
    // template_id?: number | string | null;
    variants?: IStepVariant[];
  }


  interface IScheduleSettingsFormValues extends Form.IFormValues {

  }

  interface IScheduleSettingsFormErrors extends Form.IFormErrors {

  }

  interface ISpecificCampaignProps {
    params: any;
    campaignStore: ICampaignStore;
  }

  interface ISpecificCampaignStates {
    tabName?: string;
    campaignId?: number | string,
    campaignName?: string;
    contentTabInfo?: any,
    prospectsNumber?: number,
    settingsTabInfo?: any,
    newCampaign?: boolean,
    height?: number,
  }

  type ITemplateAction = 'add' | 'edit';

  interface ICampaignStepTemplateFELib {
    label: string;
    subject: string;
    body: string;
    loc?: string;
    shared_with_team?: boolean;
    id?: number | string | null;
    template_is_from_library?: boolean;
    update_for_all_linked_emails?: boolean;//only for update api call
  }

  interface ICampaignStepTemplateApp {
    category: string;
    label: string;
    subject: string;
    body: string;
    id?: number | string;
    is_owner: boolean;
    owner_name: string;
    shared_with_team: boolean;
    is_from_library?: boolean;
    update_for_all_linked_emails?: boolean;//only for update api call
  }

  interface ICampaignStepTemplateAPI {
    label: string;
    subject: string;
    body: string;
    id: number | string;
    category: string;
    created_at: string;
    is_owner: boolean;
    owner_name: string;
    shared_with_team: boolean;
  }

  interface ICampaignStepTemplateFiltered extends ICampaignStepTemplateApp {
    labelHighlighted: string;
    subjectHighlighted: string;
    bodyHighlighted: string;
  }

  interface ITemplatesForCategory {
    category: string;
    templatesData: Campaigns.ICampaignStepTemplateFELib[];
  }

  interface ITestMail {
    id?: number | string;
    subject: string;
    body: string;
    to_email: string;
    step_type: ICampaignStepType;
    campaign_email_settings_id: number
  }

  type IEmailPriority = 'first' | 'equal' | 'followup';

  interface IInvitedUser {
    agency_id: number | string;
    email: string;
    id: number | string;
    team_id: number | string;
    team_name: string;
  }

  interface ITeamMate {
    user_id: number;
    email: string;
    first_name: string;
    last_name: string;
    teams: { team_id: number | string, team_name: string, role_id: number, role_name: string, api_key: string }[];
    active?: boolean;
    scheduled_for_deletion_at?: Date
    // role: string;
    // team_id: number | string;
    // team_name: string;
  }

  interface IProspectForPreview {
    prospect_email?: string;
    prospect_id: number;
    prospect_name: string;
    prospect_linkedin?: string;
    prospect_phone?: string;
    prospect_company?: string;
  }
  interface IProspectPreview {
    prospect_email: string;
    prospect_id: number;
    prospect_name: string;
    steps: IPreviewStep[];
  }

  interface IPreviewVariant {
    body: string;
    body_preview: string;
    editable_body: string;
    subject: string;
    variant_id: number;
    variant_label: string;
  }

  // interface IPreviewStep {
  //   // body: string;
  //   // body_preview: string;
  //   // editable_body: string;
  //   // step_id: number;
  //   // step_label: string;
  //   // sent: boolean;
  //   sent_at?: string;
  //   // edited: boolean;
  //   email_thread_id?: number;
  //   completed_reason?: 'replied' | 'auto_reply' | 'unsubscribed' | 'out_of_office' | 'bounced' | 'invalid_email';

  //   // for sent emails, collapse body by default; expand on clicking
  //   body_collapsed?: boolean;
  //   selectedVariant?: number;

  //   edited: boolean;
  //   sent: boolean;
  //   step_id: number;
  //   step_label: string;
  //   variants: IPreviewVariant[];
  // }

  interface IPreviewDataObject {
    step_type: ICampaignStepType
  }

  interface IAutoEmailPreview extends IPreviewDataObject {
    step_type: "send_email"
    subject: string;
    body: string;
    editable_body: string;
    body_preview: string;
    edited: boolean;
    email_thread_id?: number;
  }

  interface IAutoEmailMagicPreview extends IPreviewDataObject {
    step_type: "auto_email_magic_content"
    generated_subject?: string;
    generated_body?: string;
    editable_body?: string;
    generated_body_preview?: string;
    edited: boolean;
    email_thread_id?: number;
  }

  interface IManualEmailPreviewPreview extends IPreviewDataObject {
    step_type: "manual_send_email";
    subject: string;
    body: string;
    editable_body: string;
    body_preview: string;
    edited: boolean;
    email_thread_id?: number;
  }

  interface IManualEmailMagicPreview extends IPreviewDataObject {
    step_type: "manual_email_magic_content";
    generated_subject?: string;
    generated_body?: string;
    editable_body?: string;
    generated_body_preview?: string;
    edited: boolean;
    email_thread_id?: number;
  }

  interface ILinkedinViewProfilePreview extends IPreviewDataObject {
    step_type: "linkedin_view_profile";
  }

  interface ILinkedinConnectionRequestPreview extends IPreviewDataObject {
    step_type: "send_linkedin_connection_request";
    subject: string;
    body: string;
    body_preview: string;
  }
  interface ILinkedinInMailPreview extends IPreviewDataObject {
    step_type: "send_linkedin_inmail";
    subject: string;
    body: string;
    body_preview: string;
  }
  interface ILinkedinMessagePreview extends IPreviewDataObject {
    step_type: "send_linkedin_message";
    body: string;
    body_preview: string;
  }

  interface IWhatsappPreview extends IPreviewDataObject {
    step_type: "send_whatsapp_message";
    body: string;
    body_preview: string;
  }

  interface ISmsPreview extends IPreviewDataObject {
    step_type: "send_sms";
    body: string;
    body_preview: string;
  }

  interface ICallPreview extends IPreviewDataObject {
    step_type: "call";
    body: string;
    body_preview: string;

  }

  interface IMoveToAnotherCampaignPreview extends IPreviewDataObject {
    step_type: "move_to_another_campaign",
    move_to_another_campaign_id: number
  }

  interface IGeneralPreview extends IPreviewDataObject {
    step_type: "general_task";
    body: string;
    body_preview: string;
  }

  interface IAutoLinkedinConnectionRequestPreview extends IPreviewDataObject {
    step_type: "auto_send_linkedin_connection_request",
    body: string;
    body_preview?: string,
  }

  interface IAutoLinkedinInMailPreview extends IPreviewDataObject {
    step_type: "auto_send_linkedin_inmail"
    subject: string,
    body_preview: string,
    body: string;
  }

  interface IAutoLinkedinMessagePreview extends IPreviewDataObject {
    step_type: "auto_send_linkedin_message"
    body_preview: string
    body: string;
  }

  interface IAutoLinkedinViewPreview extends IPreviewDataObject {
    step_type: "auto_linkedin_view_profile"
  }




  // interface ICallPreview extends IPreviewDataObject{
  //   step_type : "call";
  //   body: string;
  //   body_preview: string;
  // }


  type PreviewData = IAutoEmailPreview | IManualEmailPreviewPreview | ILinkedinViewProfilePreview
    | ILinkedinConnectionRequestPreview | ILinkedinInMailPreview | ILinkedinMessagePreview
    | ISmsPreview | IWhatsappPreview | IGeneralPreview | ICallPreview | IAutoLinkedinConnectionRequestPreview
    | IAutoLinkedinInMailPreview
    | IAutoLinkedinMessagePreview
    | IAutoLinkedinViewPreview
    | IMoveToAnotherCampaignPreview
    | IManualEmailMagicPreview
    | IAutoEmailMagicPreview

  interface IPreviewStep {
    // body: string;
    // body_preview: string;
    preview_data: PreviewData;
    // editable_body: string;
    step_id: number;
    step_label: string;
    // subject: string;
    sent: boolean;
    sent_at?: string;
    step_type: ICampaignStepType;
    // edited: boolean;
    // email_thread_id?: number;
    completed_reason?: 'replied' | 'auto_reply' | 'unsubscribed' | 'out_of_office' | 'bounced' | 'invalid_email';

    // for sent emails, collapse body by default; expand on clicking
    body_collapsed?: boolean;
  }

  type IEditorModalType = 'campaign-step' | 'step-variant' | 'template' | 'inbox' | 'signature' | 'manual-task' | 'drip-campaign';

  // FIXME - code duplication, remove this and import from sr-core-components
  type IEmailProviderKey = 'gmailapi' | 'gmail_alias' | 'exchange' | 'other' | 'mailgun' | '' | 'office365' | 'sendgrid' | 'gmail_asp' | 'namecheap' | 'rediff' | 'amazon_ses';

  interface IEmailList {
    id: number;
    name: string;
    can_send: boolean;
    can_receive: boolean;
    quota: number;
    service_provider?: IEmailProviderKey;
    signature?: string;
    used_in_campaign: boolean;
    tag?: string;

    //for dropdown
    key: any;
    text: any;
    value: any;
  }

  interface IEmailBodyPrompt {
    recipient_position: string,
    department: string,
    industry: string,
    email_type: string,
    writing_style: string,
    email_structure: string,
    merge_tags_to_use: string[],
    max_number_of_words: number,
    tone_of_voice: string,
    ignore_dropdowns: boolean,
    additional_comment: string
  }

  type ITabRouteType = "content" | "prospects" | "settings" | "preview" | "channel_setup" | "channel_setup/general" | "channel_setup/email" | "channel_setup/sms" | "channel_setup/whatsapp" | "channel_setup/linkedin" | "channel_setup/call";

  interface ISubTab {
    title: string;
    tabRoute: ITabRouteType;
    step_type: Campaigns.ICampaignStepType;
  }

  interface INewCampaignTabData {
    tabNumber: number;
    title: string;
    tabRoute: ITabRouteType;
    tabDescription: string;
    disabled: boolean;
    errorMessage: string;
    // isDone: boolean;
    subTabs?: ISubTab[];
  }

  interface IRunningCampaignProspect {
    campaign_id: number;
    campaign_name: string;
    owner_name?: string;
    prospect_id?: number;
  }
  interface IChannelSettings {
    linkedin_uuid?: string;
    whatsapp_uuid?: string;
    sms_uuid?: string;
  }

}