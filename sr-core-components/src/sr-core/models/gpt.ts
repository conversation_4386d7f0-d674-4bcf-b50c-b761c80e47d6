export interface IEmailBodyPromptRequest {
  recipient_position?: string,
  department?: string,
  industry?: string,
  email_type?: string,
  writing_style?: string,
  email_structure?: string,
  merge_tags_to_use?: string[],
  max_number_of_words?: number,
  tone_of_voice?: string,
  additional_comment?: string
}

export interface IGenerateSubjectRequest {
  email_body: string,
  subject_line_type?: string,
  max_chars: number,
  capitalization?: string,
  avoid_words?: string,
  use_emoticons: boolean
}
export interface IRegenerateEmailBodyRequest {
  previous_email_body: string;
  command?: string;
}
export interface IRegenerateSubjectRequest {
  email_body: string;
  previous_subjects: string[];
}
