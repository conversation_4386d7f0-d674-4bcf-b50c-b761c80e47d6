export interface IFormatGroup {
    formatgroup: {
      icon: string;
      tooltip: string;
      items: string;
    }
  }
  
export interface ITinyMceOptions {
    plugins: string;
    auto_focus: true | undefined;
    toolbar: string;
    table_toolbar: string;
    toolbar_groups: IFormatGroup;
    forced_root_block: boolean;
    branding: boolean;
    statusbar: boolean;
    menubar: boolean;
    contextmenu?: string
    fontsize_formats: string;
    body_class: string;
    content_style: string;
    autoresize_min_height: number;
    autoresize_bottom_margin: number;
    entity_encoding: "named" | "numeric" | "raw" | "named,numeric" | "named+numeric" | "numeric,named" | "numeric+named" | undefined;
    font_formats: string;
    allow_conditional_comments: boolean;
    paste_as_text: boolean;
    setup: any;
    toolbar_mode: "floating" | "sliding" | "scrolling" | "wrap" | undefined;
    toolbar_location: string;
    min_height?: number;
    max_height?: number;
    images_upload_handler?: (blobInfo: any, success: any, failure: any) => void;

}