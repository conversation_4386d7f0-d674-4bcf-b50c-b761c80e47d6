import { ColumnDef } from "./column";
import { LogIn } from "./login";
import { SRSearch } from "./search";

export declare namespace HLDataGrid {

  interface IComponents {
    // Ref https://www.ag-grid.com/react-data-grid/components/
    [key: string]: React.ComponentType<any> | React.FunctionComponent<any>;
  }
  interface IColumn {
    key: string;
    name: string;
    is_custom: boolean;
    field_type: ColumnDef.IColumnType;
    sortable: boolean;
    filterable: boolean;
    resizable: boolean;
    // filterRenderer?: any;
    formatter?: Function;
    getRowMetaData?: Function;
    locked?: boolean;
  }

  interface IPropsOld {
    dataGridName: string;
    rows: any[];
    columns: ColumnDef.IColumnDefDataGrid[];
    heightOffset: any;
    selectedIndices?: string[];
    onRowSelectionChange?: (selectedIndices: string[]) => any;
    // onFilterChange?: (filters: Prospects.IProspectSearchFilter[]) => any;
    onSortChange?: (sortColumn: string, sortDirection: SRSearch.ISortDirection) => any;
    showFilterRow: boolean;
    showCheckGrid: boolean;
    isEmpty: boolean;
    rowRenderer?: any;
  }

  interface IDataGridColumn {
    headerName: string;
    field: string;
    sortable?:boolean
    is_custom: boolean; // TODO: remove
    field_type: ColumnDef.IColumnType;
    hide?: boolean;
    allowSelection?: boolean;
    width?: number;
    locked?: boolean;
    headerTooltip?: string;
    cellRenderer?: (params: any) => any;
    minWidth?: number;
  }

  interface IPropsDataGrid {
    dataGridName: string;
    height?:string
    loggedInUserId: number;
    rows: any[];
    columns: IDataGridColumn[];
    heightOffset?: number;
    selectedIds?: number[];
    selectedUuids?: string[];
    onRowSelectionChange?: (selectedIndices: (string | number)[], selectedIndicesUuids: string[]) => any;
    selectRowPermission?: LogIn.IPermissions;
    suppressRowClickSelection?: boolean;
    // isRowSelectableCustom?: (rowNode: RowNode) => boolean;
    // defaultColDef?: ColDef;
    components?: IComponents;
  }

  interface IAGGridCellRendererParam<T> {
    value: any;
    data: T;
    colDef: {
      field: string;
    };
  }

  type IFilterValuesFn = (row: any, columnFilter: IColumnFilter, columnKey: string) => boolean;

  interface IColumnFilter {
    column: ColumnDef.IColumnDefDataGrid;
    filterTerm: any;
    filterValues: IFilterValuesFn;
    rawValue: any;
  }
}