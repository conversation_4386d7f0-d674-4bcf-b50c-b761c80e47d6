import { Alerts } from "./alerts";
import { Form } from "./form";
import { LogIn } from "./login";

export declare namespace Settings {


  interface ISettings {
    emailData?: IEmailData[];
    isLoading?: boolean;
    isEmailSettingsModalOpen?: Boolean;
    isEmailSettingsModalLoading?: boolean;
    isCustomTrackingModalOpen?: Boolean;
    isCustomTrackingModalLoading?: Boolean;
    isDKIMSignatureModalOpen?: boolean;
    isDKIMSignatureModalLoading?: boolean;
    isDKIMSignatureModalVerifyDKIMResponse: IEmailDKIMRecord;
    isDKIMSignatureVerified?: boolean,
    isEmailSignatureModalOpen?: boolean;
    isEmailSignatureModalLoading?: boolean;
    currentEmailData?: IEmailData;
    showEmailProviderList?: boolean;
    isLoadingAuthFlow?: boolean;
    action?: IActions;
    selectedProviderKey?: IEmailProviderKey;
    slectedEmailAccountDkimData?: IEmailDKIMRecord;
    isOwnerChangeModalOpen?: boolean;
    isOwnerChangeModalLoading?: boolean;
  }

  type IEmailProviderKey = 'gmailapi' | 'gmail_alias' | 'exchange' | 'other' | 'mailgun' | '' | 'office365' | 'sendgrid' | 'gmail_asp' | 'namecheap' | 'rediff' | 'amazon_ses';

  interface IEmailProvider {
    key: IEmailProviderKey;
    displayName: string;
    image?: string;
    //to remove
    icon?: string;
    btnClass?: string;
    text?: string;
    tooltip?: React.ReactNode | string;
  }

  type ICampaignUseStatusForEmailSetting = 'is_not_assigned_to_any_campaign' | 'has_running_campaign' | 'no_running_campaign'

  interface IEmailData extends IEmailSettings {
    email: string;
    email_address_host: string;
    error: string | null;
    // sender_name: string;
    first_name: string;
    last_name: string;
    can_send: boolean;
    can_receive: boolean;
    created_at: number;
    id: number | string;
    uuid: string;
    can_auto_start_warmup: boolean;

    can_auto_reconnect_via_oauth: boolean;

    quota_per_day: number;
    oauth2_enabled: boolean;

    min_delay_seconds: number;
    max_delay_seconds: number;

    // username: string;//same for smtp and imap
    smtp_username?: string;
    smtp_password: string;
    imap_username?: string;
    imap_password: string;
    // account_name_same_as_email?: boolean;
    signature?: string;

    //specific to mailgun,sendgrid
    api_key?: string;
    mailgun_priv_key?: string;
    email_domain?: string;
    mailgun_region?: 'us' | 'eu';

    dns_host?: string;
    tracking_domain_host?: string;
    custom_tracking_cname_value?: string;
    custom_tracking_domain_is_verified?: boolean;
    custom_tracking_domain_is_ssl_enabled?: boolean;
    cc_emails?: string;
    bcc_emails?: string;

    owner_id?: number | string;
    owner_name?: string;

    app_specific_password?: string;
    campaign_use_status_for_email_setting: ICampaignUseStatusForEmailSetting
    rms_ip?: string
    tag?: string;
  }

  interface IEmailSettings {
    service_provider: IEmailProviderKey;

    smtp_host: string;
    smtp_port: number | null;

    imap_host: string;
    imap_port: number | null;
  }
  export type ISendActions = 'send' | 'receive' | 'send_receive'

  interface IEmailSettingsV2 {
    service_provider: IEmailProviderKey;

    smtp_host: string;
    smtp_port: number[] | null;

    imap_host: string;
    imap_port: number[] | null;

    actions: ISendActions
    same_smtp_imap_mail: boolean
  }

  interface IEmailDKIMRecord {
    domain: string;
    record: string;
    selector: string;
    active: boolean;
    error?: string;
  }

  type IActions = 'Add' | 'Edit';

  interface HelpfulSolutionList {
    solution: String,
    url: String
  }

  export interface HelpfulErrorDetails {
    error: String
    header: String
    helpful_message: String
    solution_list: HelpfulSolutionList[]
  }

  export interface HelpfulErrorDetailsResponse {
    helpful_error_details?: HelpfulErrorDetails
  }

  interface ISettingsProps {
    emailData?: IEmailData;
    onSubmit: (values: Form.IFormValues, resetFormFn?: () => any) => void;
    onClose: () => void;
    onHelpfulMessageDismiss?: () => void;
    action: IActions;
    isSubmitting: boolean;
    service_provider?: Settings.IEmailProviderKey;
    keyToReRender?: number;
    updatekeyToReRender?: any;
    canEdit: boolean;
    canReconnect: boolean;
    step_index?: 1 | 2;
    onNext: (values: Form.IFormValues) => void;
    onBack: () => void;

    alertStore?: Alerts.IAlertStore;
    // remove later
    logInStore?: LogIn.ILogInStore;
    accountId?: number | string;
    helpful_error_details?: HelpfulErrorDetails
  }

  type IBlacklistType = 'email' | 'domain' | 'phone';

  interface IBlacklistProps {
    onSubmit: (values: Form.IFormValues, resetFormFn?: () => any) => void;
    onClose: () => void;
    modalLoading: boolean;
    selectedBlacklistType: IBlacklistType;
  }

  interface IEmailDataErrors extends Form.IFormErrors {
  }

  type IPlanName = 'standard' | 'ultimate' | 'custom' | 'pro' | 'inactive' | 'trial';

  interface IRolePermissions {
    id: number;
    permissions: LogIn.IPermissions;
    role_name: string;
  }

  interface IRolePermissionsResponse {
    roles: IRolePermissions[]
  }

  interface IUpdateRolePermissionsResponse {
    roles: IRolePermissions[],
    updated_role: IRolePermissions
  }


  interface IWebhooks {
    id: number;
    target_url: string,
    events?: string[];
    source: string;
    version: string,
    active: boolean,
    error: string,
    error_at: number,
    created_at: number,
    campaign_ids: number[],
    reply_sentiment_uuids: string[],
    prospect_category_ids: number[]
  }




  type IDataplatformType = 'hunter' | 'uplead' | 'dropcontact' | 'anymailfinder' | 'clearbit' | 'aeroleads';
  type IDataplatformSearchType = 'manual' | 'csv';

  // interface IDataplatforms {
  //   hunter: string;
  //   uplead: string;
  //   dropcontact: string;
  //   anymailfinder: string;
  //   clearbit: string;
  //   aeroleads: string;
  // }

  interface allowedDropdownFieldOptions {
    value: string,
    key: string,
    text: string
  }

  interface allowedFields {
    display_name: string,
    key: string,
    filed_type: string,
    options: allowedDropdownFieldOptions[]
  }
  interface IDataplatform {
    api_key?: string;
    text_id: IDataplatformType;
    display_name: string;
    logo_url: string;
    allowed_fields: allowedFields[]
  }
  interface IEmailAccount {
    id: number;
    email: string;
    service_provider: string;
  }

  interface IUserRoleAndIds {
    user_role_id: number,
    role: string
  }

  interface ICreateOrUpdateLinkedinAccountData {
    first_name: string,
    last_name: string,
    email: string,
    password?: string,
    profile_url: string,
    country?: string,
    owner_id: number,
    view_profile_limit_per_day: number,
    inmail_limit_per_day: number,
    message_limit_per_day: number,
    connection_request_limit_per_day: number,
    allow_automated_task?: boolean,
    li_at_cookie?: string,
    li_a_cookie?: string
  }

  interface ILinkedinAccountSettingsData {
    uuid: string,
    first_name: string,
    last_name: string,
    email: string,
    profile_url: string,
    country: string,
    owner_id: number,
    owner_first_name: string,
    owner_last_name: string,
    view_profile_limit_per_day: number,
    inmail_limit_per_day: number,
    message_limit_per_day: number,
    connection_request_limit_per_day: number,
    captain_data_user_id?: string,
    captain_data_account_id?: string,
    status: 'active' | 'need_to_assign_proxy' | 'need_to_recreate_session' | 'pushed_to_queue_for_recreating_session' | 're_assign_cookie',
    is_session_cookie_synced: boolean
  }

  interface ICreateOrUpdateWhatsappAccountData {
    first_name: string,
    last_name: string,
    whatsapp_number: string,
    owner_id: number,
    whatsapp_message_limit_per_day: number
  }

  interface IWhatsappAccountSettingsData {
    uuid: string,
    first_name: string,
    last_name: string,
    whatsapp_number: string,
    owner_id: number,
    owner_first_name: string,
    owner_last_name: string,
    whatsapp_message_limit_per_day: number
  }

  interface ICreateOrUpdateSmsData {
    first_name: string,
    last_name: string,
    phone_number: string,
    owner_id: number,
    sms_limit_per_day: number
  }

  interface ISmsSettingsData {
    uuid: string,
    first_name: string,
    last_name: string,
    phone_number: string,
    owner_id: number,
    owner_first_name: string,
    owner_last_name: string,
    sms_limit_per_day: number
  }

  interface IInternalEmailsAndDomains {
    internal_emails: string[],
    internal_domain: string[]
  }

  interface ICallSettingsData {
    uuid: string,
    first_name: string,
    last_name: string,
    phone_number: string,
    owner_account_id: number,
    country: string,
    call_limit_per_day: number,
    enable_forwarding: boolean,
    record_call: boolean;
    forward_to?: string,
    is_active: boolean,
    caller_id?: string;
  }
  type IPhoneType = 'local' | 'mobile' | 'tollfree'

  interface IBuyNumberData {
    first_name: string,
    last_name: string,
    country_code: string,
    phone_type: IPhoneType,
    enable_call_recording: boolean,
    enable_forward: boolean,
    forward_number?: string,
    call_limit: number,
    phone_uuid?: string,
    caller_id?: string,
  }

  interface INewCallSetting {
    first_name: string,
    last_name: string,
    call_limit: number
  }

  interface IPriceObject {
    phone_type: string,
    base_price: number,
    current_price: number
  }

  interface INumberPriceDetails {
    countryISO: string,
    prices: IPriceObject[],
    currency: string
  }

  interface ICountryObject {
    country_name: string,
    countryISO_code: string,
    country_phone_code: string
  }

}
