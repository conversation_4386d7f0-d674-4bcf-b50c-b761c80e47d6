import { ISRDropdownOption } from "@sr/design-component"

export const recipientPositionOptions: ISRDropdownOption[] = [
  {
    displayText: 'No Prompt',
    value: ''
  }, {
    displayText: 'CEO',
    value: 'CEO'
  }, {
    displayText: 'CXO',
    value: 'CXO'
  }, {
    displayText: 'Managing Director',
    value: 'Managing Director'
  }, {
    displayText: 'Head',
    value: 'Head'
  }, {
    displayText: 'Leadership',
    value: 'Leadership'
  }, {
    displayText: 'Manager',
    value: 'Manager'
  }
]

export const departmentOptions: ISRDropdownOption[] = [
  {
    displayText: 'No Prompt',
    value: ''
  }, {
    displayText: 'Sales',
    value: 'Sales'
  }, {
    displayText: 'Finance',
    value: 'Finance'
  }, {
    displayText: 'IT',
    value: 'IT'
  }, {
    displayText: 'Marketing',
    value: 'Marketing'
  }, {
    displayText: 'Human Resource',
    value: 'Human Resource'
  }, {
    displayText: 'Operations',
    value: 'Operations'
  }, {
    displayText: 'Legal',
    value: 'Legal'
  },
]

export const industryOptions: ISRDropdownOption[] = [
  {
    displayText: 'No Prompt',
    value: ''
  }, {
    displayText: 'Healthcare',
    value: 'Healthcare'
  }, {
    displayText: 'Software',
    value: 'Software'
  }, {
    displayText: 'Banking',
    value: 'Banking'
  }, {
    displayText: 'Power',
    value: 'Power'
  }, {
    displayText: 'Pharmaceuticals',
    value: 'Pharmaceuticals'
  }, {
    displayText: 'Retail',
    value: 'Retail'
  }, {
    displayText: 'E-commerce',
    value: 'E-commerce'
  }, {
    displayText: 'Manufacturing',
    value: 'Manufacturing'
  }, {
    displayText: 'Industrial',
    value: 'Industrial'
  }, {
    displayText: 'Telecommunications',
    value: 'Telecommunications'
  }, {
    displayText: 'Electronics',
    value: 'Electronics'
  }, {
    displayText: 'Education',
    value: 'Education'
  }, {
    displayText: 'E-learning',
    value: 'E-learning'
  }, {
    displayText: 'Hospitality',
    value: 'Hospitality'
  }, {
    displayText: 'Tourism',
    value: 'Tourism'
  }, {
    displayText: 'Transportation',
    value: 'Transportation'
  }, {
    displayText: 'Logistics',
    value: 'Logistics'
  }, {
    displayText: 'Media',
    value: 'Media'
  }, {
    displayText: 'Entertainment',
    value: 'Entertainment'
  }, {
    displayText: 'Energy',
    value: 'Energy'
  }, {
    displayText: 'Utilities',
    value: 'Utilities'
  }, {
    displayText: 'Government',
    value: 'Government'
  }, {
    displayText: 'Public Sector',
    value: 'Public Sector'
  }, {
    displayText: 'Real Estate',
    value: 'Real Estate'
  }, {
    displayText: 'Property Management',
    value: 'Property Management'
  }, {
    displayText: 'Legal',
    value: 'Legal'
  }, {
    displayText: 'Business Consulting',
    value: 'Business Consulting'
  }, {
    displayText: 'Cloud Computing',
    value: 'Cloud Computing'
  }, {
    displayText: 'Non-profit and Charitable Organizations',
    value: 'Non-profit and Charitable Organizations'
  }, {
    displayText: 'Events',
    value: 'Events'
  }, {
    displayText: 'Conferences',
    value: 'Conferences'
  }
]

export const emailTypeOptions: ISRDropdownOption[] = [
  {
    displayText: 'No Prompt',
    value: ''
  }, {
    displayText: 'Introduction',
    value: 'Introduction'
  }, {
    displayText: 'Follow-up',
    value: 'Follow-up'
  }, {
    displayText: 'Sales',
    value: 'Sales'
  }, {
    displayText: 'Networking',
    value: 'Networking'
  }, {
    displayText: 'Job Application',
    value: 'Job Application'
  }, {
    displayText: 'Event Invitation',
    value: 'Event Invitation'
  }, {
    displayText: 'Survey',
    value: 'Survey'
  }, {
    displayText: 'Content Promotion',
    value: 'Content Promotion'
  }, {
    displayText: 'Re-engagement',
    value: 'Re-engagement'
  }, {
    displayText: 'Testimonial Request',
    value: 'Testimonial Request'
  }, {
    displayText: 'Partnership Proposal',
    value: 'Partnership Proposal'
  }, {
    displayText: 'Product Launch',
    value: 'Product Launch'
  }, {
    displayText: 'Sponsorship Request',
    value: 'Sponsorship Request'
  }, {
    displayText: 'Donation Request',
    value: 'Donation Request'
  }, {
    displayText: 'Value Proposition',
    value: 'Value Proposition'
  }, {
    displayText: 'Product Demo',
    value: 'Product Demo'
  }, {
    displayText: 'Free Trial',
    value: 'Free Trial'
  }, {
    displayText: 'Industry Insights',
    value: 'Industry Insights'
  }, {
    displayText: 'Competitive Analysis',
    value: 'Competitive Analysis'
  }, {
    displayText: 'Referral',
    value: 'Referral'
  }, {
    displayText: 'Exclusive Offer',
    value: 'Exclusive Offer'
  }, {
    displayText: 'Resource Sharing',
    value: 'Resource Sharing'
  }, {
    displayText: 'Customer Success Story',
    value: 'Customer Success Story'
  }, {
    displayText: 'Personalized Video Email',
    value: 'Personalized Video Email'
  }
]

export const writingStyleOptions: ISRDropdownOption[] = [
  {
    displayText: 'No Prompt',
    value: ''
  }, {
    displayText: 'Personalized',
    value: 'Personalized'
  }, {
    displayText: 'Storytelling',
    value: 'Storytelling'
  }, {
    displayText: 'Direct',
    value: 'Direct'
  }, {
    displayText: 'Humorous',
    value: 'Humorous'
  }, {
    displayText: 'Benefit-driven',
    value: 'Benefit-driven'
  }
]

export const emailStructureOptions: ISRDropdownOption[] = [
  {
    displayText: 'No Prompt',
    value: ''
  }, {
    displayText: 'Introduction-Based',
    value: 'Introduction-Based'
  }, {
    displayText: 'Problem-solution',
    value: 'Problem-solution'
  }, {
    displayText: 'Benefit-Focused',
    value: 'Benefit-Focused'
  }, {
    displayText: 'Storytelling',
    value: 'Storytelling'
  }, {
    displayText: 'Question-Based',
    value: 'Question-Based'
  }, {
    displayText: 'Social Proof',
    value: 'Social Proof'
  }, {
    displayText: 'Personalized Research',
    value: 'Personalized Research'
  }, {
    displayText: 'Curiosity-Piquing',
    value: 'Curiosity-Piquing'
  }, {
    displayText: 'Follow-Up',
    value: 'Follow-Up'
  }, {
    displayText: 'Collaborative Approach',
    value: 'Collaborative Approach'
  }
]

export const getMergeTagsToUseOptions = (availableTags: string[]): ISRDropdownOption[] => {
  return (availableTags.map(tag => {
    return ({
      displayText: tag,
      value: tag
    })
  }))
}

export const voiceToneOptions: ISRDropdownOption[] = [
  {
    displayText: 'No Prompt',
    value: ''
  }, {
    displayText: 'Professional',
    value: 'Professional'
  }, {
    displayText: 'Friendly',
    value: 'Friendly'
  }, {
    displayText: 'Personalized',
    value: 'Personalized'
  }, {
    displayText: 'Excitement/Enthusiasm',
    value: 'Excitement/Enthusiasm'
  }, {
    displayText: 'Urgency',
    value: 'Urgency'
  }, {
    displayText: 'Humorous',
    value: 'Humorous'
  }, {
    displayText: 'Empathetic',
    value: 'Empathetic'
  }, {
    displayText: 'Curiosity/Evocative',
    value: 'Curiosity/Evocative'
  }
]

export const subjectLineTypeOptions: ISRDropdownOption[] = [
  {
    displayText: 'No Prompt',
    value: ''
  }, {
    displayText: 'Personalized',
    value: 'Personalized'
  }, {
    displayText: 'Storytelling',
    value: 'Storytelling'
  }, {
    displayText: 'Direct',
    value: 'Direct'
  }, {
    displayText: 'Humorous',
    value: 'Humorous'
  }, {
    displayText: 'Benefit-driven',
    value: 'Benefit-driven'
  }
]

export const capitalizationOptions: ISRDropdownOption[] = [
  {
    displayText: 'No Prompt',
    value: ''
  }, {
    displayText: 'Title Case',
    value: 'Title Case'
  }, {
    displayText: 'Small Case',
    value: 'Small Case'
  }
]

export function getCharDropdown() {
  const dropdownOptions = []
  for (var i = 20; i <= 80; i += 5) {
    dropdownOptions.push({
      displayText: i + "",
      value: i
    })
  }

  return dropdownOptions;
}