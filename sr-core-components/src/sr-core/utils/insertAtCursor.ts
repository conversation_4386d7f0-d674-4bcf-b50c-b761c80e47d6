export function insertText(element: any, text: string) {
    var txtArea = element;
    console.log('insert at cursor', txtArea);
    if ((document as any).selection) {
      txtArea.focus();
      const sel = (document as any).selection.createRange();
      sel.text = text;
    }
    //MOZILLA and others
    else if (txtArea.selectionStart || txtArea.selectionStart == '0') {
      var startPos = txtArea.selectionStart;
      var endPos = txtArea.selectionEnd;
      txtArea.value = txtArea.value.substring(0, startPos)
        + text
        + txtArea.value.substring(endPos, txtArea.value.length);
      txtArea.selectionStart = startPos + text.length;
      txtArea.selectionEnd = startPos + text.length;
    } else {
      txtArea.value += text;
    }
  
    return txtArea.value || '';
  
    // var scrollPos = txtarea.scrollTop;
    // var caretPos = txtarea.selectionStart;
  
    // var front = (txtarea.value).substring(0, caretPos);
    // var back = (txtarea.value).substring(txtarea.selectionEnd, txtarea.value.length);
    // txtarea.value = front + text + back;
    // caretPos = caretPos + text.length;
    // txtarea.selectionStart = caretPos;
    // txtarea.selectionEnd = caretPos;
    // txtarea.focus();
    // txtarea.scrollTop = scrollPos;
    // console.log(input);
    // if (input == undefined) { return; }
    // var scrollPos = input.scrollTop;
    // var pos = 0;
    // var browser = ((input.selectionStart || input.selectionStart == "0") ?
    //   "ff" : (document.selection ? "ie" : false));
    // if (browser == "ie") {
    //   input.focus();
    //   var range = document.selection.createRange();
    //   range.moveStart("character", -input.value.length);
    //   pos = range.text.length;
    // }
    // else if (browser == "ff") { pos = input.selectionStart };
  
    // var front = (input.value).substring(0, pos);
    // var back = (input.value).substring(pos, input.value.length);
    // input.value = front + text + back;
    // pos = pos + text.length;
    // if (browser == "ie") {
    //   input.focus();
    //   var range = document.selection.createRange();
    //   range.moveStart("character", -input.value.length);
    //   range.moveStart("character", pos);
    //   range.moveEnd("character", 0);
    //   range.select();
    // }
    // else if (browser == "ff") {
    //   input.selectionStart = pos;
    //   input.selectionEnd = pos;
    //   input.focus();
    // }
    // input.scrollTop = scrollPos;
  }