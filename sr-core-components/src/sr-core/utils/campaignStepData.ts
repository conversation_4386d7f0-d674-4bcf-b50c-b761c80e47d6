import { ISRDropdownOption } from "@sr/design-component";
import { map as lo_map } from 'lodash'
import { Campaigns } from "../models/campaigns";

export type linkedin_actions = "view_profile" | "connection_request" | "inmail" | "message" | "auto_view_profile" | "auto_connection_request" | "auto_inmail" | "auto_message" | "move_to_another_campaign";
export type channel_type = 'email_channel' | 'linkedin_channel' | 'whatsapp_channel' | 'sms_channel' | 'general_channel' | 'call_channel' | 'independent_channel';

export const getSubject = (variant?: Campaigns.IStepVariant): string => {
  if (variant?.step_data) {
    const stepData = variant.step_data

    if (stepData.step_type === "send_email") {
      return stepData.subject
    }
    else if (stepData.step_type === "manual_send_email") {
      return stepData.subject
    }
    else if (stepData.step_type === "send_linkedin_connection_request") {
      return ""
    }
    else if (stepData.step_type === "send_linkedin_inmail") {
      if (stepData.subject) {
        return stepData.subject
      }
      else {
        return ""
      }
    }
    else if (stepData.step_type === "auto_linkedin_view_profile") {
      return ""
    }
    else if (stepData.step_type === "auto_send_linkedin_connection_request") {
      return ""
    }
    else if (stepData.step_type === "auto_send_linkedin_inmail") {
      return stepData.subject
    }
    else if (stepData.step_type === "auto_send_linkedin_message") {
      return ""
    }
    else if (stepData.step_type === "send_linkedin_message") {
      return ""
    }
    else if (stepData.step_type === "linkedin_view_profile") {
      return ""
    }
    else if (stepData.step_type === "general_task") {
      return ""
    }
    else if (stepData.step_type === "send_sms") {
      return ""
    }
    else if (stepData.step_type === "send_whatsapp_message") {
      return ""
    }
    else if (stepData.step_type === 'call') {
      return ""
    }
    else if (stepData.step_type === "move_to_another_campaign"){
      return ""
    }
    else {
      return ""
    }
  } else {
    return ""
  }
}

export const getBody = (variant?: Campaigns.IStepVariant): string => {
  if (variant?.step_data) {

    const stepData = variant.step_data

    if (stepData.step_type === "send_email") {
      return stepData.body
    }
    else if (stepData.step_type === "manual_send_email") {
      return stepData.body
    }
    else if (stepData.step_type === "send_linkedin_connection_request") {
      return stepData.body
    }
    else if (stepData.step_type === "send_linkedin_inmail") {
      return stepData.body
    }
    else if (stepData.step_type === "send_linkedin_message") {
      return stepData.body
    }
    else if (stepData.step_type === "linkedin_view_profile") {
      return ""
    }
    else if (stepData.step_type === "auto_linkedin_view_profile") {
      return ""
    }
    else if (stepData.step_type === "auto_send_linkedin_connection_request") {
      return stepData.body || ""
    }
    else if (stepData.step_type === "auto_send_linkedin_inmail") {
      return stepData.body
    }
    else if (stepData.step_type === "auto_send_linkedin_message") {
      return stepData.body
    }
    else if (stepData.step_type === "general_task") {
      return ""
    }
    else if (stepData.step_type === "send_sms") {
      return stepData.body
    }
    else if (stepData.step_type === "send_whatsapp_message") {
      return stepData.body
    }
    else if (stepData.step_type === "call") {
      return stepData.body
    }
    else if (stepData.step_type === "move_to_another_campaign"){
      return ""
    }
    else {
      return ""
    }
  } else {
    return ""
  }
}

export const getActionTab = (variant?: Campaigns.IStepVariant): string => {
  if (variant?.step_data) {

    const stepData = variant.step_data

    if (stepData.step_type === "send_email") {
      return "mail"
    }
    else if (stepData.step_type === "manual_send_email") {
      return "mail"
    }
    else if (stepData.step_type === "send_linkedin_connection_request") {
      return "linkedin"
    }
    else if (stepData.step_type === "send_linkedin_inmail") {
      return "linkedin"
    }
    else if (stepData.step_type === "send_linkedin_message") {
      return "linkedin"
    }
    else if (stepData.step_type === "linkedin_view_profile") {
      return "linkedin"
    }
    else if (stepData.step_type === "auto_linkedin_view_profile") {
      return "linkedin"
    }
    else if (stepData.step_type === "auto_send_linkedin_connection_request") {
      return "linkedin"
    }
    else if (stepData.step_type === "auto_send_linkedin_inmail") {
      return "linkedin"
    }
    else if (stepData.step_type === "auto_send_linkedin_message") {
      return "linkedin"
    }
    else if (stepData.step_type === "general_task") {
      return "general"
    }
    else if (stepData.step_type === "send_sms") {
      return "sms"
    }
    else if (stepData.step_type === "send_whatsapp_message") {
      return "whatsapp"
    }
    else if (stepData.step_type === "call") {
      return "call"
    }
    else if (stepData.step_type === "move_to_another_campaign"){
      return "independent"
    }
    else {
      return "mail"
    }
  } else {
    return "mail"
  }
}

export const getLinkedinTouchType = (variant?: Campaigns.IStepVariant) => {
  if (variant?.step_data) {

    const stepType = variant.step_data.step_type

    if (stepType === "linkedin_view_profile") {
      return "view_profile"
    }
    else if (stepType === "send_linkedin_connection_request") {
      return "connection_request"
    }
    else if (stepType === "send_linkedin_inmail") {
      return "inmail"
    }
    else if (stepType === "auto_linkedin_view_profile") {
      return "auto_view_profile"
    }
    else if (stepType === "auto_send_linkedin_connection_request") {
      return "auto_connection_request"
    }
    else if (stepType === "auto_send_linkedin_inmail") {
      return "auto_inmail"
    }
    else if (stepType === "auto_send_linkedin_message") {
      return "auto_message"
    }
    else {
      return "message"
    }
  } else {
    return undefined;
  }
}

export const getLinkedInStepDisplayName = (action: linkedin_actions) => {
  if (action === 'connection_request')
    return "Connection Request"
  else if (action === 'inmail')
    return "In-mail"
  else if (action === 'message')
    return "Message"
  else if (action === 'view_profile')
    return "View Profile"
  else if (action === "auto_connection_request")
    return "Auto Connection Request"
  else if (action === "auto_inmail")
    return "Auto In-mail"
  else if (action === "auto_message")
    return "Auto Message"
  else if (action === "auto_view_profile")
    return "Auto View Profile"
  else return ""
}

export const getLinkedinStepType = (action: linkedin_actions) => {
  if (action === "connection_request") {
    return "send_linkedin_connection_request"
  }
  else if (action === "inmail") {
    return "send_linkedin_inmail"
  }
  else if (action === "message") {
    return "send_linkedin_message"
  }
  else if (action === "view_profile") {
    return "linkedin_view_profile"
  }
  else if (action === "auto_connection_request") {
    return "auto_send_linkedin_connection_request"
  }
  else if (action === "auto_inmail") {
    return "auto_send_linkedin_inmail"
  }
  else if (action === "auto_message") {
    return "auto_send_linkedin_message"
  }
  else {
    return "auto_linkedin_view_profile"
  }
}

export const getTextPreview = (variant?: Campaigns.IStepVariant) => {
  if (variant?.step_data) {
    if (variant.step_data.step_type === "send_email" || variant.step_data.step_type === "manual_send_email") {
      return variant.step_data.text_preview
    } else if (variant.step_data.step_type == 'general_task') {
      return variant.notes || ""
    } else {
      const body = getBody(variant)
      return body
    }
  } else {
    return ""
  }
}

export const getChannelFromStepType = (step_type: Campaigns.ICampaignStepType) => {
  if (step_type === "general_task") {
    return "General"
  }
  else if (step_type === "linkedin_view_profile") {
    return "Linkedin"
  }
  else if (step_type === "send_email") {
    return "Email"
  }
  else if (step_type === "send_linkedin_connection_request") {
    return "Linkedin"
  }
  else if (step_type === "send_linkedin_inmail") {
    return "Linkedin"
  }
  else if (step_type === "send_linkedin_message") {
    return "Linkedin"
  }
  else if (step_type === "auto_linkedin_view_profile") {
    return "Linkedin"
  }
  else if (step_type === "auto_send_linkedin_connection_request") {
    return "Linkedin"
  }
  else if (step_type === "auto_send_linkedin_inmail") {
    return "Linkedin"
  }
  else if (step_type === "auto_send_linkedin_message") {
    return "Linkedin"
  }
  else if (step_type === "send_sms") {
    return "Sms"
  }
  else if (step_type === "send_whatsapp_message") {
    return "Whatsapp"
  }
  else if (step_type === "call") {
    return "Call"
  }
  else if (step_type === "move_to_another_campaign"){
    return "Independent"
  }
  else {
    return "Email"
  }
}

export function getPriorityOptions(): ISRDropdownOption[] {
  let options: ISRDropdownOption[] = [
    {
      value: "critical",
      displayText: 'Critical'
    },
    {
      value: "high",
      displayText: 'High'
    },
    {
      value: "normal",
      displayText: 'Normal'
    },
    {
      value: "low",
      displayText: "Low"
    }
  ]
  return options;
}

export function getMailStepTypeOptions(showManualStep?: boolean): ISRDropdownOption[] {
  let options: ISRDropdownOption[] = [
    {
      value: 'send_email',
      displayText: 'Auto'
    }
  ]
  if (showManualStep) {
    options.push({
      value: "manual_send_email",
      displayText: 'Manual'
    })
  }
  return options;
}

export function getLinkedinStepTypeOptions(enable_linkedin_automation?: boolean) {
  let options: { value: linkedin_actions, displayText: string }[] = [
    {
      value: "view_profile",
      displayText: 'View Profile - Manual'
    },
    {
      value: "connection_request",
      displayText: 'Connection Request - Manual'
    },
    {
      value: "inmail",
      displayText: 'InMail - Manual'
    },
    {
      value: "message",
      displayText: 'Message - Manual'
    }
  ]

  if (enable_linkedin_automation) {
    options.push({
      value: "auto_view_profile",
      displayText: "View Profile - Auto"
    }, {
      value: "auto_connection_request",
      displayText: "Connection Request - Auto"
    }, {
      value: "auto_inmail",
      displayText: "Inmail - Auto"
    }, {
      value: "auto_message",
      displayText: "Message - Auto"
    })
  }
  return options;
}

export interface NewSettingsData {
  priority: Campaigns.priority,
  step_delay?: number,
  action?: Campaigns.ICampaignStepType | linkedin_actions
}

export function getMergeTagOptions(availableTags: string[]): ISRDropdownOption[] {
  let options: ISRDropdownOption[] = lo_map(availableTags, (tag) => {
    return { displayText: tag, value: tag };
  });
  return options;
}

export const getChannelNameFromChannelType = (channelType: channel_type) => {
  return channelType === 'email_channel' ? "email" :
    channelType === 'general_channel' ? "general" :
      channelType === 'linkedin_channel' ? "linkedIn" :
        channelType === 'sms_channel' ? "sms" :
          channelType === 'call_channel' ? "call" :
            channelType === 'whatsapp_channel' ? "whatsApp" : 
              channelType === 'independent_channel' ? "Independent" : ""
}

export const getChannelTypeFromVariant = (variant: Campaigns.IStepVariant): channel_type => {

  const stepData = variant.step_data

  if (stepData.step_type === "send_email") {
    return "email_channel"
  }
  else if (stepData.step_type === "manual_send_email") {
    return "email_channel"
  }
  else if (stepData.step_type === "send_linkedin_connection_request") {
    return "linkedin_channel"
  }
  else if (stepData.step_type === "send_linkedin_inmail") {
    return "linkedin_channel"
  }
  else if (stepData.step_type === "send_linkedin_message") {
    return "linkedin_channel"
  }
  else if (stepData.step_type === "linkedin_view_profile") {
    return "linkedin_channel"
  }
  else if (stepData.step_type === "auto_linkedin_view_profile") {
    return "linkedin_channel"
  }
  else if (stepData.step_type === "auto_send_linkedin_connection_request") {
    return "linkedin_channel"
  }
  else if (stepData.step_type === "auto_send_linkedin_inmail") {
    return "linkedin_channel"
  }
  else if (stepData.step_type === "auto_send_linkedin_message") {
    return "linkedin_channel"
  }
  else if (stepData.step_type === "general_task") {
    return "general_channel"
  }
  else if (stepData.step_type === "send_sms") {
    return "sms_channel"
  }
  else if (stepData.step_type === "send_whatsapp_message") {
    return "whatsapp_channel"
  }
  else if (stepData.step_type === "call") {
    return "call_channel"
  }
  else if (stepData.step_type === 'move_to_another_campaign') {
    return "independent_channel"
  }
  else {
    return "email_channel"
  }
}