import {groupBy, map} from 'lodash';
import { Campaigns } from '../models/campaigns';
import { Prospects } from '../models/prospect';

export function getTemplates(allTemplates: Campaigns.ICampaignStepTemplateApp[]): Campaigns.ITemplatesForCategory[] {
  // Group the templates by category
  const groupedByCategory = groupBy(allTemplates, t => t.category);

  // Map the grouped categories to the desired format
  const groupedTemplates = map(groupedByCategory, (catTemplates: Campaigns.ICampaignStepTemplateApp[], category: string) => {
    const cat: Campaigns.ITemplatesForCategory = {
      category: category,
      templatesData: map(catTemplates, template => {
        const catT: Campaigns.ICampaignStepTemplateFELib = {
          label: template.label,
          subject: template.subject,
          body: template.body,
          id: template.id ? template.id : null,
          template_is_from_library: template.is_from_library,
        };

        return catT;
      })
    };

    return cat;
  });

  return groupedTemplates;
}
  
  export function insertText(element: any, text: string) {
    var txtArea = element;
    console.log('insert at cursor', txtArea);
    if ((document as any).selection) {
      txtArea.focus();
      const sel = (document as any).selection.createRange();
      sel.text = text;
    }
    //MOZILLA and others
    else if (txtArea.selectionStart || txtArea.selectionStart == '0') {
      var startPos = txtArea.selectionStart;
      var endPos = txtArea.selectionEnd;
      txtArea.value = txtArea.value.substring(0, startPos)
        + text
        + txtArea.value.substring(endPos, txtArea.value.length);
      txtArea.selectionStart = startPos + text.length;
      txtArea.selectionEnd = startPos + text.length;
    } else {
      txtArea.value += text;
    }
  
    return txtArea.value || '';
  }

/*
  12-july-2024:
    filter was extracted out as a common function to be used at multiple places,
    to search accross all deduplication columns
*/
export function commonSearchFilterObj(prospectSearchString: string){
  const sub_filter: Prospects.ICustomDGSubFilterItem = {
    allowedFilterOperators: [
      {
        "display_name": "Contains",
        "key": "contains"
      },
      {
        "display_name": "Not contains",
        "key": "not_contains"
      },
      {
        "display_name": "Equals",
        "key": "equals"
      },
      {
        "display_name": "Not equals",
        "key": "not_equals"
      }
    ],
    field_type : "text",
    operator: "contains",
    value_display_name : prospectSearchString,
    value : prospectSearchString,
    is_custom : false,
    field: "email",
    field_display_name: "Email"
  }

  const company_filter: Prospects.ICustomDGSubFilterItem = {
    ...sub_filter,
    field: "company",
    field_display_name: "Company"
  }

  const subFilterFirstName: Prospects.ICustomDGSubFilterItem = {
    ...sub_filter,
    field: 'first_name',
    field_display_name: 'First name'
  }
  const subFilterLastName: Prospects.ICustomDGSubFilterItem = {
    ...sub_filter,
    field: 'last_name',
    field_display_name: 'Last name'
  }

  const phone_filter: Prospects.ICustomDGSubFilterItem = {
    ...sub_filter,
    field: 'phone',
    field_display_name: 'Phone'
  }

  const linkedin_filter: Prospects.ICustomDGSubFilterItem = {
    ...sub_filter,
    field: 'linkedin_url',
    field_display_name: 'Linkedin Url'
  }


  const filters: Prospects.ICustomDGSubFilter = {
    clause : "OR",
    filters: [sub_filter, company_filter, subFilterFirstName, subFilterLastName, phone_filter, linkedin_filter]
  }


  
  const query = {
    owner_ids: [0],
    clause: "AND",
    filters: prospectSearchString.trim().length ? [filters] : [],
  } as Prospects.ICustomDGFilter;

  return query
}


export function getLinkedInText(url: string){
  
  const tempSplit = url.trim().split('linkedin.com/');
  const res = tempSplit.length == 2 ? tempSplit[1] : url.trim()
  return res;
}

export function getTitle(data: Prospects.IProspectObject) {
  if(data.first_name || data.last_name){
    return (data.first_name +" "+data.last_name).trim()
  }else if(data.email){
    return data.email
  } else if(data.phone){
    return data.phone
  } else if(data.linkedin_url){
    return getLinkedInText(data.linkedin_url)
  } else {
    return " "
  }
}