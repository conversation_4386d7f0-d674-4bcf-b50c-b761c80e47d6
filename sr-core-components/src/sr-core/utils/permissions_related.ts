import { LogIn } from "../models/login"

export function checkPermission(currentPermission: LogIn.IPermissionV2, isOwner?: boolean): boolean {
  if (isOwner === undefined) {
    return (currentPermission.ownership === 'all' || currentPermission.ownership === 'owned')
  }
  else if (isOwner) {
    return (currentPermission.ownership === 'all' || currentPermission.ownership === 'owned')
  }
  else {
    return currentPermission.ownership === 'all'
  }

}
